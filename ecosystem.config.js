import 'pinpoint-node-agent'  

module.exports = {
    apps : [{
      name: 'agent',
      script: './bin/www',
  
      // Options reference: https://pm2.keymetrics.io/docs/usage/application-declaration/
      autorestart: true,
      watch: false,
      max_memory_restart: '500M',
      instances: 0,
      env: {
        "PINPOINT_CONTAINER": "false",
        "PINPOINT_ENABLE": "true",
        "PINPOINT_AGENT_ID": "billpayments",
        "PINPOINT_APPLICATION_NAME": "reminder",
        "PINPOINT_COLLECTOR_IP": "pinpoint-central-collector.mypaytm.com",
        "PINPOINT_LOG_LEVEL": "DEBUG"
      },
      env_production: {
        NODE_ENV: 'staging',
      }
    }],
  };