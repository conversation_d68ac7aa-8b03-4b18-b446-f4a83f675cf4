/*
  jshint 
    esversion: 8
 */

    'use strict';

    import { describe, it, before, beforeEach, afterEach } from 'mocha';
    import chai, { assert } from "chai";
    import sinonChai from "sinon-chai";
    import chaiAsPromised from "chai-as-promised";
    import MOMENT from 'moment'
    import sinon from 'sinon';
    import VA<PERSON><PERSON><PERSON><PERSON> from 'validator';
    import _ from 'lodash'
    import utility from '../../lib/index'
    
    import SERVICE from '../../services'
    import STARTUP_MOCK from '../__mocks__/startUp'
    import postpaidSmsParsing from '../../services/smsParsingBillPayment/postpaidSmsParsing';
import postpaidSmsParsingBillPaid from '../../services/smsParsingBillPayment/postpaidSmsParsingBillPaid';
    
    chai.use(chaiAsPromised);
    chai.use(sinonChai);
    
    const { expect } = chai;
    
    let server;

    it.skip("Moduele: postpaidSmsParsingBillPaid suite :: initializeVariable", function(){
        let serviceObj;
    
        let data, record;
        before(function () {    
            STARTUP_MOCK.init(function(error, options){
                serviceObj = new postpaidSmsParsingBillPaid(options);
                done();
            });
        });

        it("update variables if updated in config",()=>{
            let og = _.get(serviceObj.config, ['OPERATOR_TABLE_REGISTRY'],{});
            _.set(serviceObj.config, ['OPERATOR_TABLE_REGISTRY'],{airtel : 'bills_airtel2'});
            
            serviceObj.initializeVariable()
            assert.deepEqual(serviceObj.recent_bills_operators.airtel,'bills_airtel2')
            // /** Revert to original table */
            _.set(serviceObj.config, ['OPERATOR_TABLE_REGISTRY'],og);
            serviceObj.initializeVariable()
        })
    })

    it.skip("Moduele: postpaidSmsParsingBillPaid suite :: executeStrategy", function(){
        let serviceObj;
    
        let data, record;
        before(function () {    
            STARTUP_MOCK.init(function(error, options){
                serviceObj = new postpaidSmsParsingBillPaid(options);
                done();
            });
        });

        it("Empty SMS record",()=>{
            let stub1 = sinon.stub(serviceObj, "processRecord").returns(null);
            let record = {};
            serviceObj.executeStrategy((error)=>{
                if (error) {
                    expect(error).to.be.equal(null);
                }
            },record);
            expect(stub1).to.have.callCount(1);
            stub1.restore();
        });

        it('should handle null record', (done) => {
            const doneSpy = sinon.spy();
            let record = null;
            serviceObj.executeStrategy(doneSpy, null, record);
            expect(doneSpy.calledOnce).to.be.true;
            done();
        });

        it("valid SMS record",()=>{
            let record = {"data":[{"appVersion":"10.32.1","netWorkType":"4G","latitude":19.5977419,"deviceDateTime":1694585795530,"collector_timestamp":1694585797371,"wakeUpTimeInterval":16622187,"osVersion":"29","osType":"android","model":"CPH2137","msg_id":"d3da2a82-342e-4cb5-93e1-567d52b6f116","cId":"1707104162","longitude":76.7073808,"timestamp":"Wed Sep 13 06:16:37 UTC 2023","appCount":2,"uploadFrequency":14400,"clientId":"androidapp","eventType":"smsEvent","uploadTime":1694585796081,"true_client_ip":"2409:4042:e80:9e7c:de09:97b8:d24d:a4a","realTime":false,"db_name":"sms_parsed_data","newUser":false,"event_name":"sms","batteryPercentage":37,"smsUUID":"7a94021e-632f-4346-91ae-65de910d69a2","smsDateTime":1694585781607,"smsBody":"Thanks for Online payment of Rs 2090.00 towards MSEDCL Energy Bill For Cons No ************.Follow Us on Social Media @Facebook/Twitter/YouTube/Instagram.","smsSenderID":"AX-MSEDCL","smsReceiver":"7249201212","smsOperator":"Jio","predicted_category":"electricity","electricity_details":{"primary_ca_no":"************","secondary_ca_no":null,"bill_due_date":null,"bill_due_amount":"2090.0","operator":"MSEDCL"},"level_2_category":2}],"kafka_topic":["SMS_PARSER_ELECTRICITY"]};
            let stub1 = sinon.stub(serviceObj, "processRecord").returns();
            let stub2 = sinon.stub(utility, "_sendMetricsToDD").returns(null);
            serviceObj.executeStrategy((error)=>{
                if (error) {
                    expect(error).to.be.equal(null);
                }
            },record);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(0);
            stub1.restore();
            stub2.restore();
        });

        it("invalid SMS record",(done)=>{
            let record = {"data":[{"appVersion":"10.32.1","netWorkType":"4G","latitude":19.5977419,"deviceDateTime":1694585795530,"collector_timestamp":1694585797371,"wakeUpTimeInterval":16622187,"osVersion":"29","osType":"android","model":"CPH2137","msg_id":"d3da2a82-342e-4cb5-93e1-567d52b6f116","cId":"1707104162","longitude":76.7073808,"timestamp":"Wed Sep 13 06:16:37 UTC 2023","appCount":2,"uploadFrequency":14400,"clientId":"androidapp","eventType":"smsEvent","uploadTime":1694585796081,"true_client_ip":"2409:4042:e80:9e7c:de09:97b8:d24d:a4a","realTime":false,"db_name":"sms_parsed_data","newUser":false,"event_name":"sms","batteryPercentage":37,"smsUUID":"7a94021e-632f-4346-91ae-65de910d69a2","smsDateTime":1694585781607,"smsBody":"Thanks for Online payment of Rs 2090.00 towards MSEDCL Energy Bill For Cons No ************.Follow Us on Social Media @Facebook/Twitter/YouTube/Instagram.","smsSenderID":"AX-MSEDCL","smsReceiver":"7249201212","smsOperator":"Jio","predicted_category":"electricity","electricity_details":{"primary_ca_no":"","secondary_ca_no":null,"bill_due_date":null,"bill_due_amount":"2090.0","operator":"MSEDCL"},"level_2_category":2}],"kafka_topic":["SMS_PARSER_ELECTRICITY"]};
            let stub1 = sinon.stub(serviceObj, 'processRecord').callsFake((record, callback) => {
                callback(new Error('Invalid Record'));
            });
            const doneSpy = sinon.spy();
            let stub2 = sinon.stub(utility, "_sendMetricsToDD").returns(null);
            serviceObj.executeStrategy(doneSpy, record, {});

            setTimeout(() => {
                expect(doneSpy.calledOnce).to.be.true;
                expect(stub1).to.have.callCount(1);
                expect(stub2).to.have.callCount(1);
            }, 0);
            stub1.restore();
            stub2.restore();
            done();
        });
    })
    
    it.skip("Moduele: postpaidSmsParsingBillPaid suite :: validation record", function () {
      let serviceObj;
    
      let data, record;
      before(function () {    
          STARTUP_MOCK.init(function(error, options){
              serviceObj = new postpaidSmsParsingBillPaid(options);
              done();
          });
      });
        
        it("processRecord |  validateAndProcessRecord | Invalid record ",()=>{
            let record = {"appVersion":"10.32.1","netWorkType":"4G","latitude":19.5977419,"deviceDateTime":1694585795530,"collector_timestamp":1694585797371,"wakeUpTimeInterval":16622187,"osVersion":"29","osType":"android","model":"CPH2137","msg_id":"d3da2a82-342e-4cb5-93e1-567d52b6f116","cId":"1707104162","longitude":76.7073808,"timestamp":"Wed Sep 13 06:16:37 UTC 2023","appCount":2,"uploadFrequency":14400,"clientId":"androidapp","eventType":"smsEvent","uploadTime":1694585796081,"true_client_ip":"2409:4042:e80:9e7c:de09:97b8:d24d:a4a","realTime":false,"db_name":"sms_parsed_data","newUser":false,"event_name":"sms","batteryPercentage":37,"smsUUID":"7a94021e-632f-4346-91ae-65de910d69a2","smsDateTime":1694585781607,"smsBody":"Thanks for Online payment of Rs 2090.00 towards MSEDCL Energy Bill For Cons No ************.Follow Us on Social Media @Facebook/Twitter/YouTube/Instagram.","smsSenderID":"AX-MSEDCL","smsReceiver":"7249201212","smsOperator":"Jio","predicted_category":"electricity","level_2_category":2};
            let emptyrecord=null;
            serviceObj.validateAndProcessRecord((errorResponse,processedRecord)=>{
                expect(errorResponse).to.be.equal('invalid_record');
            },emptyrecord);
        });

        it("processRecord |  validateAndProcessRecord | Invalid rechargeNumber (rechargeNumber having text in mobile category)",()=>{
            let record = {"appVersion":"10.32.1","deviceDateTime":1694585795530,"msg_id":"d3da2a82-342e-4cb5-93e1-567d52b6f116","cId":"1707104162","timestamp":"Wed Sep 13 06:16:37 UTC 2023","uploadTime":1694585796081,"smsUUID":"7a94021e-632f-4346-91ae-65de910d69a2","smsDateTime":1694585781607,"smsBody":"Thanks for Online payment of Rs 2090.00 towards MSEDCL Energy Bill For Cons No ************.Follow Us on Social Media @Facebook/Twitter/YouTube/Instagram.","smsSenderID":"AX-MSEDCL","smsReceiver":"7249201212","smsOperator":"Jio","predicted_category":"mobile prepaid","telecom_details":{"mobile_number":"123456789Q","bill_due_date":null,"bill_due_amount":"2090.0","operator":"jio"},"level_2_category":2};
            serviceObj.validateAndProcessRecord((errorResponse,processedRecord)=>{
                expect(errorResponse).to.be.equal('rechargeNumber Invalid');
            },record);
        });

        it("processRecord |  validateAndProcessRecord | Invalid rechargeNumber (rechargeNumber is null in mobile category then smsReceiver will pick)",()=>{
            let record = {"appVersion":"10.32.1","deviceDateTime":1694585795530,"msg_id":"d3da2a82-342e-4cb5-93e1-567d52b6f116","cId":"1707104162","timestamp":"Wed Sep 13 06:16:37 UTC 2023","uploadTime":1694585796081,"smsUUID":"7a94021e-632f-4346-91ae-65de910d69a2","smsDateTime":1694585781607,"smsBody":"Thanks for Online payment of Rs 2090.00 towards MSEDCL Energy Bill For Cons No ************.Follow Us on Social Media @Facebook/Twitter/YouTube/Instagram.","smsSenderID":"AX-MSEDCL","smsReceiver":"7249201212","smsOperator":"Jio","predicted_category":"mobile prepaid","telecom_details":{"mobile_number":null,"bill_due_date":null,"bill_due_amount":"2090.0","operator":"airtel"},"level_2_category":2};
            serviceObj.validateAndProcessRecord((errorResponse,processedRecord)=>{
                expect(errorResponse).to.be.equal(null);
                expect(processedRecord.customerId).to.be.equal(VALIDATOR.toInt(record.cId));
                expect(processedRecord.rechargeNumber).to.be.equal(record.smsReceiver);
            },record);
        });

        it("processRecord |  validateAndProcessRecord | Invalid rechargeNumber (rechargeNumber is null and smsReceiver is null in mobile category)",()=>{
            let record = {"appVersion":"10.32.1","deviceDateTime":1694585795530,"msg_id":"d3da2a82-342e-4cb5-93e1-567d52b6f116","cId":"1707104162","timestamp":"Wed Sep 13 06:16:37 UTC 2023","uploadTime":1694585796081,"smsUUID":"7a94021e-632f-4346-91ae-65de910d69a2","smsDateTime":1694585781607,"smsBody":"Thanks for Online payment of Rs 2090.00 towards MSEDCL Energy Bill For Cons No ************.Follow Us on Social Media @Facebook/Twitter/YouTube/Instagram.","smsSenderID":"AX-MSEDCL","smsReceiver":null,"smsOperator":"Jio","predicted_category":"mobile prepaid","telecom_details":{"mobile_number":null,"bill_due_date":null,"bill_due_amount":"2090.0","operator":"airtel"},"level_2_category":2};
            serviceObj.validateAndProcessRecord((errorResponse,processedRecord)=>{
                expect(errorResponse).to.be.equal("rechargeNumber Empty");
            },record);
        });

        it("processRecord |  validateAndProcessRecord | Invalid rechargeNumber (rechargeNumber having less than 10 digit in mobile category)",()=>{
          let record = {"appVersion":"10.32.1","deviceDateTime":1694585795530,"msg_id":"d3da2a82-342e-4cb5-93e1-567d52b6f116","cId":"1707104162","timestamp":"Wed Sep 13 06:16:37 UTC 2023","uploadTime":1694585796081,"smsUUID":"7a94021e-632f-4346-91ae-65de910d69a2","smsDateTime":1694585781607,"smsBody":"Thanks for Online payment of Rs 2090.00 towards MSEDCL Energy Bill For Cons No ************.Follow Us on Social Media @Facebook/Twitter/YouTube/Instagram.","smsSenderID":"AX-MSEDCL","smsReceiver":"7249201212","smsOperator":"Jio","predicted_category":"mobile prepaid","telecom_details":{"mobile_number":"1234","bill_due_date":null,"bill_due_amount":"2090.0","operator":"jio"},"level_2_category":2};
          serviceObj.validateAndProcessRecord((errorResponse,processedRecord)=>{
              expect(errorResponse).to.be.equal('rechargeNumber Invalid');
          },record);
        });

        it("processRecord |  validateAndProcessRecord | productId missing ",()=>{
          let record = {"cId":"1707104162","timestamp":"Wed Sep 13 06:16:37 UTC 2023","predicted_category":"electricity","electricity_details":{"primary_ca_no":"************","bill_due_amount":"2090.0","operator":"tata power"},"level_2_category":2};
          serviceObj.validateAndProcessRecord((errorResponse,processedRecord)=>{
                expect(errorResponse).to.be.equal("Mandatory Params productId is Missing / Invalid");
                expect(processedRecord.customerId).to.be.equal(VALIDATOR.toInt(record.cId));
                expect(processedRecord.rechargeNumber).to.be.equal(record.electricity_details.primary_ca_no);
                expect(processedRecord.operator).to.be.equal(_.toLower(record.electricity_details.operator));
                let service = record.category;
                if(service == "mobile prepaid")
                  service = "MOBILE"
                else
                  service = "ELECTRICITY";
                expect(processedRecord.category).to.be.equal(service);
            },record);
        });

        it("processRecord |  validateAndProcessRecord | customerId missing ",()=>{
          let record = {"cId":null,"timestamp":"Wed Sep 13 06:16:37 UTC 2023","predicted_category":"electricity","electricity_details":{"primary_ca_no":"************","bill_due_amount":"2090.0","operator":"tneb"},"level_2_category":2};
          serviceObj.validateAndProcessRecord((errorResponse,processedRecord)=>{
                expect(errorResponse).to.be.equal("Mandatory Params customerId is Missing / Invalid");
                expect(processedRecord.customerId).to.be.equal(null);
                expect(processedRecord.rechargeNumber).to.be.equal(record.electricity_details.primary_ca_no);
                expect(processedRecord.operator).to.be.equal(_.toLower(record.electricity_details.operator));
                let service = record.category;
                if(service == "mobile prepaid")
                  service = "MOBILE"
                else
                  service = "ELECTRICITY";
                expect(processedRecord.category).to.be.equal(service);
            },record);
        });
       
        it("processRecord |  validateAndProcessRecord | rechargeNumber missing ",()=>{
          let record = {"cId":"1234567","timestamp":"Wed Sep 13 06:16:37 UTC 2023","predicted_category":"electricity","electricity_details":{"primary_ca_no":null,"bill_due_amount":"2090.0","operator":"tneb"},"level_2_category":2};
          serviceObj.validateAndProcessRecord((errorResponse,processedRecord)=>{
                expect(errorResponse).to.be.equal("Mandatory Params rechargeNumber is Missing / Invalid");
                expect(processedRecord.customerId).to.be.equal(VALIDATOR.toInt(record.cId));
                expect(processedRecord.rechargeNumber).to.be.equal(record.electricity_details.primary_ca_no);
                expect(processedRecord.operator).to.be.equal(_.toLower(record.electricity_details.operator));
                let service = record.category;
                if(service == "mobile prepaid")
                  service = "MOBILE"
                else
                  service = "ELECTRICITY";
                expect(processedRecord.category).to.be.equal(service);
            },record);
        });
    
        it("processRecord |  validateAndProcessRecord | currentPaidAmount missing ",()=>{
          let record = {"cId":"123456","timestamp":"Wed Sep 13 06:16:37 UTC 2023","predicted_category":"electricity","electricity_details":{"primary_ca_no":"************","bill_due_amount":null,"operator":"tneb"},"level_2_category":2};
          serviceObj.validateAndProcessRecord((errorResponse,processedRecord)=>{
                expect(errorResponse).to.be.equal("Mandatory Params currentPaidAmount is Missing / Invalid");
                expect(processedRecord.customerId).to.be.equal(VALIDATOR.toInt(record.cId));
                expect(processedRecord.rechargeNumber).to.be.equal(record.electricity_details.primary_ca_no);
                expect(processedRecord.operator).to.be.equal(_.toLower(record.electricity_details.operator));
                let service = record.category;
                if(service == "mobile prepaid")
                  service = "MOBILE"
                else
                  service = "ELECTRICITY";
                expect(processedRecord.category).to.be.equal(service);
            },record);
        });
         
        it("processRecord |  validateAndProcessRecord | tableName not found ",()=>{
            let record = {"cId":"1707104162","timestamp":"Wed Sep 13 06:16:37 UTC 2023","predicted_category":"electricity","electricity_details":{"primary_ca_no":"************","bill_due_amount":"2090.0","operator":"msedcl"},"level_2_category":2};
            serviceObj.validateAndProcessRecord((errorResponse,processedRecord)=>{
              expect(errorResponse).to.be.equal(`Table not found for ${record.electricity_details.operator}`)  
              expect(processedRecord.customerId).to.be.equal(VALIDATOR.toInt(record.cId));
                expect(processedRecord.rechargeNumber).to.be.equal(record.electricity_details.primary_ca_no);
                expect(processedRecord.operator).to.be.equal(_.toLower(record.electricity_details.operator));
                let service = record.category;
                if(service == "mobile prepaid")
                  service = "MOBILE"
                else
                  service = "ELECTRICITY";
                expect(processedRecord.category).to.be.equal(service);
                expect(processedRecord.productId).to.be.equal(_.get(serviceObj.config,['DYNAMIC_CONFIG', 'DWH_ELECTRICITY_SMS_PARSING_CONFIG', 'OPERATOR_PRODUCT_ID_MAPPING', 'PID_MAP', processedRecord.operator],null));
            },record);
        });
    
        it("processRecord |  validateAndProcessRecord | success | valid record ",()=>{
          let record = {"cId":"1707104162","timestamp":"Wed Sep 13 06:16:37 UTC 2023","predicted_category":"electricity","electricity_details":{"primary_ca_no":"************","bill_due_amount":"2090.0","operator":"tneb"},"level_2_category":2};
          serviceObj.validateAndProcessRecord((errorResponse,processedRecord)=>{
              expect(processedRecord.tableName).to.be.equal(_.get(serviceObj.config,['OPERATOR_TABLE_REGISTRY', processedRecord.operator],null));
              expect(processedRecord.customerId).to.be.equal(VALIDATOR.toInt(record.cId));
              expect(processedRecord.rechargeNumber).to.be.equal(record.electricity_details.primary_ca_no);
              expect(processedRecord.operator).to.be.equal(_.toLower(record.electricity_details.operator));
              let service = record.category;
              if(service == "mobile prepaid")
                service = "MOBILE"
              else
                service = "ELECTRICITY";
              expect(processedRecord.category).to.be.equal(service);
              expect(processedRecord.productId).to.be.equal(_.get(serviceObj.config,['DYNAMIC_CONFIG', 'DWH_ELECTRICITY_SMS_PARSING_CONFIG', 'OPERATOR_PRODUCT_ID_MAPPING', 'PID_MAP', processedRecord.operator],null));
            },record);
        });
    })

    it.skip('Async function getRecordsFromDb', () => {
        let serviceObj;
      
        before(() => {
            STARTUP_MOCK.init(function(error, options){
                serviceObj = new postpaidSmsParsing(options);
          });
        })
      
        it('should handle error from bills.getBillsOfSameRech', (done) => {
          
          const billsMock = {
            getBillsOfSameRech: sinon.stub().callsFake((callback) => {
              callback(new Error('DB query error'), null);
            }),
          };
          let fakeRecord={};
      
          serviceObj.bills = billsMock; 
      
            serviceObj.getRecordsFromDb((err, result) => {
           
            assert.instanceOf(err, Error);
            assert.isFalse(result); 
            done();
          }, fakeRecord);
      
          });
      
        it('should handle no data found', (done) => {
          const billsMock = {
            getBillsOfSameRech: sinon.stub().callsFake((callback) => {
              callback(null, []); 
            }),
          };
      
          let fakeRecord={};
    
          serviceObj.bills = billsMock; 
      
            serviceObj.getRecordsFromDb((err, result) => {
            assert.isNull(err); 
            assert.isFalse(result); 
            done();
          }, fakeRecord);
      
          });
      
        it('should handle data found', (done) => {
          
          const data = [
            {}
          ];
          const billsMock = {
            getBillsOfSameRech: sinon.stub().callsFake((callback) => {
              callback(null, data);
            }),
          };
          let fakeRecord={};
    
      
          serviceObj.bills = billsMock; 
      
            serviceObj.getRecordsFromDb((err, result) => {
            assert.isNull(err); 
            assert.isTrue(result); 
            
            done();
          }, fakeRecord);
      
          });
      });
    
    it.skip("Module: postpaid SMS Parsing suite :: Record processing", function () {
        let serviceObj;
    
        let data, record;
        before(function () {    
            STARTUP_MOCK.init(function(error, options){
                serviceObj = new postpaidSmsParsingBillPaid(options);
                done();
            });
        });
    
        it("processRecords  | error in validate function", (done) => {

          let record = {"cId":"1707104162","timestamp":"Wed Sep 13 06:16:37 UTC 2023","predicted_category":"electricity","electricity_details":{"primary_ca_no":"************","bill_due_amount":"2090.0","operator":"tneb"},"level_2_category":2};
            let processedRecord = { operator: 'tneb',
            customerId: 1707104162,
            rechargeNumber: '************',
            gateway: null,
            billFetchDate: null,
            billDate: null,
            dueDate: null,
            currentPaidAmount: -2090,
            status: 14,
            paytype: undefined,
            customerMobile: null,
            customerEmail: null,
            extra: null,
            paymentDate: "2023-09-26T00:00:00.000Z",
            msgId: '',
            senderId: null,
            category: 'ELECTRICITY',
            appVersion: null,
            smsSenderID: '',
            productId: 1234567,
            service: '',
            debugKey: 'rech_num:************::operator:tneb::service:::productId:1234567',
            oldProductId: 1234567,
            tableName: 'bills_tneb' };
    
            let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').callsFake(function fakeFn(done) {
                return done("error in validateAndProcessedRecord", processedRecord);
            });
    
            let stub2 = sinon.stub(serviceObj, 'getForwardActionFlow').callsFake(function fakeFn(done) {
                return done(null, 'findAndCreateToCassandra', processedRecord);
            });
    
            let stub3 = sinon.stub(serviceObj, 'updateCassandra').callsFake(function fakeFn(done) {
                return done(null, 'findAndCreateToCassandra', processedRecord);
            });
    
    
            serviceObj.processRecord(record, function (error) {
                expect(error).to.be.equal(undefined);
                expect(stub1).to.have.callCount(1);
                expect(stub2).to.have.callCount(0);
                expect(stub3).to.have.callCount(0);
                stub1.restore();
                stub2.restore();
                stub3.restore();
                return done();
            });
        });
    
        it("processRecords  | error in getForward action function", (done) => {
    
          let record = {"cId":"1707104162","timestamp":"Wed Sep 13 06:16:37 UTC 2023","predicted_category":"electricity","electricity_details":{"primary_ca_no":"************","bill_due_amount":"2090.0","operator":"tneb"},"level_2_category":2};
          let processedRecord = { operator: 'tneb',
          customerId: 1707104162,
          rechargeNumber: '************',
          gateway: null,
          billFetchDate: null,
          billDate: null,
          dueDate: null,
          currentPaidAmount: -2090,
          status: 14,
          paytype: undefined,
          customerMobile: null,
          customerEmail: null,
          extra: null,
          paymentDate: "2023-09-26T00:00:00.000Z",
          msgId: '',
          senderId: null,
          category: 'ELECTRICITY',
          appVersion: null,
          smsSenderID: '',
          productId: 1234567,
          service: '',
          debugKey: 'rech_num:************::operator:tneb::service:::productId:1234567',
          oldProductId: 1234567,
          tableName: 'bills_tneb' };
    
            let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').callsFake(function fakeFn(done) {
                return done(null, processedRecord);
            });
    
            let stub2 = sinon.stub(serviceObj, 'getForwardActionFlow').callsFake(function fakeFn(done) {
                return done("error in getForwardActionFlow");
            });
    
            let stub3 = sinon.stub(serviceObj, 'updateCassandra').callsFake(function fakeFn(done) {
                return done(null, 'findAndCreateToCassandra', processedRecord);
            });

            serviceObj.processRecord(record, function (error) {
                expect(error).to.be.equal(undefined);
                expect(stub1).to.have.callCount(1);
                expect(stub2).to.have.callCount(1);
                expect(stub3).to.have.callCount(0);

                stub1.restore();
                stub2.restore();
                stub3.restore();
    
                return done();
            });
        });
    
        it("processRecords  | error in update cassandra function", (done) => {
    
          let record = {"cId":"1707104162","timestamp":"Wed Sep 13 06:16:37 UTC 2023","predicted_category":"electricity","electricity_details":{"primary_ca_no":"************","bill_due_amount":"2090.0","operator":"tneb"},"level_2_category":2};
          let processedRecord = { operator: 'tneb',
          customerId: 1707104162,
          rechargeNumber: '************',
          gateway: null,
          billFetchDate: null,
          billDate: null,
          dueDate: null,
          currentPaidAmount: -2090,
          status: 14,
          paytype: undefined,
          customerMobile: null,
          customerEmail: null,
          extra: null,
          paymentDate: "2023-09-26T00:00:00.000Z",
          msgId: '',
          senderId: null,
          category: 'ELECTRICITY',
          appVersion: null,
          smsSenderID: '',
          productId: 1234567,
          service: '',
          debugKey: 'rech_num:************::operator:tneb::service:::productId:1234567',
          oldProductId: 1234567,
          tableName: 'bills_tneb' };
    
            let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').callsFake(function fakeFn(done) {
                return done(null, processedRecord);
            });
    
            let stub2 = sinon.stub(serviceObj, 'getForwardActionFlow').callsFake(function fakeFn(done) {
                return done(null, "findAndCreateToCassandra", processedRecord);
            });
    
            let stub3 = sinon.stub(serviceObj, 'updateCassandra').callsFake(function fakeFn(done) {
                return done("Error in updateCassandra");
            });
  
            serviceObj.processRecord(record, function (error) {
                expect(error).to.be.equal(undefined);
                expect(stub1).to.have.callCount(1);
                expect(stub2).to.have.callCount(1);
                expect(stub3).to.have.callCount(1);
    
                stub1.restore();
                stub2.restore();
                stub3.restore();
    
                return done();
            });
        });
    
        it("processRecords | findAndCreateToCassandra flow | recordNotFound", (done) => {
            
          let record = {"cId":"1707104162","timestamp":"Wed Sep 13 06:16:37 UTC 2023","predicted_category":"electricity","electricity_details":{"primary_ca_no":"************","bill_due_amount":"2090.0","operator":"tneb"},"level_2_category":2};
          let processedRecord = { operator: 'tneb',
          customerId: 1707104162,
          rechargeNumber: '************',
          gateway: null,
          billFetchDate: null,
          billDate: null,
          dueDate: null,
          currentPaidAmount: -2090,
          status: 14,
          paytype: undefined,
          customerMobile: null,
          customerEmail: null,
          extra: null,
          paymentDate: "2023-09-26T00:00:00.000Z",
          msgId: '',
          senderId: null,
          category: 'ELECTRICITY',
          appVersion: null,
          smsSenderID: '',
          productId: 1234567,
          service: '',
          debugKey: 'rech_num:************::operator:tneb::service:::productId:1234567',
          oldProductId: 1234567,
          tableName: 'bills_tneb' };
    
            let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').callsFake(function fakeFn(done) {
                return done(null, processedRecord);
            });
    
            let stub2 = sinon.stub(serviceObj, 'getForwardActionFlow').callsFake(function fakeFn(done) {
                return done(null, 'findAndCreateToCassandra', processedRecord);
            });
    
            let stub3 = sinon.stub(serviceObj, 'updateCassandra').callsFake(function fakeFn(done) {
                return done(null, processedRecord);
            });

            serviceObj.processRecord(record, function (error) {
                expect(error).to.be.equal(undefined);
                expect(stub1).to.have.callCount(1);
                expect(stub2).to.have.callCount(1);
                expect(stub3).to.have.callCount(1);
    
                stub1.restore();
                stub2.restore();
                stub3.restore();
                return done();
            });
        });
    
        it("processRecords | Cassandra insert | record Not Found Of SameCustId", (done) => {
            
            let record = {"cId":"1707104162","timestamp":"Wed Sep 13 06:16:37 UTC 2023","predicted_category":"electricity","electricity_details":{"primary_ca_no":"************","bill_due_amount":"2090.0","operator":"tneb"},"level_2_category":2};
            let processedRecord = { operator: 'tneb',
            customerId: 1707104162,
            rechargeNumber: '************',
            gateway: null,
            billFetchDate: null,
            billDate: null,
            dueDate: null,
            currentPaidAmount: -2090,
            status: 14,
            paytype: undefined,
            customerMobile: null,
            customerEmail: null,
            extra: null,
            paymentDate: "2023-09-26T00:00:00.000Z",
            msgId: '',
            senderId: null,
            category: 'ELECTRICITY',
            appVersion: null,
            smsSenderID: '',
            productId: 1234567,
            service: '',
            debugKey: 'rech_num:************::operator:tneb::service:::productId:1234567',
            oldProductId: 1234567,
            tableName: 'bills_tneb' };
    
            let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').callsFake(function fakeFn(done) {
                return done(null, processedRecord);
            });
    
            let stub2 = sinon.stub(serviceObj, 'getForwardActionFlow').callsFake(function fakeFn(done) {
                return done(null, 'findAndCreateToCassandra', processedRecord);
            });
    
            let stub3 = sinon.stub(serviceObj, 'updateCassandra').callsFake(function fakeFn(done) {
                return done(null, processedRecord);
            });
    
            serviceObj.processRecord(record, function (error) {
                expect(error).to.be.equal(undefined);
                expect(stub1).to.have.callCount(1);
                expect(stub2).to.have.callCount(1);
                expect(stub3).to.have.callCount(1);
    
                stub1.restore();
                stub2.restore();
                stub3.restore();
                return done();
            });
        });
    
        it("getForwardActionFlow | record Found in DB | error : ", (done) => {
            
          let record = {"cId":"1707104162","timestamp":"Wed Sep 13 06:16:37 UTC 2023","predicted_category":"electricity","electricity_details":{"primary_ca_no":"************","bill_due_amount":"2090.0","operator":"tneb"},"level_2_category":2};
          let processedRecord = { operator: 'tneb',
          customerId: 1707104162,
          rechargeNumber: '************',
          gateway: null,
          billFetchDate: null,
          billDate: null,
          dueDate: null,
          currentPaidAmount: -2090,
          status: 14,
          paytype: undefined,
          customerMobile: null,
          customerEmail: null,
          extra: null,
          paymentDate: "2023-09-26T00:00:00.000Z",
          msgId: '',
          senderId: null,
          category: 'ELECTRICITY',
          appVersion: null,
          smsSenderID: '',
          productId: 1234567,
          service: '',
          debugKey: 'rech_num:************::operator:tneb::service:::productId:1234567',
          oldProductId: 1234567,
          tableName: 'bills_tneb',recordFoundOfSameCustId : true};
    
            let stub1 = sinon.stub(serviceObj.postpaidSmsParsing, 'getRecordsFromDb').callsFake(function fakeFn(done) {
                return done(null, processedRecord);
            });
    
            serviceObj.getForwardActionFlow((errorResponse,action)=>{
                expect(errorResponse).to.be.equal("record found in table");
                expect(stub1).to.have.callCount(1);
                stub1.restore();
                return done();
            },processedRecord);
    
        });
    });