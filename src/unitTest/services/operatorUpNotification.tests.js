/*
  jshint 
    esversion: 8
 */

    'use strict';

    import { describe, it, before, beforeEach, afterEach } from 'mocha';
    import chai from "chai";
    import sinonChai from "sinon-chai";
    import chaiAsPromised from "chai-as-promised";
    import MOMENT from 'moment'
    import sinon from 'sinon';
    import _ from 'lodash'
    
    import SERVICE from '../../services'
    import STARTUP_MOCK from '../__mocks__/startUp'
    import SmsParsingCCBills from '../../services/smsParsingCCBills';
    
    chai.use(chaiAsPromised);
    chai.use(sinonChai);
    
    const { expect } = chai;
    
    let server;
    
    describe("Module: Operator up notification sonsumer suite :: Kafka consumer validations", function () {
        let serviceObj;
    
        let data, record;
        before(function () {
            STARTUP_MOCK.init(function(error, options){
                serviceObj = new SERVICE.OperatorUpNotification(options);
                done();
            });
        });
    
    
        it("testing initialize consumer | default values", () => {
            // let stub = sinon.stub(serviceObj.notify, 'configureRedis').callsFake(function(callback){
            //     return callback(null);
            // })
            let cb = sinon.spy();
            serviceObj.configureKafka(cb);
            expect(cb).to.have.callCount(1);
            expect(cb).to.have.calledWith(null)
            // stub.restore();
            
        });
    
        it("start service || ensure service initialises consumer", () => {
            let initializeStub = sinon.stub(serviceObj, 'configureKafka').yields(null);
            serviceObj.start();
            expect(initializeStub).to.have.been.calledOnce;
        });
    
        it("processKafkaData || ensure empty records are validated", () => {
            let record = {}
            let processedRecords = serviceObj.execSteps(record);
            expect(processedRecords).to.be.equal(undefined)
        })
    
    });
    
    
    describe("Module: operator up notification Consumer suite", function () {
        let serviceObj;
    
        before(function (done) {
            STARTUP_MOCK.init(function(error, options){
                serviceObj = new SERVICE.OperatorUpNotification(options);
                done();
            });
        });
    
        beforeEach(function () {
            server = sinon.fakeServer.create();
        });
    
        afterEach(function () {
            server.restore();
        });
        
        it("execSteps || ensure empty records are validated", () => {
            let record = {}
            let processedRecords = serviceObj.execSteps(record);
            expect(processedRecords).to.be.equal(undefined)
        })
        it("execSteps || greyScaleEnv || Error in commitOffset function", () => {
            let record = [
            ];
            let stub1 = sinon.stub(serviceObj, "processBatch").resolves();
            sinon.stub(process, 'exit');
            let clock = sinon.useFakeTimers();
            serviceObj.kafkaConsumer = {
                _pauseConsumer : () => {
                    return;
                },
                commitOffset: (data, cb) => {
                    return cb("Error in commitOffset");
                },
                _resumeConsumer: () => {
                }
            };
            serviceObj.greyScaleEnv = true;
            
            let processedRecords = serviceObj.execSteps(record);
            clock.tick(40 * 60 * 1000)
            expect(processedRecords).to.be.equal(undefined);
            expect(stub1).to.have.callCount(0);
            stub1.restore();
            process.exit.restore();
        })
        it("execSteps || greyScaleEnv || commitOffset function", () => {
            let record = [
            ];
            let stub1 = sinon.stub(serviceObj, "processBatch").resolves();
            let clock = sinon.useFakeTimers();
    
            serviceObj.kafkaConsumer = {
                _pauseConsumer : () => {
                    return;
                },
                commitOffset: (data, cb) => {
                    return cb();
                },
                _resumeConsumer: () => {
    
                }
            };
            serviceObj.greyScaleEnv = true;
            
            let processedRecords = serviceObj.execSteps(record);
            clock.tick(40 * 60 * 1000)
            expect(processedRecords).to.be.equal(undefined);
            expect(stub1).to.have.callCount(0);
            stub1.restore();
        })
        it("execSteps || processBatch function", () => {
            let data = {"abs":"123"}
            let payLoad = {
                value: JSON.stringify(data)
            };
            let record = [
                {...payLoad}
            ];
            let clock = sinon.useFakeTimers();
            let stub1 = sinon.stub(serviceObj, 'processBatch').yields();
            
            serviceObj.kafkaConsumer = {
                _pauseConsumer : () => {
                    return;
                },
                commitOffset: (data, cb) => {
                    return cb();
                },
                _resumeConsumer: () => {
                }
            };
            let processedRecords = serviceObj.execSteps(record);
            clock.tick(40 * 60 * 1000)
            expect(processedRecords).to.be.equal(undefined);
            expect(stub1).to.have.callCount(1);
            stub1.restore();
        })
    
        it("processBatch function | InValid Kafka record received", (done) => {
            let records = [{ partition: 1, topic: 'operator_health', value : ''}];
            let clock = sinon.useFakeTimers();
            serviceObj.processBatch(records, function (){
                return done();
            });
            clock.tick(40 * 60 * 1000);    
        });

        it.skip("processBatch function | operator is not up received", (done) => {
            let records = [{ partition: 1, topic: 'operator_health', value : '{"gateway_name":"adanigas","operator":"adani gas limited","request_type":"RECHARGE","operator_down":true}'}];
            let clock = sinon.useFakeTimers();
        });

        it("processBatch function | Valid Kafka record received", (done) => {
            let records = [{ partition: 1, topic: 'operator_health', value : '{"gateway_name":"adanigas","operator":"adani gas limited","request_type":"RECHARGE","operator_down":false}'}];
            let clock = sinon.useFakeTimers();
            let get = (cb) => {
                return cb(null,[{"gateway":"adanigas","operator":"adani gas limited","recharge_number":1, "customer_id": 1}]);
            }
            let update = (cb) => {
                return cb(null);
            }
            let send = (cb) => {
                return cb(null);
            }
            serviceObj.droppedTransactions.getDroppedTransaction = sinon.spy(get);
            serviceObj.droppedTransactions.updatedDroppedTransaction = sinon.spy(update);
            serviceObj.sendNotification = sinon.spy(send);

            serviceObj.processBatch(records, function (){
                return done();
            }); 
            clock.tick(40 * 60 * 1000);      
        });


        it("processRecord function | InValid Kafka record received", (done) => {
            let records = { partition: 1, topic: 'operator_health', value : ''};
            serviceObj.recordMap = {};
            serviceObj.processRecord(records, function (error){
                expect(error).to.be.equal('Invalid record!');    
                return done();
            });    
        });

        it.skip("processRecord function | operator is not up received", (done) => {
            let records = { partition: 1, topic: 'operator_health', value : '{"gateway_name":"adanigas","operator":"adani gas limited","request_type":"RECHARGE","operator_down":true}'};
            serviceObj.recordMap = {};
            serviceObj.processRecord(records, function (error){
                expect(error).to.be.equal('operator is not up yet!');    
                return done();
            });    
        });

        it("processRecord function | Valid Kafka record received", (done) => {
            let records = { partition: 1, topic: 'operator_health', value : '{"gateway_name":"adanigas","operator":"adani gas limited","request_type":"RECHARGE","operator_down":false}'};
            serviceObj.recordMap = {};
            let get = (cb) => {
                return cb(null,[{"gateway":"adanigas","operator":"adani gas limited","recharge_number":1, "customer_id": 1}]);
            }
            let update = (cb) => {
                return cb();
            }
            let send = (cb) => {
                return cb();
            }
            serviceObj.droppedTransactions.getDroppedTransaction = sinon.spy(get);
            serviceObj.droppedTransactions.updatedDroppedTransaction = sinon.spy(update);
            serviceObj.sendNotification = sinon.spy(send);

            serviceObj.processRecord(records, function (error){
                console.log('operator valid', error);
                expect(serviceObj.droppedTransactions.updatedDroppedTransaction).to.have.callCount(1);
                expect(serviceObj.droppedTransactions.getDroppedTransaction).to.have.callCount(1);
                expect(serviceObj.sendNotification).to.have.callCount(1)
                
                expect(error).to.be.equal(null);
                return done();
            });    
        });

        it("processRecord function | send notification send error", (done) => {
            let records = { partition: 1, topic: 'operator_health', value : '{"gateway_name":"adanigas","operator":"adani gas limited","request_type":"RECHARGE","operator_down":false}'};
            serviceObj.recordMap = {};
            let get = (cb) => {
                return cb(null,[{"gateway":"adanigas","operator":"adani gas limited","recharge_number":1, "customer_id": 1}]);
            };
            let update = (cb) => {
                return cb();
            };
            let send = (cb) => {
                return cb("template id is missing");
            };
            serviceObj.droppedTransactions.getDroppedTransaction = sinon.spy(get);
            serviceObj.droppedTransactions.updatedDroppedTransaction = sinon.spy(update);
            serviceObj.sendNotification = sinon.spy(send);

            serviceObj.processRecord(records, function (error){
                console.log('operator notification', error);
                expect(error).to.be.equal("template id is missing");
                expect(serviceObj.droppedTransactions.updatedDroppedTransaction).to.have.callCount(0)
                return done();
            });    
        });

        it("processRecord function | no fropped transations in db", (done) => {
            let records = { partition: 1, topic: 'operator_health', value : '{"gateway_name":"adanigas","operator":"adani gas limited","request_type":"RECHARGE","operator_down":false}'};
            serviceObj.recordMap = {};
            let get = (cb) => {
                return cb(null,[]);
            }
            let update = (cb) => {
                return cb();
            }
            let send = (cb) => {
                return cb();
            }
            serviceObj.droppedTransactions.getDroppedTransaction = sinon.spy(get);
            serviceObj.droppedTransactions.updatedDroppedTransaction = sinon.spy(update);
            serviceObj.sendNotification = sinon.spy(send);

            serviceObj.processRecord(records, function (error){
                console.log('operator fropped', error);
                expect(error).to.be.equal(null);
                expect(serviceObj.droppedTransactions.updatedDroppedTransaction).to.have.callCount(0)
                expect(serviceObj.sendNotification).to.have.callCount(0)
                return done();
            });    
        });
       
    });
    