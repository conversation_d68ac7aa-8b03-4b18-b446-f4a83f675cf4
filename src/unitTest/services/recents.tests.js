/*
  jshint 
    esversion: 8
 */

'use strict';

import { describe, it, before} from 'mocha';
import chai from "chai";
import sinonChai from "sinon-chai";
import sinon from 'sinon';
import chaiAsPromised from "chai-as-promised";
import MOMENT from 'moment'
import _, { isNull } from 'lodash';

import STARTUP_MOCK from '../__mocks__/startUp'

import SERVICE from '../../services'

chai.use(chaiAsPromised);
chai.use(sinonChai);

const { expect } = chai;

describe("Module: recents service test suite", function () {
    let serviceObj;

    before(function (done) {
        STARTUP_MOCK.init(function(error, options){
            options.config = options.config || {};
            options.config.ENCRYPTION_CONFIG = options.config.ENCRYPTION_CONFIG || {};
            options.config.ENCRYPTION_CONFIG.DEFAULT = options.config.ENCRYPTION_CONFIG.DEFAULT || {};
            options.config.ENCRYPTION_CONFIG.DEFAULT.IV = '68e232d2639555a0cf08aaed9d50a025'; //adding staging keys as mock config
            options.config.ENCRYPTION_CONFIG.DEFAULT.ENCRYPTION_KEY = 'vOVH6sdmpNWjRRIqCc7rdxs01lwHzfr3'; 
            serviceObj = new SERVICE.recentBills(options);
            done();
        });
    });

    it("_prepareDataToInsert | airtel Operator | self payment", (done) => {
        let recordData = {"id":"************","catalogProductID":"194","reqType":"RECHARGE","userData_recharge_number":"recents_airtel_rn_001","customerInfo_customer_id":667773,"userData_amount":600,"inStatusMap_responseCode":"00","productInfo_operator":"airtel","productInfo_paytype" : "postpaid","currentGw":"bharatbillpay","customerInfo_customer_type":1};
        serviceObj._prepareDataToInsert(recordData,done);
    });
    // it("_prepareDataToInsert | airtel Operator | payment by other customer", (done) => {
    //     let recordData = {"id":"************","catalogProductID":"194","reqType":"RECHARGE","userData_recharge_number":"airtelRn","customerInfo_customer_id":667774,"userData_amount":600,"inStatusMap_responseCode":"00","productInfo_operator":"airtel","productInfo_paytype" : "postpaid","currentGw":"bharatbillpay"};
    //     serviceObj._prepareDataToInsert(recordData,done);
    // });
    it("_prepareDataToInsert | CC Operator | paytmfirstcc | Paid by self", (done) => {
        let recordData = {"id":"************","catalogProductID":"194","reqType":"RECHARGE","userData_recharge_number":"CC_Oper_paytmfirst","customerInfo_customer_id":667770,"userData_amount":111,"inStatusMap_responseCode":"00","productInfo_operator":"paytmfirstcc","productInfo_paytype" : "credit card","currentGw":"cc_visa","recentData" : {"creditCardId" : "sjdfbjhdf9898ydvhdjknfkh"}};
        serviceObj._prepareDataToInsert(recordData,done);
    });
    it("_prepareDataToInsert | CC Operator | paytmfirstcc | Payment by other customer", (done) => {
        let recordData = {"id":"************","catalogProductID":"194","reqType":"RECHARGE","userData_recharge_number":"CC_Oper_paytmfirst","customerInfo_customer_id":667771,"userData_amount":111,"inStatusMap_responseCode":"00","productInfo_operator":"paytmfirstcc","productInfo_paytype" : "credit card","currentGw":"cc_visa","recentData" : {"creditCardId" : "sjdfbjhdf9898ydvhdjknfkh"}};
        serviceObj._prepareDataToInsert(recordData,done);
    });
    it("_prepareDataToInsert | CC Operator | visa_hdfcbank | refId == hdfc_ref_id_001 | Paid by self", (done) => {
        let recordData = {"id":"************","catalogProductID":"194","reqType":"RECHARGE","userData_recharge_number":"5555 77XX XXXX 8767","customerInfo_customer_id":667770,"userData_amount":600,"inStatusMap_responseCode":"00","productInfo_operator":"visa_hdfcbank","productInfo_paytype" : "credit card","currentGw":"cc_visa","recentData" : {"creditCardId" : "hdfc_ref_id_001"}};
        serviceObj._prepareDataToInsert(recordData,done);
    });
    it("_prepareDataToInsert | CC Operator | neft_hdfcbank | refId == hdfc_ref_id_001 | Paid by friend", (done) => {
        let recordData = {"id":"************","catalogProductID":"194","reqType":"RECHARGE","userData_recharge_number":"5555 77XX XXXX 8767","customerInfo_customer_id":667771,"userData_amount":600,"inStatusMap_responseCode":"00","productInfo_operator":"neft_hdfcbank","productInfo_paytype" : "credit card","currentGw":"cc_neft","recentData" : {"creditCardId" : "hdfc_ref_id_001"}};
        serviceObj._prepareDataToInsert(recordData,done);
    });
    it("_prepareDataToInsert | CC Operator | neft_hdfcbank | refId == hdfc_ref_id_001 | Paid by self", (done) => {
        let recordData = {"id":"************","catalogProductID":"194","reqType":"RECHARGE","userData_recharge_number":"5555 77XX XXXX 8767","customerInfo_customer_id":667770,"userData_amount":600,"inStatusMap_responseCode":"00","productInfo_operator":"neft_hdfcbank","productInfo_paytype" : "credit card","currentGw":"cc_visa","recentData" : {"creditCardId" : "hdfc_ref_id_001"}};
        serviceObj._prepareDataToInsert(recordData,done);
    });

    it("_prepareDataToInsert | CC Operator | full execution", (done) => {
        let recordData = {"id":"************","catalogProductID":"194","reqType":"RECHARGE","userData_recharge_number":"5555 77XX XXXX 8767","customerInfo_customer_id":667770,"userData_amount":600,"inStatusMap_responseCode":"00","productInfo_operator":"neft_sbi","productInfo_service":"financial services","productInfo_paytype" : "credit card","currentGw":"cc_visa","userData_recharge_number": "XXXX XXXX XXXX 1429","userData_recharge_number_2_length": 0,"userData_recharge_number_3": "8f9c80b3-5342-4dc2-a301-f331efe1647b","userData_recharge_number_length": 19,"recentData" : {"creditCardId" : "hdfc_ref_id_001"}};
        let tableName = 'bills_creditcard';
        let customerId = 667770;
        let recentBillsData = {};
        let fromRecents = true;
        let cb = sinon.spy();
        serviceObj.nonPaytmKafkaPublisher = {
            publishData : () => {
                return cb(null, {})
            }
        }

        let stub1 = sinon.stub(serviceObj.billsModel, 'getBillByCustomer').callsFake(function(cb,tableName, customerId) {
            return cb(null,[]);
        });
        let stub2 = sinon.stub(serviceObj.billSubscriber, 'createRecentBill').callsFake(function(cb,recentBillsData, fromRecents){
            return cb(null,null);
        })
        let stub3 = sinon.stub(serviceObj.billSubscriber, 'updateRecentBill').callsFake(function(cb,recentBillsData, fromRecents){
            return cb(null,null);
        })
        let stub4 = sinon.stub(serviceObj, 'pushToKafkaForAutomaticSync').callsFake(function(cb,recentBillsData, tableName){
            return cb(null);
        })
        let stub5 = sinon.stub(serviceObj, 'storePaymentsCache').callsFake(function(recentBillsData, cb){
            return cb(null);
        })
        let stub6 = sinon.stub(serviceObj, 'publishCtEvents').callsFake(function(cb, recentBillsData){
            return cb(null)
        });
        let stub7 = sinon.stub(serviceObj.recentsLayer, 'update').callsFake(function(cb){
            return cb(null)
        });

        let stub8 = sinon.stub(serviceObj, 'publishNonPaytmEvents').callsFake(function(cb, recentBillsData){
            return cb(null)
        });
        // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
        serviceObj._prepareDataToInsert(recordData,function(){
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            stub7.restore();
            stub8.restore();
            expect(stub1).to.have.callCount(1)
            expect(stub2).to.have.callCount(1)
            expect(stub3).to.have.callCount(0)
            expect(stub4).to.have.callCount(1)
            expect(stub5).to.have.callCount(1)
            expect(stub6).to.have.callCount(1)
            expect(stub7).to.have.callCount(0)
            expect(stub8).to.have.callCount(1)
            // expect(cb).to.have.been.calledWith(null)
            return done();
        });
    });

    it("_prepareDataToInsert | CC Operator | tokenised card | full execution", (done) => {
        let recordData = {"id":"************","catalogProductID":"194","reqType":"RECHARGE","userData_recharge_number":"5555 77XX XXXX 8767","customerInfo_customer_id":667770,"userData_amount":600,"inStatusMap_responseCode":"00","productInfo_operator":"neft_sbi","productInfo_service":"financial services","productInfo_paytype" : "credit card","currentGw":"cc_visa","userData_recharge_number": "XXXX XXXX XXXX 1429","userData_recharge_number_2_length": 0,"userData_recharge_number_3": "8f9c80b3-5342-4dc2-a301-f331efe1647b","userData_recharge_number_length": 19,"recentData" : {"creditCardId" : "hdfc_ref_id_001"},"metaData": "{\"id\":\"*********\",\"tin\":\"634f867323050239fdf554d5\",\"bin_number\":\"XXXXXX\",\"platform_fee\":0,\"customer_mobile\":\"**********\",\"platform_fee_hash\":\"f0025ebdaa005b8544862cb8268e1190f0d556b6fc0f4bf1c5ec1a2000cf3208\",\"enable_visa_direct\":\"0\",\"panUniqueReference\":\"V0010013018048422780791418024\",\"platform_fee_label\":\"You will be charged a convenience fee of\",\"bill_payment_source\":\"recent\",\"enable_bill_payment\":\"1\",\"check_existing_order\":true,\"payment_request_type\":\"CC_BILL_PAYMENT\",\"platform_fee_label_v2\":\"Platform Fee\",\"platform_fee_additional_label\":\"Convenience Fee\",\"check_operator_down\":false,\"recharge_benefits\":{}}"};
        let tableName = 'bills_creditcard';
        let customerId = 667770;
        let recentBillsData = {};
        let fromRecents = true;
        let cb = sinon.spy();
        serviceObj.nonPaytmKafkaPublisher = {
            publishData : () => {
                return cb(null, {})
            }
        }

        let stub1 = sinon.stub(serviceObj.billsModel, 'getBillByCustomer').callsFake(function(cb,tableName, customerId) {
            return cb(null,[]);
        });
        let stub2 = sinon.stub(serviceObj.billSubscriber, 'createRecentBill').callsFake(function(cb,recentBillsData, fromRecents){
            return cb(null,null);
        })
        let stub3 = sinon.stub(serviceObj.billSubscriber, 'updateRecentBill').callsFake(function(cb,recentBillsData, fromRecents){
            return cb(null,null);
        })
        let stub4 = sinon.stub(serviceObj, 'pushToKafkaForAutomaticSync').callsFake(function(cb,recentBillsData, tableName){
            return cb(null);
        })
        let stub5 = sinon.stub(serviceObj, 'storePaymentsCache').callsFake(function(recentBillsData, cb){
            return cb(null);
        })
        let stub6 = sinon.stub(serviceObj, 'publishCtEvents').callsFake(function(cb, recentBillsData){
            return cb(null)
        });
        let stub7 = sinon.stub(serviceObj.recentsLayer, 'update').callsFake(function(cb){
            return cb(null)
        });
        let stub8 = sinon.stub(serviceObj, 'publishNonPaytmEvents').callsFake(function(cb, recentBillsData){
            return cb(null)
        });
        // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
        serviceObj._prepareDataToInsert(recordData,function(){
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            stub7.restore();
            stub8.restore();
            expect(stub1).to.have.callCount(1)
            expect(stub2).to.have.callCount(1)
            expect(stub3).to.have.callCount(0)
            expect(stub4).to.have.callCount(1)
            expect(stub5).to.have.callCount(1)
            expect(stub6).to.have.callCount(1)
            expect(stub7).to.have.callCount(0)
            expect(stub8).to.have.callCount(1)
            // expect(cb).to.have.been.calledWith(null)
            return done();
        });
    });

    it("_prepareDataToInsert | CC Operator | full execution-in case of data mismatch in table and payload", (done) => {
        let recordData = {"id":"************","catalogProductID":"194","reqType":"RECHARGE","userData_recharge_number":"5555 77XX XXXX 8767","customerInfo_customer_id":667770,"userData_amount":600,"inStatusMap_responseCode":"00","productInfo_operator":"neft_sbi","productInfo_service":"financial services","productInfo_paytype" : "credit card","currentGw":"cc_visa","userData_recharge_number": "XXXX XXXX XXXX 4259","userData_recharge_number_2_length": 0,"userData_recharge_number_3": "8f9c80b3-5342-4dc2-a301-f331efe1647b","userData_recharge_number_length": 19,"recentData" : {"creditCardId" : "hdfc_ref_id_001"}};
        let tableName = 'bills_creditcard';
        let customerId = 667770;
        let recentBillsData = {};
        let fromRecents = true;
        let cb = sinon.spy();
        serviceObj.nonPaytmKafkaPublisher = {
            publishData : () => {
                return cb(null, {})
            }
        }

        let stub1 = sinon.stub(serviceObj.billsModel, 'getBillByCustomer').callsFake(function(cb,tableName, customerId) {
            return cb(null,[{id:***********,customer_id:**********,recharge_number:'XXXX XXXX XXXX 4259',product_id:**********,reference_id:'2020110110621b8a9bdad6d33d918777983e120ef078b',operator:'neft_hdfcbank',amount:5000,bill_date:'2024-04-10T00:00:00.000Z',due_date:'2024-06-03T23:59:59.000Z',bill_fetch_date:'2024-05-24T00:00:00.000Z',next_bill_fetch_date:'2024-06-21T17:08:22.000Z',gateway:'euronetpostpaid',paytype:'credit card',service:'financial services',circle:'',customer_mobile:'**********',customer_email:'',payment_channel:null,retry_count:0,status:4,reason:null,extra:'{"updated_source":"validationSync","updated_data_source":"validationSync","created_source":"validationSync","source_subtype_2":"PARTIAL_BILL","is_bbps":true,"recon_id":"q7HnkjDL1duFZYi3pM0Nkpj8y6o=","user_type":"RU"}',published_date:null,created_at:'2024-05-24T17:08:28.000Z',updated_at:'2024-05-24T17:08:28.000Z',user_data:'{"recharge_number_3":"2020110110621b8a9bdad6d33d918777983e120ef078b","recharge_number_5":"**********","recharge_number_6":"4259"}',notification_status:1,payment_date:null,service_id:0,customerOtherInfo:'{"customerId":"**********","subscriberNumber":"XXXX XXXX XXXX 4131","subscriberName":null,"subscriberEmailId":null,"subscriberDOB":null,"subscriberAltNumber":null,"subscriberAddress":null,"subscriberGender":null,"subscriberCity":null,"minReloadAmount":null,"currentBillAmount":"5000","billDueDate":"2024-06-03","billDate":"2024-04-10","currentMinBillAmount":"1500"}',is_automatic:1,par_id:null,tin:null,bank_name:'hdfc',card_network:'visa',tokenOrderId:'',data_source:null}]);
        });
        let stub2 = sinon.stub(serviceObj.billSubscriber, 'createRecentBill').callsFake(function(cb,recentBillsData, fromRecents){
            return cb(null,null);
        })
        let stub3 = sinon.stub(serviceObj.billSubscriber, 'updateRecentBill').callsFake(function(cb,recentBillsData, fromRecents){
            return cb(null,null);
        })
        let stub4 = sinon.stub(serviceObj, 'pushToKafkaForAutomaticSync').callsFake(function(cb,recentBillsData, tableName){
            return cb(null);
        })
        let stub5 = sinon.stub(serviceObj, 'storePaymentsCache').callsFake(function(recentBillsData, cb){
            return cb(null);
        })
        let stub6 = sinon.stub(serviceObj, 'publishCtEvents').callsFake(function(cb, recentBillsData){
            return cb(null)
        });
        let stub7 = sinon.stub(serviceObj.recentsLayer, 'update').callsFake(function(cb){
            return cb(null)
        });
        let stub8 = sinon.stub(serviceObj, 'publishNonPaytmEvents').callsFake(function(cb, recentBillsData){
            return cb(null)
        });
        // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
        serviceObj._prepareDataToInsert(recordData,function(){
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            stub7.restore();
            stub8.restore();
            expect(stub1).to.have.callCount(1)
            expect(stub2).to.have.callCount(1)
            expect(stub3).to.have.callCount(0)
            expect(stub4).to.have.callCount(1)
            expect(stub5).to.have.callCount(1)
            expect(stub6).to.have.callCount(1)
            expect(stub7).to.have.callCount(0)
            expect(stub8).to.have.callCount(1)
            // expect(cb).to.have.been.calledWith(null)
            return done();
        });
    });

    it("_prepareDataToInsert | CC Operator | table name not found | full execution", (done) => {
        let recordData = {"id":"************","catalogProductID":"194","reqType":"RECHARGE","userData_recharge_number":"5555 77XX XXXX 8767","customerInfo_customer_id":667770,"userData_amount":600,"inStatusMap_responseCode":"00","productInfo_operator":"neft_sbibank","productInfo_service":"financial services","productInfo_paytype" : "credit card","currentGw":"cc_visa","userData_recharge_number": "XXXX XXXX XXXX 1429","userData_recharge_number_2_length": 0,"userData_recharge_number_3": "8f9c80b3-5342-4dc2-a301-f331efe1647b","userData_recharge_number_length": 19,"recentData" : {"creditCardId" : "hdfc_ref_id_001"},"metaData": "{\"id\":\"*********\",\"tin\":\"634f867323050239fdf554d5\",\"bin_number\":\"XXXXXX\",\"platform_fee\":0,\"customer_mobile\":\"**********\",\"platform_fee_hash\":\"f0025ebdaa005b8544862cb8268e1190f0d556b6fc0f4bf1c5ec1a2000cf3208\",\"enable_visa_direct\":\"0\",\"panUniqueReference\":\"V0010013018048422780791418024\",\"platform_fee_label\":\"You will be charged a convenience fee of\",\"bill_payment_source\":\"recent\",\"enable_bill_payment\":\"1\",\"check_existing_order\":true,\"payment_request_type\":\"CC_BILL_PAYMENT\",\"platform_fee_label_v2\":\"Platform Fee\",\"platform_fee_additional_label\":\"Convenience Fee\",\"check_operator_down\":false,\"recharge_benefits\":{}}"};
        let tableName = 'bills_creditcard';
        let customerId = 667770;
        let recentBillsData = {};
        let fromRecents = true;
        let cb = sinon.spy();
        serviceObj.nonPaytmKafkaPublisher = {
            publishData : () => {
                return cb(null, {})
            }
        }

        let stub1 = sinon.stub(serviceObj.billsModel, 'getBillByCustomer').callsFake(function(cb,tableName, customerId) {
            return cb(null,[]);
        });
        let stub2 = sinon.stub(serviceObj.billSubscriber, 'createRecentBill').callsFake(function(cb,recentBillsData, fromRecents){
            return cb(null,null);
        })
        let stub3 = sinon.stub(serviceObj.billSubscriber, 'updateRecentBill').callsFake(function(cb,recentBillsData, fromRecents){
            return cb(null,null);
        })
        let stub4 = sinon.stub(serviceObj, 'pushToKafkaForAutomaticSync').callsFake(function(cb,recentBillsData, tableName){
            return cb(null);
        })
        let stub5 = sinon.stub(serviceObj, 'storePaymentsCache').callsFake(function(recentBillsData, cb){
            return cb(null);
        })
        let stub6 = sinon.stub(serviceObj, 'publishCtEvents').callsFake(function(cb, recentBillsData){
            return cb(null)
        });
        let stub7 = sinon.stub(serviceObj.recentsLayer, 'update').callsFake(function(cb){
            return cb(null)
        });
        // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
        serviceObj._prepareDataToInsert(recordData,function(){
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            stub7.restore();
            
            expect(stub1).to.have.callCount(0)
            expect(stub2).to.have.callCount(0)
            expect(stub3).to.have.callCount(0)
            expect(stub4).to.have.callCount(0)
            expect(stub5).to.have.callCount(0)
            expect(stub6).to.have.callCount(0)
            expect(stub7).to.have.callCount(0)
            expect(cb).to.have.callCount(0)
            return done();
        });
    });
    it("_prepareDataToInsert | CC Operator | payment channel disabled | full execution", (done) => {
        let recordData = {"id":"************","customerInfo_channel_id":"DUMMY_CHANNEL","catalogProductID":"194","reqType":"RECHARGE","userData_recharge_number":"5555 77XX XXXX 8767","customerInfo_customer_id":667770,"userData_amount":600,"inStatusMap_responseCode":"00","productInfo_operator":"neft_sbi","productInfo_service":"financial services","productInfo_paytype" : "credit card","currentGw":"cc_visa","userData_recharge_number": "XXXX XXXX XXXX 1429","userData_recharge_number_2_length": 0,"userData_recharge_number_3": "8f9c80b3-5342-4dc2-a301-f331efe1647b","userData_recharge_number_length": 19,"recentData" : {"creditCardId" : "hdfc_ref_id_001"},"metaData": "{\"id\":\"*********\",\"tin\":\"634f867323050239fdf554d5\",\"bin_number\":\"XXXXXX\",\"platform_fee\":0,\"customer_mobile\":\"**********\",\"platform_fee_hash\":\"f0025ebdaa005b8544862cb8268e1190f0d556b6fc0f4bf1c5ec1a2000cf3208\",\"enable_visa_direct\":\"0\",\"panUniqueReference\":\"V0010013018048422780791418024\",\"platform_fee_label\":\"You will be charged a convenience fee of\",\"bill_payment_source\":\"recent\",\"enable_bill_payment\":\"1\",\"check_existing_order\":true,\"payment_request_type\":\"CC_BILL_PAYMENT\",\"platform_fee_label_v2\":\"Platform Fee\",\"platform_fee_additional_label\":\"Convenience Fee\",\"check_operator_down\":false,\"recharge_benefits\":{}}"};
        let tableName = 'bills_creditcard';
        let customerId = 667770;
        let recentBillsData = {};
        let fromRecents = true;
        let cb = sinon.spy();
        serviceObj.nonPaytmKafkaPublisher = {
            publishData : () => {
                return cb(null, {})
            }
        }

        let stub1 = sinon.stub(serviceObj.billsModel, 'getBillByCustomer').callsFake(function(cb,tableName, customerId) {
            return cb(null,[]);
        });
        let stub2 = sinon.stub(serviceObj.billSubscriber, 'createRecentBill').callsFake(function(cb,recentBillsData, fromRecents){
            return cb(null,null);
        })
        let stub3 = sinon.stub(serviceObj.billSubscriber, 'updateRecentBill').callsFake(function(cb,recentBillsData, fromRecents){
            return cb(null,null);
        })
        let stub4 = sinon.stub(serviceObj, 'pushToKafkaForAutomaticSync').callsFake(function(cb,recentBillsData, tableName){
            return cb(null);
        })
        let stub5 = sinon.stub(serviceObj, 'storePaymentsCache').callsFake(function(recentBillsData, cb){
            return cb(null);
        })
        let stub6 = sinon.stub(serviceObj, 'publishCtEvents').callsFake(function(cb, recentBillsData){
            return cb(null)
        });
        let stub7 = sinon.stub(serviceObj.recentsLayer, 'update').callsFake(function(cb){
            return cb(null)
        });
        // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
        serviceObj._prepareDataToInsert(recordData,function(){
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            stub7.restore();
            
            expect(stub1).to.have.callCount(0)
            expect(stub2).to.have.callCount(0)
            expect(stub3).to.have.callCount(0)
            expect(stub4).to.have.callCount(0)
            expect(stub5).to.have.callCount(0)
            expect(stub6).to.have.callCount(0)
            expect(stub7).to.have.callCount(0)
            expect(cb).to.have.callCount(0)
            return done();
        });
    });

    it("_prepareDataToInsert | CC Operator | error from resetTransactionHistory | full execution", (done) => {
        let recordData = {"id":"************","catalogProductID":"194","reqType":"RECHARGE","userData_recharge_number":"5555 77XX XXXX 8767","customerInfo_customer_id":667770,"userData_amount":600,"inStatusMap_responseCode":"00","productInfo_operator":"neft_sbi","productInfo_service":"financial services","productInfo_paytype" : "credit card","currentGw":"cc_visa","userData_recharge_number": "XXXX XXXX XXXX 1429","userData_recharge_number_2_length": 0,"userData_recharge_number_3": "8f9c80b3-5342-4dc2-a301-f331efe1647b","userData_recharge_number_length": 19,"recentData" : {"creditCardId" : "hdfc_ref_id_001"},"metaData": "{\"id\":\"*********\",\"tin\":\"634f867323050239fdf554d5\",\"bin_number\":\"XXXXXX\",\"platform_fee\":0,\"customer_mobile\":\"**********\",\"platform_fee_hash\":\"f0025ebdaa005b8544862cb8268e1190f0d556b6fc0f4bf1c5ec1a2000cf3208\",\"enable_visa_direct\":\"0\",\"panUniqueReference\":\"V0010013018048422780791418024\",\"platform_fee_label\":\"You will be charged a convenience fee of\",\"bill_payment_source\":\"recent\",\"enable_bill_payment\":\"1\",\"check_existing_order\":true,\"payment_request_type\":\"CC_BILL_PAYMENT\",\"platform_fee_label_v2\":\"Platform Fee\",\"platform_fee_additional_label\":\"Convenience Fee\",\"check_operator_down\":false,\"recharge_benefits\":{}}"};
        let tableName = 'bills_creditcard';
        let customerId = 667770;
        let recentBillsData = {};
        let fromRecents = true;
        let cb = sinon.spy();
        serviceObj.nonPaytmKafkaPublisher = {
            publishData : () => {
                return cb(null, {})
            }
        }

        let stub1 = sinon.stub(serviceObj.billsModel, 'getBillByCustomer').callsFake(function(cb,tableName, customerId) {
            return cb(null,[]);
        });
        let stub2 = sinon.stub(serviceObj.billSubscriber, 'createRecentBill').callsFake(function(cb,recentBillsData, fromRecents){
            return cb(null,null);
        })
        let stub3 = sinon.stub(serviceObj.billSubscriber, 'updateRecentBill').callsFake(function(cb,recentBillsData, fromRecents){
            return cb(null,null);
        })
        let stub4 = sinon.stub(serviceObj, 'pushToKafkaForAutomaticSync').callsFake(function(cb,recentBillsData, tableName){
            return cb(null);
        })
        let stub5 = sinon.stub(serviceObj, 'storePaymentsCache').callsFake(function(recentBillsData, cb){
            return cb(null);
        })
        let stub6 = sinon.stub(serviceObj, 'publishCtEvents').callsFake(function(cb, recentBillsData){
            return cb(null)
        });
        let stub7 = sinon.stub(serviceObj.recentsLayer, 'update').callsFake(function(cb){
            return cb(null)
        });
        let stub8 = sinon.stub(serviceObj, 'resetDataFromTransactionHistory').callsFake(function(recentBillsData,cb){
            return cb('Error in reseting transaction history');
        })
        let stub9 = sinon.stub(serviceObj, 'publishNonPaytmEvents').callsFake(function(cb, recentBillsData){
            return cb(null)
        });
        
        // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
        serviceObj._prepareDataToInsert(recordData,function(){
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            stub7.restore();
            stub8.restore();
            stub9.restore();
            expect(stub1).to.have.callCount(1)
            expect(stub2).to.have.callCount(1)
            expect(stub3).to.have.callCount(0)
            expect(stub4).to.have.callCount(1)
            expect(stub5).to.have.callCount(1)
            expect(stub6).to.have.callCount(1)
            expect(stub7).to.have.callCount(0)
            expect(stub8).to.have.callCount(1)
            expect(stub9).to.have.callCount(1)
            // expect(cb).to.have.callCount(1)
            return done();
        });
    });

    // it("_prepareDataToInsert | LIC Operator | stopped syncing payments for lic where automatic=0 | full execution", (done) => {
    //     let recordData = {"id":"************","catalogProductID":"194","reqType":"RECHARGE","userData_recharge_number":"5555 77XX XXXX 8767","customerInfo_customer_id":667770,"userData_amount":600,"inStatusMap_responseCode":"00","productInfo_operator":"lic","productInfo_service":"financial servi","productInfo_paytype" : "postpaid","currentGw":"cc_visa","userData_recharge_number": "XXXX XXXX XXXX 1429","userData_recharge_number_2_length": 0,"userData_recharge_number_3": "8f9c80b3-5342-4dc2-a301-f331efe1647b","userData_recharge_number_length": 19,"recentData" : {"creditCardId" : "hdfc_ref_id_001"},"metaData": "{\"id\":\"*********\",\"tin\":\"634f867323050239fdf554d5\",\"bin_number\":\"XXXXXX\",\"platform_fee\":0,\"customer_mobile\":\"**********\",\"platform_fee_hash\":\"f0025ebdaa005b8544862cb8268e1190f0d556b6fc0f4bf1c5ec1a2000cf3208\",\"enable_visa_direct\":\"0\",\"panUniqueReference\":\"V0010013018048422780791418024\",\"platform_fee_label\":\"You will be charged a convenience fee of\",\"bill_payment_source\":\"recent\",\"enable_bill_payment\":\"1\",\"check_existing_order\":true,\"payment_request_type\":\"CC_BILL_PAYMENT\",\"platform_fee_label_v2\":\"Platform Fee\",\"platform_fee_additional_label\":\"Convenience Fee\",\"check_operator_down\":false,\"recharge_benefits\":{}}"};
    //     let tableName = 'bills_creditcard';
    //     let customerId = 667770;
    //     let recentBillsData = {};
    //     let fromRecents = true;
    //     let cb = sinon.spy();
    //     serviceObj.nonPaytmKafkaPublisher = {
    //         publishData : () => {
    //             return cb(null, {})
    //         }
    //     }

    //     let stub1 = sinon.stub(serviceObj.billsModel, 'getBillByCustomer').callsFake(function(cb,tableName, customerId) {
    //         return cb(null,[]);
    //     });
    //     let stub2 = sinon.stub(serviceObj.billSubscriber, 'createRecentBill').callsFake(function(cb,recentBillsData, fromRecents){
    //         return cb(null,null);
    //     })
    //     let stub3 = sinon.stub(serviceObj.billSubscriber, 'updateRecentBill').callsFake(function(cb,recentBillsData, fromRecents){
    //         return cb(null,null);
    //     })
    //     let stub4 = sinon.stub(serviceObj, 'pushToKafkaForAutomaticSync').callsFake(function(cb,recentBillsData, tableName){
    //         return cb(null);
    //     })
    //     let stub5 = sinon.stub(serviceObj, 'storePaymentsCache').callsFake(function(recentBillsData, cb){
    //         return cb(null);
    //     })
    //     let stub6 = sinon.stub(serviceObj, 'publishCtEvents').callsFake(function(cb, recentBillsData){
    //         return cb(null)
    //     });
    //     let stub7 = sinon.stub(serviceObj.recentsLayer, 'update').callsFake(function(cb){
    //         return cb(null)
    //     });
    //     let stub8 = sinon.stub(serviceObj, 'resetDataFromTransactionHistory').callsFake(function(recentBillsData,cb){
    //         return cb(isNull);
    //     })
    //     let stub9 = sinon.stub(serviceObj.billsModel, 'getBillsOfSameRech').callsFake(function(cb,tableName, customerId) {
    //         return cb(null,[]);
    //     });
    //     // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
    //     serviceObj._prepareDataToInsert(recordData,function(){
    //         stub1.restore();
    //         stub2.restore();
    //         stub3.restore();
    //         stub4.restore();
    //         stub5.restore();
    //         stub6.restore();
    //         stub7.restore();
    //         stub8.restore();
    //         stub9.restore();

    //         expect(stub1).to.have.callCount(0)
    //         expect(stub2).to.have.callCount(0)
    //         expect(stub3).to.have.callCount(0)
    //         expect(stub4).to.have.callCount(0)
    //         expect(stub5).to.have.callCount(0)
    //         expect(stub6).to.have.callCount(0)
    //         expect(stub7).to.have.callCount(0)
    //         expect(stub8).to.have.callCount(1)
    //         expect(stub9).to.have.callCount(1)
    //         expect(cb).to.have.callCount(0)
    //         return done();
    //     });
    // });

    it("_prepareDataToInsert | rent payment | exception | full execution", (done) => {
        let recordData = {"id":"************","catalogProductID":"194","reqType":"RECHARGE","userData_recharge_number":"5555 77XX XXXX 8767","customerInfo_customer_id":667770,"userData_amount":600,"inStatusMap_responseCode":"07","productInfo_operator":"rent payment","productInfo_service":"rent payment","productInfo_paytype" : "postpaid","currentGw":"cc_visa","userData_recharge_number": "XXXX XXXX XXXX 1429","userData_recharge_number_2_length": 0,"userData_recharge_number_3": "8f9c80b3-5342-4dc2-a301-f331efe1647b","userData_recharge_number_length": 19,"recentData" : {"creditCardId" : "hdfc_ref_id_001"},"metaData": "{\"id\":\"*********\",\"tin\":\"634f867323050239fdf554d5\",\"bin_number\":\"XXXXXX\",\"platform_fee\":0,\"customer_mobile\":\"**********\",\"platform_fee_hash\":\"f0025ebdaa005b8544862cb8268e1190f0d556b6fc0f4bf1c5ec1a2000cf3208\",\"enable_visa_direct\":\"0\",\"panUniqueReference\":\"V0010013018048422780791418024\",\"platform_fee_label\":\"You will be charged a convenience fee of\",\"bill_payment_source\":\"recent\",\"enable_bill_payment\":\"1\",\"check_existing_order\":true,\"payment_request_type\":\"CC_BILL_PAYMENT\",\"platform_fee_label_v2\":\"Platform Fee\",\"platform_fee_additional_label\":\"Convenience Fee\",\"check_operator_down\":false,\"recharge_benefits\":{}}"};
        let tableName = 'bills_creditcard';
        let customerId = 667770;
        let recentBillsData = {};
        let fromRecents = true;
        let cb = sinon.spy();
        serviceObj.nonPaytmKafkaPublisher = {
            publishData : () => {
                return cb(null, {})
            }
        }

        let stub1 = sinon.stub(serviceObj.billsModel, 'getBillByCustomer').callsFake(function(cb,tableName, customerId) {
            return cb(null,[]);
        });
        let stub2 = sinon.stub(serviceObj.billSubscriber, 'createRecentBill').callsFake(function(cb,recentBillsData, fromRecents){
            return cb(null,null);
        })
        let stub3 = sinon.stub(serviceObj.billSubscriber, 'updateRecentBill').callsFake(function(cb,recentBillsData, fromRecents){
            return cb(null,null);
        })
        let stub4 = sinon.stub(serviceObj, 'pushToKafkaForAutomaticSync').callsFake(function(cb,recentBillsData, tableName){
            return cb(null);
        })
        let stub5 = sinon.stub(serviceObj, 'storePaymentsCache').callsFake(function(recentBillsData, cb){
            return cb(null);
        })
        let stub6 = sinon.stub(serviceObj, 'publishCtEvents').callsFake(function(cb, recentBillsData){
            return cb(null)
        });
        let stub7 = sinon.stub(serviceObj.recentsLayer, 'update').callsFake(function(cb){
            return cb(null)
        });
        let stub8 = sinon.stub(serviceObj, 'resetDataFromTransactionHistory').callsFake(function(recentBillsData,cb){
            return cb(null);
        })
        let stub9 = sinon.stub(serviceObj.billsModel, 'getBillsOfSameRech').callsFake(function(cb,tableName, customerId) {
            return cb(null,[]);
        });
        // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
        serviceObj._prepareDataToInsert(recordData,function(){
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            stub7.restore();
            stub8.restore();
            stub9.restore();

            expect(stub1).to.have.callCount(0)
            expect(stub2).to.have.callCount(0)
            expect(stub3).to.have.callCount(0)
            expect(stub4).to.have.callCount(0)
            expect(stub5).to.have.callCount(1)
            expect(stub6).to.have.callCount(1)
            expect(stub7).to.have.callCount(0)
            expect(stub8).to.have.callCount(1)
            expect(stub9).to.have.callCount(1)
            expect(cb).to.have.callCount(0)
            return done();
        });
    });

    it("_prepareDataToInsert | CC Operator | error in createRecentBills | full execution", (done) => {
        let recordData = {"id":"************","catalogProductID":"194","reqType":"RECHARGE","userData_recharge_number":"5555 77XX XXXX 8767","customerInfo_customer_id":667770,"userData_amount":600,"inStatusMap_responseCode":"00","productInfo_operator":"neft_sbi","productInfo_service":"financial services","productInfo_paytype" : "credit card","currentGw":"cc_visa","userData_recharge_number": "XXXX XXXX XXXX 1429","userData_recharge_number_2_length": 0,"userData_recharge_number_3": "8f9c80b3-5342-4dc2-a301-f331efe1647b","userData_recharge_number_length": 19,"recentData" : {"creditCardId" : "hdfc_ref_id_001"}};
        let tableName = 'bills_creditcard';
        let customerId = 667770;
        let recentBillsData = {};
        let fromRecents = true;
        let cb = sinon.spy();
        serviceObj.nonPaytmKafkaPublisher = {
            publishData : () => {
                return cb(null, {})
            }
        }

        let stub1 = sinon.stub(serviceObj.billsModel, 'getBillByCustomer').callsFake(function(cb,tableName, customerId) {
            return cb(null,[]);
        });
        let stub2 = sinon.stub(serviceObj.billSubscriber, 'createRecentBill').callsFake(function(cb,recentBillsData, fromRecents){
            return cb('Error while creating recent bill');
        })
        let stub3 = sinon.stub(serviceObj.billSubscriber, 'updateRecentBill').callsFake(function(cb,recentBillsData, fromRecents){
            return cb(null,null);
        })
        let stub4 = sinon.stub(serviceObj, 'pushToKafkaForAutomaticSync').callsFake(function(cb,recentBillsData, tableName){
            return cb(null);
        })
        let stub5 = sinon.stub(serviceObj, 'storePaymentsCache').callsFake(function(recentBillsData, cb){
            return cb(null);
        })
        let stub6 = sinon.stub(serviceObj, 'publishCtEvents').callsFake(function(cb, recentBillsData){
            return cb(null)
        });
        let stub7 = sinon.stub(serviceObj.recentsLayer, 'update').callsFake(function(cb){
            return cb(null)
        });
        // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
        serviceObj._prepareDataToInsert(recordData,function(){
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            stub7.restore();
            
            expect(stub1).to.have.callCount(1)
            expect(stub2).to.have.callCount(1)
            expect(stub3).to.have.callCount(0)
            expect(stub4).to.have.callCount(0)
            expect(stub5).to.have.callCount(0)
            expect(stub6).to.have.callCount(0)
            expect(stub7).to.have.callCount(0)
            expect(cb).to.have.callCount(0)
            return done();
        });
    });

    it("_prepareDataToInsert | CC Operator | error in nonpaytm publisher | full execution", (done) => {
        let recordData = {"id":"************","catalogProductID":"194","reqType":"RECHARGE","userData_recharge_number":"5555 77XX XXXX 8767","customerInfo_customer_id":667770,"userData_amount":600,"inStatusMap_responseCode":"00","productInfo_operator":"neft_sbi","productInfo_service":"financial services","productInfo_paytype" : "credit card","currentGw":"cc_visa","userData_recharge_number": "XXXX XXXX XXXX 1429","userData_recharge_number_2_length": 0,"userData_recharge_number_3": "8f9c80b3-5342-4dc2-a301-f331efe1647b","userData_recharge_number_length": 19,"recentData" : {"creditCardId" : "hdfc_ref_id_001"}};
        let tableName = 'bills_creditcard';
        let customerId = 667770;
        let recentBillsData = {};
        let fromRecents = true;
        let cb = sinon.spy();
        serviceObj.nonPaytmKafkaPublisher = {
            publishData : ([], cb) => {
                return cb('Error in publishing')
            }
        }

        let stub1 = sinon.stub(serviceObj.billsModel, 'getBillByCustomer').callsFake(function(cb,tableName, customerId) {
            return cb(null,[]);
        });
        let stub2 = sinon.stub(serviceObj.billSubscriber, 'createRecentBill').callsFake(function(cb,recentBillsData, fromRecents){
            return cb(null,null);
        })
        let stub3 = sinon.stub(serviceObj.billSubscriber, 'updateRecentBill').callsFake(function(cb,recentBillsData, fromRecents){
            return cb(null,null);
        })
        let stub4 = sinon.stub(serviceObj, 'pushToKafkaForAutomaticSync').callsFake(function(cb,recentBillsData, tableName){
            return cb(null);
        })
        let stub5 = sinon.stub(serviceObj, 'storePaymentsCache').callsFake(function(recentBillsData, cb){
            return cb(null);
        })
        let stub6 = sinon.stub(serviceObj, 'publishCtEvents').callsFake(function(cb, recentBillsData){
            return cb(null)
        });
        let stub7 = sinon.stub(serviceObj.recentsLayer, 'update').callsFake(function(cb){
            return cb(null)
        });
        // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
        serviceObj._prepareDataToInsert(recordData,function(){
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            stub7.restore();
            
            expect(stub1).to.have.callCount(1)
            expect(stub2).to.have.callCount(1)
            expect(stub3).to.have.callCount(0)
            expect(stub4).to.have.callCount(1)
            expect(stub5).to.have.callCount(1)
            expect(stub6).to.have.callCount(1)
            expect(stub7).to.have.callCount(0)
            expect(cb).to.have.callCount(0)
            return done();
        });
    });

    it("_prepareDataToInsert | CC Operator | no error in nonpaytm publisher | full execution", (done) => {
        let recordData = {"id":"************","catalogProductID":"194","reqType":"RECHARGE","userData_recharge_number":"5555 77XX XXXX 8767","customerInfo_customer_id":667770,"userData_amount":600,"inStatusMap_responseCode":"00","productInfo_operator":"neft_sbi","productInfo_service":"financial services","productInfo_paytype" : "credit card","currentGw":"cc_visa","userData_recharge_number": "XXXX XXXX XXXX 1429","userData_recharge_number_2_length": 0,"userData_recharge_number_3": "8f9c80b3-5342-4dc2-a301-f331efe1647b","userData_recharge_number_length": 19,"recentData" : {"creditCardId" : "hdfc_ref_id_001"}};
        let tableName = 'bills_creditcard';
        let customerId = 667770;
        let recentBillsData = {};
        let fromRecents = true;
        let cb = sinon.spy();
        serviceObj.nonPaytmKafkaPublisher = {
            publishData : ([], cb) => {
                return cb(null)
            }
        }

        let stub1 = sinon.stub(serviceObj.billsModel, 'getBillByCustomer').callsFake(function(cb,tableName, customerId) {
            return cb(null,[]);
        });
        let stub2 = sinon.stub(serviceObj.billSubscriber, 'createRecentBill').callsFake(function(cb,recentBillsData, fromRecents){
            return cb(null,null);
        })
        let stub3 = sinon.stub(serviceObj.billSubscriber, 'updateRecentBill').callsFake(function(cb,recentBillsData, fromRecents){
            return cb(null,null);
        })
        let stub4 = sinon.stub(serviceObj, 'pushToKafkaForAutomaticSync').callsFake(function(cb,recentBillsData, tableName){
            return cb(null);
        })
        let stub5 = sinon.stub(serviceObj, 'storePaymentsCache').callsFake(function(recentBillsData, cb){
            return cb(null);
        })
        let stub6 = sinon.stub(serviceObj, 'publishCtEvents').callsFake(function(cb, recentBillsData){
            return cb(null)
        });
        let stub7 = sinon.stub(serviceObj.recentsLayer, 'update').callsFake(function(cb){
            return cb(null)
        });
        // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
        serviceObj._prepareDataToInsert(recordData,function(){
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            stub7.restore();
            
            expect(stub1).to.have.callCount(1)
            expect(stub2).to.have.callCount(1)
            expect(stub3).to.have.callCount(0)
            expect(stub4).to.have.callCount(1)
            expect(stub5).to.have.callCount(1)
            expect(stub6).to.have.callCount(1)
            expect(stub7).to.have.callCount(0)
            expect(cb).to.have.callCount(0)
            return done();
        });
    });

    it("_prepareDataToInsert | CC Operator | notify recent true | full execution", (done) => {
        let recordData = {"id":"************","catalogProductID":"194","reqType":"RECHARGE","userData_recharge_number":"5555 77XX XXXX 8767","customerInfo_customer_id":667770,"userData_amount":600,"inStatusMap_responseCode":"00","productInfo_operator":"neft_sbi","productInfo_service":"financial services","productInfo_paytype" : "credit card","currentGw":"cc_visa","userData_recharge_number": "XXXX XXXX XXXX 1429","userData_recharge_number_2_length": 0,"userData_recharge_number_3": "8f9c80b3-5342-4dc2-a301-f331efe1647b","userData_recharge_number_length": 19,"recentData" : {"creditCardId" : "hdfc_ref_id_001"}};
        let tableName = 'bills_creditcard';
        let customerId = 667770;
        let recentBillsData = {};
        let fromRecents = true;
        let cb = sinon.spy();
        serviceObj.nonPaytmKafkaPublisher = {
            publishData : ([], cb) => {
                return cb(null)
            }
        }

        let stub1 = sinon.stub(serviceObj.billsModel, 'getBillByCustomer').callsFake(function(cb,tableName, customerId) {
            return cb(null,[]);
        });
        let stub2 = sinon.stub(serviceObj.billSubscriber, 'createRecentBill').callsFake(function(cb,recentBillsData, fromRecents){
            return cb(null,null);
        })
        let stub3 = sinon.stub(serviceObj.billSubscriber, 'updateRecentBill').callsFake(function(cb,recentBillsData, fromRecents){
            return cb(null,null);
        })
        let stub4 = sinon.stub(serviceObj, 'pushToKafkaForAutomaticSync').callsFake(function(cb,recentBillsData, tableName){
            return cb(null);
        })
        let stub5 = sinon.stub(serviceObj, 'storePaymentsCache').callsFake(function(recentBillsData, cb){
            return cb(null);
        })
        let stub6 = sinon.stub(serviceObj, 'publishCtEvents').callsFake(function(cb, recentBillsData){
            return cb(null)
        });
        let stub7 = sinon.stub(serviceObj.recentsLayer, 'update').callsFake(function(cb){
            return cb(null)
        });
        // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
        serviceObj._prepareDataToInsert(recordData,function(){
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            stub7.restore();
            
            expect(stub1).to.have.callCount(1)
            expect(stub2).to.have.callCount(1)
            expect(stub3).to.have.callCount(0)
            expect(stub4).to.have.callCount(1)
            expect(stub5).to.have.callCount(1)
            expect(stub6).to.have.callCount(1)
            expect(stub7).to.have.callCount(0)
            expect(cb).to.have.callCount(0)
            return done();
        });
    });

    it("preProcessRecentsData | Non CC Operator", (done) => {
        let recentBillsData = {isCreditCardOperator : false};
        serviceObj.preProcessRecentsData(function(err){
            expect(err).to.be.equal(undefined);
            return done();
        },recentBillsData);
    });
    it("preProcessRecentsData | CC Operator | paytmfirstcc | refId == null", (done) => {
        let recentBillsData = {referenceId : null,isCreditCardOperator : true, operator : "paytmfirstcc"};
        serviceObj.preProcessRecentsData(function(err){
            expect(err).to.be.equal("Invalid referenceId received null");
            expect(recentBillsData.referenceId).to.be.equal(null);
            return done();
        },recentBillsData);
    });
    it("preProcessRecentsData | CC Operator | paytmfirstcc | refId -> Alpha numeric", (done) => {
        let recentBillsData = {referenceId : "alphanumeric",isCreditCardOperator : true, operator : "paytmfirstcc"};
        serviceObj.preProcessRecentsData(function(err){
            expect(err).to.be.equal(undefined);
            expect(recentBillsData.referenceId).to.be.equal("alphanumeric");
            return done();
        },recentBillsData);
    });
    //TODO: We can set assertions by mocking PG API
    
    // it("preProcessRecentsData | CC Operator | paytmfirstcc | refId -> numeric", (done) => {
    //     let recentBillsData = {referenceId : 565746 ,isCreditCardOperator : true, operator : "paytmfirstcc"};
    //     serviceObj.preProcessRecentsData(function(err){
    //         expect(err).to.be.equal(null);
    //         expect(recentBillsData.referenceId).to.be.equal("alphanumeric");
    //         return done();
    //     },recentBillsData);
    // }); 
    it("preProcessRecentsData | CC Operator | visa_hdfcbank | refId -> numeric", (done) => {
        let recentBillsData = {referenceId : "565746" ,isCreditCardOperator : true, operator : "visa_hdfcbank"};
        serviceObj.preProcessRecentsData(function(err){
            expect(err).to.be.equal(undefined);
            expect(recentBillsData.referenceId).to.be.equal("565746");
            return done();
        },recentBillsData);
    });

});

describe("Module publisher:: Recents :: CT tests", function () {
    let serviceObj;
    let data = {"id":"************","catalogProductID":"194","reqType":"RECHARGE","userData_recharge_number":"CC_Oper_paytmfirst","customerInfo_customer_id":667771,"userData_amount":111,"inStatusMap_responseCode":"00","productInfo_operator":"paytmfirstcc","productInfo_paytype" : "credit card","currentGw":"cc_visa","recentData" : {"creditCardId" : "sjdfbjhdf9898ydvhdjknfkh"}};


    before(function () {
        STARTUP_MOCK.init(function(error, options){
            // billsSubscriberObj = new EmiDueCommonConsumer(options);
            serviceObj = new SERVICE.recentBills(options);
            done();
        });
    });

    it("publishCtEvents function | check function calls", (done) => { 
        let tempRecord = _.cloneDeep(data)
        // tempRecord.dbData = data

        let cb = sinon.spy();

        serviceObj.ctKafkaPublisher = {
            publishData : () => {
                return cb(null, [])
            }
        }
        let stub1 = sinon.stub(serviceObj.commonLib, 'getRetailerData').yields(null, 1)
        let stub2 = sinon.stub(serviceObj.commonLib, 'getCvrData').yields(null, 'abc.jpg')
        let stub3 = sinon.stub(serviceObj.reminderUtils, 'createCTPipelinePayload',).returns({});

        serviceObj.publishCtEvents(cb, tempRecord);
        stub1.restore();
        stub2.restore();
        stub3.restore();

        expect(stub1).to.have.callCount(1)
        expect(stub2).to.have.callCount(1)
        expect(stub3).to.have.callCount(1)
        expect(cb).to.have.been.calledWith(null)
        return done();
    })

    it("publishCtEvents function | no retailerStatus", (done) => {
        let tempRecord = _.cloneDeep(data)
        let cb = sinon.spy();

        serviceObj.ctKafkaPublisher = {
            publishData : () => {
                return cb(null, data)
            }
        }
        let stub1 = sinon.stub(serviceObj.commonLib, 'getRetailerData').yields("error")
        let stub2 = sinon.stub(serviceObj.commonLib, 'getCvrData').yields(null, 'abc.jpg')
        let stub3 = sinon.stub(serviceObj.reminderUtils, 'createCTPipelinePayload',).returns({});

        serviceObj.publishCtEvents(cb, tempRecord);
        stub1.restore();
        stub2.restore();
        stub3.restore();

        expect(stub1).to.have.callCount(1)
        expect(stub2).to.have.callCount(0)
        expect(stub3).to.have.callCount(0)
        return done();
    })

    it("publishCtEvents function | no thumbnail", (done) => {
        let tempRecord = _.cloneDeep(data)
        let cb = sinon.spy();

        serviceObj.ctKafkaPublisher = {
            publishData : () => {
                return cb(null, data)
            }
        }
        let stub1 = sinon.stub(serviceObj.commonLib, 'getRetailerData').yields(null, 1)
        let stub2 = sinon.stub(serviceObj.commonLib, 'getCvrData').yields("error")
        let stub3 = sinon.stub(serviceObj.reminderUtils, 'createCTPipelinePayload',).returns({});

        serviceObj.publishCtEvents(cb, tempRecord);
        stub1.restore();
        stub2.restore();
        stub3.restore();

        expect(stub1).to.have.callCount(1)
        expect(stub2).to.have.callCount(1)
        expect(stub3).to.have.callCount(0)
        return done();
    })

    it("publishCtEvents function | notification status!=1", (done) => { 
        let tempRecord = _.cloneDeep(data)
        _.set(tempRecord, 'notificationStatus', 0)
        // tempRecord.dbData = data

        let cb = sinon.spy();

        serviceObj.ctKafkaPublisher = {
            publishData : () => {
                return cb(null, [])
            }
        }
        let stub1 = sinon.stub(serviceObj.commonLib, 'getRetailerData').yields(null, 1)
        let stub2 = sinon.stub(serviceObj.commonLib, 'getCvrData').yields(null, 'abc.jpg')
        let stub3 = sinon.stub(serviceObj.reminderUtils, 'createCTPipelinePayload',).returns({});

        serviceObj.publishCtEvents(cb, tempRecord);
        stub1.restore();
        stub2.restore();
        stub3.restore();

        expect(stub1).to.have.callCount(1)
        expect(stub2).to.have.callCount(1)
        expect(stub3).to.have.callCount(1)
        expect(cb).to.have.been.calledWith(null)
        return done();
    })

    
})