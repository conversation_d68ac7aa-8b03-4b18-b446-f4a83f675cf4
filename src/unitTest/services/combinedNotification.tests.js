/*
  jshint 
    esversion: 8
 */

'use strict';
import { describe, it, before, afterEach, beforeEach} from 'mocha';
import sinon from 'sinon';
import chai from "chai";
import sinonChai from "sinon-chai";
import chaiAsPromised from "chai-as-promised";
import _ from 'lodash';
import CombinedNotification from '../../services/combinedNotification';
import STARTUP_MOCK from '../__mocks__/startUp'

chai.use(chaiAsPromised);
chai.use(sinonChai);

const { expect } = chai;

describe("Module: CombinedNotification service test suite", function () {
    let combinedNotificationObj, options;

    before(function (done) {
        STARTUP_MOCK.init(function(error, mock_options){
            options = mock_options;
            combinedNotificationObj = new CombinedNotification(options);
            done();
        });
    });

    describe("constructor", function() {
        it("should initialize all required properties", function() {
            expect(combinedNotificationObj.L).to.exist;
            expect(combinedNotificationObj.config).to.exist;
            expect(combinedNotificationObj.utilityInstance).to.exist;
            expect(combinedNotificationObj.combinedNotificationUtil).to.exist;
            expect(combinedNotificationObj.DUMMY_VALUE).to.equal("dummy");
        });
    });

    describe("validateDataToProcessForNotification", function() {
        it("should delegate to combinedNotificationUtil", function(done) {
            const mockPayload = { test: "data" };
            
            sinon.stub(combinedNotificationObj.combinedNotificationUtil, 'validateDataToProcessForNotification')
                .callsFake((callback, payload) => {
                    expect(payload).to.deep.equal(mockPayload);
                    callback(null, { validated: true });
                });

            combinedNotificationObj.validateDataToProcessForNotification(function(error, result) {
                expect(error).to.be.null;
                expect(result).to.deep.equal({ validated: true });
                
                combinedNotificationObj.combinedNotificationUtil.validateDataToProcessForNotification.restore();
                done();
            }, mockPayload);
        });
    });

    describe("convertKafkaPayloadToRecord", function() {
        it("should delegate to combinedNotificationUtil", function() {
            const mockPayload = { test: "data" };
            const expectedResult = { converted: true };
            
            sinon.stub(combinedNotificationObj.combinedNotificationUtil, 'convertKafkaPayloadToRecord')
                .withArgs(mockPayload)
                .returns(expectedResult);

            const result = combinedNotificationObj.convertKafkaPayloadToRecord(mockPayload);
            
            expect(result).to.deep.equal(expectedResult);
            
            combinedNotificationObj.combinedNotificationUtil.convertKafkaPayloadToRecord.restore();
        });
    });

    describe("prepareNotification", function() {
        let mockKafkaPayload;

        beforeEach(function() {
            mockKafkaPayload = {
                source: "test-source",
                notificationType: "COMBINED",
                data: {
                    customer_id: 12345,
                    service: "electricity",
                    combined_data: [
                        {
                            rechargeNumber: "RN001",
                            operator: "UPPCL",
                            dueDate: 1640995200000,
                            dueAmount: 150.50,
                            productId: 123,
                            userType: "RU",
                            service: "electricity",
                            status: 1,
                            notification_status: 1
                        },
                        {
                            rechargeNumber: "RN002",
                            operator: "UPPCL",
                            dueDate: 1641081600000,
                            dueAmount: 200.75,
                            productId: 124,
                            userType: "NONRU",
                            service: "electricity",
                            status: 2,
                            notification_status: 1
                        }
                    ]
                }
            };
        });

        afterEach(function() {
            // Restore all stubs after each test
            sinon.restore();
        });

        it("should prepare notification successfully for valid payload", async function() {
            // Mock utility instance methods
            sinon.stub(combinedNotificationObj.utilityInstance, 'getTemplates').returns({
                PUSH: 12345
            });
            
            sinon.stub(combinedNotificationObj, 'sendNotification').callsFake((record, notificationRecord, payLoad, callback) => {
                callback(null);
            });

            return new Promise((resolve, reject) => {
                combinedNotificationObj.prepareNotification(function(error, result) {
                    try {
                        expect(error).to.be.null;
                        expect(result).to.equal('Combined notification processed successfully');
                        
                        // Verify sendNotification was called with correct parameters
                        expect(combinedNotificationObj.sendNotification).to.have.been.calledOnce;
                        
                        const [record, notificationRecord, payLoad] = combinedNotificationObj.sendNotification.getCall(0).args;
                        
                        // Verify record structure
                        expect(record.source).to.equal("test-source");
                        expect(record.product_id).to.equal(-1);
                        expect(record.recharge_number).to.equal("electricity");
                        
                        // Verify notification record
                        expect(notificationRecord.type).to.equal('PUSH');
                        expect(notificationRecord.recipients).to.equal('12345');
                        expect(notificationRecord.notificationType).to.equal('COMBINED');
                        expect(notificationRecord.template_id).to.equal(12345);
                        
                        // Verify payload structure
                        expect(payLoad.recharge_number).to.equal("electricity");
                        expect(payLoad.operator).to.equal("electricity");
                        expect(payLoad.service).to.equal("electricity");
                        expect(payLoad.customer_id).to.equal(12345);
                        expect(payLoad.combinedParams).to.deep.equal(mockKafkaPayload.data.combined_data);
                        expect(payLoad.totalDueAmount).to.equal(351.25);
                        expect(payLoad.billCount).to.equal(2);
                        expect(payLoad.minDueDate).to.equal(1640995200000);
                        expect(payLoad.maxDueDate).to.equal(1641081600000);
                        
                        combinedNotificationObj.utilityInstance.getTemplates.restore();
                        combinedNotificationObj.sendNotification.restore();
                        resolve();
                    } catch (err) {
                        reject(err);
                    }
                }, mockKafkaPayload, "test-table");
            });
        });

        it("should fail for empty combined_data", function(done) {
            mockKafkaPayload.data.combined_data = [];

            combinedNotificationObj.prepareNotification(function(error, result) {
                expect(error).to.equal('Invalid or empty combined_data');
                done();
            }, mockKafkaPayload, "test-table");
        });

        it("should fail for missing customer_id", function(done) {
            delete mockKafkaPayload.data.customer_id;

            combinedNotificationObj.prepareNotification(function(error, result) {
                expect(error).to.equal('customer_id is required');
                done();
            }, mockKafkaPayload, "test-table");
        });

        it("should skip notification when no template_id found", async function() {
            // Mock utility instance methods
            sinon.stub(combinedNotificationObj.utilityInstance, 'getTemplates').returns({
                PUSH: null
            });
            
            sinon.stub(combinedNotificationObj, 'sendNotification');

            return new Promise((resolve, reject) => {
                combinedNotificationObj.prepareNotification(function(error, result) {
                    try {
                        expect(error).to.be.null;
                        expect(result).to.equal('Combined notification processed successfully');
                        
                        // Verify sendNotification was not called
                        expect(combinedNotificationObj.sendNotification).to.not.have.been.called;
                        
                        combinedNotificationObj.utilityInstance.getTemplates.restore();
                        combinedNotificationObj.sendNotification.restore();
                        resolve();
                    } catch (err) {
                        reject(err);
                    }
                }, mockKafkaPayload, "test-table");
            });
        });

        it("should handle sendNotification error", async function() {
            // Mock utility instance methods
            sinon.stub(combinedNotificationObj.utilityInstance, 'getTemplates').returns({
                PUSH: 12345
            });
            
            sinon.stub(combinedNotificationObj, 'sendNotification').callsFake((record, notificationRecord, payLoad, callback) => {
                callback(new Error("Send notification failed"));
            });

            return new Promise((resolve, reject) => {
                combinedNotificationObj.prepareNotification(function(error, result) {
                    try {
                        expect(error).to.equal("Send notification failed");
                        resolve();
                    } catch (err) {
                        reject(err);
                    }
                }, mockKafkaPayload, "test-table");
            });
        });
    });

    describe("sendNotification", function() {
        let mockRecord, mockNotificationRecord, mockPayLoad;

        beforeEach(function() {
            mockRecord = {
                source: "test-source",
                product_id: -1,
                recharge_number: "electricity"
            };

            mockNotificationRecord = {
                type: 'PUSH',
                recipients: '12345',
                notificationType: 'COMBINED',
                template_id: 12345
            };

            mockPayLoad = {
                recharge_number: "electricity",
                operator: "electricity",
                service: "electricity",
                customer_id: 12345,
                category_id: 1
            };
        });

        afterEach(function() {
            // Restore all stubs after each test
            sinon.restore();
        });

        it("should send PUSH notification successfully", function(done) {
            // Mock utility instance methods
            sinon.stub(combinedNotificationObj.utilityInstance, '_utmByTemplateId').returns('&utm_source=test&utm_medium=push&utm_campaign=test');
            sinon.stub(combinedNotificationObj.utilityInstance, 'getParamsForChatAndPush').returns({});
            sinon.stub(combinedNotificationObj.utilityInstance, 'getQueryParams').returns('product_id=-1');
            sinon.stub(combinedNotificationObj.utilityInstance, 'getExtraRechargeNum').returns('');
            sinon.stub(combinedNotificationObj.utilityInstance, 'appendPromoCodeInNotification').callsFake((payLoad, notificationRecord, callback) => {
                callback(null, null);
            });
            sinon.stub(combinedNotificationObj.utilityInstance.notificationLibrary, 'getPushNotiData').returns({
                template_type: 'push',
                template_id: 12345,
                options: {
                    data: mockPayLoad
                }
            });
            sinon.stub(combinedNotificationObj.utilityInstance, 'sendProcessedNotification').callsFake((record, pushData, notificationRecord, callback) => {
                callback(null);
            });

            combinedNotificationObj.sendNotification(mockRecord, mockNotificationRecord, mockPayLoad, function(error) {
                expect(error).to.be.null;
                
                // Verify all utility methods were called
                expect(combinedNotificationObj.utilityInstance._utmByTemplateId).to.have.been.called;
                expect(combinedNotificationObj.utilityInstance.getParamsForChatAndPush).to.have.been.called;
                expect(combinedNotificationObj.utilityInstance.sendProcessedNotification).to.have.been.called;
                
                done();
            });
        });

        it("should fail for invalid recipients", function(done) {
            mockNotificationRecord.recipients = null;

            combinedNotificationObj.sendNotification(mockRecord, mockNotificationRecord, mockPayLoad, function(error) {
                expect(error).to.equal('Invalid recipients or template_id');
                done();
            });
        });

        it("should fail for invalid template_id", function(done) {
            mockNotificationRecord.template_id = null;

            combinedNotificationObj.sendNotification(mockRecord, mockNotificationRecord, mockPayLoad, function(error) {
                expect(error).to.equal('Invalid recipients or template_id');
                done();
            });
        });

        it("should fail for non-PUSH notification type", function(done) {
            mockNotificationRecord.type = 'SMS';

            combinedNotificationObj.sendNotification(mockRecord, mockNotificationRecord, mockPayLoad, function(error) {
                expect(error).to.equal('Only PUSH notifications are supported for combined notifications');
                done();
            });
        });

        it("should handle promo code addition", function(done) {
            // Mock utility instance methods (fresh stubs for this test)
            const _utmStub = sinon.stub(combinedNotificationObj.utilityInstance, '_utmByTemplateId').returns('&utm_source=test&utm_medium=push&utm_campaign=test');
            const paramsStub = sinon.stub(combinedNotificationObj.utilityInstance, 'getParamsForChatAndPush').returns({});
            const queryStub = sinon.stub(combinedNotificationObj.utilityInstance, 'getQueryParams').returns('product_id=-1');
            const extraStub = sinon.stub(combinedNotificationObj.utilityInstance, 'getExtraRechargeNum').returns('');
            const promoStub = sinon.stub(combinedNotificationObj.utilityInstance, 'appendPromoCodeInNotification').callsFake((payLoad, notificationRecord, callback) => {
                callback(null, {
                    promo_text: "Get 10% off",
                    promocode: "SAVE10"
                });
            });
            const pushStub = sinon.stub(combinedNotificationObj.utilityInstance.notificationLibrary, 'getPushNotiData').returns({
                template_type: 'push',
                template_id: 12345,
                options: {
                    data: mockPayLoad
                }
            });
            const sendStub = sinon.stub(combinedNotificationObj.utilityInstance, 'sendProcessedNotification').callsFake((record, pushData, notificationRecord, callback) => {
                // Verify promo code was added to payload
                expect(pushData.options.data.promo_content).to.equal("Get 10% off");
                expect(pushData.options.data.promo_code).to.equal("SAVE10");
                callback(null);
            });

            combinedNotificationObj.sendNotification(mockRecord, mockNotificationRecord, mockPayLoad, function(error) {
                expect(error).to.be.null;
                done();
            });
        });
    });

    describe("utility delegation methods", function() {
        afterEach(function() {
            sinon.restore();
        });

        it("should delegate getTemplateId to utility instance", function() {
            const mockArgs = ['PUSH', {}, 'COMBINED', {}, null, 'test-table'];
            const expectedResult = 12345;
            
            sinon.stub(combinedNotificationObj.utilityInstance, 'getTemplateId')
                .withArgs(...mockArgs)
                .returns(expectedResult);

            const result = combinedNotificationObj.getTemplateId(...mockArgs);
            
            expect(result).to.equal(expectedResult);
        });

        it("should delegate replaceUrlVariables to utility instance", function() {
            const mockTemplate = "test-{var}-template";
            const mockParams = { var: "replaced" };
            const expectedResult = "test-replaced-template";
            
            sinon.stub(combinedNotificationObj.utilityInstance, 'replaceUrlVariables')
                .withArgs(mockTemplate, mockParams)
                .returns(expectedResult);

            const result = combinedNotificationObj.replaceUrlVariables(mockTemplate, mockParams);
            
            expect(result).to.equal(expectedResult);
        });

        it("should delegate getParamsForChatAndPush to utility instance", function() {
            const mockRecord = { test: "record" };
            const mockPayload = { test: "payload" };
            const expectedResult = { params: "result" };
            
            sinon.stub(combinedNotificationObj.utilityInstance, 'getParamsForChatAndPush')
                .withArgs(mockRecord, mockPayload)
                .returns(expectedResult);

            const result = combinedNotificationObj.getParamsForChatAndPush(mockRecord, mockPayload);
            
            expect(result).to.equal(expectedResult);
        });

        it("should throw error when utility instance not available", function() {
            const originalUtilityInstance = combinedNotificationObj.utilityInstance;
            combinedNotificationObj.utilityInstance = null;

            expect(() => {
                combinedNotificationObj.getTemplateId();
            }).to.throw('Utility instance not available');

            expect(() => {
                combinedNotificationObj.replaceUrlVariables();
            }).to.throw('Utility instance not available');

            expect(() => {
                combinedNotificationObj.getParamsForChatAndPush();
            }).to.throw('Utility instance not available');

            // Restore utility instance
            combinedNotificationObj.utilityInstance = originalUtilityInstance;
        });
    });

    describe("processNotification", function() {
        it("should process notification successfully", function(done) {
            const mockData = { test: "data" };
            
            sinon.stub(combinedNotificationObj, 'validateDataToProcessForNotification').callsFake((callback, data) => {
                callback(null, { validated: true, debugKey: "test-debug" });
            });
            
            sinon.stub(combinedNotificationObj, 'prepareNotification').callsFake((callback, record, tableName) => {
                callback(null, true);
            });

            combinedNotificationObj.processNotification(function() {
                expect(combinedNotificationObj.validateDataToProcessForNotification).to.have.been.called;
                expect(combinedNotificationObj.prepareNotification).to.have.been.called;
                
                combinedNotificationObj.validateDataToProcessForNotification.restore();
                combinedNotificationObj.prepareNotification.restore();
                done();
            }, mockData);
        });

        it("should handle validation error", function(done) {
            const mockData = { test: "data" };
            
            sinon.stub(combinedNotificationObj, 'validateDataToProcessForNotification').callsFake((callback, data) => {
                callback('Validation failed', null);
            });
            
            sinon.stub(combinedNotificationObj.utilityInstance.notify, 'insertRejectedNotifications').callsFake((done, error, record, notificationRecord) => {
                expect(error).to.contain('Validation failed');
                done();
            });

            combinedNotificationObj.processNotification(function() {
                combinedNotificationObj.validateDataToProcessForNotification.restore();
                combinedNotificationObj.utilityInstance.notify.insertRejectedNotifications.restore();
                done();
            }, mockData);
        });
    });
}); 