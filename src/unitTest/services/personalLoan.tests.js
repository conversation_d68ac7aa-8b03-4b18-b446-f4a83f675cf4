/*
  jshint 
    esversion: 8
 */

    'use strict';

    import { describe, it, before} from 'mocha';
    import chai from "chai";
    import sinonChai from "sinon-chai";
    import sinon from 'sinon';
    import chaiAsPromised from "chai-as-promised";
    import MOMENT from 'moment'
    import _, { isNull } from 'lodash';
    
    import STARTUP_MOCK from '../__mocks__/startUp'
    
    import SERVICE from '../../services'
    
    chai.use(chaiAsPromised);
    chai.use(sinonChai);
    
    const { expect } = chai;
    
    it.skip("Module: personalLoan service test suite", function () {
        let serviceObj;
    
        before(function (done) {
            STARTUP_MOCK.init(function(error, options){
                serviceObj = new SERVICE.personalLoan(options);
                done();
            });
        });
    
        it("_prepareDataToInsert | success case", (done) => {
            let recordData = {"account_number":"**********","customer_id":"*********","current_month_bill_amount":199.0,"unbilled_spends":900.0,"due_amount":100,"due_date":"2023-08-31T18:30:01.000+0000","billing_date":"2023-08-01T00:20:53.000+0000","catalog_repayment_pid":*********,"product_type":"POSTPAID","mobile_number":**********,"next_bill_date":"2023-08-31T18:30:00.489+0000","event_type":"BILL_GEN"};
            let processedRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                // dueDate : "2023-09",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            }
            let tableName = 'bills_personalloan';
            let customerId = 667770;
            let recentBillsData = {};
            let fromRecents = true;
    
            let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').returns([null,processedRecord]);

            let stub2 = sinon.stub(serviceObj.billsModel, 'getBillsOfSameRech').callsFake(function(cb,tableName, customerId) {
                return cb(null,[]);
            });
            let stub3 = sinon.stub(serviceObj, 'getBillsData').returns({});
            let stub4 = sinon.stub(serviceObj.bills, 'createBillForPersonalLoan').callsFake(function(cb,recentBillsData, tableName){
                return cb(null);
            })
            
            // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
            serviceObj._prepareDataToInsert(recordData,function(){
                stub1.restore();
                stub2.restore();
                stub3.restore();
                stub4.restore();
                
                expect(stub1).to.have.callCount(1)
                expect(stub2).to.have.callCount(1)
                expect(stub3).to.have.callCount(1)
                expect(stub4).to.have.callCount(1)
                return done();
            });
        });

        it("_prepareDataToInsert | error in validation", (done) => {
            let recordData = {"account_number":"**********","customer_id":"*********","current_month_bill_amount":199.0,"unbilled_spends":900.0,"due_amount":100,"due_date":"2023-08-31T18:30:01.000+0000","billing_date":"2023-08-01T00:20:53.000+0000","catalog_repayment_pid":*********,"product_type":"POSTPAID","mobile_number":**********,"next_bill_date":"2023-08-31T18:30:00.489+0000","event_type":"BILL_GEN"};
            let processedRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                // dueDate : "2023-09",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            }
            let tableName = 'bills_personalloan';
            let customerId = 667770;
            let recentBillsData = {};
            let fromRecents = true;
    
            let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').returns(["error in validation",processedRecord]);

            let stub2 = sinon.stub(serviceObj.billsModel, 'getBillsOfSameRech').callsFake(function(cb,tableName, customerId) {
                return cb(null,[]);
            });
            let stub3 = sinon.stub(serviceObj, 'getBillsData').returns({});
            let stub4 = sinon.stub(serviceObj.bills, 'createBillForPersonalLoan').callsFake(function(cb,recentBillsData, tableName){
                return cb(null);
            })
            
            // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
            serviceObj._prepareDataToInsert(recordData,function(){
                stub1.restore();
                stub2.restore();
                stub3.restore();
                stub4.restore();
                
                expect(stub1).to.have.callCount(1)
                expect(stub2).to.have.callCount(0)
                expect(stub3).to.have.callCount(0)
                expect(stub4).to.have.callCount(0)
                return done();
            });
        });

        it("_prepareDataToInsert | error in getBillsOfSameRech", (done) => {
            let recordData = {"account_number":"**********","customer_id":"*********","current_month_bill_amount":199.0,"unbilled_spends":900.0,"due_amount":100,"due_date":"2023-08-31T18:30:01.000+0000","billing_date":"2023-08-01T00:20:53.000+0000","catalog_repayment_pid":*********,"product_type":"POSTPAID","mobile_number":**********,"next_bill_date":"2023-08-31T18:30:00.489+0000","event_type":"BILL_GEN"};
            let processedRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                // dueDate : "2023-09",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            }
            let tableName = 'bills_personalloan';
            let customerId = 667770;
            let recentBillsData = {};
            let fromRecents = true;
            serviceObj.nonPaytmKafkaPublisher = {
                publishData : () => {
                    return cb(null, {})
                }
            }
    
            let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').returns([null,processedRecord]);

            let stub2 = sinon.stub(serviceObj.billsModel, 'getBillsOfSameRech').callsFake(function(cb,tableName, customerId) {
                return cb("error in getBillsOfSameRech",[]);
            });
            let stub3 = sinon.stub(serviceObj, 'getBillsData').returns({});
            let stub4 = sinon.stub(serviceObj.bills, 'createBillForPersonalLoan').callsFake(function(cb,recentBillsData, tableName){
                return cb(null);
            })
            
            // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
            serviceObj._prepareDataToInsert(recordData,function(){
                stub1.restore();
                stub2.restore();
                stub3.restore();
                stub4.restore();
                
                expect(stub1).to.have.callCount(1)
                expect(stub2).to.have.callCount(1)
                expect(stub3).to.have.callCount(0)
                expect(stub4).to.have.callCount(0)
                return done();
            });
        });

        it("_prepareDataToInsert | null from getBillsData", (done) => {
            let recordData = {"account_number":"**********","customer_id":"*********","current_month_bill_amount":199.0,"unbilled_spends":900.0,"due_amount":100,"due_date":"2023-08-31T18:30:01.000+0000","billing_date":"2023-08-01T00:20:53.000+0000","catalog_repayment_pid":*********,"product_type":"POSTPAID","mobile_number":**********,"next_bill_date":"2023-08-31T18:30:00.489+0000","event_type":"BILL_GEN"};
            let processedRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                // dueDate : "2023-09",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            }
            let tableName = 'bills_personalloan';
            let customerId = 667770;
            let recentBillsData = {};
            let fromRecents = true;
            serviceObj.nonPaytmKafkaPublisher = {
                publishData : () => {
                    return cb(null, {})
                }
            }
    
            let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').returns([null,processedRecord]);

            let stub2 = sinon.stub(serviceObj.billsModel, 'getBillsOfSameRech').callsFake(function(cb,tableName, customerId) {
                return cb(null,[]);
            });
            let stub3 = sinon.stub(serviceObj, 'getBillsData').returns(null);
            let stub4 = sinon.stub(serviceObj.bills, 'createBillForPersonalLoan').callsFake(function(cb,recentBillsData, tableName){
                return cb(null);
            })
            
            // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
            serviceObj._prepareDataToInsert(recordData,function(){
                stub1.restore();
                stub2.restore();
                stub3.restore();
                stub4.restore();
                
                expect(stub1).to.have.callCount(1)
                expect(stub2).to.have.callCount(1)
                expect(stub3).to.have.callCount(1)
                expect(stub4).to.have.callCount(0)
                return done();
            });
        });

        it("_prepareDataToInsert | error in createBillForPersonalLoan", (done) => {
            let recordData = {"account_number":"**********","customer_id":"*********","current_month_bill_amount":199.0,"unbilled_spends":900.0,"due_amount":100,"due_date":"2023-08-31T18:30:01.000+0000","billing_date":"2023-08-01T00:20:53.000+0000","catalog_repayment_pid":*********,"product_type":"POSTPAID","mobile_number":**********,"next_bill_date":"2023-08-31T18:30:00.489+0000","event_type":"BILL_GEN"};
            let processedRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                // dueDate : "2023-09",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            }
            let tableName = 'bills_personalloan';
            let customerId = 667770;
            let recentBillsData = {};
            let fromRecents = true;
            serviceObj.nonPaytmKafkaPublisher = {
                publishData : () => {
                    return cb(null, {})
                }
            }
    
            let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').returns([null,processedRecord]);

            let stub2 = sinon.stub(serviceObj.billsModel, 'getBillsOfSameRech').callsFake(function(cb,tableName, customerId) {
                return cb(null,[]);
            });
            let stub3 = sinon.stub(serviceObj, 'getBillsData').returns({});
            let stub4 = sinon.stub(serviceObj.bills, 'createBillForPersonalLoan').callsFake(function(cb,recentBillsData, tableName){
                return cb("error in creation");
            })
            
            // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
            serviceObj._prepareDataToInsert(recordData,function(){
                stub1.restore();
                stub2.restore();
                stub3.restore();
                stub4.restore();
                
                expect(stub1).to.have.callCount(1)
                expect(stub2).to.have.callCount(1)
                expect(stub3).to.have.callCount(1)
                expect(stub4).to.have.callCount(1)
                return done();
            });
        });

        it("_prepareDataToInsert | error in getUpdatedRecordFromDB", (done) => {
            let recordData = {"account_number":"**********","customer_id":"*********","current_month_bill_amount":199.0,"unbilled_spends":900.0,"due_amount":100,"due_date":"2023-08-31T18:30:01.000+0000","billing_date":"2023-08-01T00:20:53.000+0000","catalog_repayment_pid":*********,"product_type":"POSTPAID","mobile_number":**********,"next_bill_date":"2023-08-31T18:30:00.489+0000","event_type":"BILL_GEN"};
            let processedRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                // dueDate : "2023-09",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            }
            let tableName = 'bills_personalloan';
            let customerId = 667770;
            let recentBillsData = {};
            let fromRecents = true;
            serviceObj.nonPaytmKafkaPublisher = {
                publishData : () => {
                    return cb(null, {})
                }
            }
    
            let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').returns([null,processedRecord]);

            let stub2 = sinon.stub(serviceObj.billsModel, 'getBillsOfSameRech').callsFake(function(cb,tableName, customerId) {
                return cb(null,[]);
            });
            let stub3 = sinon.stub(serviceObj, 'getBillsData').returns({});
            let stub4 = sinon.stub(serviceObj.bills, 'createBillForPersonalLoan').callsFake(function(cb,recentBillsData, tableName){
                return cb(null);
            })
            
            // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
            serviceObj._prepareDataToInsert(recordData,function(){
                stub1.restore();
                stub2.restore();
                stub3.restore();
                stub4.restore();
                
                expect(stub1).to.have.callCount(1)
                expect(stub2).to.have.callCount(1)
                expect(stub3).to.have.callCount(1)
                expect(stub4).to.have.callCount(1)
                return done();
            });
        });

        it("_prepareDataToInsert | error in publishInKafka", (done) => {
            let recordData = {"account_number":"**********","customer_id":"*********","current_month_bill_amount":199.0,"unbilled_spends":900.0,"due_amount":100,"due_date":"2023-08-31T18:30:01.000+0000","billing_date":"2023-08-01T00:20:53.000+0000","catalog_repayment_pid":*********,"product_type":"POSTPAID","mobile_number":**********,"next_bill_date":"2023-08-31T18:30:00.489+0000","event_type":"BILL_GEN"};
            let processedRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                // dueDate : "2023-09",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            }
            let tableName = 'bills_personalloan';
            let customerId = 667770;
            let recentBillsData = {};
            let fromRecents = true;
            serviceObj.nonPaytmKafkaPublisher = {
                publishData : () => {
                    return cb(null, {})
                }
            }
    
            let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').returns([null,processedRecord]);

            let stub2 = sinon.stub(serviceObj.billsModel, 'getBillsOfSameRech').callsFake(function(cb,tableName, customerId) {
                return cb(null,[]);
            });
            let stub3 = sinon.stub(serviceObj, 'getBillsData').returns({});
            let stub4 = sinon.stub(serviceObj.bills, 'createBillForPersonalLoan').callsFake(function(cb,recentBillsData, tableName){
                return cb(null);
            })
            
            // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
            serviceObj._prepareDataToInsert(recordData,function(){
                stub1.restore();
                stub2.restore();
                stub3.restore();
                stub4.restore();
                
                
                expect(stub1).to.have.callCount(1)
                expect(stub2).to.have.callCount(1)
                expect(stub3).to.have.callCount(1)
                expect(stub4).to.have.callCount(1)
                
                return done();
            });
        });

        it("_prepareDataToInsert | error in publishInKafka", (done) => {
            let recordData = {"account_number":"**********","customer_id":"*********","current_month_bill_amount":199.0,"unbilled_spends":900.0,"due_amount":100,"due_date":"2023-08-31T18:30:01.000+0000","billing_date":"2023-08-01T00:20:53.000+0000","catalog_repayment_pid":*********,"product_type":"POSTPAID","mobile_number":**********,"next_bill_date":"2023-08-31T18:30:00.489+0000","event_type":"BILL_GEN"};
            let processedRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                // dueDate : "2023-09",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            }
            let tableName = 'bills_personalloan';
            let customerId = 667770;
            let recentBillsData = {};
            let fromRecents = true;
            serviceObj.nonPaytmKafkaPublisher = {
                publishData : () => {
                    return cb(null, {})
                }
            }
    
            let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').returns([null,processedRecord]);

            let stub2 = sinon.stub(serviceObj.billsModel, 'getBillsOfSameRech').callsFake(function(cb,tableName, customerId) {
                return cb(null,[]);
            });
            let stub3 = sinon.stub(serviceObj, 'getBillsData').returns({});
            let stub4 = sinon.stub(serviceObj.bills, 'createBillForPersonalLoan').callsFake(function(cb,recentBillsData, tableName){
                return cb(null);
            })
            
            
            // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
            serviceObj._prepareDataToInsert(recordData,function(){
                stub1.restore();
                stub2.restore();
                stub3.restore();
                stub4.restore();
                
                expect(stub1).to.have.callCount(1)
                expect(stub2).to.have.callCount(1)
                expect(stub3).to.have.callCount(1)
                expect(stub4).to.have.callCount(1)
                return done();
            });
        });

        
        it.skip("validateAndProcessRecord | sucess", (done) => {
            let recordData = {"account_number":"**********","customer_id":"*********","current_month_bill_amount":199.0,"unbilled_spends":900.0,"due_amount":100,"due_date":MOMENT().add(1,'months').format("YYYY-MM-DDTHH:mm:ss.SSSZ"),"billing_date":"2023-08-01T00:20:53.000+0000","catalog_repayment_pid":**********,"product_type":"POSTPAID","mobile_number":**********,"next_bill_date":"2023-08-31T18:30:00.489+0000","event_type":"BILL_GEN"};
            let processedRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                // dueDate : "2023-09",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            }
            let tableName = 'bills_personalloan';
            let customerId = 667770;
            let recentBillsData = {};
            let fromRecents = true;
            serviceObj.nonPaytmKafkaPublisher = {
                publishData : () => {
                    return cb(null, {})
                }
            }
    
            // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
            let [error,record] = serviceObj.validateAndProcessRecord(recordData);
            console.log("record record record::",record);
            expect(record.customerId).to.equal(*********)
            expect(record.rechargeNumber).to.equal('**********')
            expect(record.currentBillAmount).to.equal(199)
            expect(record.dueAmount).to.equal(100)
            expect(record.unBilledAmount).to.equal(900)
            expect(record.dueDate).to.equal(MOMENT().add(1,'months').startOf('day').format('YYYY-MM-DD HH:mm:ss'))
            expect(record.billDate).to.equal('2023-08-01 05:50:53')
            expect(record.nextBillFetchDate).to.equal('2023-09-01 00:00:00')
            expect(record.billFetchDate).to.equal(MOMENT().format('YYYY-MM-DD HH:mm:ss'))
            expect(record.productId).to.equal(**********)
            expect(record.productType).to.equal('POSTPAID')
            expect(record.custMobile).to.equal(**********)
            expect(record.eventType).to.equal('BILL_GEN')
            expect(record.billGen).to.equal(true)
            expect(record.operator).to.equal('airtel')
            expect(record.service).to.equal('mobile')
            expect(error).to.equal(null)
            return done();
        });

        it.skip("validateAndProcessRecord | success, dueDate older", (done) => {
            let recordData = {"account_number":"**********","customer_id":"*********","current_month_bill_amount":199.0,"unbilled_spends":900.0,"due_amount":100,"due_date":MOMENT().subtract(1,'months').format("YYYY-MM-DDTHH:mm:ss.SSSZ"),"billing_date":"2023-08-01T00:20:53.000+0000","catalog_repayment_pid":**********,"product_type":"POSTPAID","mobile_number":**********,"next_bill_date":"2023-08-31T18:30:00.489+0000","event_type":"BILL_GEN"};
            let processedRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                // dueDate : "2023-09",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            }
            let tableName = 'bills_personalloan';
            let customerId = 667770;
            let recentBillsData = {};
            let fromRecents = true;
            serviceObj.nonPaytmKafkaPublisher = {
                publishData : () => {
                    return cb(null, {})
                }
            }
    
            // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
            let [error,record] = serviceObj.validateAndProcessRecord(recordData);
            console.log("record record record::",record);
            expect(record.customerId).to.equal(*********)
            expect(record.rechargeNumber).to.equal('**********')
            expect(record.currentBillAmount).to.equal(199)
            expect(record.dueAmount).to.equal(100)
            expect(record.unBilledAmount).to.equal(900)
            expect(record.dueDate).to.equal(MOMENT().subtract(1,'months').startOf('day').format('YYYY-MM-DD HH:mm:ss'))
            expect(record.billDate).to.equal('2023-08-01 05:50:53')
            expect(record.nextBillFetchDate).to.equal('2023-09-01 00:00:00')
            expect(record.billFetchDate).to.equal(MOMENT().format('YYYY-MM-DD HH:mm:ss'))
            expect(record.productId).to.equal(**********)
            expect(record.productType).to.equal('POSTPAID')
            expect(record.custMobile).to.equal(**********)
            expect(record.eventType).to.equal('BILL_GEN')
            expect(record.billGen).to.equal(true)
            expect(record.operator).to.equal('airtel')
            expect(record.service).to.equal('mobile')
            expect(error).to.equal(null)
            return done();
        });

        it.skip("validateAndProcessRecord | failure, cust_id empty string", (done) => {
            let recordData = {"account_number":"**********","customer_id":"","current_month_bill_amount":199.0,"unbilled_spends":900.0,"due_amount":100,"due_date":MOMENT().add(1,'months').format("YYYY-MM-DDTHH:mm:ss.SSSZ"),"billing_date":"2023-08-01T00:20:53.000+0000","catalog_repayment_pid":**********,"product_type":"POSTPAID","mobile_number":**********,"next_bill_date":"2023-08-31T18:30:00.489+0000","event_type":"BILL_GEN"};
            let processedRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                // dueDate : "2023-09",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            }
            let tableName = 'bills_personalloan';
            let customerId = 667770;
            let recentBillsData = {};
            let fromRecents = true;
            serviceObj.nonPaytmKafkaPublisher = {
                publishData : () => {
                    return cb(null, {})
                }
            }
    
            // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
            let [error,record] = serviceObj.validateAndProcessRecord(recordData);
            console.log("record record record::",record);
            expect(record.customerId).to.equal(null)
            expect(record.rechargeNumber).to.equal('**********')
            expect(record.currentBillAmount).to.equal(199)
            expect(record.dueAmount).to.equal(100)
            expect(record.unBilledAmount).to.equal(900)
            expect(record.dueDate).to.equal(MOMENT().add(1,'months').startOf('day').format('YYYY-MM-DD HH:mm:ss'))
            expect(record.billDate).to.equal('2023-08-01 05:50:53')
            expect(record.nextBillFetchDate).to.equal('2023-09-01 00:00:00')
            expect(record.billFetchDate).to.equal(MOMENT().format('YYYY-MM-DD HH:mm:ss'))
            expect(record.productId).to.equal(**********)
            expect(record.productType).to.equal('POSTPAID')
            expect(record.custMobile).to.equal(**********)
            expect(record.eventType).to.equal('BILL_GEN')
            expect(record.billGen).to.equal(true)
            expect(record.operator).to.equal('airtel')
            expect(record.service).to.equal('mobile')
            expect(error).to.equal('Mandatory Params customerId is Missing / Invalid')
            return done();
        });

        it.skip("validateAndProcessRecord | failure, recharge_number empty string", (done) => {
            let recordData = {"account_number":"","customer_id":"12345","current_month_bill_amount":199.0,"unbilled_spends":900.0,"due_amount":100,"due_date":MOMENT().add(1,'months').format("YYYY-MM-DDTHH:mm:ss.SSSZ"),"billing_date":"2023-08-01T00:20:53.000+0000","catalog_repayment_pid":**********,"product_type":"POSTPAID","mobile_number":**********,"next_bill_date":"2023-08-31T18:30:00.489+0000","event_type":"BILL_GEN"};
            let processedRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                // dueDate : "2023-09",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            }
            let tableName = 'bills_personalloan';
            let customerId = 667770;
            let recentBillsData = {};
            let fromRecents = true;
            serviceObj.nonPaytmKafkaPublisher = {
                publishData : () => {
                    return cb(null, {})
                }
            }
    
            // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
            let [error,record] = serviceObj.validateAndProcessRecord(recordData);
            console.log("record record record::",record);
            expect(record.customerId).to.equal(12345)
            expect(record.rechargeNumber).to.equal('')
            expect(record.currentBillAmount).to.equal(199)
            expect(record.dueAmount).to.equal(100)
            expect(record.unBilledAmount).to.equal(900)
            expect(record.dueDate).to.equal(MOMENT().add(1,'months').startOf('day').format('YYYY-MM-DD HH:mm:ss'))
            expect(record.billDate).to.equal('2023-08-01 05:50:53')
            expect(record.nextBillFetchDate).to.equal('2023-09-01 00:00:00')
            expect(record.billFetchDate).to.equal(MOMENT().format('YYYY-MM-DD HH:mm:ss'))
            expect(record.productId).to.equal(**********)
            expect(record.productType).to.equal('POSTPAID')
            expect(record.custMobile).to.equal(**********)
            expect(record.eventType).to.equal('BILL_GEN')
            expect(record.billGen).to.equal(true)
            expect(record.operator).to.equal('airtel')
            expect(record.service).to.equal('mobile')
            expect(error).to.equal('Mandatory Params rechargeNumber is Missing / Invalid')
            return done();
        });

        it.skip("validateAndProcessRecord | failure, due_amount not present empty string", (done) => {
            let recordData = {"account_number":"1234566","customer_id":"12345","current_month_bill_amount":199.0,"unbilled_spends":900.0,"due_date":MOMENT().add(1,'months').format("YYYY-MM-DDTHH:mm:ss.SSSZ"),"billing_date":"2023-08-01T00:20:53.000+0000","catalog_repayment_pid":**********,"product_type":"POSTPAID","mobile_number":**********,"next_bill_date":"2023-08-31T18:30:00.489+0000","event_type":"BILL_GEN"};
            let processedRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                // dueDate : "2023-09",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            }
            let tableName = 'bills_personalloan';
            let customerId = 667770;
            let recentBillsData = {};
            let fromRecents = true;
            serviceObj.nonPaytmKafkaPublisher = {
                publishData : () => {
                    return cb(null, {})
                }
            }
    
            // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
            let [error,record] = serviceObj.validateAndProcessRecord(recordData);
            console.log("record record record::",record);
            expect(record.customerId).to.equal(12345)
            expect(record.rechargeNumber).to.equal('1234566')
            expect(record.currentBillAmount).to.equal(199)
            expect(record.dueAmount).to.equal(null)
            expect(record.unBilledAmount).to.equal(900)
            expect(record.dueDate).to.equal(MOMENT().add(1,'months').startOf('day').format('YYYY-MM-DD HH:mm:ss'))
            expect(record.billDate).to.equal('2023-08-01 05:50:53')
            expect(record.nextBillFetchDate).to.equal('2023-09-01 00:00:00')
            expect(record.billFetchDate).to.equal(MOMENT().format('YYYY-MM-DD HH:mm:ss'))
            expect(record.productId).to.equal(**********)
            expect(record.productType).to.equal('POSTPAID')
            expect(record.custMobile).to.equal(**********)
            expect(record.eventType).to.equal('BILL_GEN')
            expect(record.billGen).to.equal(true)
            expect(record.operator).to.equal('airtel')
            expect(record.service).to.equal('mobile')
            expect(error).to.equal('Mandatory Params due_amount is Missing / Invalid')
            return done();
        });

        it.skip("validateAndProcessRecord | failure, due_amount is string", (done) => {
            let recordData = {"account_number":"1234566","customer_id":"12345","current_month_bill_amount":199.0,"unbilled_spends":900.0,"due_amount":"abc","due_date":MOMENT().add(1,'months').format("YYYY-MM-DDTHH:mm:ss.SSSZ"),"billing_date":"2023-08-01T00:20:53.000+0000","catalog_repayment_pid":**********,"product_type":"POSTPAID","mobile_number":**********,"next_bill_date":"2023-08-31T18:30:00.489+0000","event_type":"BILL_GEN"};
            let processedRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                // dueDate : "2023-09",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            }
            let tableName = 'bills_personalloan';
            let customerId = 667770;
            let recentBillsData = {};
            let fromRecents = true;
            serviceObj.nonPaytmKafkaPublisher = {
                publishData : () => {
                    return cb(null, {})
                }
            }
    
            // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
            let [error,record] = serviceObj.validateAndProcessRecord(recordData);
            console.log("record record record::",record);
            expect(record.customerId).to.equal(12345)
            expect(record.rechargeNumber).to.equal('1234566')
            expect(record.currentBillAmount).to.equal(199)
            expect(record.dueAmount).to.equal(null)
            expect(record.unBilledAmount).to.equal(900)
            expect(record.dueDate).to.equal(MOMENT().add(1,'months').startOf('day').format('YYYY-MM-DD HH:mm:ss'))
            expect(record.billDate).to.equal('2023-08-01 05:50:53')
            expect(record.nextBillFetchDate).to.equal('2023-09-01 00:00:00')
            expect(record.billFetchDate).to.equal(MOMENT().format('YYYY-MM-DD HH:mm:ss'))
            expect(record.productId).to.equal(**********)
            expect(record.productType).to.equal('POSTPAID')
            expect(record.custMobile).to.equal(**********)
            expect(record.eventType).to.equal('BILL_GEN')
            expect(record.billGen).to.equal(true)
            expect(record.operator).to.equal('airtel')
            expect(record.service).to.equal('mobile')
            expect(error).to.equal('Mandatory Params due_amount is Missing / Invalid')
            return done();
        });

        it.skip("validateAndProcessRecord | failure, due_amount is zero", (done) => {
            let recordData = {"account_number":"1234566","customer_id":"12345","current_month_bill_amount":199.0,"unbilled_spends":900.0,"due_amount":'0',"due_date":MOMENT().add(1,'months').format("YYYY-MM-DDTHH:mm:ss.SSSZ"),"billing_date":"2023-08-01T00:20:53.000+0000","catalog_repayment_pid":**********,"product_type":"POSTPAID","mobile_number":**********,"next_bill_date":"2023-08-31T18:30:00.489+0000","event_type":"BILL_GEN"};
            let processedRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                // dueDate : "2023-09",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            }
            let tableName = 'bills_personalloan';
            let customerId = 667770;
            let recentBillsData = {};
            let fromRecents = true;
            serviceObj.nonPaytmKafkaPublisher = {
                publishData : () => {
                    return cb(null, {})
                }
            }
    
            // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
            let [error,record] = serviceObj.validateAndProcessRecord(recordData);
            console.log("record record record::",record);
            expect(record.customerId).to.equal(12345)
            expect(record.rechargeNumber).to.equal('1234566')
            expect(record.currentBillAmount).to.equal(199)
            expect(record.dueAmount).to.equal(0)
            expect(record.unBilledAmount).to.equal(900)
            expect(record.dueDate).to.equal(MOMENT().add(1,'months').startOf('day').format('YYYY-MM-DD HH:mm:ss'))
            expect(record.billDate).to.equal('2023-08-01 05:50:53')
            expect(record.nextBillFetchDate).to.equal('2023-09-01 00:00:00')
            expect(record.billFetchDate).to.equal(MOMENT().format('YYYY-MM-DD HH:mm:ss'))
            expect(record.productId).to.equal(**********)
            expect(record.productType).to.equal('POSTPAID')
            expect(record.custMobile).to.equal(**********)
            expect(record.eventType).to.equal('BILL_GEN')
            expect(record.billGen).to.equal(true)
            expect(record.operator).to.equal('airtel')
            expect(record.service).to.equal('mobile')
            expect(error).to.equal(null)
            return done();
        });
        it("validateAndProcessRecord | failure, productid is string", (done) => {
            let recordData = {"account_number":"1234566","customer_id":"12345","current_month_bill_amount":199.0,"unbilled_spends":900.0,"due_amount":150,"due_date":MOMENT().add(1,'months').format("YYYY-MM-DDTHH:mm:ss.SSSZ"),"billing_date":"2023-08-01T00:20:53.000+0000","catalog_repayment_pid":"**********","product_type":"POSTPAID","mobile_number":**********,"next_bill_date":"2023-08-31T18:30:00.489+0000","event_type":"BILL_GEN"};
            let processedRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                // dueDate : "2023-09",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            }
            let tableName = 'bills_personalloan';
            let customerId = 667770;
            let recentBillsData = {};
            let fromRecents = true;
            serviceObj.nonPaytmKafkaPublisher = {
                publishData : () => {
                    return cb(null, {})
                }
            }
    
            // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
            let [error,record] = serviceObj.validateAndProcessRecord(recordData);
            console.log("record record record::",record);
            expect(record.customerId).to.equal(12345)
            expect(record.rechargeNumber).to.equal('1234566')
            expect(record.currentBillAmount).to.equal(199)
            expect(record.dueAmount).to.equal(150)
            expect(record.unBilledAmount).to.equal(900)
            expect(record.dueDate).to.equal(MOMENT().add(1,'months').startOf('day').format('YYYY-MM-DD HH:mm:ss'))
            expect(record.billDate).to.equal('2023-08-01 05:50:53')
            expect(record.nextBillFetchDate).to.equal('2023-09-01 00:00:00')
            expect(record.billFetchDate).to.equal(MOMENT().format('YYYY-MM-DD HH:mm:ss'))
            expect(record.productId).to.equal('**********')
            expect(record.productType).to.equal('POSTPAID')
            expect(record.custMobile).to.equal(**********)
            expect(record.eventType).to.equal('BILL_GEN')
            expect(record.billGen).to.equal(true)
            expect(record.operator).to.equal('airtel')
            expect(record.service).to.equal('mobile')
            expect(error).to.equal(null)
            return done();
        });

        it("getBillsData | same bill updated with billGen true", (done) => {
            let dbrecord = {transactionHistory : [{customerOtherInfo : "{\"billGen\":true}",due_date:"2023-12-05 05:30:00", amount : 100}]};
            let processedRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                dueDate : "2023-12-05 00:00:00",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            }
            let tableName = 'bills_personalloan';
            let customerId = 667770;
            let recentBillsData = {};
            let fromRecents = true;
    
            // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
            let billsData = serviceObj.getBillsData(dbrecord,processedRecord);
            expect(billsData.sameBill).to.equal(true)
            return done();
        });
    
    });