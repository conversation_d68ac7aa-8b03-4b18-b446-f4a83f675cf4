/*
  jshint 
    esversion: 8
 */

    'use strict';

    import { describe, it, before, beforeEach, afterEach } from 'mocha';
    import chai from "chai";
    import sinonChai from "sinon-chai";
    import chaiAsPromised from "chai-as-promised";
    import MOMENT from 'moment'
    import sinon from 'sinon';
    import _ from 'lodash'
    
    import SERVICE from '../../services'
    import STARTUP_MOCK from '../__mocks__/startUp'
    import SmsParsingCCBills from '../../services/smsParsingCCBills';
    import uuidv1 from 'uuidv1'

    chai.use(chaiAsPromised);
    chai.use(sinonChai);
    
    const { expect } = chai;
    
    let server;
    
    describe("Module: SMS Parsing CC Bills Consumer suite :: Kafka consumer validations", function () {
        let serviceObj;
    
        let data, record;
        before(function () {
            STARTUP_MOCK.init(function(error, options){
                options.config = options.config || {};
                options.config.ENCRYPTION_CONFIG = options.config.ENCRYPTION_CONFIG || {};
                options.config.ENCRYPTION_CONFIG.DEFAULT = options.config.ENCRYPTION_CONFIG.DEFAULT || {};
                options.config.ENCRYPTION_CONFIG.DEFAULT.IV = '68e232d2639555a0cf08aaed9d50a025'; //adding staging keys as mock config
                options.config.ENCRYPTION_CONFIG.DEFAULT.ENCRYPTION_KEY = 'vOVH6sdmpNWjRRIqCc7rdxs01lwHzfr3'; 
                serviceObj = new SERVICE.SmsParsingCCBills(options);
                done();
            });
        });
    
    
        it("testing initialize consumer | default values", () => {
            let cb = sinon.spy();
            serviceObj.configureKafka(cb);
            expect(cb).to.have.callCount(1);
            expect(cb).to.have.calledWith(null)
            
        });
    
        it("start service || ensure service initialises consumer", () => {
            let initializeStub = sinon.stub(serviceObj, 'configureKafka').yields(null);
            serviceObj.start();
            expect(initializeStub).to.have.been.calledOnce;
        });
    
        it("processKafkaData || ensure empty records are validated", () => {
            let record = {}
            let processedRecords = serviceObj.execSteps(record);
            expect(processedRecords).to.be.equal(undefined)
        })
    
    });
    
    
    describe("Module: SMS Parsing CC Bills Consumer suite", function () {
        let serviceObj;
    
        before(function (done) {
            STARTUP_MOCK.init(function(error, options){
                options.config = options.config || {};
                options.config.ENCRYPTION_CONFIG = options.config.ENCRYPTION_CONFIG || {};
                options.config.ENCRYPTION_CONFIG.DEFAULT = options.config.ENCRYPTION_CONFIG.DEFAULT || {};
                options.config.ENCRYPTION_CONFIG.DEFAULT.IV = '68e232d2639555a0cf08aaed9d50a025'; //adding staging keys as mock config
                options.config.ENCRYPTION_CONFIG.DEFAULT.ENCRYPTION_KEY = 'vOVH6sdmpNWjRRIqCc7rdxs01lwHzfr3'; 
                serviceObj = new SERVICE.SmsParsingCCBills(options);
                done();
            });
    
            // LIB.init({}, function (error, optionsResponse) {
            //     if (error) {
            //         done(error);
            //     } else {
            //         options = optionsResponse;
            //         serviceObj = new SERVICE.SmsParsingCCBills(options);
            //         serviceObj.start();
    
            //         let queries = [
            //             "CREATE TABLE IF NOT EXISTS `bills_creditcard` ( `id` bigint unsigned NOT NULL AUTO_INCREMENT, `customer_id` bigint unsigned NOT NULL, `recharge_number` varchar(255) DEFAULT NULL, `product_id` int unsigned DEFAULT NULL, `reference_id` varchar(255) DEFAULT NULL, `operator` varchar(255) DEFAULT NULL, `amount` decimal(12,2) DEFAULT '0.00', `bill_date` datetime DEFAULT NULL COMMENT 'operator bill generation date', `due_date` datetime DEFAULT NULL, `bill_fetch_date` datetime DEFAULT NULL, `next_bill_fetch_date` datetime DEFAULT NULL, `gateway` varchar(255) DEFAULT NULL, `paytype` varchar(255) DEFAULT NULL, `service` varchar(255) DEFAULT NULL, `circle` varchar(255) DEFAULT NULL, `customer_mobile` varchar(15) DEFAULT NULL, `customer_email` varchar(255) DEFAULT NULL, `payment_channel` varchar(255) DEFAULT NULL, `retry_count` int unsigned DEFAULT '0', `status` int NOT NULL DEFAULT '0', `reason` text, `extra` text, `published_date` datetime DEFAULT NULL, `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, `user_data` text, `notification_status` tinyint DEFAULT '1', `payment_date` datetime DEFAULT NULL, `service_id` int NOT NULL DEFAULT '0', `customerOtherInfo` text, `is_automatic` tinyint(1) NOT NULL DEFAULT '0', PRIMARY KEY (`id`), UNIQUE KEY `customer_id_3` (`customer_id`,`recharge_number`,`reference_id`), KEY `bill_fetch_date` (`bill_fetch_date`), KEY `due_date` (`due_date`), KEY `idx_published_date` (`published_date`), KEY `idx_updated_at` (`updated_at`), KEY `idx_status_retry_count_next_bill_fetch_date` (`status`,`retry_count`,`next_bill_fetch_date`), KEY `next_bill_fetch_date` (`next_bill_fetch_date`), KEY `idx_comp` (`recharge_number`,`operator`,`service`) ) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8",
            //             "INSERT INTO `bills_creditcard` VALUES (1,667770,'5555 77XX XXXX 8767',**********,'hdfc_ref_id_001','neft_hdfcbank',-10800.00,NULL,'2019-01-01 00:00:00',NULL,'2020-10-17 01:00:00','cc_visa','credit card','mobile','',NULL,'','',0,0,NULL,NULL,NULL,'2020-10-12 14:06:56','2020-10-14 07:03:57','{}',1,'2020-10-14 12:24:18',0,NULL,0),(2,667771,'5555 77XX XXXX 8767',**********,'hdfc_ref_id_001','neft_hdfcbank',11711.00,'2020-11-11 00:00:00','2019-01-01 00:00:00','2020-10-14 14:59:48','2020-10-17 01:00:00','cc_visa','credit card','Mobile','',NULL,'','',0,0,NULL,NULL,NULL,'2020-10-12 14:06:57','2020-10-14 09:34:19','{}',1,'2020-10-14 12:24:17',0,'{\"customerId\":667771,\"lastCC\":\"8767\",\"currentBillAmount\":11711,\"currentMinBillAmount\":590,\"billDate\":\"2020-11-10T18:30:00.000Z\",\"billDueDate\":\"2020-12-10T18:30:00.000Z\",\"smsSenderID\":\"AD-HDFCBK\",\"billConsumeTimestamp\":\"2020-10-14 14:59:48\",\"debugKey\":\"smsSenderID:AD-HDFCBK_custId:667771_lastCC:8767_Id:2_MCN:5555 77XX XXXX 8767_operator:neft_hdfcbank\"}',0) ON DUPLICATE KEY UPDATE due_date = '2019-01-01',status = 0",
            //             "INSERT INTO `bills_creditcard` VALUES (123456,112233,'5555 77XX XXXX 8767',**********,'hdfc_ref_id_001','neft_hdfcbank',-10800.00,NULL,'2019-01-01 00:00:00',NULL,'2020-10-17 01:00:00','cc_visa','credit card','mobile','',NULL,'','',0,0,NULL,NULL,NULL,'2020-10-12 14:06:56','2020-10-14 07:03:57','{}',1,'2020-10-14 12:24:18',0,NULL,0),(2,667771,'5555 77XX XXXX 8767',**********,'hdfc_ref_id_001','neft_hdfcbank',11711.00,'2020-11-11 00:00:00','2019-01-01 00:00:00','2020-10-14 14:59:48','2020-10-17 01:00:00','cc_visa','credit card','Mobile','',NULL,'','',0,0,NULL,NULL,NULL,'2020-10-12 14:06:57','2020-10-14 09:34:19','{}',1,'2020-10-14 12:24:17',0,'{\"customerId\":667771,\"lastCC\":\"8767\",\"currentBillAmount\":11711,\"currentMinBillAmount\":590,\"billDate\":\"2020-11-10T18:30:00.000Z\",\"billDueDate\":\"2020-12-10T18:30:00.000Z\",\"smsSenderID\":\"AD-HDFCBK\",\"billConsumeTimestamp\":\"2020-10-14 14:59:48\",\"debugKey\":\"smsSenderID:AD-HDFCBK_custId:667771_lastCC:8767_Id:2_MCN:5555 77XX XXXX 8767_operator:neft_hdfcbank\"}',0) ON DUPLICATE KEY UPDATE due_date = '2019-01-01',status = 0",
            //             "INSERT INTO `bills_creditcard` VALUES (123457,112233,'6666 77XX XXXX 8767',**********,'hdfc_ref_id_003','neft_hdfcbank',-10800.00,NULL,'2019-01-01 00:00:00',NULL,'2020-10-17 01:00:00','cc_visa','credit card','mobile','',NULL,'','',0,0,NULL,NULL,NULL,'2020-10-12 14:06:56','2020-10-14 07:03:57','{}',1,'2020-10-14 12:24:18',0,NULL,0),(2,667771,'5555 77XX XXXX 8767',**********,'hdfc_ref_id_001','neft_hdfcbank',11711.00,'2020-11-11 00:00:00','2019-01-01 00:00:00','2020-10-14 14:59:48','2020-10-17 01:00:00','cc_visa','credit card','Mobile','',NULL,'','',0,0,NULL,NULL,NULL,'2020-10-12 14:06:57','2020-10-14 09:34:19','{}',1,'2020-10-14 12:24:17',0,'{\"customerId\":667771,\"lastCC\":\"8767\",\"currentBillAmount\":11711,\"currentMinBillAmount\":590,\"billDate\":\"2020-11-10T18:30:00.000Z\",\"billDueDate\":\"2020-12-10T18:30:00.000Z\",\"smsSenderID\":\"AD-HDFCBK\",\"billConsumeTimestamp\":\"2020-10-14 14:59:48\",\"debugKey\":\"smsSenderID:AD-HDFCBK_custId:667771_lastCC:8767_Id:2_MCN:5555 77XX XXXX 8767_operator:neft_hdfcbank\"}',0) ON DUPLICATE KEY UPDATE due_date = '2019-01-01',status = 0",
            //             "INSERT INTO `bills_creditcard` VALUES (123458,1234,'1234 77XX XXXX 1234',**********,'hdfc_ref_id_004','neft_hdfcbank',-10800.00,NULL,'2019-01-01 00:00:00',NULL,'2020-10-17 01:00:00','cc_visa','credit card','mobile','',NULL,'','',0,0,NULL,NULL,NULL,'2020-10-12 14:06:56','2020-10-14 07:03:57','{}',1,'2020-10-14 12:24:18',0,NULL,0),(2,667771,'5555 77XX XXXX 8767',**********,'hdfc_ref_id_001','neft_hdfcbank',11711.00,'2020-11-11 00:00:00','2019-01-01 00:00:00','2020-10-14 14:59:48','2020-10-17 01:00:00','cc_visa','credit card','Mobile','',NULL,'','',0,0,NULL,NULL,NULL,'2020-10-12 14:06:57','2020-10-14 09:34:19','{}',1,'2020-10-14 12:24:17',0,'{\"customerId\":667771,\"lastCC\":\"8767\",\"currentBillAmount\":11711,\"currentMinBillAmount\":590,\"billDate\":\"2020-11-10T18:30:00.000Z\",\"billDueDate\":\"2020-12-10T18:30:00.000Z\",\"smsSenderID\":\"AD-HDFCBK\",\"billConsumeTimestamp\":\"2020-10-14 14:59:48\",\"debugKey\":\"smsSenderID:AD-HDFCBK_custId:667771_lastCC:8767_Id:2_MCN:5555 77XX XXXX 8767_operator:neft_hdfcbank\"}',0) ON DUPLICATE KEY UPDATE due_date = '2019-01-01',status = 0",
            //             "delete from notification"
            //         ];
            //         options.dbInstance.exec(function (err, data) {
            //             if (err) {
            //                 console.log('Error DB entry for sms parsing service', queries.join(":"), err);
            //             }
            //             return done();
            //         }, 'DIGITAL_REMINDER_MASTER', queries.join(";"));
            //     }
            // });
        });
    
        beforeEach(function () {
            server = sinon.fakeServer.create();
        });
    
        afterEach(function () {
            server.restore();
        });
        
        it("execSteps || ensure empty records are validated", () => {
            let record = {}
            let processedRecords = serviceObj.execSteps(record);
            expect(processedRecords).to.be.equal(undefined)
        })
        it("execSteps || greyScaleEnv || Error in commitOffset function", () => {
            let record = [
            ];
            let stub1 = sinon.stub(serviceObj, "processBatch").resolves();
            sinon.stub(process, 'exit');
            let clock = sinon.useFakeTimers();
            serviceObj.kafkaSMSParsingConsumer = {
                _pauseConsumer : () => {
                    return;
                },
                commitOffset: (data, cb) => {
                    return cb("Error in commitOffset");
                },
                _resumeConsumer: () => {
                }
            };
            serviceObj.greyScaleEnv = true;
            
            let processedRecords = serviceObj.execSteps(record);
            clock.tick(40 * 60 * 1000)
            expect(processedRecords).to.be.equal(undefined);
            expect(stub1).to.have.callCount(0);
            stub1.restore();
            process.exit.restore();
        })
        it("execSteps || greyScaleEnv || commitOffset function", () => {
            let record = [
            ];
            let stub1 = sinon.stub(serviceObj, "processBatch").resolves();
            let clock = sinon.useFakeTimers();
    
            serviceObj.kafkaSMSParsingConsumer = {
                _pauseConsumer : () => {
                    return;
                },
                commitOffset: (data, cb) => {
                    return cb();
                },
                _resumeConsumer: () => {
    
                }
            };
            serviceObj.greyScaleEnv = true;
            
            let processedRecords = serviceObj.execSteps(record);
            clock.tick(40 * 60 * 1000)
            expect(processedRecords).to.be.equal(undefined);
            expect(stub1).to.have.callCount(0);
            stub1.restore();
        })
        it("execSteps || processBatch function", () => {
            let data = {"abs":"123"}
            let payLoad = {
                value: JSON.stringify(data)
            };
            let record = [
                {...payLoad}
            ];
            let clock = sinon.useFakeTimers();
            let stub1 = sinon.stub(serviceObj, 'processBatch').yields();
            
            serviceObj.kafkaSMSParsingConsumer = {
                _pauseConsumer : () => {
                    return;
                },
                commitOffset: (data, cb) => {
                    return cb();
                },
                _resumeConsumer: () => {
                }
            };
            let processedRecords = serviceObj.execSteps(record);
            clock.tick(40 * 60 * 1000)
            expect(processedRecords).to.be.equal(undefined);
            expect(stub1).to.have.callCount(1);
            stub1.restore();
        })
    
        it("processBatch function | InValid Kafka record received", (done) => {
            let record = { value : '{"isReliable": 1"smsSenderID": "AD-HDFCBK","smsDateTime": **********,"bankName": "SC","dueDate": "2022-11-20","billDate": "2022-10-01","dueAmt": "Rs.590","totalAmt": "Rs.4000","mId": "","cId":"********" }'};
            serviceObj.processBatch(record, function (error){
                return done();
            });    
        });
        it("processBatch function | Valid Kafka record received", (done) => {
            let record = { value : ['{"isReliable": 1,"smsSenderID": "AD-HDFCBK","smsDateTime": **********,"bankName": "SC","dueDate": "2022-11-20","billDate": "2022-10-01","dueAmt": "Rs.590","totalAmt": "Rs.4000","mId": "","cId":"********" }','{"isReliable": 1,"smsSenderID": "AD-HDFCBK","smsDateTime": **********,"bankName": "SC","dueDate": "2022-11-20","billDate": "2022-10-01","dueAmt": "Rs.590","totalAmt": "Rs.4000","mId": "","cId":"********" }']};
            serviceObj.processBatch(record, function (error){
                return done();
            });    
        });
        it("processBatch function | Success case execution",  ()=>{
            let data = {"isReliable": 1,"smsSenderID": "AD-HDFCBK","smsDateTime": **********,"bankName": "SC","dueDate": "2022-11-20","billDate": "2022-10-01","dueAmt": "Rs.590","totalAmt": "Rs.4000","mId": "","cId":"********" }
            let payLoad = {
                value: JSON.stringify(data)
            };
            let records = [
                { ...payLoad },
                { ...payLoad }
            ];
            serviceObj.processBatch(records,(data)=>{
                expect(data).to.be.equal(undefined);    
            });
        });
        
        it("processRecords | Invalid Kafka record received. data key is missing", (done) => {
            let recordData = { value: JSON.stringify({ "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "dueDate": "2022-12-11", "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808",  }) };
            serviceObj.processData(recordData, function (error) {
                expect(error).to.be.equal(undefined);
                return done();
            });
        });
    
        it("processRecords | Invalid Kafka record received | parsing Error", (done) => {
            let recordData = { value: {}  };
            serviceObj.processData(recordData, function (error) {
                expect(error).to.be.equal(undefined);
                return done();
            });
        });
    
        it("processRecords | Valid Kafka record received", (done) => {
            let recordData = {
                value : JSON.stringify({
                    data : [
                        { "smsSenderID": "AD-HDFCBK", "smsDateTime": "*************", "dueDate": "2022-12-11", "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "" , "cId": "667771",  "isReliable" : "1" },
                        { "smsSenderID": "AD-HDFCBK", "smsDateTime": "*************", "dueDate": "2022-12-11", "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "" , "cId": "667771",  "isReliable" : "1" }
                    ]
                })
            }
            serviceObj.processData(recordData, function (error) {
                expect(error).to.be.equal(undefined);
                return done();
            });
        });
    
        it("processRecords | Valid Kafka record received, unable to get matching mcn", (done) => {
            let recordData = {
                value : JSON.stringify({
                    data : [
                        { "smsSenderID": "AD-HDFCBK", "smsDateTime": "*************", "dueDate": MOMENT().add(15,'days').format('YYYY-MM-DD'), "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "" , "cId": "667771",  "isReliable" : "1" }                ]
                })
            }
            let stub1 = sinon.stub(serviceObj, 'getActionforCCBills').callsFake(function fakeFn(done) {
                return done("Multiple matching transactions !!");
            });
            serviceObj.processData(recordData, function (error) {
                expect(error).to.be.equal(undefined);
                expect(stub1).to.have.callCount(1);
    
                stub1.restore();
                return done();
            });
        });
    
        it("processRecords | Valid Kafka record received, no valid action found", (done) => {
            let recordData = {
                value : JSON.stringify({
                    data : [
                        { "smsSenderID": "AD-HDFCBK", "smsDateTime": "*************", "dueDate": MOMENT().add(15,'days').format('YYYY-MM-DD'), "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "" , "cId": "667771",  "isReliable" : "1" }                ]
                })
            }
            let stub1 = sinon.stub(serviceObj, 'getActionforCCBills').callsFake(function fakeFn(done) {
                return done(null, "invalid_action", {}, null);
            });

            let stub2 = sinon.stub(serviceObj.smsParsingLagDashboard, 'publishDelaysMetrics').callsFake(function fakeFn(done) {
                return done(null);
            });
            serviceObj.processData(recordData, function (error) {
                expect(error).to.be.equal(undefined);
                expect(stub1).to.have.callCount(1);
                expect(stub2).to.have.callCount(0);
    
                stub1.restore();
                stub2.restore();
                return done();
            });
        });
    
        it("processRecords | Valid Kafka record received, update action", (done) => {
            let recordData = {"smsSenderID": "AD-HDFCBK", "smsDateTime": "*************", "dueDate":  MOMENT().add(15,'days').format('YYYY-MM-DD'), "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "" , "cId": "667771",  "isReliable" : "1" }                
            let stub1 = sinon.stub(serviceObj, 'getActionforCCBills').callsFake(function fakeFn(done) {
                return done(null, "update", {}, null);
            });
            let stub2 = sinon.stub(serviceObj, 'updateCCBillAndSendNotification').callsFake(function fakeFn(done) {
                return done(null);
            });
            let stub3 = sinon.stub(serviceObj, 'shouldUpdateCCBillInSystem').returns([true,true]);
            let stub4 = sinon.stub(serviceObj, 'getDbRecordToUpdate').returns({});
            let stub5 = sinon.stub(serviceObj, 'publishCtAndPFCCEvents').callsFake(function fakeFn(done) {
                return done(null);
            });
            let stub6 = sinon.stub(serviceObj.smsParsingLagDashboard, 'publishDelaysMetrics').callsFake(function fakeFn(done) {
                return done(null);
            });
    
            serviceObj.processRecords(function (error) {
                expect(error).to.be.equal(null);
                expect(stub1).to.have.callCount(1);
                expect(stub2).to.have.callCount(1);
                expect(stub3).to.have.callCount(1);
                expect(stub4).to.have.callCount(1);
                expect(stub5).to.have.callCount(1);
                expect(stub6).to.have.callCount(1);
    
                stub1.restore();
                stub2.restore();
                stub3.restore();
                stub4.restore();
                stub5.restore();
                stub6.restore();
                return done();
            },recordData);
        });
    
        it("processRecords | Valid Kafka record received, update action", (done) => {
            let recordData = {"smsSenderID": "AD-HDFCBK", "smsDateTime": "*************", "dueDate":  MOMENT().add(15,'days').format('YYYY-MM-DD'), "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "" , "cId": "667771",  "isReliable" : "1" }                
            let stub1 = sinon.stub(serviceObj, 'getActionforCCBills').callsFake(function fakeFn(done) {
                return done(null, "update", {}, null);
            });
            let stub2 = sinon.stub(serviceObj, 'updateCCBillAndSendNotification').callsFake(function fakeFn(done) {
                return done("Error occured");
            });
            let stub3 = sinon.stub(serviceObj, 'shouldUpdateCCBillInSystem').returns([true,true]);
            let stub4 = sinon.stub(serviceObj, 'getDbRecordToUpdate').returns({});
            let stub5 = sinon.stub(serviceObj, 'publishCtAndPFCCEvents').callsFake(function fakeFn(done) {
                return done(null);
            });
    
            serviceObj.processRecords(function (error) {
                expect(error).to.be.equal("Error occured");
                expect(stub1).to.have.callCount(1);
                expect(stub2).to.have.callCount(1);
                expect(stub3).to.have.callCount(0);
                expect(stub4).to.have.callCount(0);
                expect(stub5).to.have.callCount(0);
    
                stub1.restore();
                stub2.restore();
                stub3.restore();
                stub4.restore();
                stub5.restore();
                return done();
            },recordData);
        });
    
        it("processRecords | Valid Kafka record received, create action", (done) => {
            let recordData = {"smsSenderID": "AD-HDFCBK", "smsDateTime": "*************", "dueDate":  MOMENT().add(15,'days').format('YYYY-MM-DD'), "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "" , "cId": "667771",  "isReliable" : "1" }                
            let stub1 = sinon.stub(serviceObj, 'getActionforCCBills').callsFake(function fakeFn(done) {
                return done(null, "create", {}, null);
            });
            let stub2 = sinon.stub(serviceObj, 'updateCCBillAndSendNotification').callsFake(function fakeFn(done) {
                return done(null);
            });
            let stub6 = sinon.stub(serviceObj, 'createCCBillAndSendNotification').callsFake(function fakeFn(done) {
                return done(null);
            });
            let stub3 = sinon.stub(serviceObj, 'shouldUpdateCCBillInSystem').returns([true,true]);
            let stub4 = sinon.stub(serviceObj, 'getDbRecordToUpdate').returns({});
            let stub5 = sinon.stub(serviceObj, 'publishCtAndPFCCEvents').callsFake(function fakeFn(done) {
                return done(null);
            });
            let stub7 = sinon.stub(serviceObj.smsParsingLagDashboard, 'publishDelaysMetrics').callsFake(function fakeFn(done) {
                return done(null);
            });

    
            serviceObj.processRecords(function (error) {
                expect(error).to.be.equal(null);
                expect(stub1).to.have.callCount(1);
                expect(stub2).to.have.callCount(0);
                expect(stub6).to.have.callCount(1);
                expect(stub3).to.have.callCount(1);
                expect(stub4).to.have.callCount(0);
                expect(stub5).to.have.callCount(1);
                expect(stub7).to.have.callCount(1);

                stub1.restore();
                stub2.restore();
                stub6.restore();
                stub3.restore();
                stub4.restore();
                stub5.restore();
                stub7.restore();
                return done();
            },recordData);
        });
        it("processRecords | Valid Kafka record received, create action", (done) => {
            let recordData = {"smsSenderID": "AD-HDFCBK", "smsDateTime": "*************", "dueDate":  MOMENT().add(15,'days').format('YYYY-MM-DD'), "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "" , "cId": "667771",  "isReliable" : "1" }                
            let stub1 = sinon.stub(serviceObj, 'getActionforCCBills').callsFake(function fakeFn(done) {
                return done(null, "update", {}, null);
            });
            let stub2 = sinon.stub(serviceObj, 'updateCCBillAndSendNotification').callsFake(function fakeFn(done) {
                return done(null);
            });
            let stub6 = sinon.stub(serviceObj, 'createCCBillAndSendNotification').callsFake(function fakeFn(done) {
                return done(null);
            });
            let stub3 = sinon.stub(serviceObj, 'shouldUpdateCCBillInSystem').returns([false,false]);
            let stub4 = sinon.stub(serviceObj, 'getDbRecordToUpdate').returns({});
            let stub5 = sinon.stub(serviceObj, 'publishCtAndPFCCEvents').callsFake(function fakeFn(done) {
                return done(null);
            });
            let stub7 = sinon.stub(serviceObj.smsParsingLagDashboard, 'publishDelaysMetrics').callsFake(function fakeFn(done) {
                return done(null);
            });
    
            serviceObj.processRecords(function (error) {
                expect(error).to.be.equal(null);
                expect(stub1).to.have.callCount(1);
                expect(stub2).to.have.callCount(1);
                expect(stub6).to.have.callCount(0);
                expect(stub3).to.have.callCount(1);
                expect(stub4).to.have.callCount(1);
                expect(stub5).to.have.callCount(0);
                expect(stub7).to.have.callCount(1);
    
                stub1.restore();
                stub2.restore();
                stub6.restore();
                stub3.restore();
                stub4.restore();
                stub5.restore();
                stub7.restore();
                return done();
            },recordData);
        });
    
        it("processRecords | Valid Kafka record received, create action, publishCtAndPFCCEvents error", (done) => {
            let recordData = {"smsSenderID": "AD-HDFCBK", "smsDateTime": "*************", "dueDate":  MOMENT().add(15,'days').format('YYYY-MM-DD'), "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "" , "cId": "667771",  "isReliable" : "1" }                
            let stub1 = sinon.stub(serviceObj, 'getActionforCCBills').callsFake(function fakeFn(done) {
                return done(null, "create", {}, null);
            });
            let stub2 = sinon.stub(serviceObj, 'updateCCBillAndSendNotification').callsFake(function fakeFn(done) {
                return done(null);
            });
            let stub6 = sinon.stub(serviceObj, 'createCCBillAndSendNotification').callsFake(function fakeFn(done) {
                return done(null);
            });
            let stub3 = sinon.stub(serviceObj, 'shouldUpdateCCBillInSystem').returns([true,true]);
            let stub4 = sinon.stub(serviceObj, 'getDbRecordToUpdate').returns({});
            let stub5 = sinon.stub(serviceObj, 'publishCtAndPFCCEvents').callsFake(function fakeFn(done) {
                return done("could not publish");
            });
            let stub7 = sinon.stub(serviceObj.smsParsingLagDashboard, 'publishDelaysMetrics').callsFake(function fakeFn(done) {
                return done(null);
            });
    
            serviceObj.processRecords(function (error) {
                expect(error).to.be.equal("could not publish");
                expect(stub1).to.have.callCount(1);
                expect(stub2).to.have.callCount(0);
                expect(stub6).to.have.callCount(1);
                expect(stub3).to.have.callCount(1);
                expect(stub4).to.have.callCount(0);
                expect(stub5).to.have.callCount(1);
                expect(stub7).to.have.callCount(0);
    
                stub1.restore();
                stub2.restore();
                stub6.restore();
                stub3.restore();
                stub4.restore();
                stub5.restore();
                stub7.restore();
                return done();
            },recordData);
        });
        it("validateAndInsertAnalyticsRecordInDB | valid data passed ", (done) => {
            let record = {
                category: null,
                rtspId: null,
                refId: uuidv1(),
                isUpsert: false,
                notificationCreationTime: null,
                notificationPublishTime: null,
                smsParsingEntryTime: MOMENT().format('YYYY-MM-DD HH:mm:ss'),
                smsRcvdTime: MOMENT().format('YYYY-MM-DD HH:mm:ss'),
                customerOtherInfo: record,
                type: "_dwh"
            }
            serviceObj.validateAndInsertAnalyticsRecordInDB("error", record).then((data) => {
                return done()

            })
        })
        it("validateAndInsertAnalyticsRecordInDB | invalid data passed ", (done) => {
            let record = {
                category: null,
                rtspId: null,
                refId: uuidv1(),
                isUpsert: false,
                notificationCreationTime: null,
                notificationPublishTime: null,
                smsParsingEntryTime: MOMENT().format('YYYY-MM-DD HH:mm:ss'),
                customerOtherInfo: record,
                type: "_dwh"
            }
            serviceObj.validateAndInsertAnalyticsRecordInDB("error", record)
            .then((data) => {
                return done()

            })
        })

        it("validateAndProcessRecord | valid data passed ", (done) => {
            let recordData = { "smsSenderID": "AD-HDFCBK", "smsDateTime": "*************", "dueDate":  MOMENT().add(15,'days').format('YYYY-MM-DD'), "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "" , "cId":"123456",  "isReliable" : "1", "category": "cc", "rtspId":"1111" };
            let analyticsPayload = serviceObj.initializeAnalyticsPayload();
            serviceObj.validateAndProcessRecord(recordData, (data) => {
                expect(_.get(analyticsPayload, "customerId")).to.be.equal(123456);
                expect(_.get(analyticsPayload, "category")).to.be.equal('cc');
                expect(_.get(analyticsPayload, "rtspId")).to.be.equal("1111");
                return done()

            }, analyticsPayload)
        })

        it("validateAndProcessRecord | invalid data passed ", (done) => {
            let recordData = { "smsSenderID": "AD-HDFCBK", "smsDateTime": "*************", "dueDate":  MOMENT().add(15,'days').format('YYYY-MM-DD'), "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "" , "cId":"123456",  "isReliable" : "1"};
            let analyticsPayload = serviceObj.initializeAnalyticsPayload();
            serviceObj.validateAndProcessRecord(recordData, (data) => {
                expect(_.get(analyticsPayload, "customerId")).to.be.equal(123456);
                expect(_.get(analyticsPayload, "category")).to.be.equal(null);
                expect(_.get(analyticsPayload, "rtspId")).to.be.equal(-1);
                return done()

            }, analyticsPayload)
        })
    
        it.skip("processRecords | customerId not passed", (done) => {
            let recordData = { "smsSenderID": "AD-HDFCBK", "smsDateTime": "*************", "dueDate": "2022-12-11", "billDate":  MOMENT().add(15,'days').format('YYYY-MM-DD'), "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "" ,  "isReliable" : "1" };
            serviceObj.processRecords(function (error) {
                expect(error).to.be.equal("Mandatory Params customerId is Missing / Invalid");
                return done();
            }, recordData);
        });
        it.skip("processRecords | valid record | unexpected error" , (done) => {
            let recordData = { "smsSenderID": "AD-HDFCBK", "smsDateTime": "*************", "dueDate":  MOMENT().add(15,'days').format('YYYY-MM-DD'), "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "" , "cId":"123456",  "isReliable" : "1" };
            let record = {}
            let dbRecord={}
            let recordToUpdate={}
            let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').callsFake(function(cb) {
                return cb(null,record);
            });
            let stub2 = sinon.stub(serviceObj, 'getActionforCCBills').callsFake(function fakeFn(done) {
                return done(null, 'update', dbRecord, null);
            });
            let stub3 = sinon.stub(serviceObj, 'updateCCBillAndSendNotification').callsFake(function fakeFn(done) {
                return done(null);
            });
            let stub4 = sinon.stub(serviceObj, 'createCCBillAndSendNotification').callsFake(function fakeFn(done) {
                return done(null);
            });
            let stub5 = sinon.stub(serviceObj, 'shouldUpdateCCBillInSystem').returns([false,false]);
            let stub6 = sinon.stub(serviceObj, 'getDbRecordToUpdate').returns(recordToUpdate);
            let stub7 = sinon.stub(serviceObj, 'publishCtAndPFCCEvents').callsFake(function fakeFn(done) {
                return done(null);
            });
            let stub8 = sinon.stub(serviceObj.smsParsingLagDashboard, 'publishDelaysMetrics').callsFake(function fakeFn(done) {
                return done(null);
            });
    
            
    
            serviceObj.processRecords(function (error) {
                expect(`${error}`).to.be.equal("TypeError: cb is not a function");
            }, recordData);
    
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(0);
            expect(stub3).to.have.callCount(0);
            expect(stub4).to.have.callCount(0);
            expect(stub5).to.have.callCount(0);
            expect(stub6).to.have.callCount(0);
            expect(stub7).to.have.callCount(0);
            expect(stub8).to.have.callCount(0);
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            stub7.restore();
            stub8.restore();
            done();
        });
    
        it("processRecords | valid record | action update, updateinDB false" , (done) => {
            let recordData = { "smsSenderID": "AD-HDFCBK", "smsDateTime": "*************", "dueDate":  MOMENT().add(15,'days').format('YYYY-MM-DD'), "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "" , "cId":"123456",  "isReliable" : "1" };
            let record = {}
            let dbRecord={}
            let recordToUpdate={}
            let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').callsFake(function(recordData,cb) {
                return cb(null,record);
            });
            let stub2 = sinon.stub(serviceObj, 'getActionforCCBills').callsFake(function fakeFn(done) {
                return done(null, 'update', dbRecord, null);
            });
            let stub3 = sinon.stub(serviceObj, 'updateCCBillAndSendNotification').callsFake(function fakeFn(done) {
                return done(null);
            });
            let stub4 = sinon.stub(serviceObj, 'createCCBillAndSendNotification').callsFake(function fakeFn(done) {
                return done(null);
            });
            let stub5 = sinon.stub(serviceObj, 'shouldUpdateCCBillInSystem').returns([false,false]);
            let stub6 = sinon.stub(serviceObj, 'getDbRecordToUpdate').returns(recordToUpdate);
            let stub7 = sinon.stub(serviceObj, 'publishCtAndPFCCEvents').callsFake(function fakeFn(done) {
                return done(null);
            });
            let stub8 = sinon.stub(serviceObj.smsParsingLagDashboard, 'publishDelaysMetrics').callsFake(function fakeFn(done) {
                return done(null);
            });
    
            
    
            serviceObj.processRecords(function (error) {
                expect(error).to.be.equal(null);
            }, recordData);
    
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(1);
            expect(stub4).to.have.callCount(0);
            expect(stub5).to.have.callCount(1);
            expect(stub6).to.have.callCount(1);
            expect(stub7).to.have.callCount(0);
            expect(stub8).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            stub7.restore();
            stub8.restore();
            done();
        });
    
        it("processRecords | valid record | error in ct publish" , (done) => {
            let recordData = { "smsSenderID": "AD-HDFCBK", "smsDateTime": "*************", "dueDate":  MOMENT().add(15,'days').format('YYYY-MM-DD'), "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "" , "cId":"123456",  "isReliable" : "1" };
            let record = {}
            let dbRecord={}
            let recordToUpdate={}
            let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').callsFake(function(recordData,cb) {
                return cb(null,record);
            });
            let stub2 = sinon.stub(serviceObj, 'getActionforCCBills').callsFake(function fakeFn(done) {
                return done(null, 'update', dbRecord, null);
            });
            let stub3 = sinon.stub(serviceObj, 'updateCCBillAndSendNotification').callsFake(function fakeFn(done) {
                return done(null);
            });
            let stub4 = sinon.stub(serviceObj, 'createCCBillAndSendNotification').callsFake(function fakeFn(done) {
                return done(null);
            });
            let stub5 = sinon.stub(serviceObj, 'shouldUpdateCCBillInSystem').returns([true,true]);
            let stub6 = sinon.stub(serviceObj, 'getDbRecordToUpdate').returns(recordToUpdate);
            let stub7 = sinon.stub(serviceObj, 'publishCtAndPFCCEvents').callsFake(function fakeFn(done) {
                return done("Error while publishing");
            });
            let stub8 = sinon.stub(serviceObj.smsParsingLagDashboard, 'publishDelaysMetrics').callsFake(function fakeFn(done) {
                return done(null);
            });
    
            
    
            serviceObj.processRecords(function (error) {
                expect(error).to.be.equal("Error while publishing");
            }, recordData);
    
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(1);
            expect(stub4).to.have.callCount(0);
            expect(stub5).to.have.callCount(1);
            expect(stub6).to.have.callCount(1);
            expect(stub7).to.have.callCount(1);
            expect(stub8).to.have.callCount(0);
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            stub7.restore();
            stub8.restore();
            done();
        });
    
        it("processRecords | valid record | invalid action" , (done) => {
            let recordData = { "smsSenderID": "AD-HDFCBK", "smsDateTime": "*************", "dueDate":  MOMENT().add(15,'days').format('YYYY-MM-DD'), "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "" , "cId":"123456",  "isReliable" : "1" };
            let record = {}
            let dbRecord={}
            let recordToUpdate={}
            let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').callsFake(function(recordData,cb) {
                return cb(null,record);
            });
            let stub2 = sinon.stub(serviceObj, 'getActionforCCBills').callsFake(function fakeFn(done) {
                return done(null, 'none', dbRecord, null);
            });
            let stub3 = sinon.stub(serviceObj, 'updateCCBillAndSendNotification').callsFake(function fakeFn(done) {
                return done(null);
            });
            let stub4 = sinon.stub(serviceObj, 'createCCBillAndSendNotification').callsFake(function fakeFn(done) {
                return done(null);
            });
            let stub5 = sinon.stub(serviceObj, 'shouldUpdateCCBillInSystem').returns([true,true]);
            let stub6 = sinon.stub(serviceObj, 'getDbRecordToUpdate').returns(recordToUpdate);
            let stub7 = sinon.stub(serviceObj, 'publishCtAndPFCCEvents').callsFake(function fakeFn(done) {
                return done(null);
            });
            let stub8 = sinon.stub(serviceObj.smsParsingLagDashboard, 'publishDelaysMetrics').callsFake(function fakeFn(done) {
                return done(null);
            });
    
            
    
            serviceObj.processRecords(function (error) {
                expect(error).to.be.equal('No action found for record');
            }, recordData);
    
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(0);
            expect(stub4).to.have.callCount(0);
            expect(stub5).to.have.callCount(0);
            expect(stub6).to.have.callCount(0);
            expect(stub7).to.have.callCount(0);
            expect(stub8).to.have.callCount(0);
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            stub7.restore();
            stub8.restore();
            done();
        });
    
        it("processRecords | valid record | error in createAndSendNotification" , (done) => {
            let recordData = { "smsSenderID": "AD-HDFCBK", "smsDateTime": "*************", "dueDate":  MOMENT().add(15,'days').format('YYYY-MM-DD'), "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "" , "cId":"123456",  "isReliable" : "1" };
            let record = {}
            let dbRecord={}
            let recordToUpdate={}
            let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').callsFake(function(recordData,cb) {
                return cb(null,record);
            });
            let stub2 = sinon.stub(serviceObj, 'getActionforCCBills').callsFake(function fakeFn(done) {
                return done(null, 'create', dbRecord, null);
            });
            let stub3 = sinon.stub(serviceObj, 'updateCCBillAndSendNotification').callsFake(function fakeFn(done) {
                return done(null);
            });
            let stub4 = sinon.stub(serviceObj, 'createCCBillAndSendNotification').callsFake(function fakeFn(done) {
                return done("Error in creating");
            });
            let stub5 = sinon.stub(serviceObj, 'shouldUpdateCCBillInSystem').returns([true,true]);
            let stub6 = sinon.stub(serviceObj, 'getDbRecordToUpdate').returns(recordToUpdate);
            let stub7 = sinon.stub(serviceObj, 'publishCtAndPFCCEvents').callsFake(function fakeFn(done) {
                return done(null);
            });
            let stub8 = sinon.stub(serviceObj.smsParsingLagDashboard, 'publishDelaysMetrics').callsFake(function fakeFn(done) {
                return done(null);
            });
    
            
    
            serviceObj.processRecords(function (error) {
                expect(error).to.be.equal("Error in creating");
            }, recordData);
    
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(0);
            expect(stub4).to.have.callCount(1);
            expect(stub5).to.have.callCount(0);
            expect(stub6).to.have.callCount(0);
            expect(stub7).to.have.callCount(0);
            expect(stub8).to.have.callCount(0);
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            stub7.restore();
            stub8.restore();
            done();
        });
    
        it("processRecords | valid record | error in updateAndSendNotification" , (done) => {
            let recordData = { "smsSenderID": "AD-HDFCBK", "smsDateTime": "*************", "dueDate":  MOMENT().add(15,'days').format('YYYY-MM-DD'), "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "" , "cId":"123456",  "isReliable" : "1" };
            let record = {}
            let dbRecord={}
            let recordToUpdate={}
            let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').callsFake(function(recordData,cb) {
                return cb(null,record);
            });
            let stub2 = sinon.stub(serviceObj, 'getActionforCCBills').callsFake(function fakeFn(done) {
                return done(null, 'update', dbRecord, null);
            });
            let stub3 = sinon.stub(serviceObj, 'updateCCBillAndSendNotification').callsFake(function fakeFn(done) {
                return done("Error in updating");
            });
            let stub4 = sinon.stub(serviceObj, 'createCCBillAndSendNotification').callsFake(function fakeFn(done) {
                return done(null);
            });
            let stub5 = sinon.stub(serviceObj, 'shouldUpdateCCBillInSystem').returns([true,true]);
            let stub6 = sinon.stub(serviceObj, 'getDbRecordToUpdate').returns(recordToUpdate);
            let stub7 = sinon.stub(serviceObj, 'publishCtAndPFCCEvents').callsFake(function fakeFn(done) {
                return done(null);
            });
            let stub8 = sinon.stub(serviceObj.smsParsingLagDashboard, 'publishDelaysMetrics').callsFake(function fakeFn(done) {
                return done(null);
            });
    
            
    
            serviceObj.processRecords(function (error) {
                expect(error).to.be.equal("Error in updating");
            }, recordData);
    
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(1);
            expect(stub4).to.have.callCount(0);
            expect(stub5).to.have.callCount(0);
            expect(stub6).to.have.callCount(0);
            expect(stub7).to.have.callCount(0);
            expect(stub8).to.have.callCount(0);
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            stub7.restore();
            stub8.restore();
            done();
        });
    
        it("processRecords | valid record | error in getAction" , (done) => {
            let recordData = { "smsSenderID": "AD-HDFCBK", "smsDateTime": "*************", "dueDate":  MOMENT().add(15,'days').format('YYYY-MM-DD'), "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "" , "cId":"123456",  "isReliable" : "1" };
            let record = {}
            let dbRecord={}
            let recordToUpdate={}
            let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').callsFake(function(recordData,cb) {
                return cb(null,record);
            });
            let stub2 = sinon.stub(serviceObj, 'getActionforCCBills').callsFake(function fakeFn(done) {
                return done("Multiple matching transactions !!");
            });
            let stub3 = sinon.stub(serviceObj, 'updateCCBillAndSendNotification').callsFake(function fakeFn(done) {
                return done(null);
            });
            let stub4 = sinon.stub(serviceObj, 'createCCBillAndSendNotification').callsFake(function fakeFn(done) {
                return done(null);
            });
            let stub5 = sinon.stub(serviceObj, 'shouldUpdateCCBillInSystem').returns([true,true]);
            let stub6 = sinon.stub(serviceObj, 'getDbRecordToUpdate').returns(recordToUpdate);
            let stub7 = sinon.stub(serviceObj, 'publishCtAndPFCCEvents').callsFake(function fakeFn(done) {
                return done(null);
            });
            let stub8 = sinon.stub(serviceObj.smsParsingLagDashboard, 'publishDelaysMetrics').callsFake(function fakeFn(done) {
                return done(null);
            });
    
            
    
            serviceObj.processRecords(function (error) {
                expect(error).to.be.equal("Multiple matching transactions !!");
            }, recordData);
    
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(0);
            expect(stub4).to.have.callCount(0);
            expect(stub5).to.have.callCount(0);
            expect(stub6).to.have.callCount(0);
            expect(stub7).to.have.callCount(0);
            expect(stub8).to.have.callCount(0);
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            stub7.restore();
            stub8.restore();
            done();
        });
    
        it("processRecords | valid record | update action" , (done) => {
            let recordData = { "smsSenderID": "AD-HDFCBK", "smsDateTime": "*************", "dueDate":  MOMENT().add(15,'days').format('YYYY-MM-DD'), "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "" , "cId":"123456",  "isReliable" : "1" };
            let record = {}
            let dbRecord={}
            let recordToUpdate={}
            let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').callsFake(function(recordData,cb) {
                return cb(null,record);
            });
            let stub2 = sinon.stub(serviceObj, 'getActionforCCBills').callsFake(function fakeFn(done) {
                return done(null, 'update', dbRecord, null);
            });
            let stub3 = sinon.stub(serviceObj, 'updateCCBillAndSendNotification').callsFake(function fakeFn(done) {
                return done(null);
            });
            let stub4 = sinon.stub(serviceObj, 'createCCBillAndSendNotification').callsFake(function fakeFn(done) {
                return done(null);
            });
            let stub5 = sinon.stub(serviceObj, 'shouldUpdateCCBillInSystem').returns([true,true]);
            let stub6 = sinon.stub(serviceObj, 'getDbRecordToUpdate').returns(recordToUpdate);
            let stub7 = sinon.stub(serviceObj, 'publishCtAndPFCCEvents').callsFake(function fakeFn(done) {
                return done(null);
            });
            let stub8 = sinon.stub(serviceObj.smsParsingLagDashboard, 'publishDelaysMetrics').callsFake(function fakeFn(done) {
                return done(null);
            });
    
            
    
            serviceObj.processRecords(function (error) {
                expect(error).to.be.equal(null);
            }, recordData);
    
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(1);
            expect(stub4).to.have.callCount(0);
            expect(stub5).to.have.callCount(1);
            expect(stub6).to.have.callCount(1);
            expect(stub7).to.have.callCount(1);
            expect(stub8).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            stub7.restore();
            stub8.restore();
            done();
        });
    
        it("processRecords | valid record | create action" , (done) => {
            let recordData = { "smsSenderID": "AD-HDFCBK", "smsDateTime": "*************", "dueDate":  MOMENT().add(15,'days').format('YYYY-MM-DD'), "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "" , "cId":"123456",  "isReliable" : "1" };
            let record = {}
            let dbRecord={}
            let recordToUpdate={}
            let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').callsFake(function(recordData,cb) {
                return cb(null,record);
            });
            let stub2 = sinon.stub(serviceObj, 'getActionforCCBills').callsFake(function fakeFn(done) {
                return done(null, 'create', dbRecord, null);
            });
            let stub3 = sinon.stub(serviceObj, 'updateCCBillAndSendNotification').callsFake(function fakeFn(done) {
                return done(null);
            });
            let stub4 = sinon.stub(serviceObj, 'createCCBillAndSendNotification').callsFake(function fakeFn(done) {
                return done(null);
            });
            let stub5 = sinon.stub(serviceObj, 'shouldUpdateCCBillInSystem').returns([true,true]);
            let stub6 = sinon.stub(serviceObj, 'getDbRecordToUpdate').returns(recordToUpdate);
            let stub7 = sinon.stub(serviceObj, 'publishCtAndPFCCEvents').callsFake(function fakeFn(done) {
                return done(null);
            });
            let stub8 = sinon.stub(serviceObj.smsParsingLagDashboard, 'publishDelaysMetrics').callsFake(function fakeFn(done) {
                return done(null);
            });
    
            
    
            serviceObj.processRecords(function (error) {
                expect(error).to.be.equal(null);
            }, recordData);
    
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(0);
            expect(stub4).to.have.callCount(1);
            expect(stub5).to.have.callCount(1);
            expect(stub6).to.have.callCount(0);
            expect(stub7).to.have.callCount(1);
            expect(stub8).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            stub7.restore();
            stub8.restore();
            done();
        });
    
        // it("processRecords | Valid record | Entry exists in DB", (done) => {
        //     let recordData = { "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "dueDate": "2022-12-11", "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "8767", "mId": "", "cId": "667771", "isReliable" : "1"  };
        //     let serviceObj1 = _.cloneDeep(serviceObj);
        //     serviceObj1.kafkaBillFetchPublisher =  {
        //         publishData : (kafkaDetails, cb , arr)=>{
        //             return cb();
        //         }
        //     };
        //     serviceObj1.processRecords(function (error) {
        //         expect(error).to.be.equal(null);
        //         return done();
        //     }, recordData);
        // });
        
        it("updateCCBillAndSendNotification | action udpate successfull execution", (done) => {
            let processedRecord = { "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "dueDate":  MOMENT().add(15,'days').format('YYYY-MM-DD'), "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "8767", "mId": "", "cId": "667771", "isReliable" : "1"  };
            let dbRecord = {}
            let serviceObj1 = _.cloneDeep(serviceObj);
            
            let stub2 = sinon.stub(serviceObj1, 'shouldUpdateCCBillInSystem').returns([true,true]);
            let stub3 = sinon.stub(serviceObj1, 'getDbRecordToUpdate').returns({});
            let stub4 = sinon.stub(serviceObj1, 'updateCCBillInSystem').callsFake(function fakeFn(done) {
                return done(null);
            });
            let stub5 = sinon.stub(serviceObj1, 'sendNotification').callsFake(function fakeFn(done) {
                return done(null, {});
            });
            serviceObj1.updateCCBillAndSendNotification(function (error) {
                expect(error).to.not.be.equal(null);
                expect(stub2).to.have.callCount(1);
                expect(stub3).to.have.callCount(1);
                expect(stub4).to.have.callCount(1);
                // expect(stub5).to.have.callCount(0);
    
                stub2.restore();
                stub3.restore();
                stub4.restore();
                stub5.restore();
    
                return done();
            }, processedRecord, dbRecord, 'update');
        });
        it("updateCCBillAndSendNotification | action udpate_skipNotify successfull execution", (done) => {
            let processedRecord = { "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "dueDate":  MOMENT().add(15,'days').format('YYYY-MM-DD'), "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "8767", "mId": "", "cId": "667771", "isReliable" : "1"  };
            let dbRecord = {}
            let serviceObj1 = _.cloneDeep(serviceObj);
        
            let stub2 = sinon.stub(serviceObj1, 'shouldUpdateCCBillInSystem').returns([true,true]);
            let stub3 = sinon.stub(serviceObj1, 'getDbRecordToUpdate').returns({});
            let stub4 = sinon.stub(serviceObj1, 'updateCCBillInSystem').callsFake(function fakeFn(done) {
                return done(null);
            });
            let stub5 = sinon.stub(serviceObj1, 'sendNotification').callsFake(function fakeFn(done) {
                return done(null, {});
            });
            serviceObj1.updateCCBillAndSendNotification(function (error) {
                expect(error).to.be.equal(null);
                expect(stub2).to.have.callCount(1);
                expect(stub3).to.have.callCount(1);
                expect(stub4).to.have.callCount(1);
                expect(stub5).to.have.callCount(0);
    
                stub2.restore();
                stub3.restore();
                stub4.restore();
                stub5.restore();
    
                return done();
            }, processedRecord, dbRecord, 'update_skipNotify');
        });
        // it("updateCCBillAndSendNotification | action udpate, updateInDb=false successfull execution", (done) => {
        //     let processedRecord = { "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "dueDate":  MOMENT().add(15,'days').format('YYYY-MM-DD'), "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "8767", "mId": "", "cId": "667771", "isReliable" : "1"  };
        //     let dbRecord = {}
        //     let serviceObj1 = _.cloneDeep(serviceObj);
            
        //     let stub1 = sinon.stub(serviceObj1, 'checkUniquenessInRecents').callsFake(function fakeFn(done) {
        //         return done(null);
        //     });
        //     let stub2 = sinon.stub(serviceObj1, 'shouldUpdateCCBillInSystem').returns([false,true]);
        //     let stub3 = sinon.stub(serviceObj1, 'getDbRecordToUpdate').returns({});
        //     let stub4 = sinon.stub(serviceObj1, 'updateCCBillInSystem').callsFake(function fakeFn(done) {
        //         return done(null);
        //     });
        //     let stub5 = sinon.stub(serviceObj1, 'sendNotification').callsFake(function fakeFn(done) {
        //         return done(null, {});
        //     });
        //     serviceObj1.updateCCBillAndSendNotification(function (error) {
        //         expect(error).to.not.be.equal(null);
        //         expect(stub1).to.have.callCount(1);
        //         expect(stub2).to.have.callCount(1);
        //         expect(stub3).to.have.callCount(0);
        //         expect(stub4).to.have.callCount(0);
        //         // expect(stub5).to.have.callCount(0);
    
        //         stub1.restore();
        //         stub2.restore();
        //         stub3.restore();
        //         stub4.restore();
        //         stub5.restore();
    
        //         return done();
        //     }, processedRecord, dbRecord, 'update');
        // });
    
        it("createCCBillInSystem | recentLayerRecord null", (done) => {
            let processedRecord = { "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "dueDate":  MOMENT().add(15,'days').format('YYYY-MM-DD'), "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "8767", "mId": "", "cId": "667771", "isReliable" : "1"  };
            let dbRecord = {}
            let recentsLayerRecords = null;
            let serviceObj1 = _.cloneDeep(serviceObj);
            
            let stub1 = sinon.stub(serviceObj1.bills, 'createCCBillForCustomerId').callsFake(function(cb,dbRecord, processedRecord) {
                return cb(null, {});
            });
            serviceObj1.createCCBillInSystem(function (error) {
                expect(error).to.be.equal(null);
                expect(stub1).to.have.callCount(1);
    
                stub1.restore();
    
                return done();
            }, processedRecord, dbRecord, recentsLayerRecords);
        });
        it("createCCBillInSystem | error in createCCBillForCustomerId", (done) => {
            let processedRecord = { "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "dueDate":  MOMENT().add(15,'days').format('YYYY-MM-DD'), "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "8767", "mId": "", "cId": "667771", "isReliable" : "1"  };
            let dbRecord = {}
            let recentsLayerRecords = {}
            let serviceObj1 = _.cloneDeep(serviceObj);
            
            let stub1 = sinon.stub(serviceObj1.bills, 'createCCBillForCustomerId').callsFake(function(cb,dbRecord, processedRecord) {
                return cb("Error in createCCBillForCustomerId");
            });
            serviceObj1.createCCBillInSystem(function (error) {
                expect(error).to.be.equal("Error in createCCBillForCustomerId");
                expect(stub1).to.have.callCount(1);
    
                stub1.restore();
    
                return done();
            }, processedRecord, dbRecord, recentsLayerRecords);
        });
        it("createCCBillInSystem | successfull execution", (done) => {
            let processedRecord = { "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "dueDate":  MOMENT().add(15,'days').format('YYYY-MM-DD'), "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "8767", "mId": "", "cId": "667771", "isReliable" : "1"  };
            let dbRecord = {}
            let recentsLayerRecords = {}
            let serviceObj1 = _.cloneDeep(serviceObj);
            
            let stub1 = sinon.stub(serviceObj1.bills, 'createCCBillForCustomerId').callsFake(function(cb,dbRecord, processedRecord) {
                return cb(null, {});
            });
            serviceObj1.createCCBillInSystem(function (error) {
                expect(error).to.be.equal(null);
                expect(stub1).to.have.callCount(1);
    
                stub1.restore();
    
                return done();
            }, processedRecord, dbRecord, recentsLayerRecords);
        });
    
        it("updateCCBillInSystem | recentRecord null", (done) => {
            let processedRecord = { "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "dueDate":  MOMENT().add(15,'days').format('YYYY-MM-DD'), "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "8767", "mId": "", "cId": "667771", "isReliable" : "1"  };
            let dbRecord = {}
            let recentsLayerRecords = null
            let newBillCycle = true;
            let serviceObj1 = _.cloneDeep(serviceObj);
            
            let stub1 = sinon.stub(serviceObj1.bills, 'updateCCBillByCustomerId').callsFake(function(cb,dbRecord, processedRecord) {
                return cb("Error in updateCCBillByCustomerId");
            });
            serviceObj1.updateCCBillInSystem(function (error) {
                expect(error).to.be.equal(null);
                expect(stub1).to.have.callCount(1);
    
                stub1.restore();
    
                return done();
            }, processedRecord, dbRecord, newBillCycle, recentsLayerRecords);
        });
        it("updateCCBillInSystem | error in updateCCBillByCustomerId", (done) => {
            let processedRecord = { "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "dueDate":  MOMENT().add(15,'days').format('YYYY-MM-DD'), "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "8767", "mId": "", "cId": "667771", "isReliable" : "1"  };
            let dbRecord = {}
            let recentsLayerRecords = {}
            let newBillCycle = true;
            let serviceObj1 = _.cloneDeep(serviceObj);
            
            let stub1 = sinon.stub(serviceObj1.bills, 'updateCCBillByCustomerId').callsFake(function(cb,dbRecord, processedRecord) {
                return cb("Error in updateCCBillByCustomerId");
            });
            serviceObj1.updateCCBillInSystem(function (error) {
                expect(error).to.be.equal(null);
                expect(stub1).to.have.callCount(1);
    
                stub1.restore();
    
                return done();
            }, processedRecord, dbRecord, newBillCycle, recentsLayerRecords);
        });
        it("updateCCBillInSystem | successfull execution", (done) => {
            let processedRecord = { "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "dueDate":  MOMENT().add(15,'days').format('YYYY-MM-DD'), "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "8767", "mId": "", "cId": "667771", "isReliable" : "1"  };
            let dbRecord = {}
            let recentsLayerRecords = {}
            let newBillCycle = true;
            let serviceObj1 = _.cloneDeep(serviceObj);
            
            let stub1 = sinon.stub(serviceObj1.bills, 'updateCCBillByCustomerId').callsFake(function(cb,dbRecord, processedRecord) {
                return cb(null, {});
            });
            serviceObj1.updateCCBillInSystem(function (error) {
                expect(error).to.be.equal(null);
                expect(stub1).to.have.callCount(1);
    
                stub1.restore();
    
                return done();
            }, processedRecord, dbRecord, newBillCycle, recentsLayerRecords);
        });
    
        it("sendNotification | error in publishing", (done) => {
            let processedRecord = { "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "billDueDate":  MOMENT().add(15,'days').format('YYYY-MM-DD'), "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "8767", "mId": "", "cId": "667771", "isReliable" : "1"  };
            let dbRecord = {}
            let recentsLayerRecords = {}
            let newBillCycle = true;
            let serviceObj1 = _.cloneDeep(serviceObj);
    
            serviceObj1.kafkaBillFetchPublisher = new serviceObj1.infraUtils.kafka.producer(); 
            serviceObj1.kafkaBillFetchPublisher.publishData = function(data,cb){};
            
            let stub1 = sinon.stub(serviceObj1.kafkaBillFetchPublisher, 'publishData').callsFake(function(dbRecord, cb) {
                return cb("ERROR IN PUBLISHING");
            });
            serviceObj1.sendNotification(function (error) {
                expect(error).to.be.equal("ERROR IN PUBLISHING");
                expect(stub1).to.have.callCount(1);
    
                stub1.restore();
    
                return done();
            },{}, processedRecord);
        });
        it("sendNotification | successfull execution", (done) => {
            let processedRecord = { "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "billDueDate":  MOMENT().add(15,'days').format('YYYY-MM-DD'), "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "8767", "mId": "", "cId": "667771", "isReliable" : "1"  };
            let dbRecord = {}
            let recentsLayerRecords = {}
            let newBillCycle = true;
            let serviceObj1 = _.cloneDeep(serviceObj);
    
            serviceObj1.kafkaBillFetchPublisher = new serviceObj1.infraUtils.kafka.producer(); 
            serviceObj1.kafkaBillFetchPublisher.publishData = function(data,cb){};
            
            let stub1 = sinon.stub(serviceObj1.kafkaBillFetchPublisher, 'publishData').callsFake(function(dbRecord, cb) {
                return cb(null);
            });
            serviceObj1.sendNotification(function (error) {
                expect(error).to.be.equal(null);
                expect(stub1).to.have.callCount(1);
    
                stub1.restore();
    
                return done();
            },{}, processedRecord);
        });
    
        it("updateCCBillInRecentSystem | successfull execution", (done) => {
            let processedRecord = { "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "billDueDate": MOMENT("2022-12-11"), "billDate": MOMENT("2020-11-11"), "dueAmt": "590", "totalAmt": "11711", "lastCC": "8767", "mId": "", "cId": "667771", "isReliable" : "1"  };
            let dbRecord = {}
            let recentsLayerRecords = {"panUniqueReference":"12345"}
            let serviceObj1 = _.cloneDeep(serviceObj);
            
            let stub1 = sinon.stub(serviceObj1.recentsLayerLib, 'update').callsFake(function(cb,dbRecord, recentsLayerRecords, processedRecord, newBillCycle) {
                return cb(null);
            });
            serviceObj1.updateCCBillInRecentSystem(function (error) {
                expect(error).to.be.equal(null);
                expect(stub1).to.have.callCount(1);
    
                stub1.restore();
    
                return done();
            }, processedRecord, dbRecord, recentsLayerRecords);
        });
        
    
        it("getPriorityCard | oldercin", (done) => {
            let processedRecord = { "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "billDueDate": MOMENT("2022-12-11"), "billDate": MOMENT("2020-11-11"), "dueAmt": "590", "totalAmt": "11711", "lastCC": "8767", "mId": "", "cId": "667771", "isReliable" : "1"  };
            let dbRecord = [{
                'reference_id':'12345672483626sahcwhqbcwqhd7981e281298dhqbdjdbjxqn',
                "recharge_number":"XXXX XXXX XXXX 1234",
            }
        ]
        let recentRecord = {}
            let recentsLayerRecords = {"panUniqueReference":"12345"}
            let serviceObj1 = _.cloneDeep(serviceObj);
            let stub1 = sinon.stub(serviceObj1.bills, 'deleteDuplicateCards').callsFake(function(cb,processedRecord, recentsLayerRecords) {
                return cb(null);
            });
            serviceObj1.getPriorityCard(function (error,result) {
                expect(error).to.be.equal(null);
                expect(stub1).to.have.callCount(0);
    
                stub1.restore();
    
                return done();
            }, dbRecord);
        });
        it("getPriorityCard | older and newer cin", (done) => {
            let processedRecord = { "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "billDueDate": MOMENT("2022-12-11"), "billDate": MOMENT("2020-11-11"), "dueAmt": "590", "totalAmt": "11711", "lastCC": "8767", "mId": "", "cId": "667771", "isReliable" : "1"  };
            let dbRecord = [{
                'reference_id':'12345672483626sahcwhqbcwqhd7981e281298dhqbdjdbjxqn',
                "recharge_number":"XXXX XXXX XXXX 1234",
            },{
                'reference_id':'123456',
                "recharge_number":"XXXX XXXX XXXX 1234",
            }
        ]
        let recentRecord = {}
            let recentsLayerRecords = {"panUniqueReference":"12345"}
            let serviceObj1 = _.cloneDeep(serviceObj);
            let stub1 = sinon.stub(serviceObj1.bills, 'deleteDuplicateCards').callsFake(function(cb,processedRecord, recentsLayerRecords) {
                return cb("Error in deleting");
            });
            serviceObj1.getPriorityCard(function (error,result) {
                expect(error).to.be.equal(null);
                expect(stub1).to.have.callCount(1);
    
                stub1.restore();
    
                return done();
            }, dbRecord);
        });
        it("getPriorityCard | multiple tokenised cards", (done) => {
            let processedRecord = { "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "billDueDate": MOMENT("2022-12-11"), "billDate": MOMENT("2020-11-11"), "dueAmt": "590", "totalAmt": "11711", "lastCC": "8767", "mId": "", "cId": "667771", "isReliable" : "1"  };
            let dbRecord = [{
                'par_id':"123",
                'reference_id':'12345672483626',
                "recharge_number":"XXXX XXXX XXXX 1234",
                "is_encrypted":1
            },{
                'par_id':"123",
                'reference_id':'123456',
                "recharge_number":"XXXX XXXX XXXX 1234",
            }
        ]
        let recentRecord = {}
            let recentsLayerRecords = {"panUniqueReference":"12345"}
            let serviceObj1 = _.cloneDeep(serviceObj);
            let stub1 = sinon.stub(serviceObj1.bills, 'deleteDuplicateCards').callsFake(function(cb,processedRecord, recentsLayerRecords) {
                return cb(null);
            });
            let stub2 = sinon.stub(serviceObj1.smsParsingSyncCCBillLib, 'checkEncryptedCards').callsFake(function(cb,dbRecord) {
                return cb(null, dbRecord[0]);
            })
            serviceObj1.getPriorityCard(function (error,result) {
                expect(error).to.be.equal(null);
                expect(stub1).to.have.callCount(0);
                expect(stub2).to.have.callCount(1);
    
                stub1.restore();
                stub2.restore();
                return done();
            }, dbRecord);
        });
        it("getPriorityCard | multiple newer cin cards", (done) => {
            let processedRecord = { "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "billDueDate": MOMENT("2022-12-11"), "billDate": MOMENT("2020-11-11"), "dueAmt": "590", "totalAmt": "11711", "lastCC": "8767", "mId": "", "cId": "667771", "isReliable" : "1"  };
            let dbRecord = [{
                'reference_id':'12345672483626',
                "recharge_number":"XXXX XXXX XXXX 1234",
            },{
                'reference_id':'123456',
                "recharge_number":"XXXX XXXX XXXX 1234",
            }
        ]
        let recentRecord = {}
            let recentsLayerRecords = {"panUniqueReference":"12345"}
            let serviceObj1 = _.cloneDeep(serviceObj);
            let stub1 = sinon.stub(serviceObj1.bills, 'deleteDuplicateCards').callsFake(function(cb,processedRecord, recentsLayerRecords) {
                return cb(null);
            });
            serviceObj1.getPriorityCard(function (error,result) {
                expect(error).to.be.equal('Multiple encrypted cards exist');
                expect(stub1).to.have.callCount(0);
    
                stub1.restore();
    
                return done();
            }, dbRecord);
        });
        it("getPriorityCard | multiple older cin cards", (done) => {
            let processedRecord = { "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "billDueDate": MOMENT("2022-12-11"), "billDate": MOMENT("2020-11-11"), "dueAmt": "590", "totalAmt": "11711", "lastCC": "8767", "mId": "", "cId": "667771", "isReliable" : "1"  };
            let dbRecord = [{
                'reference_id':'123456724836268394726dgebfheufcvwuegd3ye83ry38ry83ru93',
                "recharge_number":"XXXX XXXX XXXX 1234",
            },{
                'reference_id':'123456724836268394726dgebfheufcvwuegd3ye83ry38ry83ru93',
                "recharge_number":"XXXX XXXX XXXX 1234",
            }
        ]
        let recentRecord = {}
            let recentsLayerRecords = {"panUniqueReference":"12345"}
            let serviceObj1 = _.cloneDeep(serviceObj);
            let stub1 = sinon.stub(serviceObj1.bills, 'deleteDuplicateCards').callsFake(function(cb,processedRecord, recentsLayerRecords) {
                return cb(null);
            });
            serviceObj1.getPriorityCard(function (error,result) {
                expect(error).to.be.equal('Multiple encrypted cards exist');
                expect(stub1).to.have.callCount(0);
    
                stub1.restore();
    
                return done();
            }, dbRecord);
        });
        it("getPriorityCard | Error in deleting", (done) => {
            let processedRecord = { "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "billDueDate": MOMENT("2022-12-11"), "billDate": MOMENT("2020-11-11"), "dueAmt": "590", "totalAmt": "11711", "lastCC": "8767", "mId": "", "cId": "667771", "isReliable" : "1"  };
            let dbRecord = [{
                'reference_id':'123456724836268394726dgebfheufcvwuegd3ye83ry38ry83ru93',
                "recharge_number":"XXXX XXXX XXXX 1234",
            },{
                "recharge_number":"XXXX XXXX XXXX 1234",
                'par_id' : '1234567',
                'reference_id':'1234567',
            },{
                "recharge_number":"XXXX XXXX XXXX 1234",
                'reference_id':'1234567scsc',
            }
        ]
        let recentRecord = {}
            let recentsLayerRecords = {"panUniqueReference":"12345"}
            let serviceObj1 = _.cloneDeep(serviceObj);
            let stub1 = sinon.stub(serviceObj1.bills, 'deleteDuplicateCards').callsFake(function(cb,processedRecord, recentsLayerRecords) {
                return cb("Error in deleting");
            });
            serviceObj1.getPriorityCard(function (error,result) {
                expect(error).to.be.equal(null);
                expect(stub1).to.have.callCount(2);
    
                stub1.restore();
    
                return done();
            }, dbRecord);
        });
        it("getPriorityCard | successfull execution", (done) => {
            let processedRecord = { "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "billDueDate": MOMENT("2022-12-11"), "billDate": MOMENT("2020-11-11"), "dueAmt": "590", "totalAmt": "11711", "lastCC": "8767", "mId": "", "cId": "667771", "isReliable" : "1"  };
            let dbRecord = [{
                'reference_id':'123456724836268394726dgebfheufcvwuegd3ye83ry38ry83ru93',
                "recharge_number":"XXXX XXXX XXXX 1234",
            },{
                "recharge_number":"XXXX XXXX XXXX 1234",
                'par_id' : '1234567',
                'reference_id':'1234567',
            },{
                "recharge_number":"XXXX XXXX XXXX 1234",
                'reference_id':'1234567scsc',
            }
        ]
        let recentRecord = {}
            let recentsLayerRecords = {"panUniqueReference":"12345"}
            let serviceObj1 = _.cloneDeep(serviceObj);
            let stub1 = sinon.stub(serviceObj1.bills, 'deleteDuplicateCards').callsFake(function(cb,processedRecord, recentsLayerRecords) {
                return cb(null);
            });
            serviceObj1.getPriorityCard(function (error,result) {
                expect(error).to.be.equal(null);
                expect(stub1).to.have.callCount(2);
    
                stub1.restore();
    
                return done();
            }, dbRecord);
        });
    
        it("getMcnByBank | successfull execution", (done) => {
            let processedRecord = { "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "dueDate": "2022-12-11", "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "mId": "", "cId": "667771", "isReliable" : "1"  };
            let bankName = "SC";
            let dbRecord = [{
                recharge_number : 'XXXX XXXX XXXX 1234',
                bank_name : 'scb'
            },
            {
                recharge_number : 'XXXX XXXX XXXX XX34',
                bank_name : 'hdfc'
            }]
            let recentsLayerRecords = {}
            let serviceObj1 = _.cloneDeep(serviceObj);
            
            let stub1 = sinon.stub(serviceObj1.bills, 'getBillByCustomer').callsFake(function(cb,bankName, processedRecord) {
                return cb(null, dbRecord);
            });
            let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
            serviceObj1.getMcnByBank(processedRecord, bankName, function (error,result) {
                expect(error).to.be.equal(null);
                expect(result).to.be.equal('1234');
                expect(stub1).to.have.callCount(1);
                expect(stub2).to.have.callCount(1);
    
                stub1.restore();
                stub2.restore();
    
                return done();
            });
        });
        it("getMcnByBank | more than 1 mcn", (done) => {
            let processedRecord = { "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "dueDate": "2022-12-11", "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "mId": "", "cId": "667771", "isReliable" : "1"  };
            let bankName = "SC";
            let dbRecord = [{
                recharge_number : 'XXXX XXXX XXXX 1234',
                bank_name : 'scb'
            },
            {
                recharge_number : 'XXXX XXXX XXXX XX34',
                bank_name : 'scb'
            }]
            let recentsLayerRecords = {}
            let serviceObj1 = _.cloneDeep(serviceObj);
            
            let stub1 = sinon.stub(serviceObj1.bills, 'getBillByCustomer').callsFake(function(cb,bankName, processedRecord) {
                return cb(null, dbRecord);
            });
            let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
            serviceObj1.getMcnByBank(processedRecord, bankName, function (error,result) {
                expect(error).to.be.equal("Multiple matching bankName record found");
                expect(result).to.be.equal(undefined);
                expect(stub1).to.have.callCount(1);
                expect(stub2).to.have.callCount(0);
    
                stub1.restore();
                stub2.restore();
    
                return done();
            });
        });
        it("getMcnByBank | no matching mcn", (done) => {
            let processedRecord = { "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "dueDate": "2022-12-11", "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "mId": "", "cId": "667771", "isReliable" : "1"  };
            let bankName = "SC";
            let dbRecord = [{
                recharge_number : 'XXXX XXXX XXXX 1234',
                bank_name : 'hdfc'
            },
            {
                recharge_number : 'XXXX XXXX XXXX XX34',
                bank_name : ''
            }]
            let recentsLayerRecords = {}
            let serviceObj1 = _.cloneDeep(serviceObj);
            
            let stub1 = sinon.stub(serviceObj1.bills, 'getBillByCustomer').callsFake(function(cb,bankName, processedRecord) {
                return cb(null, dbRecord);
            });
            let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
            serviceObj1.getMcnByBank(processedRecord, bankName, function (error,result) {
                expect(error).to.be.equal("No matching bankName record found");
                expect(result).to.be.equal(undefined);
                expect(stub1).to.have.callCount(1);
                expect(stub2).to.have.callCount(0);
    
                stub1.restore();
                stub2.restore();
    
                return done();
            });
        });
        it("getMcnByBank | no cards", (done) => {
            let processedRecord = { "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "dueDate": "2022-12-11", "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "mId": "", "cId": "667771", "isReliable" : "1"  };
            let bankName = "SC";
            let dbRecord = null;
            let recentsLayerRecords = {}
            let serviceObj1 = _.cloneDeep(serviceObj);
            
            let stub1 = sinon.stub(serviceObj1.bills, 'getBillByCustomer').callsFake(function(cb,bankName, processedRecord) {
                return cb(null, dbRecord);
            });
            let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
            serviceObj1.getMcnByBank(processedRecord, bankName, function (error,result) {
                expect(error).to.be.equal("No cards found for cust_id");
                expect(result).to.be.equal(undefined);
                expect(stub1).to.have.callCount(1);
                expect(stub2).to.have.callCount(0);
    
                stub1.restore();
                stub2.restore();
    
                return done();
            });
        });
        it("getMcnByBank | error in getBillByCustomer", (done) => {
            let processedRecord = { "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "dueDate": "2022-12-11", "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "mId": "", "cId": "667771", "isReliable" : "1"  };
            let bankName = "SC";
            let dbRecord = null;
            let recentsLayerRecords = {}
            let serviceObj1 = _.cloneDeep(serviceObj);
            
            let stub1 = sinon.stub(serviceObj1.bills, 'getBillByCustomer').callsFake(function(cb,bankName, processedRecord) {
                return cb("Error in getBillByCustomer");
            });
            let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
            serviceObj1.getMcnByBank(processedRecord, bankName, function (error,result) {
                expect(error).to.be.equal("Error in getBillByCustomer");
                expect(result).to.be.equal(undefined);
                expect(stub1).to.have.callCount(1);
                expect(stub2).to.have.callCount(0);
    
                stub1.restore();
                stub2.restore();
    
                return done();
            });
        });
        it("getMcnByBank | cust id not present", (done) => {
            let processedRecord = { "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "dueDate": "2022-12-11", "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "mId": "", "isReliable" : "1"  };
            let bankName = "SC";
            let dbRecord = null;
            let recentsLayerRecords = {}
            let serviceObj1 = _.cloneDeep(serviceObj);
            
            let stub1 = sinon.stub(serviceObj1.bills, 'getBillByCustomer').callsFake(function(cb,bankName, processedRecord) {
                return cb("Error in getBillByCustomer");
            });
            let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
            serviceObj1.getMcnByBank(processedRecord, bankName, function (error,result) {
                expect(error).to.be.equal("CustomerId not found");
                expect(result).to.be.equal(undefined);
                expect(stub1).to.have.callCount(0);
                expect(stub2).to.have.callCount(0);
    
                stub1.restore();
                stub2.restore();
    
                return done();
            });
        });
    
        it("extractLastCC | cust id not present", (done) => {
            let record = [{
                recharge_number : 'XXXX XXXX XXXX 1234'
            }]
            let serviceObj1 = _.cloneDeep(serviceObj);
            let lastCC = serviceObj1.extractLastCC(record)
            expect(lastCC).to.be.equal('1234');
                return done();
        });
        it("extractLastCC | cust id not present", (done) => {
            let record = [{
                recharge_number : 'XXXX XXXX XXXX XX34'
            }]
            let serviceObj1 = _.cloneDeep(serviceObj);
            let lastCC = serviceObj1.extractLastCC(record)
            expect(lastCC).to.be.equal('34');
                return done();
        });
        it("extractLastCC | cust id not present", (done) => {
            let record = [{
                recharge_number : 'XXXX XXXX XXXX1234'
            }]
            let serviceObj1 = _.cloneDeep(serviceObj);
            let lastCC = serviceObj1.extractLastCC(record)
            expect(lastCC).to.be.equal('1234');
                return done();
        });
        it("extractLastCC | cust id not present", (done) => {
            let record = [{
                recharge_number : '2345 XXXX XXXX1234'
            }]
            let serviceObj1 = _.cloneDeep(serviceObj);
            let lastCC = serviceObj1.extractLastCC(record)
            expect(lastCC).to.be.equal('1234');
                return done();
        });
        it("extractLastCC | cust id not present", (done) => {
            let record = [{
                recharge_number : '2345XXXX XXXX1234'
            }]
            let serviceObj1 = _.cloneDeep(serviceObj);
            let lastCC = serviceObj1.extractLastCC(record)
            expect(lastCC).to.be.equal('1234');
                return done();
        });
        it("extractLastCC | cust id not present", (done) => {
            let record = [{
                recharge_number : '2345XXXXXXXX1234'
            }]
            let serviceObj1 = _.cloneDeep(serviceObj);
            let lastCC = serviceObj1.extractLastCC(record)
            expect(lastCC).to.be.equal('1234');
                return done();
        });
        it("extractLastCC | cust id not present", (done) => {
            let record = [{
                recharge_number : '2345XXXXXXX21234'
            }]
            let serviceObj1 = _.cloneDeep(serviceObj);
            let lastCC = serviceObj1.extractLastCC(record)
            expect(lastCC).to.be.equal('1234');
                return done();
        });
        it("extractLastCC | cust id not present", (done) => {
            let record = [{
                recharge_number : '2345XXXXXXXXXX4'
            }]
            let serviceObj1 = _.cloneDeep(serviceObj);
            let lastCC = serviceObj1.extractLastCC(record)
            expect(lastCC).to.be.equal('4');
                return done();
        });
        it("extractLastCC | cust id not present", (done) => {
            let record = [{
                recharge_number : 'XXXX XXXX XX3 237'
            }]
            let serviceObj1 = _.cloneDeep(serviceObj);
            let lastCC = serviceObj1.extractLastCC(record)
            expect(lastCC).to.be.equal('3237');
                return done();
        });
        it("extractLastCC | cust id not present", (done) => {
            let record = [{
                recharge_number : 'XXXX XXXX XX32 37'
            }]
            let serviceObj1 = _.cloneDeep(serviceObj);
            let lastCC = serviceObj1.extractLastCC(record)
            expect(lastCC).to.be.equal('3237');
                return done();
        });
    
    
        
        it("getSingleMatchingCardByCustomer | Valid record | Error from db", (done) => {
            let processedRecord = { "currentBillAmount": 112233, "billDate": MOMENT().add(-10, 'days'), "billDueDate": MOMENT().add(12, 'days'), "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "", "customerId": "98786755" , "isReliable" : "1"};
            let stub1 = sinon.stub(serviceObj.bills,'getBillByCustomer').callsFake(function(cb){
                return cb('Error from db')
            })
            serviceObj.getActionforCCBills(function (error, action, dbRecordResp , message) {
                expect(error).to.be.equal('Error from db');
                expect(stub1).to.have.callCount(1);
                stub1.restore();
                return done();
            }, processedRecord);
        });
        it("getSingleMatchingCardByCustomer | Valid record | par_id case 1", (done) => {
            let processedRecord = { "currentBillAmount": 112233, "billDate": MOMENT().add(-10, 'days'), "billDueDate": MOMENT().add(12, 'days'), "currentMinBillAmount": 590, "totalAmt": "11711", "lastCC": "2808", "mId": "", "customerId": "98786755" , "isReliable" : "1"};
            let dbRecord = [{
                "recharge_number":"XXXX XXXX XXXX 2808",
                "par_id":"123456"
            }]
            let stub1 = sinon.stub(serviceObj.bills,'getBillByCustomer').callsFake(function(cb){
                return cb(null, dbRecord);
            })
            serviceObj.getActionforCCBills(function (error, action, dbRecordResp , message) {
                expect(error).to.be.equal(null);
                expect(stub1).to.have.callCount(1);
                stub1.restore();
                return done();
            }, processedRecord);
        });
        it("getSingleMatchingCardByCustomer | Valid record | par_id case 2", (done) => {
            let processedRecord = { "currentBillAmount": 112233, "billDate": MOMENT().add(-10, 'days'), "billDueDate": MOMENT().add(12, 'days'), "currentMinBillAmount": 590, "totalAmt": "11711", "lastCC": "2808", "mId": "", "customerId": "98786755" , "isReliable" : "1"};
            let dbRecord = [{
                "recharge_number":"XXXX XXXX XXXX 2808",
                "par_id":"123456"
            }]
            let stub1 = sinon.stub(serviceObj.bills,'getBillByCustomer').callsFake(function(cb){
                return cb(null, dbRecord);
            })
            let stub2 = sinon.stub(serviceObj, 'shouldRecordBeSkipped').returns([true,null])
            serviceObj.getActionforCCBills(function (error, action, dbRecordResp , message) {
                expect(error).to.be.equal(null);
                expect(stub1).to.have.callCount(1);
                stub1.restore();
                stub2.restore();
                return done();
            }, processedRecord);
        });
        it("getSingleMatchingCardByCustomer | Valid record | Entry do not exists in DB", (done) => {
            let processedRecord = { "currentBillAmount": 112233, "billDate": MOMENT().add(-10, 'days'), "billDueDate": MOMENT().add(12, 'days'), "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "", "customerId": "98786755" , "isReliable" : "1"};
            serviceObj.getActionforCCBills(function (error, action, dbRecordResp , message) {
                expect(error).to.be.equal(null);
                expect(message).to.be.equal("Record not found");
                expect(action).to.be.equal("create");
                expect(dbRecordResp).to.be.equal(null);
                return done();
            }, processedRecord);
        });
    
        it("getSingleMatchingCardByCustomer | Valid record | No matching last4 digits MCN matching in db", (done) => {
            let processedRecord = { "currentBillAmount": 112233, "billDate": MOMENT().add(-10, 'days'), "billDueDate": MOMENT().add(12, 'days'), "currentMinBillAmount": "590", "currentBillAmount": "11711", "lastCC": "9876", "customerId": 98786756 , "isReliable" : "1"};
            serviceObj.getActionforCCBills(function (error, action, dbRecordResp , message) {
                expect(error).to.be.equal(null);
                expect(message).to.be.equal("No records found with given last4Digits");
                expect(action).to.be.equal("create");
                expect(dbRecordResp).to.be.equal(null);
                return done();
            }, processedRecord);
        });
    
        it("getSingleMatchingCardByCustomer | Valid record | MULTIPLE_MATCHING_MCN", (done) => {
            let processedRecord = { "currentBillAmount": 112233, "billDate": MOMENT().add(-10, 'days'), "billDueDate": MOMENT().add(12, 'days'), "currentMinBillAmount": 590, "currentBillAmount": "11711", "lastCC": "8767", "customerId": 987654318888888888 , "isReliable" : "1" };
            serviceObj.getActionforCCBills(function (error, action, dbRecordResp , message) {
                expect(action).to.be.equal(undefined);
                expect(dbRecordResp).to.be.equal(undefined);
                expect(message).to.be.equal(undefined);
                return done();
            }, processedRecord);
        });
    
        it("getSingleMatchingCardByCustomer | Valid record | MULTIPLE_MATCHING_MCN", (done) => {
            let processedRecord = { "currentBillAmount": 112233, "billDate": MOMENT().add(-10, 'days'), "billDueDate": MOMENT().add(12, 'days'), "currentMinBillAmount": 590, "currentBillAmount": "11711", "lastCC": "8767", "customerId": 987654318881 , "isReliable" : "1" };
            serviceObj.getActionforCCBills(function (error, action, dbRecordResp , message) {
                expect(error).to.be.equal(null);
                expect(action).to.be.equal('update');
                expect(message).to.be.equal(null);
                return done();
            }, processedRecord);
        });
        
        it("getSingleMatchingCardByCustomer | Valid record | MATCHING_MCN", (done) => {
            let processedRecord = { "currentBillAmount": 112233, "billDate": MOMENT().add(-10, 'days'), "billDueDate": MOMENT().add(12, 'days'), "currentMinBillAmount": 590, "currentBillAmount": "11711", "lastCC": "8767", "customerId": 667771 , "isReliable" : "1"};
            serviceObj.getActionforCCBills(function (error, action, dbRecordResp , message) {
                expect(error).to.be.equal(null);
                expect(message).to.be.equal(null);
                expect(action).to.be.equal("update");
                return done();
            }, processedRecord);
        });
    
        it("shouldRecordBeSkipped | different amount | same date", () => {
            let processedRecord = { "currentBillAmount": 112233, "billDate": MOMENT().add(-10, 'days'), "billDueDate": MOMENT().add(12, 'days'), "currentMinBillAmount": "590", "currentBillAmount": "11711", "lastCC": "8767", "customerId": 667771 , "isReliable" : "1"};
            let dbRecord = { "amount": 1234, "payment_date": MOMENT().add(-10, 'days'), "due_date": null, "status": 11, "customerOtherInfo": null }; 
            let [action,error] = serviceObj.shouldRecordBeSkipped(dbRecord, processedRecord);
            expect(action).to.be.false;
        })
    
        it("shouldRecordBeSkipped | amount within grace | date within grace", () => {
            let processedRecord = { "currentBillAmount": 112230, "billDate": MOMENT().add(0, 'days'), "billDueDate": MOMENT().add(12, 'days'), "currentMinBillAmount": "590", "lastCC": "8767", "customerId": 667771 , "isReliable" : "1"};
            let dbRecord = { "amount": 112233, "payment_date": MOMENT().add(2, 'days'), "due_date": null, "status": 11, "customerOtherInfo": null, "extra" : `{"customer_type":"2","last_paid_amount":112231}` }; 
            let [action,error] = serviceObj.shouldRecordBeSkipped(dbRecord, processedRecord);
            expect(action).to.be.true;
        })
    
        it("shouldRecordBeSkipped | different amount | different date", () => {
            let processedRecord = { "currentBillAmount": 11711, "billDate": MOMENT().add(-10, 'days'), "billDueDate": MOMENT().add(12, 'days'), "currentMinBillAmount": "590", "currentBillAmount": "11711", "lastCC": "8767", "customerId": 667771 , "isReliable" : "1"};
            let dbRecord = { "amount": 1234, "payment_date": MOMENT().add(-20, 'days'), "due_date": null, "status": 11, "customerOtherInfo": null }; 
            let [action,error] = serviceObj.shouldRecordBeSkipped(dbRecord, processedRecord);
            expect(action).to.be.false;
        })
    
        it("shouldUpdateCCBillInSystem | Null Arguments", () => {
            let processedRecord = null;
            let dbRecord = null;
            let [updateInDB, newBillCycle] = serviceObj.shouldUpdateCCBillInSystem(processedRecord, dbRecord);
            expect(updateInDB).to.be.false;
            expect(newBillCycle).to.be.false;
        });
    
    
        it("shouldUpdateCCBillInSystem | null dbRecord", () => {
            let processedRecord = { "currentBillAmount": 112233, "billDate": MOMENT().add(-10, 'days'), "billDueDate": MOMENT().add(12, 'days'), "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "", "cId": "**********", "isReliable" : "1"};
            let dbRecord = null;
    
            let [updateInDB, newBillCycle] = serviceObj.shouldUpdateCCBillInSystem(processedRecord, dbRecord);
            expect(updateInDB).to.be.false;
            expect(newBillCycle).to.be.false;
        });
    
    
        it("shouldUpdateCCBillInSystem | Valid Record#1 | dbDueDate = null", () => {
            let processedRecord = { "currentBillAmount": 112233, "billDate": MOMENT().add(-10, 'days'), "billDueDate": null, "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "", "cId": "**********", "isReliable" : "1" };
            let dbRecord = { "amount": 2, "bill_date": MOMENT().add(-40).format('YYYY-MM-DD HH:mm:ss'), "due_date": null, "status": 11, "customerOtherInfo": null };
    
            let [updateInDB, newBillCycle] = serviceObj.shouldUpdateCCBillInSystem(processedRecord, dbRecord);
            expect(updateInDB).to.be.true;
            expect(newBillCycle).to.be.true;
        });
        it("shouldUpdateCCBillInSystem | Valid Record#2 | bill amount >= db amount", () => {
            let processedRecord = { "currentBillAmount": 10549, "billDate": MOMENT().add(-10, 'days'), "billDueDate": MOMENT().add(12, 'days'), "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "", "cId": "**********", "isReliable" : "1" };
            let dbRecord = { "amount": 200, "bill_date": MOMENT().add(-40).format('YYYY-MM-DD HH:mm:ss'), "due_date": MOMENT().add(12, 'days'), "status": 4, "customerOtherInfo": null };
    
            let [updateInDB, newBillCycle] = serviceObj.shouldUpdateCCBillInSystem(processedRecord, dbRecord);
            expect(updateInDB).to.be.false;
            expect(newBillCycle).to.be.false;
        });
    
        it("shouldUpdateCCBillInSystem | Valid Record#3 | bill amount < db amount", () => {
            let processedRecord = { "currentBillAmount": 200, "billDate": MOMENT().add(-10, 'days'), "billDueDate": MOMENT().add(12, 'days'), "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "", "cId": "**********", "isReliable" : "1" };
            let dbRecord = { "amount": 5000, "bill_date": MOMENT().add(-40).format('YYYY-MM-DD HH:mm:ss'), "due_date": MOMENT().add(12, 'days'), "status": 11, "customerOtherInfo": null };
    
            let [updateInDB, newBillCycle] = serviceObj.shouldUpdateCCBillInSystem(processedRecord, dbRecord);
            expect(updateInDB).to.be.true;
            expect(newBillCycle).to.be.false;
        });
    
        it("getDbRecordToUpdate | Valid Record | newBillCycle=true", () => {
            let processedRecord = { "currentBillAmount": 112233, "billDate": MOMENT().add(-10, 'days'), "billDueDate": MOMENT().add(12, 'days'), "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "", "cId": "**********", "isReliable" : "1" };
            let dbRecord = { "amount": 2, "bill_date": MOMENT().add(-40).format('YYYY-MM-DD HH:mm:ss'), "due_date": MOMENT().add(-20).format('YYYY-MM-DD HH:mm:ss'), "status": 11, "customerOtherInfo": null };
            let newBillCycle = true;
    
            let response = serviceObj.getDbRecordToUpdate(processedRecord, dbRecord, newBillCycle);
            expect(response.amount).to.be.equal(112233);
            expect(response.bill_date).to.be.equal(processedRecord.billDate.format('YYYY-MM-DD HH:mm:ss'));
            expect(response.due_date).to.be.equal(processedRecord.billDueDate.format('YYYY-MM-DD HH:mm:ss'));
            expect(response.status).to.be.equal(4);
            expect(MOMENT(response.bill_fetch_date, 'YYYY-MM-DD HH:mm:ss').diff(MOMENT(), 'seconds')).not.to.be.above(10);
    
            let processedRecord2 = _.cloneDeep(processedRecord);
            processedRecord2.billDate = processedRecord2.billDate.format('YYYY-MM-DD HH:mm:ss');
            processedRecord2.billDueDate = processedRecord2.billDueDate.format('YYYY-MM-DD HH:mm:ss');
            expect(response.customerOtherInfo).to.be.equal(JSON.stringify(processedRecord2));
        });
        it("getDbRecordToUpdate | Valid Record | newBillCycle=false", () => {
            let processedRecord = { "currentBillAmount": 112233, "billDate": MOMENT().add(-10, 'days'), "billDueDate": MOMENT().add(12, 'days'), "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "", "cId": "**********", "isReliable" : "1" };
            let dbRecord = { "amount": 2, "bill_date": MOMENT().add(-40).format('YYYY-MM-DD HH:mm:ss'), "due_date": MOMENT().add(-20).format('YYYY-MM-DD HH:mm:ss'), "status": 11, "customerOtherInfo": null, "bill_fetch_date": "2020-10-14T14:59:48.000Z" };
            let newBillCycle = false;
    
            let response = serviceObj.getDbRecordToUpdate(processedRecord, dbRecord, newBillCycle);
            expect(response.amount).to.be.equal(112233);
            expect(MOMENT(response.bill_date, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD HH:')).to.be.equal(processedRecord.billDate.format('YYYY-MM-DD HH:'));
            expect(MOMENT(response.due_date, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD HH:')).to.be.equal(processedRecord.billDueDate.format('YYYY-MM-DD HH:'));
            expect(response.status).to.be.equal(4);
            expect(response.bill_fetch_date).to.be.equal("2020-10-14 14:59:48");
    
            let processedRecord2 = _.cloneDeep(processedRecord);
            processedRecord2.billDate = processedRecord2.billDate.format('YYYY-MM-DD HH:mm:ss');
            processedRecord2.billDueDate = processedRecord2.billDueDate.format('YYYY-MM-DD HH:mm:ss');
            expect(response.customerOtherInfo).to.be.equal(JSON.stringify(processedRecord2));
        });
    
        it("validateAndProcessRecord | unexpected error", () => {
            let recordData = JSON.parse(JSON.stringify({ "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "dueDate": "2022-12-11", "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "mId": "","bankName":"SC", "cId": "**********", "isReliable" : "1" }));
            let bankName='SC'
            let stub1 = sinon.stub(serviceObj, 'getMcnByBank').callsFake(function(recordData,bankName,cb){
                return cbi(null,'1234')
            })
            serviceObj.validateAndProcessRecord(recordData,function(err, data){
                expect(`${err}`).to.be.equal('ReferenceError: cbi is not defined');
                expect(stub1).to.have.callCount(1);
                stub1.restore();
            });
        });
        it.skip("validateAndProcessRecord | Valid Record", () => {
            let recordData = JSON.parse(JSON.stringify({ "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "dueDate": "2022-12-11", "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "mId": "","bankName":"SC", "cId": "**********", "isReliable" : "1" }));
            let bankName='SC'
            let stub1 = sinon.stub(serviceObj, 'getMcnByBank').callsFake(function(recordData,bankName,cb){
                return cb(null,'1234')
            })
            serviceObj.validateAndProcessRecord(recordData,function(err, data){
                expect(err).to.be.equal(null);
                expect(data.customerId).to.be.equal(**********);
                expect(data.lastCC).to.be.equal("1234");
                expect(data.currentBillAmount).to.be.equal(11711);
                expect(data.currentMinBillAmount).to.be.equal(590);
                expect(data.billDate.format('YYYY-MM-DD HH:mm:ss')).to.be.equal("2020-11-11 00:00:00");
                expect(data.billDueDate.format('YYYY-MM-DD HH:mm:ss')).to.be.equal("2022-12-11 00:00:00");
                expect(data.smsSenderID).to.be.equal("AD-HDFCBK");
                expect(stub1).to.have.callCount(1);
                stub1.restore();
            });
        });
        it.skip("validateAndProcessRecord | Valid Record | As per Production data", () => {
            let recordData = JSON.parse(JSON.stringify({ "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "dueDate": "2022-12-11", "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "", "cId": "**********", "isReliable" : "1" }));
            serviceObj.validateAndProcessRecord(recordData,function(err, data){
                expect(err).to.be.equal(null);
                expect(data.customerId).to.be.equal(**********);
                expect(data.lastCC).to.be.equal("2808");
                expect(data.currentBillAmount).to.be.equal(11711);
                expect(data.currentMinBillAmount).to.be.equal(590);
                expect(data.billDate.format('YYYY-MM-DD HH:mm:ss')).to.be.equal("2020-11-11 00:00:00");
                expect(data.billDueDate.format('YYYY-MM-DD HH:mm:ss')).to.be.equal("2022-12-11 00:00:00");
                expect(data.smsSenderID).to.be.equal("AD-HDFCBK");
            });
        });
        it.skip("validateAndProcessRecord | Valid Record | currentMinBillAmount == 0", () => {
            let recordData = JSON.parse(JSON.stringify({ "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "dueDate": "2022-12-11", "billDate": "2020-11-11", "dueAmt": "0", "totalAmt": "11711", "lastCC": "2808", "mId": "", "cId": "**********", "isReliable" : "1" }) );
            serviceObj.validateAndProcessRecord(recordData, function(error, data){
                expect(error).to.be.equal(null);
                expect(data.customerId).to.be.equal(**********);
                expect(data.lastCC).to.be.equal("2808");
                expect(data.currentBillAmount).to.be.equal(11711);
                expect(data.currentMinBillAmount).to.be.equal(0);
                expect(data.billDate.format('YYYY-MM-DD HH:mm:ss')).to.be.equal("2020-11-11 00:00:00");
                expect(data.billDueDate.format('YYYY-MM-DD HH:mm:ss')).to.be.equal("2022-12-11 00:00:00");
                expect(data.smsSenderID).to.be.equal("AD-HDFCBK");
            });
        });
        it("validateAndProcessRecord | invalid Arg | currentMinBillAmount is -ve", () => {
            let recordData = JSON.parse(JSON.stringify({ "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "dueDate": "2022-12-11", "billDate": "2020-11-11", "currentMinBillAmount": -10, "totalAmt": "11711", "lastCC": "2808", "mId": "", "cId": "**********", "isReliable" : "1" }) );
            serviceObj.validateAndProcessRecord(recordData,function(error,data){
                expect(error).to.be.equal(null);
            });
        });
        it("validateAndProcessRecord | invalid Arg | currentMinBillAmount is not a number", () => {
            let recordData = JSON.parse(JSON.stringify({ "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "dueDate": "2022-12-11", "billDate": "2020-11-11", "dueAmt": "XXXXX", "totalAmt": "11711", "lastCC": "2808", "mId": "", "cId": "**********", "isReliable" : "1" }) );
            serviceObj.validateAndProcessRecord(recordData, function(error,data){
                expect(error).to.be.equal(null);
            });
        });
        it.skip("validateAndProcessRecord | Invalid Arg | record not passed", () => {
            let recordData = null;
            serviceObj.validateAndProcessRecord(recordData, function(error, data){
                expect(error).to.be.equal("Invalid record");
            });
        });
        it("validateAndProcessRecord | Invalid record | record not passed", () => {
            let recordData = null;
            serviceObj.validateAndProcessRecord(recordData, function(error, data){
                expect(error).to.be.equal("Invalid record");
            });
        });
        it("validateAndProcessRecord | customerId not passed", () => {
            let recordData = JSON.parse(JSON.stringify({ "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "dueDate": "2022-12-11", "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "", "isReliable" : "1" }) );
            serviceObj.validateAndProcessRecord(recordData, function(error, data){
                expect(error).to.be.equal("Mandatory Params customerId is Missing / Invalid");
            });
        });
        it("validateAndProcessRecord | customerId - As Number Valid Case", () => {
            let recordData = JSON.parse(JSON.stringify({ "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "dueDate": "2022-12-11", "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "", "cId": 112233, "isReliable" : "1" }) );
            serviceObj.validateAndProcessRecord(recordData, function(error, data){
                expect(error).to.be.equal(null);
            });
        });
        it("validateAndProcessRecord | Invalid customerId - alpha numeric", () => {
            let recordData = JSON.parse(JSON.stringify({ "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "dueDate": "2022-12-11", "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "", "cId": "alphanumeric", "isReliable" : "1" }) );
            serviceObj.validateAndProcessRecord(recordData, function(error, data){
                expect(error).to.be.equal("Mandatory Params customerId is Missing / Invalid");
            });
        });
        it("validateAndProcessRecord | InValid Record | Missing bill date due date", () => {
            let recordData = JSON.parse(JSON.stringify({ "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "dueDate2": "11/DEC/2020", "billDate2": "11/NOV/2020", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "", "cId": "**********", "isReliable" : "1" }) );
            serviceObj.validateAndProcessRecord(recordData, function(error, data){
                expect(error).to.be.equal(null);
            });
        });
        it("validateAndProcessRecord | InValid Record | Missing all mandatory params", () => {
            let recordData = JSON.parse(JSON.stringify({ "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "dueDate2": "11/DEC/2020", "billDate2": "11/NOV/2020", "dueAmt2": "590", "totalAmt2": "11711", "lastCC2": "2808", "mId": "", "cId2": "**********", "isReliable" : "1" }) );
            serviceObj.validateAndProcessRecord(recordData, function(error, data){
                expect(error).to.be.equal("Mandatory Params customerId,lastCC is Missing / Invalid");
            });
        });
        it.skip("validateAndProcessRecord | InValid Record | Old Bill Record - due date < now", () => {
            let recordData = JSON.parse(JSON.stringify({ "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "dueDate": "2019-12-11", "billDate": "2019-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "", "cId": "**********", "isReliable" : "1" }) );
            serviceObj.validateAndProcessRecord(recordData, function(error, data){
                expect(error).to.be.equal("Old Bill received with due date 2019-12-11 00:00:00");
            });
        });
        it("validateAndProcessRecord | InValid Record | lastCC length > 4", () => {
            let recordData = JSON.parse(JSON.stringify({ "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "dueDate": "2022-12-12", "billDate": "2022-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "28088", "mId": "", "cId": "**********", "isReliable" : "1" }) );
            serviceObj.validateAndProcessRecord(recordData, function(error, data){
                expect(data.lastCC).to.be.equal("8088");
            });
            
        });
        it("validateAndProcessRecord | InValid Record | lastCC length < 2", () => {
            let recordData = JSON.parse(JSON.stringify({ "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "dueDate": "2022-12-12", "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "8", "mId": "", "cId": "**********", "isReliable" : "1" }) );
            serviceObj.validateAndProcessRecord(recordData, function(error, data){
                expect(error).to.be.equal("Mandatory Params lastCC-length-1 is Missing / Invalid");
            });
        });
    
    
        it("createCCBillAndSendNotification | unexpected error" , (done) => {
            let recordData = { "smsSenderID": "AD-HDFCBK", "smsDateTime": "*************", "dueDate": "2022-12-11", "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "" , "cId":"123456", "currentBillAmount": 112233,"billDueDate": MOMENT().add(12, 'days'), "currentMinBillAmount": 590,  "isReliable" : "1" };
            let customerId ="123456";
            let savedCardData = {"product":{}}
            let status = "SUCCESS";
            let type = 'MATCHING_MCN';
            let data = {};
            let dbRecord=[];
            let recordToUpdate={};
            let uniqueKey = '0_hdfc_visa'
    
            serviceObj.nonPaytmKafkaPublisher = new serviceObj.infraUtils.kafka.producer(); 
            serviceObj.nonPaytmKafkaPublisher.publishData = function(data,cb){};
            console.log("nonPaytmKafkaPublisher", serviceObj.nonPaytmKafkaPublisher);
    
            let stub1 = sinon.stub(serviceObj.paymentGatewayUtils, 'getCreditCardDataFromSaga').callsFake(function(customerId) {
                return cb(null,savedCardData);
            });
            let stub2 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getProcessedSagaSavedCardsData').callsFake(function(cb,recordData, data) {
                return cb({status, type, data:savedCardData});
            });
            let stub4 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getUniqueKeyForSavedCardsData').returns(uniqueKey);
            let stub3 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'isPaytmFirstCCInSagaCCDetails').returns(false);
            let stub5 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getFinancialServicesPID').callsFake(function(cb,uniqueKey) {
                return cb(null,'1200763862');
            });
            let stub6 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'formatDataForNonPaytmCards').returns(recordToUpdate);
            let stub7 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getFormattedData').returns(recordToUpdate);
            
            let stub9 = sinon.stub(serviceObj.remindableUsersLibrary, '_getUserDetails').callsFake(function(cb,recordData) {
                return cb(null,true);
            });
            let stub10 = sinon.stub(serviceObj, 'createCCBillInSystem').callsFake(function(cb,recordData,sagaSavedCCData, dbRecord) {
                return cb(null,{});
            });
            let stub11 = sinon.stub(serviceObj, 'sendNotification').callsFake(function(cb,recordData) {
                return cb(null,{});
            });
            let stub12 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'formatDataForDeletingNonPaytmCards').returns(recordToUpdate);
            let stub13 = sinon.stub(serviceObj.nonPaytmKafkaPublisher, 'publishData').callsFake(function (data,cb) {
                return cb("Error occured in publishdata");
            });
            
    
            serviceObj.createCCBillAndSendNotification(function (error) {
                expect(`${error}`).to.be.equal('ReferenceError: cb is not defined');
            }, recordData);
    
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(0);
            expect(stub3).to.have.callCount(0);
            expect(stub4).to.have.callCount(0);
            expect(stub5).to.have.callCount(0);
            expect(stub6).to.have.callCount(0);
            expect(stub7).to.have.callCount(0);
            expect(stub9).to.have.callCount(0);
            expect(stub10).to.have.callCount(0);
            expect(stub11).to.have.callCount(0);
            expect(stub12).to.have.callCount(0);
            expect(stub13).to.have.callCount(0);
    
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            stub7.restore();
            stub9.restore();
            stub10.restore();
            stub11.restore();
            stub12.restore();
            stub13.restore();
            done();
        });
        it("createCCBillAndSendNotification | error in publishData" , (done) => {
            let recordData = { "smsSenderID": "AD-HDFCBK", "smsDateTime": "*************", "dueDate": "2022-12-11", "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "" , "cId":"123456", "currentBillAmount": 112233,"billDueDate": MOMENT().add(12, 'days'), "currentMinBillAmount": 590,  "isReliable" : "1" };
            let customerId ="123456";
            let savedCardData = {"product":{}}
            let status = "SUCCESS";
            let type = 'MATCHING_MCN';
            let data = {};
            let dbRecord=[];
            let recordToUpdate={};
            let uniqueKey = '0_hdfc_visa'
    
            serviceObj.nonPaytmKafkaPublisher = new serviceObj.infraUtils.kafka.producer(); 
            serviceObj.nonPaytmKafkaPublisher.publishData = function(data,cb){};
            console.log("nonPaytmKafkaPublisher", serviceObj.nonPaytmKafkaPublisher);
    
            let stub1 = sinon.stub(serviceObj.paymentGatewayUtils, 'getCreditCardDataFromSaga').callsFake(function(cb,customerId) {
                return cb(null,savedCardData);
            });
            let stub2 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getProcessedSagaSavedCardsData').callsFake(function(cb,recordData, data) {
                return cb({status, type, data:savedCardData});
            });
            let stub4 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getUniqueKeyForSavedCardsData').returns(uniqueKey);
            let stub3 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'isPaytmFirstCCInSagaCCDetails').returns(false);
            let stub5 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getFinancialServicesPID').callsFake(function(cb,uniqueKey) {
                return cb(null,'1200763862');
            });
            let stub6 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'formatDataForNonPaytmCards').returns(recordToUpdate);
            let stub7 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getFormattedData').returns(recordToUpdate);
            let stub9 = sinon.stub(serviceObj.remindableUsersLibrary, '_getUserDetails').callsFake(function(cb,recordData) {
                return cb(null,true);
            });
            let stub10 = sinon.stub(serviceObj, 'createCCBillInSystem').callsFake(function(cb,recordData,sagaSavedCCData, dbRecord) {
                return cb(null,{});
            });
            let stub11 = sinon.stub(serviceObj, 'sendNotification').callsFake(function(cb,recordData) {
                return cb(null,{});
            });
            let stub12 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'formatDataForDeletingNonPaytmCards').returns(recordToUpdate);
            let stub13 = sinon.stub(serviceObj.nonPaytmKafkaPublisher, 'publishData').callsFake(function (data,cb) {
                return cb("Error occured in publishdata");
            });
            
    
            serviceObj.createCCBillAndSendNotification(function (error) {
                expect(error).to.be.equal(null);
            }, recordData);
    
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(1);
            expect(stub4).to.have.callCount(1);
            expect(stub5).to.have.callCount(1);
            expect(stub6).to.have.callCount(0);
            expect(stub7).to.have.callCount(1);
            expect(stub9).to.have.callCount(1);
            expect(stub10).to.have.callCount(1);
            expect(stub11).to.have.callCount(1);
            expect(stub12).to.have.callCount(1);
            expect(stub13).to.have.callCount(1);
    
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            stub7.restore();
            stub9.restore();
            stub10.restore();
            stub11.restore();
            stub12.restore();
            stub13.restore();
            done();
        });
        it("createCCBillAndSendNotification | error in createCCBillInSystem" , (done) => {
            let recordData = { "smsSenderID": "AD-HDFCBK", "smsDateTime": "*************", "dueDate": "2022-12-11", "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "" , "cId":"123456","currentBillAmount": 112233,"billDueDate": MOMENT().add(12, 'days'), "currentMinBillAmount": 590,  "isReliable" : "1" };
            let customerId ="123456";
            let savedCardData = {"product":{}}
            let status = "SUCCESS";
            let type = 'MATCHING_MCN';
            let data = {};
            let dbRecord=[];
            let recordToUpdate={};
            let uniqueKey = '0_hdfc_visa'
    
            serviceObj.nonPaytmKafkaPublisher = new serviceObj.infraUtils.kafka.producer(); 
            serviceObj.nonPaytmKafkaPublisher.publishData = function(data,cb){};
            console.log("nonPaytmKafkaPublisher", serviceObj.nonPaytmKafkaPublisher);
    
            let stub1 = sinon.stub(serviceObj.paymentGatewayUtils, 'getCreditCardDataFromSaga').callsFake(function(cb,customerId) {
                return cb(null,savedCardData);
            });
            let stub2 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getProcessedSagaSavedCardsData').callsFake(function(cb,recordData, data) {
                return cb({status, type, data:savedCardData});
            });
            let stub4 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getUniqueKeyForSavedCardsData').returns(uniqueKey);
            let stub3 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'isPaytmFirstCCInSagaCCDetails').returns(false);
            let stub5 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getFinancialServicesPID').callsFake(function(cb,uniqueKey) {
                return cb(null,'1200763862');
            });
            let stub6 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'formatDataForNonPaytmCards').returns(recordToUpdate);
            let stub7 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getFormattedData').returns(recordToUpdate);
            let stub9 = sinon.stub(serviceObj.remindableUsersLibrary, '_getUserDetails').callsFake(function(cb,recordData) {
                return cb(null,true);
            });
            let stub10 = sinon.stub(serviceObj, 'createCCBillInSystem').callsFake(function(cb,recordData,sagaSavedCCData, dbRecord) {
                return cb("Error in createCCBillInSystem");
            });
            let stub11 = sinon.stub(serviceObj, 'sendNotification').callsFake(function(cb,recordData) {
                return cb(null,{});
            });
            let stub12 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'formatDataForDeletingNonPaytmCards').returns(recordToUpdate);
            let stub13 = sinon.stub(serviceObj.nonPaytmKafkaPublisher, 'publishData').callsFake(function (data,cb) {
                return cb(null);
            });
            
    
            serviceObj.createCCBillAndSendNotification(function (error) {
                expect(error).to.be.equal("Error in createCCBillInSystem");
            }, recordData);
    
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(1);
            expect(stub4).to.have.callCount(1);
            expect(stub5).to.have.callCount(1);
            expect(stub6).to.have.callCount(0);
            expect(stub7).to.have.callCount(1);
            expect(stub9).to.have.callCount(1);
            expect(stub10).to.have.callCount(1);
            expect(stub11).to.have.callCount(0);
            expect(stub12).to.have.callCount(0);
            expect(stub13).to.have.callCount(0);
    
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            stub7.restore();
            stub9.restore();
            stub10.restore();
            stub11.restore();
            stub12.restore();
            stub13.restore();
            done();
        });
        it("createCCBillAndSendNotification | not required _getUserDetails" , (done) => {
            let recordData = { "smsSenderID": "AD-HDFCBK", "smsDateTime": "*************", "dueDate": "2022-12-11", "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "" , "cId":"123456","currentBillAmount": 112233,"billDueDate": MOMENT().add(12, 'days'), "currentMinBillAmount": 590,  "isReliable" : "1" };
            let customerId ="123456";
            let savedCardData = {"product":{}}
            let status = "SUCCESS";
            let type = 'MATCHING_MCN';
            let data = {};
            let dbRecord=[];
            let recordToUpdate={"customer_email":"abc.gmail.com","customer_mobile":123};
            let uniqueKey = '0_hdfc_visa'
    
            serviceObj.nonPaytmKafkaPublisher = new serviceObj.infraUtils.kafka.producer(); 
            serviceObj.nonPaytmKafkaPublisher.publishData = function(data,cb){};
            console.log("nonPaytmKafkaPublisher", serviceObj.nonPaytmKafkaPublisher);
    
            let stub1 = sinon.stub(serviceObj.paymentGatewayUtils, 'getCreditCardDataFromSaga').callsFake(function(cb,customerId) {
                return cb(null,savedCardData);
            });
            let stub2 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getProcessedSagaSavedCardsData').callsFake(function(cb,recordData, data) {
                return cb({status, type, data:savedCardData});
            });
            let stub4 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getUniqueKeyForSavedCardsData').returns(uniqueKey);
            let stub3 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'isPaytmFirstCCInSagaCCDetails').returns(false);
            let stub5 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getFinancialServicesPID').callsFake(function(cb,uniqueKey) {
                return cb(null,'1200763862');
            });
            let stub6 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'formatDataForNonPaytmCards').returns(recordToUpdate);
            let stub7 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getFormattedData').returns(recordToUpdate);
            let stub9 = sinon.stub(serviceObj.remindableUsersLibrary, '_getUserDetails').callsFake(function(cb,recordData) {
                return cb('Error in _getUserDetails');
            });
            let stub10 = sinon.stub(serviceObj, 'createCCBillInSystem').callsFake(function(cb,recordData,sagaSavedCCData, dbRecord) {
                return cb(null);
            });
            let stub11 = sinon.stub(serviceObj, 'sendNotification').callsFake(function(cb,recordData) {
                return cb(null,{});
            });
            let stub12 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'formatDataForDeletingNonPaytmCards').returns(recordToUpdate);
            let stub13 = sinon.stub(serviceObj.nonPaytmKafkaPublisher, 'publishData').callsFake(function (data,cb) {
                return cb(null);
            });
            
    
            serviceObj.createCCBillAndSendNotification(function (error) {
                expect(error).to.be.equal(null);
            }, recordData);
    
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(1);
            expect(stub4).to.have.callCount(1);
            expect(stub5).to.have.callCount(1);
            expect(stub6).to.have.callCount(0);
            expect(stub7).to.have.callCount(1);
            expect(stub9).to.have.callCount(0);
            expect(stub10).to.have.callCount(1);
            expect(stub11).to.have.callCount(1);
            expect(stub12).to.have.callCount(1);
            expect(stub13).to.have.callCount(1);
    
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            stub7.restore();
            stub9.restore();
            stub10.restore();
            stub11.restore();
            stub12.restore();
            stub13.restore();
            done();
        });
        it("createCCBillAndSendNotification | error in _getUserDetails" , (done) => {
            let recordData = { "smsSenderID": "AD-HDFCBK", "smsDateTime": "*************", "dueDate": "2022-12-11", "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "" , "cId":"123456","currentBillAmount": 112233,"billDueDate": MOMENT().add(12, 'days'), "currentMinBillAmount": 590,  "isReliable" : "1" };
            let customerId ="123456";
            let savedCardData = {"product":{}}
            let status = "SUCCESS";
            let type = 'MATCHING_MCN';
            let data = {};
            let dbRecord=[];
            let recordToUpdate={}
            let uniqueKey = '0_hdfc_visa'
    
            serviceObj.nonPaytmKafkaPublisher = new serviceObj.infraUtils.kafka.producer(); 
            serviceObj.nonPaytmKafkaPublisher.publishData = function(data,cb){};
            console.log("nonPaytmKafkaPublisher", serviceObj.nonPaytmKafkaPublisher);
    
            let stub1 = sinon.stub(serviceObj.paymentGatewayUtils, 'getCreditCardDataFromSaga').callsFake(function(cb,customerId) {
                return cb(null,savedCardData);
            });
            let stub2 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getProcessedSagaSavedCardsData').callsFake(function(cb,recordData, data) {
                return cb({status, type, data:savedCardData});
            });
            let stub4 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getUniqueKeyForSavedCardsData').returns(uniqueKey);
            let stub3 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'isPaytmFirstCCInSagaCCDetails').returns(false);
            let stub5 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getFinancialServicesPID').callsFake(function(cb,uniqueKey) {
                return cb(null,'1200763862');
            });
            let stub6 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'formatDataForNonPaytmCards').returns(recordToUpdate);
            let stub7 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getFormattedData').returns(recordToUpdate);
            let stub9 = sinon.stub(serviceObj.remindableUsersLibrary, '_getUserDetails').callsFake(function(cb,recordData) {
                return cb('Error in _getUserDetails');
            });
            let stub10 = sinon.stub(serviceObj, 'createCCBillInSystem').callsFake(function(cb,recordData,sagaSavedCCData, dbRecord) {
                return cb(null);
            });
            let stub11 = sinon.stub(serviceObj, 'sendNotification').callsFake(function(cb,recordData) {
                return cb(null,{});
            });
            let stub12 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'formatDataForDeletingNonPaytmCards').returns(recordToUpdate);
            let stub13 = sinon.stub(serviceObj.nonPaytmKafkaPublisher, 'publishData').callsFake(function (data,cb) {
                return cb(null);
            });
            
    
            serviceObj.createCCBillAndSendNotification(function (error) {
                expect(error).to.be.equal(null);
            }, recordData);
    
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(1);
            expect(stub4).to.have.callCount(1);
            expect(stub5).to.have.callCount(1);
            expect(stub6).to.have.callCount(0);
            expect(stub7).to.have.callCount(1);
            expect(stub9).to.have.callCount(1);
            expect(stub10).to.have.callCount(1);
            expect(stub11).to.have.callCount(1);
            expect(stub12).to.have.callCount(1);
            expect(stub13).to.have.callCount(1);
    
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            stub7.restore();
            stub9.restore();
            stub10.restore();
            stub11.restore();
            stub12.restore();
            stub13.restore();
            done();
        });
        it("createCCBillAndSendNotification | recent data < 1" , (done) => {
            let recordData = { "smsSenderID": "AD-HDFCBK", "smsDateTime": "*************", "dueDate": "2022-12-11", "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "" , "cId":"123456","currentBillAmount": 112233,"billDueDate": MOMENT().add(12, 'days'), "currentMinBillAmount": 590,  "isReliable" : "1" };
            let customerId ="123456";
            let savedCardData = {"product":{}}
            let status = "SUCCESS";
            let type = 'MATCHING_MCN';
            let data = {};
            let dbRecord=[];
            let recordToUpdate={}
            let uniqueKey = '0_hdfc_visa'
    
            serviceObj.nonPaytmKafkaPublisher = new serviceObj.infraUtils.kafka.producer(); 
            serviceObj.nonPaytmKafkaPublisher.publishData = function(data,cb){};
            console.log("nonPaytmKafkaPublisher", serviceObj.nonPaytmKafkaPublisher);
    
            let stub1 = sinon.stub(serviceObj.paymentGatewayUtils, 'getCreditCardDataFromSaga').callsFake(function(cb,customerId) {
                return cb(null,savedCardData);
            });
            let stub2 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getProcessedSagaSavedCardsData').callsFake(function(cb,recordData, data) {
                return cb({status, type, data:savedCardData});
            });
            let stub4 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getUniqueKeyForSavedCardsData').returns(uniqueKey);
            let stub3 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'isPaytmFirstCCInSagaCCDetails').returns(false);
            let stub5 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getFinancialServicesPID').callsFake(function(cb,uniqueKey) {
                return cb(null,'1200763862');
            });
            let stub6 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'formatDataForNonPaytmCards').returns(recordToUpdate);
            let stub7 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getFormattedData').returns(recordToUpdate);
            let stub9 = sinon.stub(serviceObj.remindableUsersLibrary, '_getUserDetails').callsFake(function(cb,recordData) {
                return cb(null,true);
            });
            let stub10 = sinon.stub(serviceObj, 'createCCBillInSystem').callsFake(function(cb,recordData,sagaSavedCCData, dbRecord) {
                return cb(null);
            });
            let stub11 = sinon.stub(serviceObj, 'sendNotification').callsFake(function(cb,recordData) {
                return cb(null,{});
            });
            let stub12 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'formatDataForDeletingNonPaytmCards').returns(recordToUpdate);
            let stub13 = sinon.stub(serviceObj.nonPaytmKafkaPublisher, 'publishData').callsFake(function (data,cb) {
                return cb(null);
            });
            
    
            serviceObj.createCCBillAndSendNotification(function (error) {
                expect(error).to.be.equal(null);
            }, recordData);
    
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(1);
            expect(stub4).to.have.callCount(1);
            expect(stub5).to.have.callCount(1);
            expect(stub6).to.have.callCount(0);
            expect(stub7).to.have.callCount(1);
            expect(stub9).to.have.callCount(1);
            expect(stub10).to.have.callCount(1);
            expect(stub11).to.have.callCount(1);
            expect(stub12).to.have.callCount(1);
            expect(stub13).to.have.callCount(1);
    
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            stub7.restore();
            stub9.restore();
            stub10.restore();
            stub11.restore();
            stub12.restore();
            stub13.restore();
            done();
        });
        it("createCCBillAndSendNotification | checkUniquenessInRecents error" , (done) => {
            let recordData = { "smsSenderID": "AD-HDFCBK", "smsDateTime": "*************", "dueDate": "2022-12-11", "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "" , "cId":"123456","currentBillAmount": 112233,"billDueDate": MOMENT().add(12, 'days'), "currentMinBillAmount": 590,  "isReliable" : "1" };
            let customerId ="123456";
            let savedCardData = {"product":{}}
            let status = "SUCCESS";
            let type = 'MATCHING_MCN';
            let data = {};
            let dbRecord={}
            let recordToUpdate={}
            let uniqueKey = '0_hdfc_visa'
    
            serviceObj.nonPaytmKafkaPublisher = new serviceObj.infraUtils.kafka.producer(); 
            serviceObj.nonPaytmKafkaPublisher.publishData = function(data,cb){};
            console.log("nonPaytmKafkaPublisher", serviceObj.nonPaytmKafkaPublisher);
    
            let stub1 = sinon.stub(serviceObj.paymentGatewayUtils, 'getCreditCardDataFromSaga').callsFake(function(cb,customerId) {
                return cb(null,savedCardData);
            });
            let stub2 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getProcessedSagaSavedCardsData').callsFake(function(cb,recordData, data) {
                return cb({status, type, data:savedCardData});
            });
            let stub4 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getUniqueKeyForSavedCardsData').returns(uniqueKey);
            let stub3 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'isPaytmFirstCCInSagaCCDetails').returns(false);
            let stub5 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getFinancialServicesPID').callsFake(function(cb,uniqueKey) {
                return cb(null,'1200763862');
            });
            let stub6 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'formatDataForNonPaytmCards').returns(recordToUpdate);
            let stub7 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getFormattedData').returns(recordToUpdate);
            let stub9 = sinon.stub(serviceObj.remindableUsersLibrary, '_getUserDetails').callsFake(function(cb,recordData) {
                return cb(null,true);
            });
            let stub10 = sinon.stub(serviceObj, 'createCCBillInSystem').callsFake(function(cb,recordData,sagaSavedCCData, dbRecord) {
                return cb(null);
            });
            let stub11 = sinon.stub(serviceObj, 'sendNotification').returns(null);
            let stub12 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'formatDataForDeletingNonPaytmCards').returns(recordToUpdate);
            let stub13 = sinon.stub(serviceObj.nonPaytmKafkaPublisher, 'publishData').callsFake(function (data,cb) {
                return cb(null);
            });
            
    
            serviceObj.createCCBillAndSendNotification(function (error) {
            }, recordData);
    
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(1);
            expect(stub4).to.have.callCount(1);
            expect(stub5).to.have.callCount(1);
            expect(stub6).to.have.callCount(0);
            expect(stub7).to.have.callCount(1);
            expect(stub9).to.have.callCount(1);
            expect(stub10).to.have.callCount(1);
            expect(stub11).to.have.callCount(1);
            expect(stub12).to.have.callCount(0);
            expect(stub13).to.have.callCount(0);
    
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            stub7.restore();
            stub9.restore();
            stub10.restore();
            stub11.restore();
            stub12.restore();
            stub13.restore();
            done();
        });
        it("createCCBillAndSendNotification | product_id data doesnt exist" , (done) => {
            let recordData = { "smsSenderID": "AD-HDFCBK", "smsDateTime": "*************", "dueDate": "2022-12-11", "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "" , "cId":"123456","currentBillAmount": 112233,"billDueDate": MOMENT().add(12, 'days'), "currentMinBillAmount": 590,  "isReliable" : "1" };
            let customerId ="123456";
            let savedCardData = {"product":{}}
            let status = "SUCCESS";
            let type = 'MATCHING_MCN';
            let data = {};
            let dbRecord={}
            let recordToUpdate={}
            let uniqueKey = '0_hdfc_visa'
    
            serviceObj.nonPaytmKafkaPublisher = new serviceObj.infraUtils.kafka.producer(); 
            serviceObj.nonPaytmKafkaPublisher.publishData = function(data,cb){};
            console.log("nonPaytmKafkaPublisher", serviceObj.nonPaytmKafkaPublisher);
    
            let stub1 = sinon.stub(serviceObj.paymentGatewayUtils, 'getCreditCardDataFromSaga').callsFake(function(cb,customerId) {
                return cb(null,savedCardData);
            });
            let stub2 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getProcessedSagaSavedCardsData').callsFake(function(cb,recordData, data) {
                return cb({status, type, data:savedCardData});
            });
            let stub4 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getUniqueKeyForSavedCardsData').returns(uniqueKey);
            let stub3 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'isPaytmFirstCCInSagaCCDetails').returns(false);
            let stub5 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getFinancialServicesPID').callsFake(function(cb,uniqueKey) {
                return cb(null,'12345');
            });
            let stub6 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'formatDataForNonPaytmCards').returns(recordToUpdate);
            let stub7 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getFormattedData').returns(recordToUpdate);
            let stub9 = sinon.stub(serviceObj.remindableUsersLibrary, '_getUserDetails').callsFake(function(cb,recordData) {
                return cb(null,true);
            });
            let stub10 = sinon.stub(serviceObj, 'createCCBillInSystem').callsFake(function(cb,recordData,sagaSavedCCData, dbRecord) {
                return cb(null);
            });
            let stub11 = sinon.stub(serviceObj, 'sendNotification').returns(null);
            let stub12 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'formatDataForDeletingNonPaytmCards').returns(recordToUpdate);
            let stub13 = sinon.stub(serviceObj.nonPaytmKafkaPublisher, 'publishData').callsFake(function (data,cb) {
                return cb(null);
            });
            
    
            serviceObj.createCCBillAndSendNotification(function (error) {
                expect(error).to.be.equal("CVR data not exists for productId:12345");
            }, recordData);
    
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(1);
            expect(stub4).to.have.callCount(1);
            expect(stub5).to.have.callCount(1);
            expect(stub6).to.have.callCount(0);
            expect(stub7).to.have.callCount(0);
            expect(stub9).to.have.callCount(0);
            expect(stub10).to.have.callCount(0);
            expect(stub11).to.have.callCount(0);
            expect(stub12).to.have.callCount(0);
            expect(stub13).to.have.callCount(0);
    
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            stub7.restore();
            stub9.restore();
            stub10.restore();
            stub11.restore();
            stub12.restore();
            stub13.restore();
            done();
        });
        it("createCCBillAndSendNotification | getFinancialServicesPID ERROR" , (done) => {
            let recordData = { "smsSenderID": "AD-HDFCBK", "smsDateTime": "*************", "dueDate": "2022-12-11", "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "" , "cId":"123456","currentBillAmount": 112233,"billDueDate": MOMENT().add(12, 'days'), "currentMinBillAmount": 590,  "isReliable" : "1" };
            let customerId ="123456";
            let savedCardData = {"product":{}}
            let status = "SUCCESS";
            let type = 'MATCHING_MCN';
            let data = {};
            let dbRecord={}
            let recordToUpdate={}
            let uniqueKey = '0_hdfc_visa'
    
            serviceObj.nonPaytmKafkaPublisher = new serviceObj.infraUtils.kafka.producer(); 
            serviceObj.nonPaytmKafkaPublisher.publishData = function(data,cb){};
            console.log("nonPaytmKafkaPublisher", serviceObj.nonPaytmKafkaPublisher);
    
            let stub1 = sinon.stub(serviceObj.paymentGatewayUtils, 'getCreditCardDataFromSaga').callsFake(function(cb,customerId) {
                return cb(null,savedCardData);
            });
            let stub2 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getProcessedSagaSavedCardsData').callsFake(function(cb,recordData, data) {
                return cb({status, type, data:savedCardData});
            });
            let stub4 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getUniqueKeyForSavedCardsData').returns(uniqueKey);
            let stub3 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'isPaytmFirstCCInSagaCCDetails').returns(false);
            let stub5 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getFinancialServicesPID').callsFake(function(cb,uniqueKey) {
                return cb("Error in picking pid");
            });
            let stub6 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'formatDataForNonPaytmCards').returns(recordToUpdate);
            let stub7 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getFormattedData').returns(recordToUpdate);
            let stub9 = sinon.stub(serviceObj.remindableUsersLibrary, '_getUserDetails').callsFake(function(cb,recordData) {
                return cb(null,true);
            });
            let stub10 = sinon.stub(serviceObj, 'createCCBillInSystem').callsFake(function(cb,recordData,sagaSavedCCData, dbRecord) {
                return cb(null);
            });
            let stub11 = sinon.stub(serviceObj, 'sendNotification').returns(null);
            let stub12 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'formatDataForDeletingNonPaytmCards').returns(recordToUpdate);
            let stub13 = sinon.stub(serviceObj.nonPaytmKafkaPublisher, 'publishData').callsFake(function (data,cb) {
                return cb(null);
            });
            
    
            serviceObj.createCCBillAndSendNotification(function (error) {
                expect(error).to.be.equal("Error in picking pid");
            }, recordData);
    
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(1);
            expect(stub4).to.have.callCount(1);
            expect(stub5).to.have.callCount(1);
            expect(stub6).to.have.callCount(0);
            expect(stub7).to.have.callCount(0);
            expect(stub9).to.have.callCount(0);
            expect(stub10).to.have.callCount(0);
            expect(stub11).to.have.callCount(0);
            expect(stub12).to.have.callCount(0);
            expect(stub13).to.have.callCount(0);
    
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            stub7.restore();
            stub9.restore();
            stub10.restore();
            stub11.restore();
            stub12.restore();
            stub13.restore();
            done();
        });
        it.skip("createCCBillAndSendNotification | getProcessedSagaSavedCardsData STATUS:ERROR" , (done) => {
            let recordData = { "smsSenderID": "AD-HDFCBK", "smsDateTime": "*************", "dueDate": "2022-12-11", "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "" , "cId":"123456",  "isReliable" : "1" };
            let customerId ="123456";
            let savedCardData = {"product":{}}
            let status = "ERROR";
            let type = 'PROCESSING_FAILURE';
            let data = {};
            let dbRecord={}
            let recordToUpdate={}
            let uniqueKey = '0_hdfc_visa'
    
            serviceObj.nonPaytmKafkaPublisher = new serviceObj.infraUtils.kafka.producer(); 
            serviceObj.nonPaytmKafkaPublisher.publishData = function(data,cb){};
            console.log("nonPaytmKafkaPublisher", serviceObj.nonPaytmKafkaPublisher);
    
            let stub1 = sinon.stub(serviceObj.paymentGatewayUtils, 'getCreditCardDataFromSaga').callsFake(function(cb,customerId) {
                return cb(null,savedCardData);
            });
            let stub2 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getProcessedSagaSavedCardsData').callsFake(function(cb,recordData, data) {
                return cb({status, type, data:savedCardData});
            });
            let stub4 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getUniqueKeyForSavedCardsData').returns(uniqueKey);
            let stub3 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'isPaytmFirstCCInSagaCCDetails').returns(false);
            let stub5 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getFinancialServicesPID').callsFake(function(cb,uniqueKey) {
                return cb(null, '1200763862');
            });
            let stub6 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'formatDataForNonPaytmCards').returns(recordToUpdate);
            let stub7 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getFormattedData').returns(recordToUpdate);
            let stub9 = sinon.stub(serviceObj.remindableUsersLibrary, '_getUserDetails').callsFake(function(cb,recordData) {
                return cb(null,true);
            });
            let stub10 = sinon.stub(serviceObj, 'createCCBillInSystem').callsFake(function(cb,recordData,sagaSavedCCData, dbRecord) {
                return cb(null);
            });
            let stub11 = sinon.stub(serviceObj, 'sendNotification').returns(null);
            let stub12 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'formatDataForDeletingNonPaytmCards').returns(recordToUpdate);
            let stub13 = sinon.stub(serviceObj.nonPaytmKafkaPublisher, 'publishData').callsFake(function (data,cb) {
                return cb(null);
            });
            
    
            serviceObj.createCCBillAndSendNotification(function (error) {
                expect(error).to.be.equal('PROCESSING_FAILURE');
            }, recordData);
    
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(0);
            expect(stub4).to.have.callCount(0);
            expect(stub5).to.have.callCount(0);
            expect(stub6).to.have.callCount(0);
            expect(stub7).to.have.callCount(0);
            expect(stub9).to.have.callCount(0);
            expect(stub10).to.have.callCount(0);
            expect(stub11).to.have.callCount(0);
            expect(stub12).to.have.callCount(0);
            expect(stub13).to.have.callCount(0);
    
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            stub7.restore();
            stub9.restore();
            stub10.restore();
            stub11.restore();
            stub12.restore();
            stub13.restore();
            done();
        });
        it("createCCBillAndSendNotification | Error in getCreditCardDataFromSaga" , (done) => {
            let recordData = { "smsSenderID": "AD-HDFCBK", "smsDateTime": "*************", "dueDate": "2022-12-11", "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "" , "cId":"123456","currentBillAmount": 112233,"billDueDate": MOMENT().add(12, 'days'), "currentMinBillAmount": 590,  "isReliable" : "1" };
            let customerId ="123456";
            let savedCardData = {"product":{}}
            let status = "SUCCESS";
            let type = 'MATCHING_MCN';
            let data = {};
            let dbRecord={}
            let recordToUpdate={}
            let uniqueKey = '0_hdfc_visa'
    
            serviceObj.nonPaytmKafkaPublisher = new serviceObj.infraUtils.kafka.producer(); 
            serviceObj.nonPaytmKafkaPublisher.publishData = function(data,cb){};
            console.log("nonPaytmKafkaPublisher", serviceObj.nonPaytmKafkaPublisher);
    
            let stub1 = sinon.stub(serviceObj.paymentGatewayUtils, 'getCreditCardDataFromSaga').callsFake(function(cb,customerId) {
                return cb("Error in saved card api");
            });
            let stub2 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getProcessedSagaSavedCardsData').callsFake(function(cb,recordData, data) {
                return cb({status, type, data:savedCardData});
            });
            let stub4 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getUniqueKeyForSavedCardsData').returns(uniqueKey);
            let stub3 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'isPaytmFirstCCInSagaCCDetails').returns(false);
            let stub5 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getFinancialServicesPID').callsFake(function(cb,uniqueKey) {
                return cb(null, '1200763862');
            });
            let stub6 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'formatDataForNonPaytmCards').returns(recordToUpdate);
            let stub7 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getFormattedData').returns(recordToUpdate);
            let stub9 = sinon.stub(serviceObj.remindableUsersLibrary, '_getUserDetails').callsFake(function(cb,recordData) {
                return cb(null,true);
            });
            let stub10 = sinon.stub(serviceObj, 'createCCBillInSystem').callsFake(function(cb,recordData,sagaSavedCCData, dbRecord) {
                return cb(null);
            });
            let stub11 = sinon.stub(serviceObj, 'sendNotification').returns(null);
            let stub12 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'formatDataForDeletingNonPaytmCards').returns(recordToUpdate);
            let stub13 = sinon.stub(serviceObj.nonPaytmKafkaPublisher, 'publishData').callsFake(function (data,cb) {
                return cb(null);
            });
            
    
            serviceObj.createCCBillAndSendNotification(function (error) {
                expect(error).to.be.equal("Error in saved card api");
            }, recordData);
    
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(0);
            expect(stub3).to.have.callCount(0);
            expect(stub4).to.have.callCount(0);
            expect(stub5).to.have.callCount(0);
            expect(stub6).to.have.callCount(0);
            expect(stub7).to.have.callCount(0);
            expect(stub9).to.have.callCount(0);
            expect(stub10).to.have.callCount(0);
            expect(stub11).to.have.callCount(0);
            expect(stub12).to.have.callCount(0);
            expect(stub13).to.have.callCount(0);
    
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            stub7.restore();
            stub9.restore();
            stub10.restore();
            stub11.restore();
            stub12.restore();
            stub13.restore();
            done();
        });
        it("createCCBillAndSendNotification | valid data | paytm data" , (done) => {
            let recordData = { "smsSenderID": "AD-HDFCBK", "smsDateTime": "*************", "dueDate": "2022-12-11", "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "" , "cId":"123456", "currentBillAmount": 112233,"billDueDate": MOMENT().add(12, 'days'), "currentMinBillAmount": 590, "isReliable" : "1" };
            let customerId ="123456";
            let savedCardData = {"product":{}}
            let status = "SUCCESS";
            let type = 'MATCHING_MCN';
            let data = {};
            let dbRecord={}
            let recordToUpdate={}
            let uniqueKey = '0_hdfc_visa'
    
            serviceObj.nonPaytmKafkaPublisher = new serviceObj.infraUtils.kafka.producer(); 
            serviceObj.nonPaytmKafkaPublisher.publishData = function(data,cb){};
            console.log("nonPaytmKafkaPublisher", serviceObj.nonPaytmKafkaPublisher);
    
            let stub1 = sinon.stub(serviceObj.paymentGatewayUtils, 'getCreditCardDataFromSaga').callsFake(function(cb,customerId) {
                return cb(null,savedCardData);
            });
            let stub2 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getProcessedSagaSavedCardsData').callsFake(function(cb,recordData, data) {
                return cb({status, type, data:savedCardData});
            });
            let stub4 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getUniqueKeyForSavedCardsData').returns(uniqueKey);
            let stub3 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'isPaytmFirstCCInSagaCCDetails').returns(false);
            let stub5 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getFinancialServicesPID').callsFake(function(cb,uniqueKey) {
                return cb(null, '1200763862');
            });
            let stub6 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'formatDataForNonPaytmCards').returns(recordToUpdate);
            let stub7 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getFormattedData').returns(recordToUpdate);
            let stub9 = sinon.stub(serviceObj.remindableUsersLibrary, '_getUserDetails').callsFake(function(cb,recordData) {
                return cb(null,true);
            });
            let stub10 = sinon.stub(serviceObj, 'createCCBillInSystem').callsFake(function(cb,recordData,sagaSavedCCData, dbRecord) {
                return cb(null);
            });
            let stub11 = sinon.stub(serviceObj, 'sendNotification').callsFake(function(cb,recordData) {
                return cb(null,{});
            });
            let stub12 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'formatDataForDeletingNonPaytmCards').returns(recordToUpdate);
            let stub13 = sinon.stub(serviceObj.nonPaytmKafkaPublisher, 'publishData').callsFake(function (data,cb) {
                return cb(null);
            });
            
    
            serviceObj.createCCBillAndSendNotification(function (error) {
                expect(error).to.be.equal(null);
            }, recordData);
    
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(1);
            expect(stub4).to.have.callCount(1);
            expect(stub5).to.have.callCount(1);
            expect(stub6).to.have.callCount(0);
            expect(stub7).to.have.callCount(1);
            expect(stub9).to.have.callCount(1);
            expect(stub10).to.have.callCount(1);
            expect(stub11).to.have.callCount(1);
            expect(stub12).to.have.callCount(1);
            expect(stub13).to.have.callCount(1);
    
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            stub7.restore();
            stub9.restore();
            stub10.restore();
            stub11.restore();
            stub12.restore();
            stub13.restore();
            done();
        });
        it("createCCBillAndSendNotification | valid data | nonPaytm data" , (done) => {
            let recordData = { "smsSenderID": "AD-HDFCBK", "smsDateTime": "*************", "dueDate": "2022-12-11", "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "" , "cId":"123456", "currentBillAmount": 112233,"billDueDate": MOMENT().add(12, 'days'), "currentMinBillAmount": 590, "isReliable" : "1" };
            let customerId ="123456";
            let savedCardData = {}
            let status = "SUCCESS";
            let type = 'NO_MATCHING_MCN';
            let data = {};
            data.nonPaytmCreditCard = {};
            let dbRecord={}
            let recordToUpdate={}
            let uniqueKey = '0_hdfc_visa'
    
            serviceObj.nonPaytmKafkaPublisher = new serviceObj.infraUtils.kafka.producer(); 
            serviceObj.nonPaytmKafkaPublisher.publishData = function(data,cb){};
            console.log("nonPaytmKafkaPublisher", serviceObj.nonPaytmKafkaPublisher);
    
            let stub1 = sinon.stub(serviceObj.paymentGatewayUtils, 'getCreditCardDataFromSaga').callsFake(function(cb,customerId) {
                return cb(null,savedCardData);
            });
            let stub2 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getProcessedSagaSavedCardsData').callsFake(function(cb,recordData, sagaSavedCardsData) {
                return cb({status, type, data});
            });
            let stub3 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'isPaytmFirstCCInSagaCCDetails').returns(false);
            let stub4 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getUniqueKeyForSavedCardsData').returns(uniqueKey);
            let stub5 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getFinancialServicesPID').callsFake(function(cb,uniqueKey) {
                return cb(null, '1200763862');
            });
            let stub6 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'formatDataForNonPaytmCards').returns(recordToUpdate);
            let stub7 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'getFormattedData').returns(recordToUpdate);
            let stub9 = sinon.stub(serviceObj.remindableUsersLibrary, '_getUserDetails').returns(recordToUpdate);
            let stub10 = sinon.stub(serviceObj, 'createCCBillInSystem').returns(recordToUpdate);
            let stub11 = sinon.stub(serviceObj, 'sendNotification').returns(recordToUpdate);
            let stub12 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, 'formatDataForDeletingNonPaytmCards').returns(recordToUpdate);
            let stub13 = sinon.stub(serviceObj.nonPaytmKafkaPublisher, 'publishData').callsFake(function (data,cb) {
                return cb(null);
            });
            
    
            serviceObj.createCCBillAndSendNotification(function (error) {
                expect(error).to.be.equal(null);
            }, recordData);
    
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(0);
            expect(stub4).to.have.callCount(1);
            expect(stub5).to.have.callCount(1);
            expect(stub6).to.have.callCount(1);
            expect(stub7).to.have.callCount(0);
            expect(stub9).to.have.callCount(0);
            expect(stub10).to.have.callCount(0);
            expect(stub11).to.have.callCount(0);
            expect(stub12).to.have.callCount(0);
            expect(stub13).to.have.callCount(1);
    
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            stub7.restore();
            stub9.restore();
            stub10.restore();
            stub11.restore();
            stub12.restore();
            stub13.restore();
            done();
        });
    
    
       
    
    
        it("parseAmount | Valid Record | Integer - Rs.590", () => {
            expect(serviceObj.parseAmount("Rs.590")).to.be.equal(590);
        });
        it("parseAmount | Valid Record | Float - Rs.590.78", () => {
            expect(serviceObj.parseAmount("Rs.590.78")).to.be.equal(590.78);
        });
        it("parseAmount | Valid Record | Amount negative with whitespaces with multiple dots -> Rs   ....-1223.34", () => {
            expect(serviceObj.parseAmount("Rs   ....-1223.34")).to.be.equal(-1223.34);
        });
        it("parseAmount | Valid Record | Amount negative with whitespaces with multiple dots -> Rs   ....+1223.34", () => {
            expect(serviceObj.parseAmount("Rs   ....+1223.34")).to.be.equal(1223.34);
        });
        it("parseAmount | Valid Record | zero amount -> Rs.0", () => {
            expect(serviceObj.parseAmount("Rs.0")).to.be.equal(0);
        });
        it("parseAmount | Valid Record | without Rs. string", () => {
            expect(serviceObj.parseAmount("590")).to.be.equal(590);
        });
        it("parseAmount | Valid Record | without Rs. string 1", () => {
            expect(serviceObj.parseAmount("590.00")).to.be.equal(590);
        });
        it("parseAmount | Valid Record | without Rs. string 2", () => {
            expect(serviceObj.parseAmount("590.50")).to.be.equal(590.5);
        });
        it("parseAmount | Valid Record | without Rs. string", () => {
            expect(serviceObj.parseAmount("-590")).to.be.equal(-590);
        });
        it("parseAmount | Valid Record | as Number", () => {
            expect(serviceObj.parseAmount(590)).to.be.equal(590);
        });
        it("parseAmount | Valid Record | as Number", () => {
            expect(serviceObj.parseAmount(590.67)).to.be.equal(590.67);
        });
        it("parseAmount | InValid Record | as null", () => {
            expect(serviceObj.parseAmount(null)).to.be.equal(null);
        });
        it("parseAmount | InValid Record | as normal string", () => {
            expect(serviceObj.parseAmount("amount")).to.be.equal(null);
        });
    
        //Just for sake of coverage and to check record processing pipeline behaving as expected
        // it("processBatch | Valid Record for coverage check", (done) => {
        //     let recordData = [{ value: JSON.stringify({ "data" : [{"smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "dueDate": "2022-12-11", "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "1234", "mId": "", "cId": "1000314176", "isReliable" : "1"}] }) }];
        //     serviceObj.processBatch(recordData, done);
        // });
       
    });
    
    describe("Module publisher:: SMS PARSING Consumer :: CT tests", function () {
        let serviceObj;
    
        let data, record;
        before(function () {
            STARTUP_MOCK.init(function(error, options){
                options.config = options.config || {};
                options.config.ENCRYPTION_CONFIG = options.config.ENCRYPTION_CONFIG || {};
                options.config.ENCRYPTION_CONFIG.DEFAULT = options.config.ENCRYPTION_CONFIG.DEFAULT || {};
                options.config.ENCRYPTION_CONFIG.DEFAULT.IV = '68e232d2639555a0cf08aaed9d50a025'; //adding staging keys as mock config
                options.config.ENCRYPTION_CONFIG.DEFAULT.ENCRYPTION_KEY = 'vOVH6sdmpNWjRRIqCc7rdxs01lwHzfr3'; 
                serviceObj = new SmsParsingCCBills(options);
                done();
            });
        });
    
    //     it("publishCtAndPFCCEvents function | check function calls", (done) => {
    //         let recordData = JSON.parse(JSON.stringify({ "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "dueDate": "2022-12-11", "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "", "cId": "**********", "isReliable" : "1" }));
    //         serviceObj.validateAndProcessRecord(recordData, function(error,data){
    //             record=data;
    //             let cb = sinon.spy();
    
    //             serviceObj.ctKafkaPublisher = {
    //                 publishData : () => {
    //                     return cb(null, data)
    //                 }
    //             }
    //             serviceObj.paytmFirstKafkaPublisher = {
    //                 publishData : () => {
    //                     return cb(null,data)
    //                 }
    //             }
    //             let stub1 = sinon.stub(serviceObj.commonLib, 'getRetailerData').yields(null, 1)
    //             let stub2 = sinon.stub(serviceObj.commonLib, 'getCvrData').yields(null, 'abc.jpg')
    //             let stub3 = sinon.stub(serviceObj.reminderUtils, 'createCTPipelinePayload',).returns({});
        
    //             serviceObj.publishCtAndPFCCEvents(cb, record);
    //             stub1.restore();
    //             stub2.restore();
    //             stub3.restore();
        
    //             expect(stub1).to.have.callCount(1)
    //             expect(stub2).to.have.callCount(1)
    //             expect(stub3).to.have.callCount(1)
    //             expect(cb).to.have.been.calledWith(null)
    //             return done();
    //         })
    //     });
    //     it("publishCtAndPFCCEvents function | check function calls", (done) => {
    //         let recordData = JSON.parse(JSON.stringify({ "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "dueDate": "2022-12-11", "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "", "cId": "**********", "isReliable" : "1" }));
    //             record=data;
    
    //             serviceObj.paytmFirstKafkaPublisher = new serviceObj.infraUtils.kafka.producer(); 
    //             serviceObj.paytmFirstKafkaPublisher.publishData = function(data,cb){};
    //             serviceObj.ctKafkaPublisher = new serviceObj.infraUtils.kafka.producer(); 
    //             serviceObj.ctKafkaPublisher.publishData = function(data,cb){};
    
    //             let stub1 = sinon.stub(serviceObj.commonLib, 'getRetailerData').yields(null, 1)
    //             let stub2 = sinon.stub(serviceObj.commonLib, 'getCvrData').yields(null, 'abc.jpg')
    //             let stub3 = sinon.stub(serviceObj.reminderUtils, 'createCTPipelinePayload',).returns({});
    //             let stub4 = sinon.stub(serviceObj.ctKafkaPublisher, 'publishData').callsFake(function(recordData,cb){
    //                 return cb(null);
    //             });
    //             let stub5 = sinon.stub(serviceObj.paytmFirstKafkaPublisher, 'publishData',).callsFake(function(recordData,cb){
    //                 return cb(null);
    //             });
    
    //             serviceObj.publishCtAndPFCCEvents(function(error,result){
    //             expect(stub1).to.have.callCount(1)
    //             expect(stub2).to.have.callCount(1)
    //             expect(stub3).to.have.callCount(1)
    //             stub1.restore();
    //             stub2.restore();
    //             stub3.restore();
    //             return done();
    //             }, record);
                
    //     });
    //     it("publishCtAndPFCCEvents function | check function calls", (done) => {
    //         let recordData = JSON.parse(JSON.stringify({ "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "dueDate": "2022-12-11", "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "", "cId": "**********", "isReliable" : "1" }));
    //             record=data;
    
    //             serviceObj.paytmFirstKafkaPublisher = new serviceObj.infraUtils.kafka.producer(); 
    //             serviceObj.paytmFirstKafkaPublisher.publishData = function(data,cb){};
    //             serviceObj.ctKafkaPublisher = new serviceObj.infraUtils.kafka.producer(); 
    //             serviceObj.ctKafkaPublisher.publishData = function(data,cb){};
    
    //             let stub1 = sinon.stub(serviceObj.commonLib, 'getRetailerData').yields(null, 1)
    //             let stub2 = sinon.stub(serviceObj.commonLib, 'getCvrData').yields(null, 'abc.jpg')
    //             let stub3 = sinon.stub(serviceObj.reminderUtils, 'createCTPipelinePayload',).returns({});
    //             let stub4 = sinon.stub(serviceObj.ctKafkaPublisher, 'publishData').callsFake(function(recordData,cb){
    //                 return cb("error");
    //             });
    //             let stub5 = sinon.stub(serviceObj.paytmFirstKafkaPublisher, 'publishData',).callsFake(function(recordData,cb){
    //                 return cb("error");
    //             });
    
    //             serviceObj.publishCtAndPFCCEvents(function(error,result){
    //             expect(stub1).to.have.callCount(1)
    //             expect(stub2).to.have.callCount(1)
    //             expect(stub3).to.have.callCount(1)
    //             stub1.restore();
    //             stub2.restore();
    //             stub3.restore();
    //             return done();
    //             }, record);
                
    // });
    // it("publishCtAndPFCCEvents function | check function calls", (done) => {
    //     let recordData = JSON.parse(JSON.stringify({ "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "dueDate": "2022-12-11", "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "", "cId": "**********", "isReliable" : "1" }));
    //         record={"notification_status":0};
    
    //         serviceObj.paytmFirstKafkaPublisher = new serviceObj.infraUtils.kafka.producer(); 
    //         serviceObj.paytmFirstKafkaPublisher.publishData = function(data,cb){};
    //         serviceObj.ctKafkaPublisher = new serviceObj.infraUtils.kafka.producer(); 
    //         serviceObj.ctKafkaPublisher.publishData = function(data,cb){};
    
    //         let stub1 = sinon.stub(serviceObj.commonLib, 'getRetailerData').yields(null, 1)
    //         let stub2 = sinon.stub(serviceObj.commonLib, 'getCvrData').yields(null, 'abc.jpg')
    //         let stub3 = sinon.stub(serviceObj.reminderUtils, 'createCTPipelinePayload',).returns({});
    //         let stub4 = sinon.stub(serviceObj.ctKafkaPublisher, 'publishData').callsFake(function(recordData,cb){
    //             return cb("error");
    //         });
    //         let stub5 = sinon.stub(serviceObj.paytmFirstKafkaPublisher, 'publishData',).callsFake(function(recordData,cb){
    //             return cb("error");
    //         });
    
    //         serviceObj.publishCtAndPFCCEvents(function(error,result){
    //         expect(stub1).to.have.callCount(1)
    //         expect(stub2).to.have.callCount(1)
    //         expect(stub3).to.have.callCount(1)
    //         stub1.restore();
    //         stub2.restore();
    //         stub3.restore();
    //         return done();
    //         }, record);
            
    //     });         
    //     it("publishCtAndPFCCEvents function | no retailerStatus", (done) => {
    //         let recordData = JSON.parse(JSON.stringify({ "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "dueDate": "2022-12-11", "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "", "cId": "**********", "isReliable" : "1" }));
    //         serviceObj.validateAndProcessRecord(recordData, function(error, data){
    //             record = data
    //             let cb = sinon.spy();
    
    //         serviceObj.ctKafkaPublisher = {
    //             publishData : () => {
    //                 return cb(null, data)
    //             }
    //         }
    //         serviceObj.paytmFirstKafkaPublisher = {
    //             publishData : () => {
    //                 return cb(null, data)
    //             }
    //         }
    //         let stub1 = sinon.stub(serviceObj.commonLib, 'getRetailerData').yields("error")
    //         let stub2 = sinon.stub(serviceObj.commonLib, 'getCvrData').yields(null, 'abc.jpg')
    //         let stub3 = sinon.stub(serviceObj.reminderUtils, 'createCTPipelinePayload',).returns({});
    //         let stub4 = sinon.stub(serviceObj, 'paytmFirstKafkaPublisher').returns(null);
    
    //         serviceObj.publishCtAndPFCCEvents(cb, record);
    //         stub1.restore();
    //         stub2.restore();
    //         stub3.restore();
    //         stub4.restore();
    
    //         expect(stub1).to.have.callCount(1)
    //         expect(stub2).to.have.callCount(0)
    //         expect(stub3).to.have.callCount(0)
    //         expect(stub4).to.have.callCount(0)
    //         return done();
    //         });
    //     })
    
        // it("publishCtAndPFCCEvents function | no thumbnail", (done) => {
        //     let recordData = JSON.parse(JSON.stringify({ "smsSenderID": "AD-HDFCBK", "smsDateTime": *************, "dueDate": "2022-12-11", "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "", "cId": "**********", "isReliable" : "1" }));
        //     serviceObj.validateAndProcessRecord(recordData, function(error, data){
        //         record= data
        //         let cb = sinon.spy();
    
        //     serviceObj.ctKafkaPublisher = {
        //         publishData : () => {
        //             return cb(null, data)
        //         }
        //     }
        //     serviceObj.paytmFirstKafkaPublisher = {
        //         publishData : () => {
        //             return cb(null, data)
        //         }
        //     }
        //     let stub1 = sinon.stub(serviceObj.commonLib, 'getRetailerData').yields(null, 1)
        //     let stub2 = sinon.stub(serviceObj.commonLib, 'getCvrData').yields("error")
        //     let stub3 = sinon.stub(serviceObj.reminderUtils, 'createCTPipelinePayload',).returns({});
    
        //     serviceObj.publishCtAndPFCCEvents(cb, record);
        //     stub1.restore();
        //     stub2.restore();
        //     stub3.restore();
    
        //     expect(stub1).to.have.callCount(1)
        //     expect(stub2).to.have.callCount(1)
        //     expect(stub3).to.have.callCount(0)
        //     return done();
        // })
        //     });

            it("processRecords | valid record | Partial Record " , (done) => {
                let recordData = { "smsSenderID": "AD-HDFCBK", "smsDateTime": "*************", "dueDate":  null, "billDate": "2020-11-11", "dueAmt": null, "totalAmt": null, "lastCC": "2808", "mId": "" , "cId":"123456",  "isReliable" : "1" };
                let record = {}
                let dbRecord={}
                let recordToUpdate={}
                let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').callsFake(function(recordData,cb) {
                    return cb(null,record);
                });
                let stub2 = sinon.stub(serviceObj, 'getActionforCCBills').callsFake(function fakeFn(done) {
                    return done("smsDueDate is null and The Parameters Present in the Payload are not valid");
                });
        
        
                serviceObj.processRecords(function (error) {
                    expect(error).to.be.equal("smsDueDate is null and The Parameters Present in the Payload are not valid");
                }, recordData);
        
                expect(stub1).to.have.callCount(1);
                expect(stub2).to.have.callCount(1);
                stub1.restore();
                stub2.restore();
                done();
            });
    })