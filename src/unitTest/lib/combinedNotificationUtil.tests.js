/*
  jshint 
    esversion: 8
 */

'use strict';
import { describe, it, before, afterEach, beforeEach} from 'mocha';
import sinon from 'sinon';
import chai from "chai";
import sinonChai from "sinon-chai";
import chaiAsPromised from "chai-as-promised";
import _ from 'lodash';
import CombinedNotificationUtil from '../../lib/combinedNotificationUtil';
import STARTUP_MOCK from '../__mocks__/startUp'

chai.use(chaiAsPromised);
chai.use(sinonChai);

const { expect } = chai;

describe("Module: CombinedNotificationUtil test suite", function () {
    let combinedNotificationUtil, options;

    before(function (done) {
        STARTUP_MOCK.init(function(error, mock_options){
            options = mock_options;
            combinedNotificationUtil = new CombinedNotificationUtil(options);
            done();
        });
    });

    describe("calculateCombinedDataStats", function() {
        it("should return zero stats for empty array", function() {
            const result = combinedNotificationUtil.calculateCombinedDataStats([]);
            
            expect(result).to.deep.equal({
                totalDueAmount: 0,
                billCount: 0,
                minDueDate: null,
                maxDueDate: null
            });
        });

        it("should return zero stats for null/undefined input", function() {
            const resultNull = combinedNotificationUtil.calculateCombinedDataStats(null);
            const resultUndefined = combinedNotificationUtil.calculateCombinedDataStats(undefined);
            
            const expectedResult = {
                totalDueAmount: 0,
                billCount: 0,
                minDueDate: null,
                maxDueDate: null
            };

            expect(resultNull).to.deep.equal(expectedResult);
            expect(resultUndefined).to.deep.equal(expectedResult);
        });

        it("should calculate stats correctly for single bill", function() {
            const combinedData = [
                {
                    dueAmount: 150.50,
                    dueDate: 1640995200000 // 2022-01-01
                }
            ];

            const result = combinedNotificationUtil.calculateCombinedDataStats(combinedData);
            
            expect(result).to.deep.equal({
                totalDueAmount: 150.50,
                billCount: 1,
                minDueDate: "2022-01-01",
                maxDueDate: "2022-01-01"
            });
        });

        it("should calculate stats correctly for multiple bills with different due dates", function() {
            const combinedData = [
                {
                    dueAmount: 100.25,
                    dueDate: 1640995200000 // 2022-01-01
                },
                {
                    dueAmount: 200.75,
                    dueDate: 1641081600000 // 2022-01-02
                },
                {
                    dueAmount: 50.00,
                    dueDate: 1640908800000 // 2021-12-31
                }
            ];

            const result = combinedNotificationUtil.calculateCombinedDataStats(combinedData);
            
            expect(result).to.deep.equal({
                totalDueAmount: 351.00,
                billCount: 3,
                minDueDate: "2021-12-31", // 2021-12-31 (earliest)
                maxDueDate: "2022-01-02"  // 2022-01-02 (latest)
            });
        });

        it("should handle bills with missing due amounts", function() {
            const combinedData = [
                {
                    dueAmount: 100.00,
                    dueDate: 1640995200000
                },
                {
                    // missing dueAmount
                    dueDate: 1641081600000
                },
                {
                    dueAmount: 50.00,
                    dueDate: 1640908800000
                }
            ];

            const result = combinedNotificationUtil.calculateCombinedDataStats(combinedData);
            
            expect(result.totalDueAmount).to.equal(150.00);
            expect(result.billCount).to.equal(3);
        });

        it("should handle bills with missing due dates", function() {
            const combinedData = [
                {
                    dueAmount: 100.00,
                    dueDate: 1640995200000
                },
                {
                    dueAmount: 200.00
                    // missing dueDate
                }
            ];

            const result = combinedNotificationUtil.calculateCombinedDataStats(combinedData);
            
            expect(result.totalDueAmount).to.equal(300.00);
            expect(result.billCount).to.equal(2);
            expect(result.minDueDate).to.equal("2022-01-01");
            expect(result.maxDueDate).to.equal("2022-01-01");
        });

        it("should handle invalid due amounts", function() {
            const combinedData = [
                {
                    dueAmount: "invalid",
                    dueDate: 1640995200000
                },
                {
                    dueAmount: 100.00,
                    dueDate: 1641081600000
                }
            ];

            const result = combinedNotificationUtil.calculateCombinedDataStats(combinedData);
            
            expect(result.totalDueAmount).to.equal(100.00);
            expect(result.billCount).to.equal(2);
        });
    });

    describe("convertKafkaPayloadToRecord", function() {
        it("should parse valid kafka payload correctly", function() {
            const kafkaPayload = {
                value: JSON.stringify({
                    correlationId: "test-correlation-123",
                    notificationType: "COMBINED",
                    source: "test-source",
                    data: {
                        customer_id: 12345,
                        service: "electricity"
                    }
                })
            };

            const result = combinedNotificationUtil.convertKafkaPayloadToRecord(kafkaPayload);
            
            expect(result).to.deep.equal({
                correlationId: "test-correlation-123",
                notificationType: "COMBINED",
                source: "test-source",
                data: {
                    customer_id: 12345,
                    service: "electricity"
                }
            });
        });

        it("should return null for invalid JSON", function() {
            const kafkaPayload = {
                value: "invalid-json"
            };

            const result = combinedNotificationUtil.convertKafkaPayloadToRecord(kafkaPayload);
            expect(result).to.be.null;
        });

        it("should return null for missing value", function() {
            const kafkaPayload = {};

            const result = combinedNotificationUtil.convertKafkaPayloadToRecord(kafkaPayload);
            expect(result).to.be.null;
        });
    });

    describe("validateDataToProcessForNotification", function() {
        let validRecord;

        beforeEach(function() {
            validRecord = {
                correlationId: "test-correlation-123",
                notificationType: "COMBINED",
                source: "test-source",
                data: {
                    customer_id: 12345,
                    service: "electricity",
                    combined_data: [
                        {
                            rechargeNumber: "RN001",
                            operator: "UPPCL",
                            dueDate: 1640995200000,
                            dueAmount: 150.50,
                            productId: 123,
                            userType: "RU",
                            service: "electricity",
                            status: 1,
                            notification_status: 1
                        }
                    ]
                }
            };
        });

        it("should validate successfully for valid record", function(done) {
            sinon.stub(combinedNotificationUtil, 'convertKafkaPayloadToRecord').returns(validRecord);

            combinedNotificationUtil.validateDataToProcessForNotification(function(error, result) {
                expect(error).to.be.null;
                expect(result).to.deep.equal(validRecord);
                
                combinedNotificationUtil.convertKafkaPayloadToRecord.restore();
                done();
            }, {});
        });

        it("should fail validation when convertKafkaPayloadToRecord returns null", function(done) {
            sinon.stub(combinedNotificationUtil, 'convertKafkaPayloadToRecord').returns(null);

            combinedNotificationUtil.validateDataToProcessForNotification(function(error, result) {
                expect(error).to.equal('unable to get valid data from kafka payload');
                expect(result).to.be.null;
                
                combinedNotificationUtil.convertKafkaPayloadToRecord.restore();
                done();
            }, {});
        });

        describe("correlationId validation", function() {
            it("should fail for missing correlationId", function(done) {
                delete validRecord.correlationId;
                sinon.stub(combinedNotificationUtil, 'convertKafkaPayloadToRecord').returns(validRecord);

                combinedNotificationUtil.validateDataToProcessForNotification(function(error, result) {
                    expect(error).to.equal('correlationId is required and must be a non-empty string');
                    
                    combinedNotificationUtil.convertKafkaPayloadToRecord.restore();
                    done();
                }, {});
            });

            it("should fail for empty correlationId", function(done) {
                validRecord.correlationId = "";
                sinon.stub(combinedNotificationUtil, 'convertKafkaPayloadToRecord').returns(validRecord);

                combinedNotificationUtil.validateDataToProcessForNotification(function(error, result) {
                    expect(error).to.equal('correlationId is required and must be a non-empty string');
                    
                    combinedNotificationUtil.convertKafkaPayloadToRecord.restore();
                    done();
                }, {});
            });

            it("should fail for non-string correlationId", function(done) {
                validRecord.correlationId = 123;
                sinon.stub(combinedNotificationUtil, 'convertKafkaPayloadToRecord').returns(validRecord);

                combinedNotificationUtil.validateDataToProcessForNotification(function(error, result) {
                    expect(error).to.equal('correlationId is required and must be a non-empty string');
                    
                    combinedNotificationUtil.convertKafkaPayloadToRecord.restore();
                    done();
                }, {});
            });
        });

        describe("notificationType validation", function() {
            it("should fail for wrong notificationType", function(done) {
                validRecord.notificationType = "BILLGEN";
                sinon.stub(combinedNotificationUtil, 'convertKafkaPayloadToRecord').returns(validRecord);

                combinedNotificationUtil.validateDataToProcessForNotification(function(error, result) {
                    expect(error).to.equal('notificationType must be "COMBINED"');
                    
                    combinedNotificationUtil.convertKafkaPayloadToRecord.restore();
                    done();
                }, {});
            });
        });

        describe("source validation", function() {
            it("should fail for missing source", function(done) {
                delete validRecord.source;
                sinon.stub(combinedNotificationUtil, 'convertKafkaPayloadToRecord').returns(validRecord);

                combinedNotificationUtil.validateDataToProcessForNotification(function(error, result) {
                    expect(error).to.equal('source is required and must be a non-empty string');
                    
                    combinedNotificationUtil.convertKafkaPayloadToRecord.restore();
                    done();
                }, {});
            });
        });

        describe("data validation", function() {
            it("should fail for missing data", function(done) {
                delete validRecord.data;
                sinon.stub(combinedNotificationUtil, 'convertKafkaPayloadToRecord').returns(validRecord);

                combinedNotificationUtil.validateDataToProcessForNotification(function(error, result) {
                    expect(error).to.equal('data is required and must be an object');
                    
                    combinedNotificationUtil.convertKafkaPayloadToRecord.restore();
                    done();
                }, {});
            });
        });

        describe("customer_id validation", function() {
            it("should fail for missing customer_id", function(done) {
                delete validRecord.data.customer_id;
                sinon.stub(combinedNotificationUtil, 'convertKafkaPayloadToRecord').returns(validRecord);

                combinedNotificationUtil.validateDataToProcessForNotification(function(error, result) {
                    expect(error).to.equal('customer_id is required and must be a positive integer');
                    
                    combinedNotificationUtil.convertKafkaPayloadToRecord.restore();
                    done();
                }, {});
            });

            it("should fail for negative customer_id", function(done) {
                validRecord.data.customer_id = -1;
                sinon.stub(combinedNotificationUtil, 'convertKafkaPayloadToRecord').returns(validRecord);

                combinedNotificationUtil.validateDataToProcessForNotification(function(error, result) {
                    expect(error).to.equal('customer_id is required and must be a positive integer');
                    
                    combinedNotificationUtil.convertKafkaPayloadToRecord.restore();
                    done();
                }, {});
            });

            it("should fail for float customer_id", function(done) {
                validRecord.data.customer_id = 123.45;
                sinon.stub(combinedNotificationUtil, 'convertKafkaPayloadToRecord').returns(validRecord);

                combinedNotificationUtil.validateDataToProcessForNotification(function(error, result) {
                    expect(error).to.equal('customer_id is required and must be a positive integer');
                    
                    combinedNotificationUtil.convertKafkaPayloadToRecord.restore();
                    done();
                }, {});
            });
        });

        describe("service validation", function() {
            it("should fail for missing service", function(done) {
                delete validRecord.data.service;
                sinon.stub(combinedNotificationUtil, 'convertKafkaPayloadToRecord').returns(validRecord);

                combinedNotificationUtil.validateDataToProcessForNotification(function(error, result) {
                    expect(error).to.equal('service is required and must be a non-empty string');
                    
                    combinedNotificationUtil.convertKafkaPayloadToRecord.restore();
                    done();
                }, {});
            });
        });

        describe("combined_data validation", function() {
            it("should fail for missing combined_data", function(done) {
                delete validRecord.data.combined_data;
                sinon.stub(combinedNotificationUtil, 'convertKafkaPayloadToRecord').returns(validRecord);

                combinedNotificationUtil.validateDataToProcessForNotification(function(error, result) {
                    expect(error).to.equal('combined_data is required and must be a non-empty array');
                    
                    combinedNotificationUtil.convertKafkaPayloadToRecord.restore();
                    done();
                }, {});
            });

            it("should fail for empty combined_data array", function(done) {
                validRecord.data.combined_data = [];
                sinon.stub(combinedNotificationUtil, 'convertKafkaPayloadToRecord').returns(validRecord);

                combinedNotificationUtil.validateDataToProcessForNotification(function(error, result) {
                    expect(error).to.equal('combined_data is required and must be a non-empty array');
                    
                    combinedNotificationUtil.convertKafkaPayloadToRecord.restore();
                    done();
                }, {});
            });

            it("should fail for combined_data array exceeding 5 items", function(done) {
                validRecord.data.combined_data = new Array(6).fill(validRecord.data.combined_data[0]);
                sinon.stub(combinedNotificationUtil, 'convertKafkaPayloadToRecord').returns(validRecord);

                combinedNotificationUtil.validateDataToProcessForNotification(function(error, result) {
                    expect(error).to.equal('combined_data array length must not exceed 5 items');
                    
                    combinedNotificationUtil.convertKafkaPayloadToRecord.restore();
                    done();
                }, {});
            });
        });

        describe("combined_data item validation", function() {
            beforeEach(function() {
                sinon.restore();
            });

            it("should fail for missing rechargeNumber", function(done) {
                delete validRecord.data.combined_data[0].rechargeNumber;
                sinon.stub(combinedNotificationUtil, 'convertKafkaPayloadToRecord').returns(validRecord);

                combinedNotificationUtil.validateDataToProcessForNotification(function(error, result) {
                    expect(error).to.equal('combined_data[0].rechargeNumber is required and must be a non-empty string');
                    
                    combinedNotificationUtil.convertKafkaPayloadToRecord.restore();
                    done();
                }, {});
            });

            it("should fail for missing operator", function(done) {
                delete validRecord.data.combined_data[0].operator;
                sinon.stub(combinedNotificationUtil, 'convertKafkaPayloadToRecord').returns(validRecord);

                combinedNotificationUtil.validateDataToProcessForNotification(function(error, result) {
                    expect(error).to.equal('combined_data[0].operator is required and must be a non-empty string');
                    
                    combinedNotificationUtil.convertKafkaPayloadToRecord.restore();
                    done();
                }, {});
            });

            it("should fail for missing dueDate", function(done) {
                delete validRecord.data.combined_data[0].dueDate;
                sinon.stub(combinedNotificationUtil, 'convertKafkaPayloadToRecord').returns(validRecord);

                combinedNotificationUtil.validateDataToProcessForNotification(function(error, result) {
                    expect(error).to.equal('combined_data[0].dueDate is required and must be a number (epoch timestamp)');
                    
                    combinedNotificationUtil.convertKafkaPayloadToRecord.restore();
                    done();
                }, {});
            });

            it("should fail for non-number dueDate", function(done) {
                validRecord.data.combined_data[0].dueDate = "invalid";
                sinon.stub(combinedNotificationUtil, 'convertKafkaPayloadToRecord').returns(validRecord);

                combinedNotificationUtil.validateDataToProcessForNotification(function(error, result) {
                    expect(error).to.equal('combined_data[0].dueDate is required and must be a number (epoch timestamp)');
                    
                    combinedNotificationUtil.convertKafkaPayloadToRecord.restore();
                    done();
                }, {});
            });

            it("should fail for missing dueAmount", function(done) {
                delete validRecord.data.combined_data[0].dueAmount;
                sinon.stub(combinedNotificationUtil, 'convertKafkaPayloadToRecord').returns(validRecord);

                combinedNotificationUtil.validateDataToProcessForNotification(function(error, result) {
                    expect(error).to.equal('combined_data[0].dueAmount is required and must be a number');
                    
                    combinedNotificationUtil.convertKafkaPayloadToRecord.restore();
                    done();
                }, {});
            });

            it("should fail for missing productId", function(done) {
                delete validRecord.data.combined_data[0].productId;
                sinon.stub(combinedNotificationUtil, 'convertKafkaPayloadToRecord').returns(validRecord);

                combinedNotificationUtil.validateDataToProcessForNotification(function(error, result) {
                    expect(error).to.equal('combined_data[0].productId is required and must be a positive integer');
                    
                    combinedNotificationUtil.convertKafkaPayloadToRecord.restore();
                    done();
                }, {});
            });

            it("should fail for invalid userType", function(done) {
                validRecord.data.combined_data[0].userType = "INVALID";
                sinon.stub(combinedNotificationUtil, 'convertKafkaPayloadToRecord').returns(validRecord);

                combinedNotificationUtil.validateDataToProcessForNotification(function(error, result) {
                    expect(error).to.equal('combined_data[0].userType is required and must be either "RU" or "NON_RU"');
                    
                    combinedNotificationUtil.convertKafkaPayloadToRecord.restore();
                    done();
                }, {});
            });

            it("should fail for service mismatch", function(done) {
                validRecord.data.combined_data[0].service = "gas";
                sinon.stub(combinedNotificationUtil, 'convertKafkaPayloadToRecord').returns(validRecord);

                combinedNotificationUtil.validateDataToProcessForNotification(function(error, result) {
                    expect(error).to.equal('combined_data[0].service must match parent data.service');
                    
                    combinedNotificationUtil.convertKafkaPayloadToRecord.restore();
                    done();
                }, {});
            });

            it("should fail for status 7", function(done) {
                validRecord.data.combined_data[0].status = 7;
                sinon.stub(combinedNotificationUtil, 'convertKafkaPayloadToRecord').returns(validRecord);

                combinedNotificationUtil.validateDataToProcessForNotification(function(error, result) {
                    expect(error).to.equal('combined_data[0].status cannot be 7 or 13');
                    
                    combinedNotificationUtil.convertKafkaPayloadToRecord.restore();
                    done();
                }, {});
            });

            it("should fail for status 13", function(done) {
                validRecord.data.combined_data[0].status = 13;
                sinon.stub(combinedNotificationUtil, 'convertKafkaPayloadToRecord').returns(validRecord);

                combinedNotificationUtil.validateDataToProcessForNotification(function(error, result) {
                    expect(error).to.equal('combined_data[0].status cannot be 7 or 13');
                    
                    combinedNotificationUtil.convertKafkaPayloadToRecord.restore();
                    done();
                }, {});
            });

            it("should fail for notification_status 0", function(done) {
                validRecord.data.combined_data[0].notification_status = 0;
                sinon.stub(combinedNotificationUtil, 'convertKafkaPayloadToRecord').returns(validRecord);

                combinedNotificationUtil.validateDataToProcessForNotification(function(error, result) {
                    expect(error).to.equal('combined_data[0].notification_status cannot be 0');
                    
                    combinedNotificationUtil.convertKafkaPayloadToRecord.restore();
                    done();
                }, {});
            });
        });

        describe("multiple items validation", function() {
            beforeEach(function() {
                sinon.restore();
            });

            it("should validate all items in array", function(done) {
                validRecord.data.combined_data.push({
                    rechargeNumber: "RN002",
                    operator: "UPPCL",
                    dueDate: 1641081600000,
                    dueAmount: 200.75,
                    productId: 124,
                    userType: "NON_RU",
                    service: "electricity",
                    status: 2,
                    notification_status: 1
                });

                sinon.stub(combinedNotificationUtil, 'convertKafkaPayloadToRecord').returns(validRecord);

                combinedNotificationUtil.validateDataToProcessForNotification(function(error, result) {
                    expect(error).to.be.null;
                    expect(result).to.deep.equal(validRecord);
                    
                    combinedNotificationUtil.convertKafkaPayloadToRecord.restore();
                    done();
                }, {});
            });

            it("should fail for invalid second item", function(done) {
                validRecord.data.combined_data.push({
                    rechargeNumber: "RN002",
                    operator: "UPPCL",
                    dueDate: 1641081600000,
                    dueAmount: 200.75,
                    productId: 124,
                    userType: "INVALID", // Invalid userType
                    service: "electricity",
                    status: 2,
                    notification_status: 1
                });

                sinon.stub(combinedNotificationUtil, 'convertKafkaPayloadToRecord').returns(validRecord);

                combinedNotificationUtil.validateDataToProcessForNotification(function(error, result) {
                    expect(error).to.equal('combined_data[1].userType is required and must be either "RU" or "NON_RU"');
                    
                    combinedNotificationUtil.convertKafkaPayloadToRecord.restore();
                    done();
                }, {});
            });
        });
    });

    describe("processForCombinedTemplate", function() {
        let mockConfig, mockRecord, mockPayload;

        beforeEach(function() {
            mockConfig = {
                DYNAMIC_CONFIG: {
                    NOTIFICATION_CONFIG: {
                        COMBINED_TEMPLATE_ID_BY_SERVICE: {
                            'BR_ELECTRICITY_COMBINED_PUSH': 1001,
                            'BR_ELECTRICITY_COMBINED_PUSH_SINGLEBILL': 1002,
                            'BR_ELECTRICITY_COMBINED_PUSH_MULTIBILL_SAMEDUEDATES': 1003,
                            'BR_ELECTRICITY_COMBINED_PUSH_MULTIBILL_DIFFDUEDATES': 1004,
                            'BR_MOBILE_COMBINED_SMS': 2001,
                            'BR_MOBILE_COMBINED_SMS_SINGLEBILL': 2002
                        }
                    }
                }
            };

            // Override config for this test
            combinedNotificationUtil.config = mockConfig;

            mockRecord = {
                operator: 'UPPCL'
            };

            mockPayload = {
                service: 'electricity'
            };
        });

        afterEach(function() {
            // Restore original config
            combinedNotificationUtil.config = options.config;
        });

        it("should return template from record.templates if present", function() {
            mockRecord.templates = {
                PUSH: 9999
            };

            const result = combinedNotificationUtil.processForCombinedTemplate(
                mockRecord, mockPayload, 'table', 'COMBINED', 'PUSH'
            );

            expect(result).to.equal(9999);
        });

        it("should return template for single bill case", function() {
            mockPayload.billCount = 1;
            mockPayload.minDueDate = 1640995200000;
            mockPayload.maxDueDate = 1640995200000;

            const result = combinedNotificationUtil.processForCombinedTemplate(
                mockRecord, mockPayload, 'table', 'COMBINED', 'PUSH'
            );

            expect(result).to.equal(1002); // SINGLEBILL template
        });

        it("should return template for multiple bills with same due dates", function() {
            mockPayload.billCount = 3;
            mockPayload.minDueDate = 1640995200000;
            mockPayload.maxDueDate = 1640995200000; // Same date

            const result = combinedNotificationUtil.processForCombinedTemplate(
                mockRecord, mockPayload, 'table', 'COMBINED', 'PUSH'
            );

            expect(result).to.equal(1003); // MULTIBILL_SAMEDUEDATES template
        });

        it("should return template for multiple bills with different due dates", function() {
            mockPayload.billCount = 3;
            mockPayload.minDueDate = 1640995200000;
            mockPayload.maxDueDate = 1641081600000; // Different date

            const result = combinedNotificationUtil.processForCombinedTemplate(
                mockRecord, mockPayload, 'table', 'COMBINED', 'PUSH'
            );

            expect(result).to.equal(1004); // MULTIBILL_DIFFDUEDATES template
        });

        it("should fallback to base template when specific template not found", function() {
            mockPayload.billCount = 1;
            mockPayload.minDueDate = 1640995200000;
            mockPayload.maxDueDate = 1640995200000;
            
            // Using a service that doesn't have specific templates, only base
            mockPayload.service = 'gas';

            const result = combinedNotificationUtil.processForCombinedTemplate(
                mockRecord, mockPayload, 'table', 'COMBINED', 'PUSH'
            );

            expect(result).to.be.null; // No template found
        });

        it("should handle missing billCount gracefully", function() {
            // billCount is missing/undefined
            delete mockPayload.billCount;

            const result = combinedNotificationUtil.processForCombinedTemplate(
                mockRecord, mockPayload, 'table', 'COMBINED', 'PUSH'
            );

            expect(result).to.equal(1001); // Base template since billCount = 0
        });

        it("should handle zero billCount", function() {
            mockPayload.billCount = 0;

            const result = combinedNotificationUtil.processForCombinedTemplate(
                mockRecord, mockPayload, 'table', 'COMBINED', 'PUSH'
            );

            expect(result).to.equal(1001); // Base template
        });

        it("should handle missing due dates", function() {
            mockPayload.billCount = 2;
            // minDueDate and maxDueDate are missing

            const result = combinedNotificationUtil.processForCombinedTemplate(
                mockRecord, mockPayload, 'table', 'COMBINED', 'PUSH'
            );

            expect(result).to.equal(1003); // MULTIBILL_SAMEDUEDATES (undefined === undefined)
        });

        it("should work with different notification types", function() {
            mockPayload.service = 'mobile';
            mockPayload.billCount = 1;

            const result = combinedNotificationUtil.processForCombinedTemplate(
                mockRecord, mockPayload, 'table', 'COMBINED', 'SMS'
            );

            expect(result).to.equal(2002); // Mobile SMS SINGLEBILL template
        });

        it("should convert service to uppercase in template key", function() {
            mockPayload.service = 'electricity'; // lowercase
            mockPayload.billCount = 1;

            const result = combinedNotificationUtil.processForCombinedTemplate(
                mockRecord, mockPayload, 'table', 'COMBINED', 'PUSH'
            );

            expect(result).to.equal(1002); // Should work with uppercase conversion
        });

        it("should return null when no template is found", function() {
            mockPayload.service = 'unknown';
            mockPayload.billCount = 1;

            const result = combinedNotificationUtil.processForCombinedTemplate(
                mockRecord, mockPayload, 'table', 'COMBINED', 'PUSH'
            );

            expect(result).to.be.null;
        });

        it("should handle errors gracefully", function() {
            // Mock _.get to throw an error
            const originalGet = _.get;
            sinon.stub(_, 'get').throws(new Error('Config error'));

            const result = combinedNotificationUtil.processForCombinedTemplate(
                mockRecord, mockPayload, 'table', 'COMBINED', 'PUSH'
            );

            expect(result).to.be.null;
            
            _.get.restore();
        });

        it("should log appropriate messages during execution", function() {
            const logSpy = sinon.spy(combinedNotificationUtil.L, 'log');
            
            mockPayload.billCount = 1;
            mockPayload.minDueDate = 1640995200000;
            mockPayload.maxDueDate = 1640995200000;

            combinedNotificationUtil.processForCombinedTemplate(
                mockRecord, mockPayload, 'table', 'COMBINED', 'PUSH'
            );

            expect(logSpy).to.have.been.called;
            expect(logSpy).to.have.been.calledWith(
                sinon.match(/processForCombinedTemplate :: getTemplateId/)
            );

            logSpy.restore();
        });

        it("should handle complex nested config structure", function() {
            mockPayload.billCount = 2;
            mockPayload.minDueDate = 1640995200000;
            mockPayload.maxDueDate = 1641081600000; // Different dates

            const result = combinedNotificationUtil.processForCombinedTemplate(
                mockRecord, mockPayload, 'table', 'COMBINED', 'PUSH'
            );

            expect(result).to.equal(1004); // MULTIBILL_DIFFDUEDATES
        });

        it("should work with mixed case service names", function() {
            mockPayload.service = 'ElectriCITY'; // Mixed case
            mockPayload.billCount = 1;

            const result = combinedNotificationUtil.processForCombinedTemplate(
                mockRecord, mockPayload, 'table', 'COMBINED', 'PUSH'
            );

            expect(result).to.equal(1002); // Should normalize to uppercase
        });

        it("should handle null/undefined payload gracefully", function() {
            const result = combinedNotificationUtil.processForCombinedTemplate(
                mockRecord, null, 'table', 'COMBINED', 'PUSH'
            );

            expect(result).to.be.null;
        });

        it("should handle null/undefined record gracefully", function() {
            const result = combinedNotificationUtil.processForCombinedTemplate(
                null, mockPayload, 'table', 'COMBINED', 'PUSH'
            );

            expect(result).to.be.null;
        });

        it("should prioritize specific template over base template", function() {
            mockPayload.billCount = 1;
            mockPayload.minDueDate = 1640995200000;
            mockPayload.maxDueDate = 1640995200000;

            // Mock config where both specific and base templates exist
            combinedNotificationUtil.config = {
                DYNAMIC_CONFIG: {
                    NOTIFICATION_CONFIG: {
                        COMBINED_TEMPLATE_ID_BY_SERVICE: {
                            'BR_ELECTRICITY_COMBINED_PUSH': 1001, // base
                            'BR_ELECTRICITY_COMBINED_PUSH_SINGLEBILL': 1002 // specific
                        }
                    }
                }
            };

            const result = combinedNotificationUtil.processForCombinedTemplate(
                mockRecord, mockPayload, 'table', 'COMBINED', 'PUSH'
            );

            expect(result).to.equal(1002); // Should return specific, not base
        });
    });

    describe("formatEpochToDate", function() {
        it("should format valid epoch timestamp to YYYY-MM-DD", function() {
            const epochTimestamp = 1640995200000; // 2022-01-01 00:00:00 UTC
            const result = combinedNotificationUtil.formatEpochToDate(epochTimestamp);
            expect(result).to.equal('2022-01-01');
        });

        it("should handle epoch timestamp as string", function() {
            const epochTimestamp = "1640995200000";
            const result = combinedNotificationUtil.formatEpochToDate(epochTimestamp);
            expect(result).to.equal('2022-01-01');
        });

        it("should return null for invalid epoch timestamp", function() {
            const result1 = combinedNotificationUtil.formatEpochToDate(null);
            const result2 = combinedNotificationUtil.formatEpochToDate(undefined);
            const result3 = combinedNotificationUtil.formatEpochToDate('invalid');
            const result4 = combinedNotificationUtil.formatEpochToDate(NaN);

            expect(result1).to.be.null;
            expect(result2).to.be.null;
            expect(result3).to.be.null;
            expect(result4).to.be.null;
        });

        it("should return null for invalid moment date", function() {
            const invalidEpoch = 'not-a-number';
            const result = combinedNotificationUtil.formatEpochToDate(invalidEpoch);
            expect(result).to.be.null;
        });

        it("should handle different epoch formats", function() {
            const epochSec = 1640995200; // seconds
            const epochMs = 1640995200000; // milliseconds
            
            const resultSec = combinedNotificationUtil.formatEpochToDate(epochSec);
            const resultMs = combinedNotificationUtil.formatEpochToDate(epochMs);
            
            expect(resultSec).to.equal('1970-01-20'); // Interpreted as milliseconds
            expect(resultMs).to.equal('2022-01-01');
        });
    });

    describe("isEligibleForCombinedNotificationExistenceCheck", function() {
        beforeEach(function() {
            combinedNotificationUtil.config = {
                DYNAMIC_CONFIG: {
                    NOTIFICATION_CONFIG: {
                        COMBINED_NOTIFICATION_VALIDATION_CONFIG: {
                            CUG_CUSTOMERID_LIST: [1111111111, 2222222222],
                            PERCENTAGE_ROLLOUT: 5
                        }
                    }
                }
            };
        });

        it("should return false for invalid notification type", function() {
            const result1 = combinedNotificationUtil.isEligibleForCombinedNotificationExistenceCheck(1111111111, 'INVALID', false, false, '2023-01-01');
            const result2 = combinedNotificationUtil.isEligibleForCombinedNotificationExistenceCheck(1111111111, null, false, false, '2023-01-01');
            const result3 = combinedNotificationUtil.isEligibleForCombinedNotificationExistenceCheck(1111111111, undefined, false, false, '2023-01-01');

            expect(result1).to.be.false;
            expect(result2).to.be.false;
            expect(result3).to.be.false;
        });

        it("should return true for valid notification types DUEDATE and BILLDUE", function() {
            const result1 = combinedNotificationUtil.isEligibleForCombinedNotificationExistenceCheck(1111111111, 'DUEDATE', false, false, '2023-01-01');
            const result2 = combinedNotificationUtil.isEligibleForCombinedNotificationExistenceCheck(1111111111, 'BILLDUE', false, false, '2023-01-01');

            expect(result1).to.be.true;
            expect(result2).to.be.true;
        });

        it("should return false for realtime processing", function() {
            const result1 = combinedNotificationUtil.isEligibleForCombinedNotificationExistenceCheck(1111111111, 'DUEDATE', true, false, '2023-01-01');
            const result2 = combinedNotificationUtil.isEligibleForCombinedNotificationExistenceCheck(1111111111, 'DUEDATE', false, true, '2023-01-01');
            const result3 = combinedNotificationUtil.isEligibleForCombinedNotificationExistenceCheck(1111111111, 'DUEDATE', true, true, '2023-01-01');

            expect(result1).to.be.false;
            expect(result2).to.be.false;
            expect(result3).to.be.false;
        });

        it("should return false for current date due date", function() {
            const today = new Date();
            const todayISO = today.toISOString();
            
            const result = combinedNotificationUtil.isEligibleForCombinedNotificationExistenceCheck(1111111111, 'DUEDATE', false, false, todayISO);
            expect(result).to.be.false;
        });

        it("should return true for future due date", function() {
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            const tomorrowISO = tomorrow.toISOString();
            
            const result = combinedNotificationUtil.isEligibleForCombinedNotificationExistenceCheck(1111111111, 'DUEDATE', false, false, tomorrowISO);
            expect(result).to.be.true;
        });

        it("should return true for past due date", function() {
            // Use a fixed past date to avoid timezone issues
            const pastDate = '2023-01-01T00:00:00.000Z'; 
            
            const result = combinedNotificationUtil.isEligibleForCombinedNotificationExistenceCheck(1111111111, 'DUEDATE', false, false, pastDate);
            expect(result).to.be.true;
        });

        it("should return true for whitelisted customer IDs", function() {
            const result1 = combinedNotificationUtil.isEligibleForCombinedNotificationExistenceCheck(1111111111, 'DUEDATE', false, false, '2023-01-01');
            const result2 = combinedNotificationUtil.isEligibleForCombinedNotificationExistenceCheck(2222222222, 'DUEDATE', false, false, '2023-01-01');

            expect(result1).to.be.true;
            expect(result2).to.be.true;
        });

        it("should return true for customers in percentage rollout", function() {
            // Customer ID 1 % 100 = 1, which is < 5 (percentage rollout)
            const result = combinedNotificationUtil.isEligibleForCombinedNotificationExistenceCheck(1, 'DUEDATE', false, false, '2023-01-01');
            expect(result).to.be.true;
        });

        it("should return false for customers not in percentage rollout and not whitelisted", function() {
            // Customer ID 9999999999 % 100 = 99, which is >= 5 (percentage rollout)
            const result = combinedNotificationUtil.isEligibleForCombinedNotificationExistenceCheck(9999999999, 'DUEDATE', false, false, '2023-01-01');
            expect(result).to.be.false;
        });

        it("should handle null due date gracefully", function() {
            const result = combinedNotificationUtil.isEligibleForCombinedNotificationExistenceCheck(1111111111, 'DUEDATE', false, false, null);
            expect(result).to.be.true;
        });
    });

    describe("validateCombinedNotificationExists", function() {
        let mockCassandraBillsModel;
        let callbackSpy;

        beforeEach(function() {
            mockCassandraBillsModel = {
                checkCombinedNotificationKey: sinon.stub()
            };
            callbackSpy = sinon.spy();
            
            combinedNotificationUtil.config = {
                DYNAMIC_CONFIG: {
                    NOTIFICATION_CONFIG: {
                        COMBINED_NOTIFICATION_VALIDATION_CONFIG: {
                            CUG_CUSTOMERID_LIST: [1111111111],
                            PERCENTAGE_ROLLOUT: 5
                        }
                    }
                }
            };
        });

        afterEach(function() {
            sinon.restore();
        });

        it("should return false for missing required fields", function() {
            const record = {
                customer_id: null,
                recharge_number: '1234567890',
                service: 'electricity',
                operator: 'test'
            };

            combinedNotificationUtil.validateCombinedNotificationExists(record, mockCassandraBillsModel, false, false, callbackSpy);

            expect(callbackSpy).to.have.been.calledWith(null, false);
        });

        it("should return false for non-eligible customer", function() {
            const record = {
                customer_id: 9999999999,
                recharge_number: '1234567890',
                service: 'electricity',
                operator: 'test',
                notificationType: 'DUEDATE',
                due_date: '2023-01-01'
            };

            combinedNotificationUtil.validateCombinedNotificationExists(record, mockCassandraBillsModel, false, false, callbackSpy);

            expect(callbackSpy).to.have.been.calledWith(null, false);
        });

        it("should check cassandra and return true when combined notification exists", function() {
            const record = {
                customer_id: 1111111111,
                recharge_number: '1234567890',
                service: 'electricity',
                operator: 'test',
                notificationType: 'DUEDATE',
                due_date: '2023-01-01'
            };

            mockCassandraBillsModel.checkCombinedNotificationKey.callsArgWith(1, 'Combined notification exists');

            combinedNotificationUtil.validateCombinedNotificationExists(record, mockCassandraBillsModel, false, false, callbackSpy);

            expect(mockCassandraBillsModel.checkCombinedNotificationKey).to.have.been.called;
            expect(callbackSpy).to.have.been.calledWith(null, true);
        });

        it("should check cassandra and return false when combined notification does not exist", function() {
            const record = {
                customer_id: 1111111111,
                recharge_number: '1234567890',
                service: 'electricity',
                operator: 'test',
                notificationType: 'DUEDATE',
                due_date: '2023-01-01'
            };

            mockCassandraBillsModel.checkCombinedNotificationKey.callsArgWith(1, null);

            combinedNotificationUtil.validateCombinedNotificationExists(record, mockCassandraBillsModel, false, false, callbackSpy);

            expect(mockCassandraBillsModel.checkCombinedNotificationKey).to.have.been.called;
            expect(callbackSpy).to.have.been.calledWith(null, false);
        });

        it("should handle cassandra errors gracefully", function() {
            const record = {
                customer_id: 1111111111,
                recharge_number: '1234567890',
                service: 'electricity',
                operator: 'test',
                notificationType: 'DUEDATE',
                due_date: '2023-01-01'
            };

            mockCassandraBillsModel.checkCombinedNotificationKey.throws(new Error('Database error'));

            combinedNotificationUtil.validateCombinedNotificationExists(record, mockCassandraBillsModel, false, false, callbackSpy);

            expect(callbackSpy).to.have.been.calledWith(null, false);
        });

        it("should pass correct parameters to cassandra", function() {
            const record = {
                customer_id: 1111111111,
                recharge_number: '1234567890',
                service: 'Electricity',
                operator: 'TEST',
                notificationType: 'DUEDATE',
                due_date: '2023-01-01'
            };

            mockCassandraBillsModel.checkCombinedNotificationKey.callsArgWith(1, null);

            combinedNotificationUtil.validateCombinedNotificationExists(record, mockCassandraBillsModel, false, false, callbackSpy);

            const expectedParams = {
                customerid: '1111111111',
                rechargenumber: '1234567890',
                service: 'Electricity',
                operator: 'TEST'
            };

            expect(mockCassandraBillsModel.checkCombinedNotificationKey).to.have.been.calledWith(expectedParams);
        });
    });

    describe("publishCombinedNotificationToRuDwh", function() {
        let mockPublisher;
        let mockConfig;
        let mockPreparedPayload;

        beforeEach(function() {
            mockPublisher = {
                publishData: sinon.stub()
            };
            
            mockConfig = {
                KAFKA: {
                    SERVICES: {
                        RU_DWH_EVENT: {
                            TOPIC: 'ru_dwh_event'
                        }
                    }
                }
            };

            mockPreparedPayload = {
                payload: {
                    data: {
                        customer_id: 1111111111
                    },
                    source_id: 123,
                    category_id: 456
                }
            };
        });

        afterEach(function() {
            sinon.restore();
        });

        it("should publish to correct topic with correct payload", function() {
            mockPublisher.publishData.callsArgWith(1, null);

            combinedNotificationUtil.publishCombinedNotificationToRuDwh(mockPreparedPayload, mockPublisher, mockConfig);

            const expectedPublisherObject = [{
                topic: 'ru_dwh_event',
                messages: JSON.stringify(mockPreparedPayload.payload),
                key: '1111111111'
            }];

            expect(mockPublisher.publishData).to.have.been.calledWith(expectedPublisherObject);
        });

        it("should handle publisher errors gracefully", function() {
            mockPublisher.publishData.callsArgWith(1, new Error('Kafka error'));

            combinedNotificationUtil.publishCombinedNotificationToRuDwh(mockPreparedPayload, mockPublisher, mockConfig);

            expect(mockPublisher.publishData).to.have.been.called;
        });

        it("should use default topic if not configured", function() {
            const configWithoutTopic = {
                KAFKA: {
                    SERVICES: {
                        RU_DWH_EVENT: {}
                    }
                }
            };

            mockPublisher.publishData.callsArgWith(1, null);

            combinedNotificationUtil.publishCombinedNotificationToRuDwh(mockPreparedPayload, mockPublisher, configWithoutTopic);

            const expectedPublisherObject = [{
                topic: 'ru_dwh_event', // default value
                messages: JSON.stringify(mockPreparedPayload.payload),
                key: '1111111111'
            }];

            expect(mockPublisher.publishData).to.have.been.calledWith(expectedPublisherObject);
        });

        it("should handle missing customer_id gracefully", function() {
            const payloadWithoutCustomerId = {
                payload: {
                    data: {},
                    source_id: 123,
                    category_id: 456
                }
            };

            mockPublisher.publishData.callsArgWith(1, null);

            combinedNotificationUtil.publishCombinedNotificationToRuDwh(payloadWithoutCustomerId, mockPublisher, mockConfig);

            const expectedPublisherObject = [{
                topic: 'ru_dwh_event',
                messages: JSON.stringify(payloadWithoutCustomerId.payload),
                key: ''
            }];

            expect(mockPublisher.publishData).to.have.been.calledWith(expectedPublisherObject);
        });
    });
}); 