/*
jshint 
esversion: 8
*/

'use strict';

import { describe, it, before, beforeEach, afterEach } from 'mocha';
import chai from "chai";
import sinonChai from "sinon-chai";
import chaiAsPromised from "chai-as-promised";
// import MOMENT from 'moment'
import sinon from 'sinon';
import _ from 'lodash'
import nock from 'nock'

import UpdateUserConsentLibrary from '../../lib/updateUserConsent'
import STARTUP_MOCK from '../__mocks__/startUp'
import CryptoJs from  'crypto-js'
import jwt from  'jsonwebtoken'
chai.use(chaiAsPromised);
chai.use(sinonChai);


const { expect } = chai;

let server;

describe("Library: UpdateUserConsentLibrary test suite", function () {
    let libraryObj;

    before(function (done) {
        STARTUP_MOCK.init(function(error, options){
            libraryObj = new UpdateUserConsentLibrary(options);
            done();
        });
    });

    beforeEach(function () {
        server = sinon.fakeServer.create();
    });

    afterEach(function () {
        server.restore();
    });


    it("getDbRecordToUpdate | preferenceValue = 1", (done) => {
        let processedRecord = { 
            "customer_id": 105439219,
            "preferenceValue" : 1,
            "debugKey" : `customer_id:${105439219}_preferenceValue:${1}`
        };
        
        let dbFormatData = libraryObj.getDbRecordToUpdate(processedRecord);
        expect(dbFormatData.customer_id).to.be.equal(105439219);
        expect(dbFormatData.is_retailer).to.be.equal(false);
        expect(dbFormatData.counter).to.be.equal(0);
        expect(dbFormatData.min_date).to.be.equal(null);
        expect(dbFormatData.whatsapp_notification_status).to.be.equal(1);
        return done();
    });

    it("getDbRecordToUpdate | preferenceValue = 0", (done) => {
        let processedRecord = { 
            "customer_id": 105439219,
            "preferenceValue" : 0,
            "debugKey" : `customer_id:${105439219}_preferenceValue:${0}`
        };
        
        let dbFormatData = libraryObj.getDbRecordToUpdate(processedRecord);
        expect(dbFormatData.customer_id).to.be.equal(105439219);
        expect(dbFormatData.is_retailer).to.be.equal(false);
        expect(dbFormatData.counter).to.be.equal(0);
        expect(dbFormatData.min_date).to.be.equal(null);
        expect(dbFormatData.whatsapp_notification_status).to.be.equal(0);
        return done();
    });


    it("getDbRecordToUpdate | preferenceValue did not exists", (done) => {
        let processedRecord = { 
            "customer_id": 105439219,
            //"preferenceValue" : 0,
            "debugKey" : `customer_id:${105439219}_preferenceValue:${null}`
        };
        
        let dbFormatData = libraryObj.getDbRecordToUpdate(processedRecord);
        expect(dbFormatData.customer_id).to.be.equal(105439219);
        expect(dbFormatData.is_retailer).to.be.equal(false);
        expect(dbFormatData.counter).to.be.equal(0);
        expect(dbFormatData.min_date).to.be.equal(null);
        expect(dbFormatData.whatsapp_notification_status).to.be.equal(-1);
        return done();
    });


    it("getRequestDataToUpdate | preferenceValue = 1", (done) => {
        let processedRecord = { 
            "customer_id": 105439219,
            "preferenceValue" : 1,
            "debugKey" : `customer_id:${105439219}_preferenceValue:${1}`
        };
        
        let requestData = libraryObj.getRequestDataToUpdate(processedRecord);
        expect(requestData.customer_id).to.be.equal(105439219);
        expect(requestData.preferenceKey).to.be.equal('ocl.user.consent.ru_whatsapp_reminders');
        expect(requestData.preferenceValue).to.be.equal('ENABLED');
        return done();
    });
    it("getRequestDataToUpdate | preferenceValue = 0", (done) => {
        let processedRecord = { 
            "customer_id": 105439219,
            "preferenceValue" : 0,
            "debugKey" : `customer_id:${105439219}_preferenceValue:${0}`
        };
        
        let requestData = libraryObj.getRequestDataToUpdate(processedRecord);
        expect(requestData.customer_id).to.be.equal(105439219);
        expect(requestData.preferenceKey).to.be.equal('ocl.user.consent.ru_whatsapp_reminders');
        expect(requestData.preferenceValue).to.be.equal('DISABLED');
        return done();
    });
    it("getRequestDataToUpdate | preferenceValue = -1", (done) => {
        let processedRecord = { 
            "customer_id": 105439219,
            "preferenceValue" : -1,
            "debugKey" : `customer_id:${105439219}_preferenceValue:${1}`
        };
        
        let requestData = libraryObj.getRequestDataToUpdate(processedRecord);
        expect(requestData.customer_id).to.be.equal(105439219);
        expect(requestData.preferenceKey).to.be.equal('ocl.user.consent.ru_whatsapp_reminders');
        expect(requestData.preferenceValue).to.be.equal('NOT_AVAILABLE');
        return done();
    });

    
    it("generateJWTForUPS | getUserPreferences JWT token ", (done) => {
        let method = "",
            requestBody = {
                customer_id : 315494420,
                preferenceKey  : "ocl.user.consent.ru_whatsapp_reminders",
                preferenceValue : "ENABLED"
                //preferenceValue : "<ENABLED/DISABLED/NOT_AVAILABLE>"
            };
        let jwt_token_code = libraryObj.generateJWTForUPS(method, requestBody);
        expect(jwt_token_code).to.be.equal('');
        return done();
    });
    
    /**
    it("getUserPreferences | getUserPreferences API ", (done) => {
        let url = "https://run.mocky.io/v3/9eb3f91b-db8e-4e28-8100-a123da09ef5b",
            requestBody = {
                customer_id : 315494418,
                preferenceKey  : "ocl.user.consent.ru_whatsapp_reminders"
            };
        libraryObj.getUserPreferences(function (error, response) {
            expect(error).to.be.equal(null);
            // response = {
            //     "status": 200,
            //     "data": {
            //         "statusInfo": {
            //             "status": "SUCCESS",
            //             "statusMessage": "Request sucessfully served"
            //         },
            //         "response": {
            //             "preference": {
            //                 "key": "ocl.user.consent.ru_whatsapp_reminders",
            //                 "value": 1
            //             }
            //         }
            //     }
            // };
            expect( _.get(response, ['data' , 'response','preference' , 'key'], '') ).to.be.equal("ocl.user.consent.ru_whatsapp_reminders");
            expect( _.get(response, ['data' , 'response','preference' , 'value'], '') ).to.be.equal(1);
            return done();
        }, requestBody,url);
        
        
    });


    it("updateUserPreferences | updateUserPreferences API ", (done) => {
        let url = "https://run.mocky.io/v3/9eb3f91b-db8e-4e28-8100-a123da09ef5b",
            requestBody = {
                customer_id : 315494418,
                preferenceKey  : "ocl.user.consent.ru_whatsapp_reminders",
                preferenceValue : 1
            };
        libraryObj.updateUserPreferences(function (error, response) {
            expect(error).to.be.equal(null);
            // response = {
            //     "status": 200,
            //     "data": {
            //         "statusInfo": {
            //             "status": "SUCCESS",
            //             "statusMessage": "Request sucessfully served"
            //         },
            //         "response": {
            //             "preference": {
            //                 "key": "ocl.user.consent.ru_whatsapp_reminders",
            //                 "value": 1
            //             }
            //         }
            //     }
            // };
            expect( _.get(response, ['data' , 'response','preference' , 'key'], '') ).to.be.equal("ocl.user.consent.ru_whatsapp_reminders");
            expect( _.get(response, ['data' , 'response','preference' , 'value'], '') ).to.be.equal(1);
            return done();
        }, requestBody,url);
        
        
    });
     */



});