/*
jshint 
esversion: 8
*/

'use strict';

import { describe, it, before, beforeEach, afterEach } from 'mocha';
import chai from "chai";
import sinonChai from "sinon-chai";
import chaiAsPromised from "chai-as-promised";
import sinon from 'sinon';
import _ from 'lodash';

import cassandraBills from '../../models/cassandraBills';
import STARTUP_MOCK from '../__mocks__/startUp';

chai.use(chaiAsPromised);
chai.use(sinonChai);

const { expect } = chai;

describe("Module: cassandraBills test suite", function () {
    let cassandraBillsModel, options, mockNotificationNewClusterClient;

    before(function (done) {
        STARTUP_MOCK.init(function(error, mock_options){
            options = mock_options;
            
            // Mock the notificationNewClusterClient
            mockNotificationNewClusterClient = {
                execute: sinon.stub()
            };
            
            // Override the client in options
            options.notificationNewClusterClient = mockNotificationNewClusterClient;
            
            cassandraBillsModel = new cassandraBills(options);
            done();
        });
    });

    describe("checkCombinedNotificationKey", function() {
        let callbackSpy;

        beforeEach(function() {
            callbackSpy = sinon.spy();
            sinon.restore();
            mockNotificationNewClusterClient.execute.reset();
        });

        afterEach(function() {
            sinon.restore();
        });

        it("should execute correct query with proper parameters", function() {
            const params = {
                customerid: '1111111111',
                rechargenumber: '1234567890',
                service: 'electricity',
                operator: 'test'
            };

            const mockResult = {
                rows: []
            };

            mockNotificationNewClusterClient.execute.resolves(mockResult);

            cassandraBillsModel.checkCombinedNotificationKey(params, callbackSpy);

            const expectedQuery = `SELECT * FROM combine_notification_key WHERE customerid = ? AND rechargenumber = ? AND service = ? AND operator = ?`;
            const expectedParams = [
                '1111111111',
                '1234567890',
                'electricity',
                'test'
            ];

            expect(mockNotificationNewClusterClient.execute).to.have.been.calledWith(
                expectedQuery,
                expectedParams,
                { prepare: true }
            );
        });

        it("should call callback with error when record exists", function(done) {
            const params = {
                customerid: '1111111111',
                rechargenumber: '1234567890',
                service: 'electricity',
                operator: 'test'
            };

            const mockResult = {
                rows: [
                    {
                        customerid: '1111111111',
                        rechargenumber: '1234567890',
                        service: 'electricity',
                        operator: 'test'
                    }
                ]
            };

            mockNotificationNewClusterClient.execute.resolves(mockResult);

            cassandraBillsModel.checkCombinedNotificationKey(params, (error, result) => {
                expect(error).to.equal('Combined notification key exists for this recharge number');
                expect(result).to.deep.equal(mockResult.rows[0]);
                done();
            });
        });

        it("should call callback with null when no record exists", function(done) {
            const params = {
                customerid: '1111111111',
                rechargenumber: '1234567890',
                service: 'electricity',
                operator: 'test'
            };

            const mockResult = {
                rows: []
            };

            mockNotificationNewClusterClient.execute.resolves(mockResult);

            cassandraBillsModel.checkCombinedNotificationKey(params, (error, result) => {
                expect(error).to.be.null;
                expect(result).to.be.null;
                done();
            });
        });

        it("should handle database errors gracefully", function(done) {
            const params = {
                customerid: '1111111111',
                rechargenumber: '1234567890',
                service: 'electricity',
                operator: 'test'
            };

            const dbError = new Error('Database connection failed');
            mockNotificationNewClusterClient.execute.rejects(dbError);

            cassandraBillsModel.checkCombinedNotificationKey(params, (error, result) => {
                expect(error).to.be.null;
                expect(result).to.be.null;
                done();
            });
        });

        it("should convert service and operator to lowercase", function() {
            const params = {
                customerid: '1111111111',
                rechargenumber: '1234567890',
                service: 'ELECTRICITY',
                operator: 'TEST_OPERATOR'
            };

            const mockResult = {
                rows: []
            };

            mockNotificationNewClusterClient.execute.resolves(mockResult);

            cassandraBillsModel.checkCombinedNotificationKey(params, callbackSpy);

            const expectedQuery = `SELECT * FROM combine_notification_key WHERE customerid = ? AND rechargenumber = ? AND service = ? AND operator = ?`;
            const expectedParams = [
                '1111111111',
                '1234567890',
                'electricity',  // converted to lowercase
                'test_operator' // converted to lowercase
            ];

            expect(mockNotificationNewClusterClient.execute).to.have.been.calledWith(
                expectedQuery,
                expectedParams,
                { prepare: true }
            );
        });

        it("should convert customerid to string", function() {
            const params = {
                customerid: 1111111111, // number
                rechargenumber: '1234567890',
                service: 'electricity',
                operator: 'test'
            };

            const mockResult = {
                rows: []
            };

            mockNotificationNewClusterClient.execute.resolves(mockResult);

            cassandraBillsModel.checkCombinedNotificationKey(params, callbackSpy);

            const expectedQuery = `SELECT * FROM combine_notification_key WHERE customerid = ? AND rechargenumber = ? AND service = ? AND operator = ?`;
            const expectedParams = [
                '1111111111', // converted to string
                '1234567890',
                'electricity',
                'test'
            ];

            expect(mockNotificationNewClusterClient.execute).to.have.been.calledWith(
                expectedQuery,
                expectedParams,
                { prepare: true }
            );
        });

        it("should handle null result gracefully", function(done) {
            const params = {
                customerid: '1111111111',
                rechargenumber: '1234567890',
                service: 'electricity',
                operator: 'test'
            };

            const mockResult = null;
            mockNotificationNewClusterClient.execute.resolves(mockResult);

            cassandraBillsModel.checkCombinedNotificationKey(params, (error, result) => {
                expect(error).to.be.null;
                expect(result).to.be.null;
                done();
            });
        });

        it("should handle undefined rows in result gracefully", function(done) {
            const params = {
                customerid: '1111111111',
                rechargenumber: '1234567890',
                service: 'electricity',
                operator: 'test'
            };

            const mockResult = {
                rows: undefined
            };
            mockNotificationNewClusterClient.execute.resolves(mockResult);

            cassandraBillsModel.checkCombinedNotificationKey(params, (error, result) => {
                expect(error).to.be.null;
                expect(result).to.be.null;
                done();
            });
        });

        it("should handle multiple rows in result", function(done) {
            const params = {
                customerid: '1111111111',
                rechargenumber: '1234567890',
                service: 'electricity',
                operator: 'test'
            };

            const mockResult = {
                rows: [
                    {
                        customerid: '1111111111',
                        rechargenumber: '1234567890',
                        service: 'electricity',
                        operator: 'test'
                    },
                    {
                        customerid: '1111111111',
                        rechargenumber: '1234567890',
                        service: 'electricity',
                        operator: 'test'
                    }
                ]
            };

            mockNotificationNewClusterClient.execute.resolves(mockResult);

            cassandraBillsModel.checkCombinedNotificationKey(params, (error, result) => {
                expect(error).to.equal('Combined notification key exists for this recharge number');
                expect(result).to.deep.equal(mockResult.rows[0]); // Should return first row
                done();
            });
        });

        it("should log appropriate messages for found record", function(done) {
            const params = {
                customerid: '1111111111',
                rechargenumber: '1234567890',
                service: 'electricity',
                operator: 'test'
            };

            const mockResult = {
                rows: [
                    {
                        customerid: '1111111111',
                        rechargenumber: '1234567890',
                        service: 'electricity',
                        operator: 'test'
                    }
                ]
            };

            mockNotificationNewClusterClient.execute.resolves(mockResult);

            // Mock the logger
            const logSpy = sinon.spy(cassandraBillsModel.L, 'log');

            cassandraBillsModel.checkCombinedNotificationKey(params, (error, result) => {
                expect(logSpy).to.have.been.calledWith(sinon.match(/Found combined notification key/));
                logSpy.restore();
                done();
            });
        });

        it("should log appropriate messages for not found record", function(done) {
            const params = {
                customerid: '1111111111',
                rechargenumber: '1234567890',
                service: 'electricity',
                operator: 'test'
            };

            const mockResult = {
                rows: []
            };

            mockNotificationNewClusterClient.execute.resolves(mockResult);

            // Mock the logger
            const logSpy = sinon.spy(cassandraBillsModel.L, 'log');

            cassandraBillsModel.checkCombinedNotificationKey(params, (error, result) => {
                expect(logSpy).to.have.been.calledWith(sinon.match(/No combined notification key found/));
                logSpy.restore();
                done();
            });
        });

        it("should handle edge case with empty strings", function() {
            const params = {
                customerid: '',
                rechargenumber: '',
                service: '',
                operator: ''
            };

            const mockResult = {
                rows: []
            };

            mockNotificationNewClusterClient.execute.resolves(mockResult);

            cassandraBillsModel.checkCombinedNotificationKey(params, callbackSpy);

            const expectedQuery = `SELECT * FROM combine_notification_key WHERE customerid = ? AND rechargenumber = ? AND service = ? AND operator = ?`;
            const expectedParams = [
                '',
                '',
                '',
                ''
            ];

            expect(mockNotificationNewClusterClient.execute).to.have.been.calledWith(
                expectedQuery,
                expectedParams,
                { prepare: true }
            );
        });
    });
}); 