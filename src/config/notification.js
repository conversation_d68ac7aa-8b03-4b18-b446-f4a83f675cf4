export default {
    common: {
        notificationConsumer: {
            // key is the option passed in argument
            1: {
                TOPIC: 'BR_NOTIFICATION',
                SCHEDULE_ON_INTERVAL: true,
                FROM: '08:00',
                TO: '22:00'
            },
            2: {
                TOPIC: 'PV_NOTIFICATION',
                SCHEDULE_ON_INTERVAL: true,
                FROM: '07:00',
                TO: '22:00'
            },
            3: {
                TOPIC: 'COMMON_NOTIFICATION',
                SCHEDULE_ON_INTERVAL: true,
                FROM: '07:00',
                TO: '22:00'
            },
            4: {
                TOPIC: 'RT_NOTIFICATION',
                SCHEDULE_ON_INTERVAL: false,
            },
            5: {
                TOPIC: 'NONRU_NOTIFICATION',
                SCHEDULE_ON_INTERVAL: true,
                FROM: '07:00',
                TO: '22:00'
            },
            7: {
                TOPIC: 'NOTIFICATION_7',
                SCHEDULE_ON_INTERVAL: true,
                FROM: '07:00',
                TO: '22:00'
            },
            8: {
                TOPIC: 'NOTIFICATION_8',
                SCHEDULE_ON_INTERVAL: true,
                FROM: '08:00',
                TO: '22:00'
            },
            9: {
                TOPIC: 'NOTIFICATION_9',
                SCHEDULE_ON_INTERVAL: true,
                FROM: '09:00',
                TO: '22:00'
            },
            10: {
                TOPIC: 'NOTIFICATION_10',
                SCHEDULE_ON_INTERVAL: true,
                FROM: '10:00',
                TO: '22:00'
            },
            11: {
                TOPIC: 'NOTIFICATION_11',
                SCHEDULE_ON_INTERVAL: true,
                FROM: '11:00',
                TO: '22:00'
            },
            12: {
                TOPIC: 'NOTIFICATION_12',
                SCHEDULE_ON_INTERVAL: true,
                FROM: '12:00',
                TO: '22:00'
            },
            13: {
                TOPIC: 'NOTIFICATION_13',
                SCHEDULE_ON_INTERVAL: true,
                FROM: '07:00', //This queue is used for email and sms notifications which have seperate TPS hence starting it at 7am
                TO: '22:00'
            },
            14: {
                TOPIC: 'NOTIFICATION_14',
                SCHEDULE_ON_INTERVAL: true,
                FROM: '14:00',
                TO: '22:00'
            },
            15: {
                TOPIC: 'NOTIFICATION_15',
                SCHEDULE_ON_INTERVAL: true,
                FROM: '15:00',
                TO: '22:00'
            },
            16: {
                TOPIC: 'NOTIFICATION_16',
                SCHEDULE_ON_INTERVAL: true,
                FROM: '16:00',
                TO: '22:00'
            },
            17: {
                TOPIC: 'NOTIFICATION_17',
                SCHEDULE_ON_INTERVAL: true,
                FROM: '17:00',
                TO: '22:00'
            },
            18: {
                TOPIC: 'NOTIFICATION_18',
                SCHEDULE_ON_INTERVAL: true,
                FROM: '18:00',
                TO: '22:00'
            },
            19: {
                TOPIC: 'NOTIFICATION_19',
                SCHEDULE_ON_INTERVAL: true,
                FROM: '19:00',
                TO: '22:00'
            },
            20: {
                TOPIC: 'NOTIFICATION_20',
                SCHEDULE_ON_INTERVAL: true,
                FROM: '20:00',
                TO: '22:00'
            },
            21: {
                TOPIC: 'NOTIFICATION_21',
                SCHEDULE_ON_INTERVAL: true,
                FROM: '21:00',
                TO: '22:00'
            },
            41: {
                TOPIC: 'HEURISTIC_NOTIFICATION',
                SCHEDULE_ON_INTERVAL: true,
                FROM: '07:00',
                TO: '22:00'
            },
            15: {
                TOPIC: 'NOTIFICATION_22',
                SCHEDULE_ON_INTERVAL: true,
                FROM: '10:00',
                TO: '22:00'
            },
            101: {
                TOPIC: 'NONRU_NOTIFICATION_REALTIME',
                SCHEDULE_ON_INTERVAL: true,
                FROM: '10:00',
                TO: '22:00'
            },
            25: {
                TOPIC: 'WA_NOTIFICATION',
                SCHEDULE_ON_INTERVAL: true,
                FROM: '07:00',
                TO: '22:00'
            },
            26: {
                TOPIC: 'WA_NOTIFICATION_RT',
                SCHEDULE_ON_INTERVAL: false,
            },
            100: {
                TOPIC: 'NONRU_NOTIFICATION_PUSH',
                SCHEDULE_ON_INTERVAL: true,
                FROM: '12:00',
                TO: '22:00'
            },
            101: {
                TOPIC: 'NONRU_NOTIFICATION_REALTIME',
                SCHEDULE_ON_INTERVAL: false
            },
            102: {
                TOPIC: 'NONRU_NOTIFICATION_WHATSAPP',
                SCHEDULE_ON_INTERVAL: true,
                FROM: '07:00',
                TO: '22:00'
            },
            103: {
                TOPIC: 'NONRU_NOTIFICATION_WHATSAPP_REALTIME',
                SCHEDULE_ON_INTERVAL: false
            }
        },
        RETRY_NOTIFICATION: {
            SCHEDULE_ON_INTERVAL: true,
            FROM: '07:00:00',
            TO: '22:00:00'
        },
        IDEAL_DELAY: 2, // delay in seconds
        categoryTopic: {
            1: "NOTIFICATION_7",
            "1_8": "NOTIFICATION_7",
            "1_9": "NOTIFICATION_7",
            "1_29": "RT_NOTIFICATION",
            "1_21" : "RT_NOTIFICATION",
            "1_24": "RT_NOTIFICATION",
            "1_21" : "RT_NOTIFICATION",
            "1_26": "RT_NOTIFICATION",
            "1_27": "RT_NOTIFICATION",
            "1_34": "RT_NOTIFICATION",
            "1_32": "RT_NOTIFICATION",
            "1_33": "RT_NOTIFICATION",
            "2_25": "RT_NOTIFICATION",
            "2_28": "RT_NOTIFICATION",
            "1_35": "RT_NOTIFICATION",
            "1_36": "RT_NOTIFICATION",
            "1_37": "RT_NOTIFICATION",
            "1_38": "RT_NOTIFICATION",
            "41_41": "HEURISTIC_NOTIFICATIONS",
            2: "PV_NOTIFICATION",
            3: "COMMON_NOTIFICATION",
            4: "COMMON_NOTIFICATION",
            5: "COMMON_NOTIFICATION",
            "2_40": "RT_NOTIFICATION",
            '1_42': 'RT_NOTIFICATION',
            2: "NOTIFICATION_7",
            3: "NOTIFICATION_7",
            4: "NOTIFICATION_7",
            5: "NOTIFICATION_7",
            6: "RT_NOTIFICATION",
            7: "NONRU_NOTIFICATION",
            14: "RT_NOTIFICATION",
            41:"HEURISTIC_NOTIFICATION"
        },
        nonRuChannelWiseTopic:{
            PUSH: "NONRU_NOTIFICATION_PUSH",
            CHAT: "NONRU_NOTIFICATION_CHAT",
            EMAIL: "NONRU_NOTIFICATION_EMAIL",
            SMS: "NONRU_NOTIFICATION_SMS",
            WHATSAPP: "NONRU_NOTIFICATION_WHATSAPP"
        },
        nonRuCategoryTopic: {
            "1_4": "NONRU_NOTIFICATION_REALTIME",
            "1_5": "NONRU_NOTIFICATION_REALTIME",
            "1_6": "NONRU_NOTIFICATION_REALTIME",
            "1_7": "NONRU_NOTIFICATION_REALTIME"
        },
        nonRuSource: {
            1: "nonRUbillDuePublisher",
            2: "nonRUbillGenPublisher",
            3: "nonRubillGenConsumer",
            4: "dataExhaust",
            5: "notifyRejectedBills", 
            6: "nonRUbillDuePublisherRealtime", //common for all services
            7: "nonRUbillGenPublisherRealtime" // fastag specific
        },
        nonRurealTimeSendNotificationTopic: "NONRU_NOTIFICATION_REALTIME",
        nonRurealTimeNotificationSourceIds: ['4','6','7'],
        nonRuCategorySourceMap: {
            1: {
                1: 1,
                2: 1,
                3: 1,
                4: 1,
                5: 1,
                6: 1,
                7: 1
            }
        },
        nonRuCategory: {
            1: "nonru",
        },
        category: {
            1: "bill",
            2: "planvalidity",
            3: "validationonly",
            4: "wifirecharge",
            5: "notificationservice",
            6: "forms",
            7: "nonru" ,//digital_reminder_rule_engine -> p2p
            13:"customnotifications",
            14:"customnotificationsapi",
            41:"heuristicnotifications"

        },
        source: {
            1: "billreminder",
            2: "planexpire",
            3: "rechargereminder",
            4: "wifiplanexpire",
            5: "notificationservice",
            6: "formsregistration",
            7: "automatic",
            8: "reminderBillDuePublisher",
            9: "airtelBillFetch", //10pm-7am -> 
            10: "smsParsingCCBillFetch",
            11 : "validationSync",
            12 : "emiDueBillFetch",
            20 : "automatic_rule_engine",
            21 : "smsParsingCCBillFetchRealtime",
            24 : "postpaidBillFetchRealtime",
            25 : "prepaidBillDueRealtime",
            26 : "dataExhaust",
            27 : "nonPaytmBillGen",
            28 : "nonPaytmBillDue",
            22 : "nonRUBillGenPublisher", //category 7
            23 : "nonRUBillDuePublisher",    //category 7
            24 : "postpaidBillFetchRealtime",
            29 : "paytmPostpaidBillFetchRealtime",
            30 : "paytmPostpaidBillFetch",
            34 : "BillGenPublisherRealTime",
            32 : "BillGenValidationSyncRealTime",
            33 : "BillGenDwhCCSmsParsingRealTime",
            31 : "reminderBillDuePublisherPaytmPostpaid",
            35 : "postpaidBillFetchDWHRealtime",
            36 : "prepaidBillDueDWHRealtime",
            37 : "smsParsingCCBillFetchDWHRealtime",
            38 : "airtelBillFetchRealtime",
            39: "reminderOldBillDuePublisher",
            40 : "prepaidsmsParsingBillDue",
            13 : "customNotifications",
            14 : "customNotificationsapi",
            15: "reminderPrepaidBillDuePublisher",
            41:"heuristicnotifications",
            42: "whatsappNotificationFallback",
            43: "rent_automatic_rule_engine",
            44: "combinedNotificationCustomerIdService"
        },
        categorySourceMap: {
            1: {
                1: 1,
                8: 1,
                9: 1,
                10 : 1,
                11 : 1,
                12 : 1,
                13 : 1, 
                15: 1,
                29 : 1,
                30 : 1,
                24 : 1,
                25 : 1,
                26 : 1,
                27 : 1,
                29 : 1,
                30 : 1,
                21 : 1,
                31 : 1,
                32 : 1,
                33 : 1,
                34 : 1,
                35 : 1,
                36 : 1,
                37 : 1,
                38 : 1,
                39: 1,
                42: 1,
                44: 1
            },
            2: {
                2: 1,
                25: 1,
                28: 1,
                36: 1,
                40: 1
            },
            3: {
                3: 1
            },
            4: {
                4: 1
            },
            5: {
                5: 1,
                20: 1,
                43: 1
            },
            6: {
                6: 1,
                7: 1,
                21: 1
            },
            7: {
                22: 1,
                23: 1
            },
            13: {
                13: 1
            },
            14: {
                14: 1
            },
            15: {
                15: 1
            },
            41: {
                41: 1
            },
            15: {
                15: 1
            }

        },
        customerType: {
            RETAILER: 4
        },
        BILL_DUE_PUBLISHER_BATCH: 500,
        BILL_GEN_DATE_PUBLISHER_BATCH: 500,
        type: {
            SMS: 1,
            PUSH: 1,
            EMAIL: 1,
            CHAT: 1,
            WHATSAPP: 1
        },
        status: {
            PENDING: 0,
            SENT: 1,
            CANCELED: 2,
            ERROR: 3,
            SUCCESS: 4,
            FAILED: 5,
            RETRY: 6,
            RESCHEDULE: 7,
            DISABLED: 8,
            RETRY_FALLBACK: 9,
            FAILED_FALLBACK: 10,
            USER_RESPONSE: 11
        },
        notificationReportStatus: {
            PENDING: 0,
            SENT: 1,
            ERROR: 3,
        },
        category_url_type: {
            mobile_prepaid: [17],
            mobile_postpaid: [21],
            datacard_prepaid: [19],
            datacard_postpaid: [23],
            dth: [18],
            utility: [104154, 107730, 64739, 37217, 75505, 123988, 205763, 132935, 127781, 46007, 77409, 101950, 68869, 78640, 100253, 166690, 26],
            utility_creditcard: [131655],
        },
        get category_url_type_map() {
            let self = this;
            if (self._category_url_type_map) {
                return self._category_url_type_map;
            }
            let map = {};
            Object.keys(self.category_url_type).forEach(function (landing_path) {
                let categoryIds = self.category_url_type[landing_path];
                categoryIds.forEach(function (category_id) {
                    map[category_id] = landing_path;
                });
            });
            self._category_url_type_map = map;
            return self._category_url_type_map;
        },
        ignoreAmountInURLForMobilePrepaidOperators : {
            'airtel' : 1,
            'jio' : 1,
            'idea' : 1,
            'vodafone' : 1,
            'vodafone idea' : 1
        },
        templateid: {
            BR_BILLGEN_EMAIL: 4597,
            BR_BILLGEN_SMS: null,
            BR_BILLGEN_PUSH: 4868,
            BR_BILLGEN_CHAT: null,
            BR_DUEDATE_EMAIL: 4602,
            BR_DUEDATE_SMS: null,
            BR_DUEDATE_PUSH: 4753,
            BR_DUEDATE_CHAT: null,
            BR_PREPAID_SMS: 6335,
            BR_PREPAID_PUSH: 6336,
            BR_PREPAID_CHAT: null
        },
        template_titles: {
            4597: 'Bill Fetch - Email',
            4598: 'Bill Fetch - SMS',
            4868: 'Bill Fetch - Push',
            4602: 'Due Date - Email',
            4600: 'Due Date - SMS',
            4753: 'Due Date - Push',
            6335: 'Prepaid - SMS',
            6336: 'Prepaid - Push'
        },
        scheduletimeinterval: {
            BR_DUEDATE_INTERVAL: [ 1 ],
            BR_BILLGENDATE_INTERVAL: [ 1 ],
            BR_BILLGEN_DUEDATE_DIFF: 3,
            BR_NOTIFICATION_START_TIME: '07:00:00',
            BR_NOTIFICATION_END_TIME: '21:30:00',
            DUE_DATE_NOTIFICATOION_EXPIRY_TIME: '22:00:00'
        },
        operatorDueDateMapping : {
            /**operatorName : [ 2, 0]  */
        },
        operatorTableDueDateMapping : {
            bills_creditcard : [7, 4, 1, 2 , 0, -1],
            bills_airteltv : [ -1, 0, 1 ]
        },
        /**
        BlackListOperator: [
            'dps_dps noida', 'dps_dps greater noida', 'manappuram', 'icici prudential life insurance',
            'indiafirst life insurance', 'matrix postpaid','tikona broadband', 'connect broadband', 'rent payment',
            'indane' , 'bharatgas' , 'hp gas' , 'lic' , 'manipur state power distribution company limited (mspdcl)'
        ],
        */
        registeredUserNotificationOperator: [
            'vodafone','vodafone idea'
        ],
        billReminderNotificationIntervals: [
            {
                timestamp1: '00:00:00',
                timestamp2: '07:00:00'
            },
            {
                timestamp1: '07:00:00',
                timestamp2: '12:00:00'
            },
            {
                timestamp1: '12:00:00',
                timestamp2: '18:00:00'
            },
            {
                timestamp1: '18:00:00',
                timestamp2: '22:00:00'
            },
            {
                timestamp1: '22:00:00',
                timestamp2: '23:59:59'
            }
        ],
        RECHARGE_NUDGE_TEMPLATE_ID: {
            "DEFAULT": {
                "d": 4879,
                "d+2": 5377
            },
            "cylinder booking": {
                "d": 7295,
                "d+2": 7295
            }
        },
        CUSTOM_NUDGE_TEMPLATE_ID: {
            "DEFAULT": 4879
        
        },
        OPERATOR_UP_NOTIFY_TEMPLATE: {
            "DEFAULT": {
                "d": 9698,
                "d+2": 9698
            }
        },
        RECHARGE_NUDGE_UTM: {
            "d": "utm_source=ValidationNotification&utm_medium=push&utm_campaign=validation",
            "d+2": "utm_source=automaticReminders&utm_medium=push&utm_campaign=billfetchdrop_d2",
            "notfound": "utm_source=nudgeServicenotFound&utm_medium=push"
        },
        DEFAULT_PVREMINDER_DAYVALUES: [0, 2],
        PVREMINDER_TEMPLATE_CONFIG: {
            '0': {
                "CUSTOMER_PUSH": 4863,
                "CUSTOMER_SMS": null,
                "CUSTOMER_CHAT": 7758,
                "RN_CUSTOMER_PUSH": 5069,
                "RN_CUSTOMER_SMS": 5138,
                "RN_CUSTOMER_CHAT": 7759,
                "RN_CUSTOMER_SMS_PAYTM_USER": null,
                "RN_CUSTOMER_SMS_NONPAYTM_USER": null,
            },
            '2': {
                "CUSTOMER_PUSH": 5193,
                "CUSTOMER_SMS": null,
                "CUSTOMER_CHAT": 7574,
                "RN_CUSTOMER_PUSH": 19635,
                "RN_CUSTOMER_CHAT": 7574,
                "RN_CUSTOMER_SMS_PAYTM_USER": null,
                "RN_CUSTOMER_SMS_NONPAYTM_USER": null,
                "MIN_PLAN_DURATION": 7 // in days
            },
            '-5': {
                "CUSTOMER_PUSH": 15729,
                "RN_CUSTOMER_PUSH":19627,
                "MIN_PLAN_DURATION": 7 // in days
            },
            '-2': {
                "CUSTOMER_PUSH": 15730,
                "RN_CUSTOMER_PUSH": 19629,
                "MIN_PLAN_DURATION": 7 // in days
            },
            '1': {
                "CUSTOMER_PUSH": 15733,
                "RN_CUSTOMER_PUSH": 19631,
                "MIN_PLAN_DURATION": 7 // in days
            },
            '-1': {
                "CUSTOMER_PUSH": 15732,
                "RN_CUSTOMER_PUSH": 19630,
                "MIN_PLAN_DURATION": 7 // in days
            },
            '5': {
                "CUSTOMER_PUSH": 15734,
                "RN_CUSTOMER_PUSH":19637,
                "MIN_PLAN_DURATION": 7 // in days
            }
        },
        /** 
        TEMPLATE_UTM: {
            notFound: {
                "utm_source": "billReminderNotFound",
                "utm_medium": "null",
                "utm_campaign": "null"
            },
            4597: {
                "utm_source": "billGenReminder",
                "utm_medium": "email",
                "utm_campaign": "4597"
            },
            4598: {
                "utm_source": "billGenReminder",
                "utm_medium": "sms",
                "utm_campaign": "4598"
            },
            4868: {
                "utm_source": "billGenReminder",
                "utm_medium": "push",
                "utm_campaign": "4868"
                
            },
            4602: {
                "utm_source": "billDueDateReminder",
                "utm_medium": "email",
                "utm_campaign": "4602"
            },
            4600: {
                "utm_source": "billDueDateReminder",
                "utm_medium": "sms",
                "utm_campaign": "4600"
            },
            4753: {
                "utm_source": "billDueDateReminder",
                "utm_medium": "push",
                "utm_campaign": "4753"
            },
            7214: {
                "utm_source": "billDueDateReminder",
                "utm_medium": "push",
                "utm_campaign": "7214"
            },
            6760: {
                "utm_source": "billDueDateReminder",
                "utm_medium": "push",
                "utm_campaign": "6760"
            },
            6759: {
                "utm_source": "billDueDateReminder",
                "utm_medium": "sms",
                "utm_campaign": "6759"
            },
            7215: {
                "utm_source": "billDueDateReminder",
                "utm_medium": "sms",
                "utm_campaign": "7215"

            },
            6801: {
                "utm_source": "billDueDateReminder",
                "utm_medium": "email",
                "utm_campaign": "6801"
            },
            6335: {
                "utm_source": "prepaidReminder_D-1",
                "utm_medium": "sms",
                "utm_campaign": "6335"
            },
            6336: {
                "utm_source": "prepaidReminder_D-1",
                "utm_medium": "push",
                "utm_campaign": "6336"
            },
            6337: {
                "utm_source": "billGenReminder_NDD",
                "utm_medium": "push",
                "utm_campaign": "6337"
            },
            6338: {
                "utm_source": "billGenReminder_NDD",
                "utm_medium": "sms",
                "utm_campaign": "6338"
            },
            6604: {
                "utm_source": "billDueDateReminder",
                "utm_medium": "email",
                "utm_campaign": "6604"
            },
            6399: {
                "utm_source": "billGenReminder",
                "utm_medium": "push",
                "utm_campaign": "6399"
            },
            6398: {
                "utm_source": "billGenReminder",
                "utm_medium": "sms",
                "utm_campaign": "6398"
            },
            7233: {
                "utm_source": "billGenReminder",
                "utm_medium": "sms",
                "utm_campaign": "7233"
            },
            7040: {
                "utm_source": "billGenReminder",
                "utm_medium": "sms",
                "utm_campaign": "7040"
            },
            7041: {
                "utm_source": "billGenReminder",
                "utm_medium": "push",
                "utm_campaign": "7041"
            },
            7234: {
                "utm_source": "billGenReminder",
                "utm_medium": "push",
                "utm_campaign": "7234"
            },
            7042: {
                "utm_source": "billDueDateReminder",
                "utm_medium": "sms",
                "utm_campaign": "7042"
            },
            7043: {
                "utm_source": "billDueDateReminder",
                "utm_medium": "push",
                "utm_campaign": "7043"
            },
            // 7092: { As discussed with shreyas disabling for paytm first cc and enabling for sms parsing credit card.
            //     "utm_source": "billGenerationReminder",
            //     "utm_medium": "email"
            //},
            7940: {
                "utm_source": "billGenReminder",
                "utm_medium": "push",
                "utm_campaign": "7940"
            },
            7129: {
                "utm_source": "billGenReminder",
                "utm_medium": "sms",
                "utm_campaign": "7129"
            },
            7130: {
                "utm_source": "billGenReminder",
                "utm_medium": "push",
                "utm_campaign": "7130"
            },
            // 7093: {As discussed with shreyas disabling for paytm first cc and enabling for sms parsing credit card.
            //     "utm_source": "billDueDateReminder",
            //     "utm_medium": "email"
            // },
            6555: {
                "utm_source": "billGenReminder",
                "utm_medium": "email",
                "utm_campaign": "6555"
            },
            4863: {
                "utm_source": "prepaidReminder_D",
                "utm_medium": "push",
                "utm_campaign": "4863"
            },
            7231: {
                "utm_source": "prepaidReminder_D-1",
                "utm_medium": "push",
                "utm_campaign": "7231"
            },
            5137: {
                "utm_source": "prepaidReminder_D",
                "utm_medium": "sms",
                "utm_campaign": "5137"
            },
            7232: {
                "utm_source": "prepaidReminder_D-1",
                "utm_medium": "sms",
                "utm_campaign": "7232"
            },
            5069: {
                "utm_source": "prepaidReminder_D_RN",
                "utm_medium": "push",
                "utm_campaign": "5069"
            },
            7236: {
                "utm_source": "prepaidReminder_D-1_RN",
                "utm_medium": "push",
                "utm_campaign": "7236"
            },
            5138: {
                "utm_source": "prepaidReminder_D_RN",
                "utm_medium": "sms",
                "utm_campaign": "5138"
            },
            7237: {
                "utm_source": "prepaidReminder_D-1_RN",
                "utm_medium": "sms",
                "utm_campaign": "7237"
            },
            5070: {
                "utm_source": "prepaidReminder_D_NPU",
                "utm_medium": "sms",
                "utm_campaign": "5070"
            },
            7238: {
                "utm_source": "prepaidReminder_D-1_NPU",
                "utm_medium": "sms",
                "utm_campaign": "7238"
            },
            5193: {
                "utm_source": "prepaidReminder_D+2",
                "utm_medium": "push",
                "utm_campaign": "5193"
            },
            7127: {
                "utm_source": "billGenReminder",
                "utm_medium": "sms",
                "utm_campaign": "7127"
            },
            7141: {
                "utm_source": "billGenReminder",
                "utm_medium": "sms",
                "utm_campaign": "7141"
            },
            7146: {
                "utm_source": "billGenReminder",
                "utm_medium": "sms",
                "utm_campaign": "7146"
            },
            7149: {
                "utm_source": "billGenReminder",
                "utm_medium": "sms",
                "utm_campaign": "7149"
            },
            7126: {
                "utm_source": "billGenReminder",
                "utm_medium": "push",
                "utm_campaign": "7126"
            },
            7133: {
                "utm_source": "billGenReminder",
                "utm_medium": "push",
                "utm_campaign": "7133"
            },
            7145: {
                "utm_source": "billGenReminder",
                "utm_medium": "push",
                "utm_campaign": "7145"
            },
            7154: {
                "utm_source": "billGenReminder",
                "utm_medium": "push",
                "utm_campaign": "7154"
            },
            7151: {
                "utm_source": "billGenReminder",
                "utm_medium": "push",
                "utm_campaign": "7151"
            },
            7098: {
                "utm_source": "billDueDateReminder",
                "utm_medium": "sms",
                "utm_campaign": "7098"
            },
            7136: {
                "utm_source": "billDueDateReminder",
                "utm_medium": "sms",
                "utm_campaign": "7136"
                
            },
            7147: {
                "utm_source": "billDueDateReminder",
                "utm_medium": "sms",
                "utm_campaign": "7147"
            },
            7099: {
                "utm_source": "billDueDateReminder",
                "utm_medium": "push",
                "utm_campaign": "7099"
            },
            7134: {
                "utm_source": "billDueDateReminder",
                "utm_medium": "push",
                "utm_campaign": "7134"
            },
            7142: {
                "utm_source": "billDueDateReminder",
                "utm_medium": "push",
                "utm_campaign": "7142"
            },
            7153: {
                "utm_source": "billDueDateReminder",
                "utm_medium": "push",
                "utm_campaign": "7153"
            },
            7131: {
                "utm_source": "IN_internal_PV1",
                "utm_medium": "sms",
                "utm_campaign": "DTH_7131"
            },
            7132: {
                "utm_source": "IN_internal_PV1",
                "utm_medium": "push",
                "utm_campaign": "DTH_7132"
            },
            7143: {
                "utm_source": "billDueDateReminder",
                "utm_medium": "push",
                "utm_campaign": "7143"
            },
            7137: {
                "utm_source": "billDueDateReminder",
                "utm_medium": "sms",
                "utm_campaign": "7137"
            },
            7186: {
                "utm_source": "billGenReminder",
                "utm_medium": "push",
                "utm_campaign": "7186"
            },
            7197: {
                "utm_source": "billDueDateReminder",
                "utm_medium": "sms",
                "utm_campaign": "7197"
            },
            7198: {
                "utm_source": "billDueDateReminder",
                "utm_medium": "push",
                "utm_campaign": "7198"
            },
            7187: {
                "utm_source": "billGenReminder",
                "utm_medium": "sms",
                "utm_campaign": "7187"
            },
            7350: {
                "utm_source": "prepaidReminder_D-6_D-3",
                "utm_medium": "push",
                "utm_campaign": "AirtelAPI_7350"
            },
            7351: {
                "utm_source": "prepaidReminder_D-1",
                "utm_medium": "push",
                "utm_campaign": "AirtelAPI_7351"
            },
            7352: {
                "utm_source": "prepaidReminder_D",
                "utm_medium": "sms",
                "utm_campaign": "AirtelAPI_7352"
            },
            7353: {
                "utm_source": "prepaidReminder_Genric",
                "utm_medium": "push",
                "utm_campaign": "AirtelAPI_7353"
            },
            7392: {
                "utm_source": "prepaidReminder_D",
                "utm_medium": "push",
                "utm_campaign": "AirtelAPI_7392"
            },
            7393: {
                "utm_source": "prepaidReminder_D+1",
                "utm_medium": "push",
                "utm_campaign": "AirtelAPI_7393"
            },
            7398: {
                "utm_source": "prepaidReminder_D+3",
                "utm_medium": "push",
                "utm_campaign": "AirtelAPI_7398"
            },
            8173: {
                "utm_source": "IN_PV_D+1",
                "utm_medium": "push",
                "utm_campaign": "8173"
            },
            8459: {
                "utm_source": "IN_PV_D+1",
                "utm_medium": "chat",
                "utm_campaign": "8459"
            },
            8456: {
                "utm_source": "IN_PV_D+2",
                "utm_medium": "push",
                "utm_campaign": "8456"
            },
            8460: {
                "utm_source": "IN_PV_D+2",
                "utm_medium": "chat",
                "utm_campaign": "8460"
            },
            8457: {
                "utm_source": "IN_PV_D",
                "utm_medium": "push",
                "utm_campaign": "8457"
            },
            8458: {
                "utm_source": "IN_PV_D",
                "utm_medium": "chat",
                "utm_campaign": "8458"
            },
            8461: {
                "utm_source": "IN_PV_D",
                "utm_medium": "sms",
                "utm_campaign": "8461"
            },

            // Chat templates
            7574: {
                "utm_source": "prepaidReminder_D+2",
                "utm_medium": "chat",
                "utm_campaign": "7574"
            },
            '7573': { 
                "utm_source": 'billGenReminder', 
                "utm_medium": "chat" ,
                "utm_campaign": "7573"
            },
            '7575': { 
                "utm_source": 'billDueDateReminder', 
                "utm_medium": "chat",
                "utm_campaign": "7575" 
            },
            '7580': { 
                "utm_source": 'billDueDateReminder', 
                "utm_medium": "chat",
                "utm_campaign": "7580"
            },
            '7581': { 
                "utm_source": 'billGenReminder', 
                "utm_medium": "chat",
                "utm_campaign": "7581"
            },
            '7582': { 
                "utm_source": 'billGenReminder', 
                "utm_medium": "chat",
                "utm_campaign": "7582"
            },
            '7583': { 
                "utm_source": 'billDueDateReminder', 
                "utm_medium": "chat",
                "utm_campaign": "7583"
            },
            '7584': { 
                "utm_source": 'billGenReminder', 
                "utm_medium": "chat" ,
                "utm_campaign": "7584"
            },
            '7587' : {
                "utm_source": 'billDueDateReminder', 
                "utm_medium": "chat",
                "utm_campaign": "7587" 
            },
            '7588': { 
                "utm_source": 'billDueDateReminder', 
                "utm_medium": "chat",
                "utm_campaign": "7588" 
            },
            '7590' : {
                "utm_source": 'billGenReminder', 
                "utm_medium": "chat",
                "utm_campaign": "7590"
            },
            '7595': { 
                "utm_source": 'billDueDateReminder', 
                "utm_medium": "chat",
                "utm_campaign": "7595" 
            },
            '7599': { 
                "utm_source": 'billGenReminder', 
                "utm_medium": "chat",
                "utm_campaign": "7599" 
            },
            '7600': { 
                "utm_source": 'IN_internal_PV1', 
                "utm_medium": "chat" ,
                "utm_campaign": "DTH_7600"
            },
            '7602': { 
                "utm_source": 'billDueDateReminder', 
                "utm_medium": "chat",
                "utm_campaign": "7602" 
            },
            '7603': { 
                "utm_source": 'billGenReminder', 
                "utm_medium": "chat",
                "utm_campaign": "7603" 
            },
            '7605': { 
                "utm_source": 'billGenReminder', 
                "utm_medium": "chat",
                "utm_campaign": "7605"
            },
            '7606': { 
                "utm_source": 'billDueDateReminder', 
                "utm_medium": "chat",
                "utm_campaign": "7606" 
            },
            '7607': { 
                "utm_source": 'billGenReminder', 
                "utm_medium": "chat",
                "utm_campaign": "7607" 
            },
            '7608': { 
                "utm_source": 'billDueDateReminder', 
                "utm_medium": "chat",
                "utm_campaign": "7608" 
            },
            '7092': {
                "utm_source": "billGenReminder",
                "utm_medium": "email",
                "utm_campaign": "7092"
            },
            '7093': {
                "utm_source": "billDueDateReminder",
                "utm_medium": "email",
                "utm_campaign": "7093"
            },
            '7758': {
                "utm_source": "prepaidReminder_D",
                "utm_medium": "chat",
                "utm_campaign": "7758"
            },
            '7759': {
                "utm_source": "prepaidReminder_D_RN",
                "utm_medium": "chat",
                "utm_campaign": "7759"
            },
            '7760': {
                "utm_source": "prepaidReminder_D-1",
                "utm_medium": "chat",
                "utm_campaign": "7760"
            },
            '7761': {
                "utm_source": "prepaidReminder_D-1_RN",
                "utm_medium": "chat",
                "utm_campaign": "7761"
            },
            '7347': {
                "utm_source": 'prepaidReminder_D-7', 
                "utm_medium": "push" ,
                "utm_campaign": "7347"
            },
            '7348': {
                "utm_source": 'prepaidReminder_D-1', 
                "utm_medium": "push",
                "utm_campaign": "7348" 
            },
            '7390': {
                "utm_source": 'prepaidReminder_D', 
                "utm_medium": "push",
                "utm_campaign": "7390" 
            },
            '7391': {
                "utm_source": 'prepaidReminder_D+2', 
                "utm_medium": "push",
                "utm_campaign": "7391" 
            },

            '7794': {
                "utm_source": 'prepaidReminder_D-7', 
                "utm_medium": "chat",
                "utm_campaign": "7794" 
            },
            '7795': {
                "utm_source": 'prepaidReminder_D-1', 
                "utm_medium": "chat",
                "utm_campaign": "7795" 
            },
            '7796': {
                "utm_source": 'prepaidReminder_D', 
                "utm_medium": "chat",
                "utm_campaign": "7796" 
            },
            '7797': {
                "utm_source": 'prepaidReminder_D+1', 
                "utm_medium": "chat",
                "utm_campaign": "7797" 
            },

            '7822': {
                "utm_source": 'prepaidReminder_D-7_POP', 
                "utm_medium": "push",
                "utm_campaign": "VISync_7822" 
            },
            '7823': {
                "utm_source": 'prepaidReminder_D-1_POP', 
                "utm_medium": "push",
                "utm_campaign": "VISync_7823" 
            },
            '7824': {
                "utm_source": 'prepaidReminder_D_POP', 
                "utm_medium": "push",
                "utm_campaign": "VISync_7824"  
            },
            '7825': {
                "utm_source": 'prepaidReminder_D+1_D+2_POP', 
                "utm_medium": "push",
                "utm_campaign": "VISync_7825"  
            },

            '7826': {
                "utm_source": 'prepaidReminder_D-3_POP', 
                "utm_medium": "chat",
                "utm_campaign": "VISync_7826"  
            },
            '7827': {
                "utm_source": 'prepaidReminder_D-1_POP', 
                "utm_medium": "chat" ,
                "utm_campaign": "VISync_7827" 
            },
            '7828': {
                "utm_source": 'prepaidReminder_D_POP', 
                "utm_medium": "chat",
                "utm_campaign": "VISync_7828"  
            },
            '7829': {
                "utm_source": 'prepaidReminder_D+1_D+2_POP', 
                "utm_medium": "chat",
                "utm_campaign": "VISync_7829"  
            },
            '7873': {
                "utm_source": 'prepaidReminder_D_POP', 
                "utm_medium": "sms",
                "utm_campaign": "VISync_7873"  
            },

            '7349': {
                "utm_source": 'prepaidReminder_D', 
                "utm_medium": "sms",
                "utm_campaign": "7349"  
            },
            
            // PostPaid generic TemplateID and UTM Mapping
            
            '8169': {
                "utm_source": 'billDueDateReminder', 
                "utm_medium": "sms",
                "utm_campaign": "8169"  
            },
            '8170': {
                "utm_source": 'billDueDateReminder', 
                "utm_medium": "push" ,
                "utm_campaign": "8170" 
            },
            '8172': {
                "utm_source": 'billDueDateReminder', 
                "utm_medium": "chat",
                "utm_campaign": "8172" 
            },

            '8167': {
                "utm_source": 'billGenReminder', 
                "utm_medium": "sms",
                "utm_campaign": "8167" 
            },
            '8168': {
                "utm_source": 'billGenReminder', 
                "utm_medium": "push",
                "utm_campaign": "8168"  
            },
            '8171': {
                "utm_source": 'billGenReminder', 
                "utm_medium": "chat",
                "utm_campaign": "8171" 
            },
            
            // Due date reminder - TemplateID and UTM Mapping for D & D-2
            
            '8470': {
                "utm_source": "billDueDateReminder",
                "utm_medium": "sms",
                "utm_campaign": "8470"
            },
            '8468': {
                "utm_source": "billDueDateReminder",
                "utm_medium": "push",
                "utm_campaign": "8468"
            },
            '8469': { 
                "utm_source": 'billDueDateReminder', 
                "utm_medium": "chat",
                "utm_campaign": "8469" 
            },
            '8472': {
                "utm_source": "billDueDateReminder",
                "utm_medium": "email",
                "utm_campaign": "8472"
            },
            '8466': {
                "utm_source": "billDueDateReminder",
                "utm_medium": "sms",
                "utm_campaign": "8466"
            },
            '8464': {
                "utm_source": "billDueDateReminder",
                "utm_medium": "push",
                "utm_campaign": "8464"
            },
            '8465': { 
                "utm_source": 'billDueDateReminder', 
                "utm_medium": "chat",
                "utm_campaign": "8465" 
            },
            '8471': {
                "utm_source": "billDueDateReminder",
                "utm_medium": "email",
                "utm_campaign": "8471"
            },
            
            //  EMI due consumer - TemplateID and UTM Mapping
            
            '8549': {
                "utm_source": 'emi_due_reminder', 
                "utm_medium": "chat",
                "utm_campaign": "hero_fincorp" 
            },
            '8550': {
                "utm_source": 'emi_due_reminder', 
                "utm_medium": "push",
                "utm_campaign": "hero_fincorp" 
            },
            '8562': {
                "utm_source": 'emi_due_reminder', 
                "utm_medium": "sms",
                "utm_campaign": "hero_fincorp" 
            },
            
            // airtelTv - TemplateID and UTM Mapping
             
            '8636': {
                "utm_source": "INPVDminus1",
                "utm_medium": "push",
                "utm_campaign": "8636"
            },
            '8637': {
                "utm_source": "INPVDminus1",
                "utm_medium": "chat",
                "utm_campaign": "8637"
            },
            '8638': {
                "utm_source": "INPVD",
                "utm_medium": "push",
                "utm_campaign": "8638"
            },
            '8639': {
                "utm_source": "INPVD",
                "utm_medium": "chat",
                "utm_campaign": "8639"
            },
            '8642': {
                "utm_source": "INPVD",
                "utm_medium": "sms",
                "utm_campaign": "8642"
            },
            '8640': {
                "utm_source": "INPVDplus1",
                "utm_medium": "push",
                "utm_campaign": "8640"
            },
            '8641': {
                "utm_source": "INPVDplus1",
                "utm_medium": "chat",
                "utm_campaign": "8641"
            },
            // aditya birla finance limited specific templates 
            '8668': {
                "utm_source": 'EEMI_ABFL_FinCorp_Bill_Gen_Push', 
                "utm_medium": "push",
                "utm_campaign": "abfl" 
            },
            '8667': {
                "utm_source": 'EMI_ABFL_FinCorp_Bill_Gen_Chat', 
                "utm_medium": "chat",
                "utm_campaign": "abfl"
            },
            '8669': {
                "utm_source": 'EMI_ABFL_FinCorp_Bill_Gen_SMS', 
                "utm_medium": "sms",
                "utm_campaign": "abfl" 
            },
            //fullerton india credit company limited specific templates
            '8666': {
                "utm_source": 'EMI_fullerton_FinCorp_Bill_Gen_Push', 
                "utm_medium": "push",
                "utm_campaign": "8666" 
            },
            '8660': {
                "utm_source": 'EMI_fullerton_FinCorp_Bill_Gen_Chat', 
                "utm_medium": "chat",
                "utm_campaign": "8660"
            },
            '8662': {
                "utm_source": 'EMI_fullerton_FinCorp_Bill_Gen_SMS', 
                "utm_medium": "sms",
                "utm_campaign": "8662" 
            },
            // fullerton india housing finance limited specific templates
            '8664': {
                "utm_source": 'EMI_fullerton_FinCorp_Bill Gen_Push', 
                "utm_medium": "push",
                "utm_campaign": "8664" 
            },
            '8663': {
                "utm_source": 'EMI_fullerton_FinCorp_Bill_Gen_Chat', 
                "utm_medium": "chat",
                "utm_campaign": "8663"
            },
            '8665': {
                "utm_source": 'EMI_fullerton_FinCorp_Bill_Gen_SMS', 
                "utm_medium": "sms",
                "utm_campaign": "8665" 
            },
            // Clix specific templates
            '8774': {
                "utm_source": 'EMI_CLIX_Bill_Gen_Push', 
                "utm_medium": "push",
                "utm_campaign": "8774" 
            },
            '8773': {
                "utm_source": 'EMI_CLIX_Bill_Gen_Chat', 
                "utm_medium": "chat",
                "utm_campaign": "8773"
            },
            // StashFin-CAAS - start
            '8823': {
                "utm_source": 'EMI_StashFin_Bill_Gen_Push', 
                "utm_medium": "push",
                "utm_campaign": "stashfin" 
            },
            '8822': {
                "utm_source": 'EMI_StashFin_Bill_Gen_Chat', 
                "utm_medium": "chat",
                "utm_campaign": "stashfin"
            },
            // StashFin-CAAS - end
            // L&T Finance Limited-CAAS - start
            '8825': {
                "utm_source": 'EMI_LnT_Bill_Gen_Push', 
                "utm_medium": "push",
                "utm_campaign": "l&tfs" 
            },
            '8824': {
                "utm_source": 'EMI_LnT_Bill_Gen_Chat',
                "utm_medium": "chat",
                "utm_campaign": "l&tfs"
            },
            // L&T Finance Limited-CAAS - end
            '8623': {
                "utm_source": 'billDueDateReminder', 
                "utm_medium": "push",
                "utm_campaign": "8623" 
            },
            '8621': {
                "utm_source": 'billDueDateReminder', 
                "utm_medium": "push",
                "utm_campaign": "8621" 
            },
            '8622': {
                "utm_source": 'billDueDateReminder', 
                "utm_medium": "push",
                "utm_campaign": "8622" 
            },
            '8627': {
                "utm_source": 'billDueDateReminder', 
                "utm_medium": "chat",
                "utm_campaign": "8627" 
            },
            '8628': {
                "utm_source": 'billDueDateReminder', 
                "utm_medium": "chat",
                "utm_campaign": "8628" 
            },
            '8629': {
                "utm_source": 'billDueDateReminder', 
                "utm_medium": "chat",
                "utm_campaign": "8629" 
            },
            '8630': {
                "utm_source": 'billDueDateReminder', 
                "utm_medium": "chat",
                "utm_campaign": "8630" 
            },
            
        },
        TEMPLATE_ID_BY_SERVICE: {
            //`BR_${service}_${notificationType}_SMS
            "BR_MOBILE_BILLGEN_SMS": 7127,
            "BR_MOBILE_BILLGEN_PUSH": 7126,
            "BR_MOBILE_BILLGEN_CHAT": 7573,

            "BR_MOBILE_DUEDATE_SMS": 7098,
            "BR_MOBILE_DUEDATE_PUSH": 7099,
            "BR_MOBILE_DUEDATE_CHAT": 7580,

            "BR_MOBILE_BILLGEN_GENERIC_SMS": 8167,
            "BR_MOBILE_BILLGEN_GENERIC_PUSH": 8168,
            "BR_MOBILE_BILLGEN_GENERIC_CHAT": 8171,
            "BR_MOBILE_BILLGEN_GENERIC_EMAIL": null,

            "BR_MOBILE_DUEDATE_GENERIC_SMS": 8169,
            "BR_MOBILE_DUEDATE_GENERIC_PUSH": 8170,
            "BR_MOBILE_DUEDATE_GENERIC_CHAT": 8172,
            "BR_MOBILE_DUEDATE_GENERIC_EMAIL": null,

            "BR_LANDLINE_BILLGEN_SMS": null,
            "BR_LANDLINE_BILLGEN_PUSH": 7133,
            "BR_LANDLINE_BILLGEN_CHAT": 7599,

            "BR_LANDLINE_DUEDATE_SMS": 7098,
            "BR_LANDLINE_DUEDATE_PUSH": 7134,
            "BR_LANDLINE_DUEDATE_CHAT": 7575,

            "BR_BROADBAND_BILLGEN_SMS": null,
            "BR_BROADBAND_BILLGEN_PUSH": 7133,
            "BR_BROADBAND_BILLGEN_CHAT": 7599,

            "BR_BROADBAND_DUEDATE_SMS": 7098,
            "BR_BROADBAND_DUEDATE_PUSH": 7134,
            "BR_BROADBAND_DUEDATE_CHAT": 7575,

            "BR_ELECTRICITY_BILLGEN_SMS": 7141,
            "BR_ELECTRICITY_BILLGEN_PUSH": 7145,
            "BR_ELECTRICITY_BILLGEN_CHAT": 7590,

            "BR_ELECTRICITY_DUEDATE_SMS": 7136,
            "BR_ELECTRICITY_DUEDATE_PUSH": 7142,
            "BR_ELECTRICITY_DUEDATE_CHAT": 7587,

            "BR_WATER_BILLGEN_SMS": null,
            "BR_WATER_BILLGEN_PUSH": 7145,
            "BR_WATER_BILLGEN_CHAT": 7590,

            "BR_WATER_DUEDATE_SMS": 7136,
            "BR_WATER_DUEDATE_PUSH": 7142,
            "BR_WATER_DUEDATE_CHAT": 7587,

            "BR_APARTMENTS_BILLGEN_SMS": 7141,
            "BR_APARTMENTS_BILLGEN_PUSH": 7145,
            "BR_APARTMENTS_BILLGEN_CHAT": 7590,

            "BR_APARTMENTS_DUEDATE_SMS": 7136,
            "BR_APARTMENTS_DUEDATE_PUSH": 7142,
            "BR_APARTMENTS_DUEDATE_CHAT": 7587,

            "BR_GAS_BILLGEN_SMS": null,
            "BR_GAS_BILLGEN_PUSH": 7145,
            "BR_GAS_BILLGEN_CHAT": 7605,

            "BR_GAS_DUEDATE_SMS": 7136,
            "BR_GAS_DUEDATE_PUSH": 7142,
            "BR_GAS_DUEDATE_CHAT": 7602,

            "BR_INSURANCE_BILLGEN_SMS": 7146,
            "BR_INSURANCE_BILLGEN_PUSH": 7154,
            "BR_INSURANCE_BILLGEN_CHAT": 7607,

            "BR_INSURANCE_DUEDATE_SMS": 7147,
            "BR_INSURANCE_DUEDATE_PUSH": 7153,
            "BR_INSURANCE_DUEDATE_CHAT": 7608,

            "BR_LOAN_BILLGEN_SMS": 7149,
            "BR_LOAN_BILLGEN_PUSH": 7151,
            "BR_LOAN_BILLGEN_CHAT": 7582,

            "BR_LOAN_DUEDATE_SMS": 7197,
            "BR_LOAN_DUEDATE_PUSH": 7198,
            "BR_LOAN_DUEDATE_CHAT": 7583,

            "BR_LOAN_BILLGEN_EMAIL": null,
            "BR_LOAN_DUEDATE_EMAIL": null,

            "BR_FINANCIAL SERVICES_BILLGEN_SMS": 7040,
            "BR_FINANCIAL SERVICES_BILLGEN_PUSH": 7041,
            "BR_FINANCIAL SERVICES_BILLGEN_CHAT": 7603,
            "BR_FINANCIAL SERVICES_BILLGEN_EMAIL": 7092,

            "BR_FINANCIAL SERVICES_DUEDATE_SMS": 7042,
            "BR_FINANCIAL SERVICES_DUEDATE_PUSH": 7043,
            "BR_FINANCIAL SERVICES_DUEDATE_CHAT": 7606,
            "BR_FINANCIAL SERVICES_DUEDATE_EMAIL": 7093,

            "BR_DTH_PREPAID_SMS": null, // 7131 
            "BR_DTH_PREPAID_PUSH": null, // 7132
            "BR_DTH_PREPAID_CHAT": null, //7600

            "BR_FINANCIAL SERVICES_7_DUEDATE_SMS": null,
            "BR_FINANCIAL SERVICES_7_DUEDATE_PUSH": 8621,
            "BR_FINANCIAL SERVICES_7_DUEDATE_CHAT": 8628,
            "BR_FINANCIAL SERVICES_7_DUEDATE_EMAIL": null,

            "BR_FINANCIAL SERVICES_4_DUEDATE_SMS": null,
            "BR_FINANCIAL SERVICES_4_DUEDATE_PUSH": 8622,
            "BR_FINANCIAL SERVICES_4_DUEDATE_CHAT": 8629,
            "BR_FINANCIAL SERVICES_4_DUEDATE_EMAIL": null,

            "BR_FINANCIAL SERVICES_1_DUEDATE_SMS": null,
            "BR_FINANCIAL SERVICES_1_DUEDATE_PUSH": 7043,
            "BR_FINANCIAL SERVICES_1_DUEDATE_CHAT": 8627,
            "BR_FINANCIAL SERVICES_1_DUEDATE_EMAIL": null,


            "BR_FINANCIAL SERVICES_2_DUEDATE_SMS": 8470,
            "BR_FINANCIAL SERVICES_2_DUEDATE_PUSH": 8468,
            "BR_FINANCIAL SERVICES_2_DUEDATE_CHAT": 8469,
            "BR_FINANCIAL SERVICES_2_DUEDATE_EMAIL": 8472,

            "BR_FINANCIAL SERVICES_0_DUEDATE_SMS": 8466,
            "BR_FINANCIAL SERVICES_0_DUEDATE_PUSH": 8464,
            "BR_FINANCIAL SERVICES_0_DUEDATE_CHAT": 8465,
            "BR_FINANCIAL SERVICES_0_DUEDATE_EMAIL": 8471,


            "BR_FINANCIAL SERVICES_-1_DUEDATE_SMS": null,
            "BR_FINANCIAL SERVICES_-1_DUEDATE_PUSH": 8623,
            "BR_FINANCIAL SERVICES_-1_DUEDATE_CHAT": 8630,
            "BR_FINANCIAL SERVICES_-1_DUEDATE_EMAIL": null

            // PV_${service}_${dayValue}_CUSTOMER_PUSH : 5193 , `PV_${service}_${dayValue}_RN_CUSTOMER_PUSH

        },
        */
    },
    production: {
        notificationapi: {
            APIURL: 'http://notifypanel-mum.paytm.com/v1/admin/notification/async/send',
            SMSAPIURL: 'http://notificationplatform-internal.paytm.com/v3/notify/sms',
            PUSHAPIURL: 'http://notificationplatform-internal.paytm.com/v3/notify/push',
            EMAILAPIURL: 'http://notificationplatform-internal.paytm.com/v3/notify/email',
            CHATAPIURL: 'http://notificationplatform-internal.paytm.com/v3/notify/chat',
            WHATSAPPAPIURL: 'http://notificationplatform-internal.paytm.com/v3/notify/whatsapp',
            // V3_API_CLIENT_ID: "",
            // V3_API_SECRET_KEY: "",
            V3_API_CLIENT_ID_FOR_HEURISTIC: "prod-paytm-ru-platform",
            V3_API_SECRET_KEY_FOR_HEURISTIC:    "fab389ebc4eee5d82b82948d63f6c23092206e74",
            STATUSAPI: 'http://notifypanel-mum.paytm.com/v1/admin/notification/internal/logs',
            DIGITALNOTIFICATIONAPI: 'http://digitalreminder-internal.paytmdgt.io/v1/notify',
            NOTIFICATION_CALLBACK_URL: 'http://digitalreminder-internal.paytmdgt.io/v3/notification/callback',
            DIGITAL_DEEPLINK_URL: 'https://catalog.paytm.com',
            DEEPLINK_URL: 'https://catalog.paytm.com',
            DEEPLINK_API: '/v1/mobile/getproductlist',
            DEFAULT_SMS_URL: 'https://paytm.com',
            PRODUCT_SERVICE_SMS_URL: {
                'mobile': 'https://paytm.com/recharge',
                'dth': 'https://paytm.com/dth-recharge',
                'electricity': 'https://paytm.com/electricity-bill-payment',
                'datacard': 'https://paytm.com/datacard-recharge',
                'broadband': 'https://paytm.com/landline-bill-payment',
                'water': 'https://paytm.com/water-bill-payment',
                'gas': 'https://paytm.com/gas-bill-payment',
                'financial services': 'https://paytm.com/credit-card-bill-payment',
                'metro': 'https://paytm.com/metro-card-recharge',
                'landline': 'https://paytm.com/landline-bill-payment',
                'education': 'https://paytm.com/education',
                'google play': 'https://paytm.com/google-play-gift-card-recharge',
                'insurance': 'https://paytm.com/insurance-premium-payment',
                'apartments': 'https://paytm.com/electricity-bill-payment',
                'loan': 'https://paytm.com/loan-emi-payment'
            },
            UTM_CONFIG: {
                'bill_generation': '&utm_source=automatedReminders&utm_medium=sms&utm_campaign=billgeneration',
                'due_date': '&utm_source=automatedReminders&utm_medium=sms&utm_campaign=duedate',
                'bill_generation_for_push': '&utm_source=automatedReminders&utm_medium=push&utm_campaign=billgeneration',
                'due_date_for_push': '&utm_source=automatedReminders&utm_medium=push&utm_campaign=duedate',
                'plan_expiry_for_sms': '&utm_source=AutomatedReminders&utm_medium=sms&utm_campaign=planexpiry'
            },
            CHANNEL_ID_MAPPING: {
                101975: "e00a3b98-a8b4-467c-b2eb-6432e6d0334a", // financial services => credit card
                156705: "e00a3b98-a8b4-467c-b2eb-6432e6d0334a", // financial services => credit card
                193345: "e00a3b98-a8b4-467c-b2eb-6432e6d0334a", // financial services => credit card
                101950: "cecbc2a3-a7f8-4346-a9f6-fd0e3a16ea8a", // apartments
                75505: "467e6bd7-42b3-489b-bb33-b567b8c010cd", // broadband, landline
                123988: "f05ed5f6-3a49-4965-ab27-bacade98088a", // cable tv
                104154: "4ed253f6-30af-4679-a27e-d3fdbb9bc2ca", // challan
                18: "157ffd94-fda9-4eb8-a1e6-b29f464154de", // dth
                26: "b7accda7-dde5-479d-b98c-f112ef15ea40", // electricity
                78640: "8a981238-63eb-4125-94f7-3a78119cad71", // gas
                64739: "5e811406-48ad-4c29-bc8d-2cc42fc900ea", // insurance
                37217: "00541727-451b-4846-95ce-99bcc6893755", // loan
                17: "b562cdef-b677-4784-ae8b-703a95023bc0", // mobile prepaid
                21: "330a4567-d701-4542-824f-817f9eb220b1", // mobile postpaid
                107730: "e134e21b-5ab3-4309-a0ca-f73d5f4cc8d4", // municipal payments
                68869: "5929612a-e5d8-4663-b9d3-4ed1b0719823", // water
                19: "3c417d42-88ff-4652-86b1-3a696100d627", // datacard prepaid
                23: "3c417d42-88ff-4652-86b1-3a696100d627", // datacard postpaid,
                166690: "2212b9c2-4a36-4aae-b19f-6473835ddcd5"// cylinder booking
            },
            RETRYCOUNT: 3,
            CHUNKSIZE: 30,
            TIMEINTERVAL: 10,
            BATCHSIZE: 1000,
            BR_CHUNKSIZE: 100
        }
    },
    staging: {
        notificationapi: {
            APIURL: 'https://notificationpanel-staging.paytm.com/v1/admin/notification/async/send',
            SMSAPIURL: 'http://notifications-platformproducer-staging.paytm.com/v3/notify/sms',
            PUSHAPIURL: 'http://notifications-platformproducer-staging.paytm.com/v3/notify/push',
            EMAILAPIURL: 'http://notifications-platformproducer-staging.paytm.com/v3/notify/email',
            CHATAPIURL: 'http://notifications-platformproducer-staging.paytm.com/v3/notify/chat',
            // WHATSAPPAPIURL: 'http://notifications-platformproducer-staging.paytm.com/v3/notify/whatsapp',
            WHATSAPPAPIURL: 'http://inmockjava.nonprod.onus.paytmdgt.io/v3/notify/whatsapp',
            //V3_API_CLIENT_ID: "",
            //V3_API_SECRET_KEY: "",
            STATUSAPI: 'http://notifypanel-mum.paytm.com/v1/admin/notification/internal/logs',
            DIGITALNOTIFICATIONAPI: 'http://localhost:7001/v1/notify',
            NOTIFICATION_CALLBACK_URL: 'https://digitalproxy-staging.paytm.com/bills/v3/notification/callback',
            DIGITAL_DEEPLINK_URL: 'https://catalog.paytm.com',
            DEEPLINK_URL: 'https://catalog.paytm.com',
            DEEPLINK_API: '/v1/mobile/getproductlist',
            DEFAULT_SMS_URL: 'https://paytm.com',
            PRODUCT_SERVICE_SMS_URL: {
                'mobile': 'https://paytm.com/recharge',
                'dth': 'https://paytm.com/dth-recharge',
                'electricity': 'https://paytm.com/electricity-bill-payment',
                'datacard': 'https://paytm.com/datacard-recharge',
                'broadband': 'https://paytm.com/landline-bill-payment',
                'water': 'https://paytm.com/water-bill-payment',
                'gas': 'https://paytm.com/gas-bill-payment',
                'financial services': 'https://paytm.com/credit-card-bill-payment',
                'metro': 'https://paytm.com/metro-card-recharge',
                'landline': 'https://paytm.com/landline-bill-payment',
                'education': 'https://paytm.com/education',
                'google play': 'https://paytm.com/google-play-gift-card-recharge',
                'insurance': 'https://paytm.com/insurance-premium-payment',
                'apartments': 'https://paytm.com/electricity-bill-payment',
                'loan': 'https://paytm.com/loan-emi-payment'
            },
            UTM_CONFIG: {
                'bill_generation': '&utm_source=automatedReminders&utm_medium=sms&utm_campaign=billgeneration',
                'due_date': '&utm_source=automatedReminders&utm_medium=sms&utm_campaign=duedate',
                'bill_generation_for_push': '&utm_source=automatedReminders&utm_medium=push&utm_campaign=billgeneration',
                'due_date_for_push': '&utm_source=automatedReminders&utm_medium=push&utm_campaign=duedate',
                'plan_expiry_for_sms': '&utm_source=AutomatedReminders&utm_medium=sms&utm_campaign=planexpiry'
            },
            CHANNEL_ID_MAPPING: {
                101975: "e00a3b98-a8b4-467c-b2eb-6432e6d0334a", // financial services => credit card
                156705: "e00a3b98-a8b4-467c-b2eb-6432e6d0334a", // financial services => credit card
                193345: "e00a3b98-a8b4-467c-b2eb-6432e6d0334a", // financial services => credit card
                101950: "cecbc2a3-a7f8-4346-a9f6-fd0e3a16ea8a", // apartments
                75505: "467e6bd7-42b3-489b-bb33-b567b8c010cd", // broadband, landline
                123988: "f05ed5f6-3a49-4965-ab27-bacade98088a", // cable tv
                104154: "4ed253f6-30af-4679-a27e-d3fdbb9bc2ca", // challan
                18: "157ffd94-fda9-4eb8-a1e6-b29f464154de", // dth
                26: "b7accda7-dde5-479d-b98c-f112ef15ea40", // electricity
                78640: "8a981238-63eb-4125-94f7-3a78119cad71", // gas
                64739: "5e811406-48ad-4c29-bc8d-2cc42fc900ea", // insurance
                37217: "00541727-451b-4846-95ce-99bcc6893755", // loan
                17: "b562cdef-b677-4784-ae8b-703a95023bc0", // mobile prepaid
                21: "330a4567-d701-4542-824f-817f9eb220b1", // mobile postpaid
                107730: "e134e21b-5ab3-4309-a0ca-f73d5f4cc8d4", // municipal payments
                68869: "5929612a-e5d8-4663-b9d3-4ed1b0719823", // water
                19: "3c417d42-88ff-4652-86b1-3a696100d627", // datacard prepaid
                23: "3c417d42-88ff-4652-86b1-3a696100d627", // datacard postpaid,
                166690: "2212b9c2-4a36-4aae-b19f-6473835ddcd5"// cylinder booking
            },
            RETRYCOUNT: 3,
            CHUNKSIZE: 30,
            TIMEINTERVAL: 2,
            BATCHSIZE: 1000,
            BR_CHUNKSIZE: 10
        }
    },
    development: {
        notificationapi: {
            APIURL: 'https://notificationpanel-staging.paytm.com/v1/admin/notification/async/send',
            STATUSAPI: 'https://notificationpanel-staging.paytm.com/v1/admin/notification/internal/logs',
            SMSAPIURL: 'http://notifications-platformproducer-staging.paytm.com/v3/notify/sms',
            PUSHAPIURL: 'http://notifications-platformproducer-staging.paytm.com/v3/notify/push',
            EMAILAPIURL: 'http://notifications-platformproducer-staging.paytm.com/v3/notify/email',
            CHATAPIURL: 'http://notifications-platformproducer-staging.paytm.com/v3/notify/chat',
            WHATSAPPAPIURL: 'http://notifications-platformproducer-staging.paytm.com/v3/notify/whatsapp',
            //V3_API_CLIENT_ID: "",
            //V3_API_SECRET_KEY: "",
            DIGITALNOTIFICATIONAPI: 'http://localhost:7000/v1/notify',
            NOTIFICATION_CALLBACK_URL: 'http://localhost:7000/v3/notification/callback',
            DIGITAL_DEEPLINK_URL: 'https://catalog.paytm.com',
            DEEPLINK_URL: 'https://catalog.paytm.com',
            DEEPLINK_API: '/v1/mobile/getproductlist',
            DEFAULT_SMS_URL: 'https://paytm.com',
            PRODUCT_SERVICE_SMS_URL: {
                'mobile': 'https://paytm.com/recharge',
                'dth': 'https://paytm.com/dth-recharge',
                'electricity': 'https://paytm.com/electricity-bill-payment',
                'datacard': 'https://paytm.com/datacard-recharge',
                'broadband': 'https://paytm.com/landline-bill-payment',
                'water': 'https://paytm.com/water-bill-payment',
                'gas': 'https://paytm.com/gas-bill-payment',
                'financial services': 'https://paytm.com/credit-card-bill-payment',
                'metro': 'https://paytm.com/metro-card-recharge',
                'landline': 'https://paytm.com/landline-bill-payment',
                'education': 'https://paytm.com/education',
                'google play': 'https://paytm.com/google-play-gift-card-recharge',
                'insurance': 'https://paytm.com/insurance-premium-payment',
                'apartments': 'https://paytm.com/electricity-bill-payment',
                'loan': 'https://paytm.com/loan-emi-payment'
            },
            UTM_CONFIG: {
                'bill_generation': '&utm_source=automatedReminders&utm_medium=sms&utm_campaign=billgeneration',
                'due_date': '&utm_source=automatedReminders&utm_medium=sms&utm_campaign=duedate',
                'bill_generation_for_push': '&utm_source=automatedReminders&utm_medium=push&utm_campaign=billgeneration',
                'due_date_for_push': '&utm_source=automatedReminders&utm_medium=push&utm_campaign=duedate',
                'plan_expiry_for_sms': '&utm_source=AutomatedReminders&utm_medium=sms&utm_campaign=planexpiry'
            },
            CHANNEL_ID_MAPPING: {
                101975: "e00a3b98-a8b4-467c-b2eb-6432e6d0334a", // financial services => credit card
                156705: "e00a3b98-a8b4-467c-b2eb-6432e6d0334a", // financial services => credit card
                193345: "e00a3b98-a8b4-467c-b2eb-6432e6d0334a", // financial services => credit card
                101950: "cecbc2a3-a7f8-4346-a9f6-fd0e3a16ea8a", // apartments
                75505: "467e6bd7-42b3-489b-bb33-b567b8c010cd", // broadband, landline
                123988: "f05ed5f6-3a49-4965-ab27-bacade98088a", // cable tv
                104154: "4ed253f6-30af-4679-a27e-d3fdbb9bc2ca", // challan
                18: "157ffd94-fda9-4eb8-a1e6-b29f464154de", // dth
                26: "b7accda7-dde5-479d-b98c-f112ef15ea40", // electricity
                78640: "8a981238-63eb-4125-94f7-3a78119cad71", // gas
                64739: "5e811406-48ad-4c29-bc8d-2cc42fc900ea", // insurance
                37217: "00541727-451b-4846-95ce-99bcc6893755", // loan
                17: "b562cdef-b677-4784-ae8b-703a95023bc0", // mobile prepaid
                21: "330a4567-d701-4542-824f-817f9eb220b1", // mobile postpaid
                107730: "e134e21b-5ab3-4309-a0ca-f73d5f4cc8d4", // municipal payments
                68869: "5929612a-e5d8-4663-b9d3-4ed1b0719823", // water
                19: "3c417d42-88ff-4652-86b1-3a696100d627", // datacard prepaid
                23: "3c417d42-88ff-4652-86b1-3a696100d627", // datacard postpaid,
                166690: "2212b9c2-4a36-4aae-b19f-6473835ddcd5"// cylinder booking
            },
            RETRYCOUNT: 3,
            CHUNKSIZE: 2,
            TIMEINTERVAL: 1,
            BATCHSIZE: 2,
            BR_CHUNKSIZE: 2
        }
    }
};
