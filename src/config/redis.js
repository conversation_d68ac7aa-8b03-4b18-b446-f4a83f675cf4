/*jshint multistr: true ,node: true*/
'use strict';

module.exports = {
    common: {
        port: '6379'
    },
    production: {
        ip: '***********',
        sentinelMasterName : 'cluster',
        sentinel:[
            { host: '***********', port: 26379 },
            { host: '**********', port: 26379 },
            { host: '***********', port: 26379 }
        ]
    },
    development: {
        ip: '127.0.0.1',
        sentinel<PERSON>asterName : 'mymaster',
        sentinel:[
            { host: '127.0.0.1', port: 26379 }
        ]
    },
    staging: {
        ip: '***********',
        sentinelMasterName : 'cluster',
        endPoints:[
            { host: '**********', port: 26379 },
            { host: '***********', port: 26379 },
            { host: '***********', port: 26379 }
        ]
    }
};
