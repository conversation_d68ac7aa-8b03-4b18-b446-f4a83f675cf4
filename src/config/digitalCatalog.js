"use strict";

module.exports = {
    common: {
        DCAT_CATEGORY_MAP: {
            'mobile': '7166',
            'dth': '7167',
            'datacard': '7171'
        },
        API: {
            getPlan: "/dcat/v1/browseplans/%s/getplan?locale=en-IN&version=13.13.13&channel=web&amount=%s&operator=%s&circle=%s",
        },
        IN_VALID_VALIDITY: ['NA', 'Unlimited', 'Lifetime', ""],
    },
    production: {
        endPoint: 'https://digitalcatalog.paytm.com',
        // check production url & category id
        // http://digitalcatalog-internal.paytmdgt.io -> please check connectivity before release
        GET_CATEGORY_FROM_CATEGORY_ID_API_URL : 'http://digitalcatalog-internal.paytmdgt.io/dcat/v1/category/156705/getcategory?channel=web&version=16.16.16&locale=en-in',
        GET_PRODUCTLIST_URL: 'https://digitalcatalog.paytm.com/dcat/v1/category/17/getproductlist',
        GET_CIRCLE_FROM_MNP_URL: 'http://mnp-internal.prod.paytmdgt.io/v1/mobile/getopcirclebyrange',
        GET_PLAN_DETAILS_URL: 'http://digitalcatalog-internal.paytmdgt.io/v1/plans_map',
    },
    development: {
        endPoint: 'https://digitalcatalog.paytm.com',
        // check url & category id 
        GET_CATEGORY_FROM_CATEGORY_ID_API_URL : 'https://run.mocky.io/v3/53643c41-bbba-447f-879d-8770af9b8279', // Secret delete link => https://designer.mocky.io/manage/delete/53643c41-bbba-447f-879d-8770af9b8279/Vc4NaA7lUGjyEQBE1OysfDcpVftJ4jwnVOd7
        GET_PRODUCTLIST_URL: 'https://digitalcatalog.paytm.com/dcat/v1/category/17/getproductlist',
        GET_CIRCLE_FROM_MNP_URL: 'https://digitalproxy.paytm.com/v1/mobile/getopcirclebyrange',
        GET_PLAN_RPS_OTT: 'https://digitalcatalog-staging.paytm.com/rps/v1/plans/298744/search',
        GET_PLAN_DETAILS_URL: 'https://digitalproxy.paytm.com/v1/plans_map',

    },
    staging: {
        endPoint: 'https://digitalcatalog.paytm.com',
        //endPoint: 'https://digitalcatalog-staging.paytm.com',
        // check staging url & category id
       // GET_CATEGORY_FROM_CATEGORY_ID_API_URL : 'https://run.mocky.io/v3/53643c41-bbba-447f-879d-8770af9b8279',
        GET_CATEGORY_FROM_CATEGORY_ID_API_URL : 'http://inmockjava.nonprod.onus.paytmdgt.io/dcat/v1/category/staging',
        GET_PRODUCTLIST_URL: 'https://digitalcatalog-staging.paytm.com/dcat/v1/category/17/getproductlist?channel=web&version=200.200.200&locale=en-in',
        GET_CIRCLE_FROM_MNP_URL: 'https://digitalapiproxy-staging.paytm.com/v1/mobile/getopcirclebyrange',
        // GET_CIRCLE_FROM_MNP_URL: 'https://digitalproxy-staging.paytm.com/v1/mobile/getopcirclebyrange',
        GET_PLAN_RPS_OTT: 'https://digitalcatalog-staging.paytm.com/rps/v1/plans/298744/search',
        GET_PLAN_DETAILS_URL: 'https://digitalcatalog-staging.paytm.com/dcat/v1/browseplans/plan-mapping',

    }
};
