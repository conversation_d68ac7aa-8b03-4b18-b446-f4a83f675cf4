"use strict";
import _ from 'lodash'
import MOMENT from 'moment'
import cassandraBills from '../models/cassandraBills'
import utility from '../lib';
import Logger from './logger';

class BillFetchAnalytics {
    
    constructor(options) {
        this.L = options.L,
            this.config = options.config;
        this.cassandraBills = new cassandraBills(options)
        this.dwhKafkaPublisher = null;
        this.infraUtils = _.get(options, 'INFRAUTILS', null);
        this.logger = new Logger(options);

    }

    initializeKafkaProducerDwhPublisher(cb) {
        var self = this;
        self.dwhKafkaPublisher =
            new self.infraUtils.kafka.producer({
                "kafkaHost": self.config.KAFKA.TOPICS.BILL_FETCH_ANALYTICS.HOSTS
            });
        return self.dwhKafkaPublisher.initProducer('high', function (error) {
            if (error) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_ANALYTICS", 'STATUS:PRODUCER_NOT_INITIALIZING', 'TYPE:PRODUCER', 'TOPIC:bills_analytics_data', 'SOURCE:MAIN_FLOW']);
                this.L.critical('Getting error in publisher configuration: ', error);
                return cb(error);
            } else {
                console.log('kafka publisher for dwh configured');
                return cb(null);
            }
        });
    }
    async saveAndPublishBillFetchAnalyticsData(data, error, cb, callbackWithError = true) {
        var self = this;
        let errorMsg = error;
        try {
            if (self.dwhKafkaPublisher == null) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_ANALYTICS", 'STATUS:PRODUCER_NOT_INITIALIZED', 'TYPE:PRODUCER', 'TOPIC:bills_analytics_data', 'SOURCE:MAIN_FLOW']);
                return self.initializeKafkaProducerDwhPublisher(async function (err) {
                    if (err) {
                        self.L.critical('Error in publishing data: ', data, " Error: ", err);
                        //metrics push
                    } else {
        
                        try{
                            await self.saveAndPublishBillsAnalyticData(data, errorMsg)
                        }
                        catch (err) {
                            self.L.error('Getting error in publishing inside: ', err);
                        }
                    }
                    if (cb) {
                        if (callbackWithError === false) {
                            return cb();
                        }
                        else {
                            return cb(errorMsg)
                        }
                    }
                    else {
                        return Promise.resolve();
                    }
                });
            } else {
                
                if(data) await self.saveAndPublishBillsAnalyticData(data, errorMsg).catch((error) =>{
                    console.log("error is ",error);
                })
                if (cb) {
                    if (callbackWithError === false) {
                        return cb();
                    }
                    else {
                        return cb(errorMsg)
                    }
                }
                else {
                    return Promise.resolve();
                }
            }
        }
        catch (error) {
            this.L.error('Getting error in publishing: ', error);
            if (cb) {
                if (callbackWithError === false) {
                    return cb();
                }
                else {
                    return cb(errorMsg)
                }
            }
            else {
                return Promise.resolve();
            }
        }
    }
    saveAndPublishBillsAnalyticData(billsRawPayload, errorMsg) {
        var self = this;
        let payload = null;
        payload = self.prepareBillFetchDWHKafkaPayload(billsRawPayload, errorMsg)
        return new Promise((resolve, reject) => {
            var self = this;
            let promise1 = Promise.resolve();
            // if(payload.customer_id ){
            //     promise1 = self.cassandraBills.saveAnalyticsDataInCassandra(payload)
            // }
            let promise2 = self.pubishAnalyticsDataToDWHKafka(payload);
            return Promise.all([promise1, promise2])
                .then(results => {
                    return resolve(results);
                })
                .catch(error => {
                    return reject(error)
                })
        });
    }
    prepareBillFetchDWHKafkaPayload(billsRawPayload, error) {
        var self = this;
        let smsDateTime = _.get(billsRawPayload, 'sms_date_time', null);

        if ((_.get(billsRawPayload, 'source', null) == "SMS_PARSING_DWH_REALTIME" || _.get(billsRawPayload, 'source', null) == 'SMS_PARSING_DWH' || _.get(billsRawPayload, 'source', null) == "SMS_PARSING_DWH_MANUAL") && _.get(billsRawPayload, 'service', null) == 'mobile') {
            if (smsDateTime == null || smsDateTime == 'null' || smsDateTime == undefined) {
                smsDateTime = null;
            }
            else{
                smsDateTime = smsDateTime.toString();
            }
        }
        else {
            
            if (smsDateTime == null || smsDateTime == 'null' || smsDateTime == undefined) {
                smsDateTime = null;
            }
            else if (typeof smsDateTime == "string") {
                smsDateTime = MOMENT(smsDateTime).format('YYYY-MM-DD HH:mm:ss');
            }
            else {
                if (smsDateTime && smsDateTime.toString().length == 10) {
                    smsDateTime = smsDateTime * 1000;
                    smsDateTime = MOMENT(smsDateTime).format('YYYY-MM-DD HH:mm:ss');
                }
                else {
                    smsDateTime = MOMENT(smsDateTime).format('YYYY-MM-DD HH:mm:ss');
                }
            }
        }
        return smsDateTime;
    }

    convertSmsDateTime(billsRawPayload, error) {

            let smsDateTime = _.get(billsRawPayload, 'sms_date_time', null);

            if ((_.get(billsRawPayload, 'source', null) == "SMS_PARSING_DWH_REALTIME" || _.get(billsRawPayload, 'source', null) == 'SMS_PARSING_DWH' || _.get(billsRawPayload, 'source', null) == "SMS_PARSING_DWH_MANUAL") && _.get(billsRawPayload, 'service', null) == 'mobile') {
                if (smsDateTime == null || smsDateTime == 'null' || smsDateTime == undefined) {
                    smsDateTime = null;
                }
                else{
                    smsDateTime = smsDateTime.toString();
                }
            }
            else {

                if (smsDateTime == null || smsDateTime == 'null' || smsDateTime == undefined) {
                    smsDateTime = null;
                }
                else if (typeof smsDateTime == "string") {
                    smsDateTime = MOMENT(smsDateTime).format('YYYY-MM-DD HH:mm:ss');
                }
                else {
                    if (smsDateTime && smsDateTime.toString().length == 10) {
                        smsDateTime = smsDateTime * 1000;
                        smsDateTime = MOMENT(smsDateTime).format('YYYY-MM-DD HH:mm:ss');
                    }
                    else {
                        smsDateTime = MOMENT(smsDateTime).format('YYYY-MM-DD HH:mm:ss');
                    }
                }
            }
            return smsDateTime;
        }


    prepareBillFetchDWHKafkaPayload(billsRawPayload, error) {
        var self=this;
        
        let smsDateTime = self.convertSmsDateTime(billsRawPayload);
        return {
            source: _.get(billsRawPayload, 'source', 'SMS_PARSING'),
            source_subtype_2: _.get(billsRawPayload, 'source_subtype_2', null),
            user_type: _.get(billsRawPayload, 'user_type', null),
            reject_reason: _.get(billsRawPayload, 'errMsg', self.createErrorMessage(error)),
            customer_id: _.get(billsRawPayload, 'customer_id', null),
            service: _.get(billsRawPayload, 'service', null),
            recharge_number: _.get(billsRawPayload, 'recharge_number', null),
            operator: _.get(billsRawPayload, 'operator', null),
            due_amount: _.get(billsRawPayload, 'due_amount', null),
            sender_id: _.get(billsRawPayload, 'sender_id', null),
            sms_id: _.get(billsRawPayload, 'sms_id', null),
            sms_date_time: smsDateTime,
            dwh_class_id: _.get(billsRawPayload, 'sms_class_id') != null || _.get(billsRawPayload, 'sms_class_id') != "" || _.get(billsRawPayload, 'sms_class_id') != undefined ? _.toString(_.get(billsRawPayload, 'sms_class_id')) : null,
            additional_info: _.get(billsRawPayload, 'additional_info', null),
            paytype:_.get(billsRawPayload, 'paytype', null),
            rawlastcc:_.get(billsRawPayload,'rawlastcc',null),
            created_at:this.getDate(),
            updated_at:this.getDate(),
            due_date :_.get(billsRawPayload,'due_date',null),
            bill_date :_.get(billsRawPayload,'bill_date',null),
            bill_fetch_date :_.get(billsRawPayload,'bill_fetch_date',null),
        }

        }
    
    createErrorMessage(error) {
        let failureReason = null;
        var self = this;

        if (error && error.message && error.stack)
            failureReason = JSON.stringify(error, self.replaceError);
        else {
            if (typeof error === 'string') {
                failureReason = error;
            }
            else {
                try {
                    failureReason = JSON.stringify(error);
                }
                catch (err) {
                    failureReason = error;
                }
            }

        }
        return failureReason;
    }
    replaceError(key, value) {
        if (value instanceof Error) {
            const newValue = Object.getOwnPropertyNames(value)
                .reduce((obj, propName) => {
                    obj[propName] = value[propName];
                    return obj;
                }, { name: value.name });
            return newValue;
        } else {
            return value;
        }
    }
    pubishAnalyticsDataToDWHKafka(billsPayload) {
        var self = this;
        var publisherObject = [{
            topic: "bills_analytics_data",
            messages: JSON.stringify(billsPayload),
            key: _.get(billsPayload, 'recharge_number', null)
        }];
        var service = _.get(billsPayload, 'service', null);
        return new Promise((resolve, reject) => {


            self.dwhKafkaPublisher.publishData(publisherObject, error => {
                if (error) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_ANALYTICS", 'STATUS:ERROR', 'TYPE:PRODUCER', 'TOPIC:bills_analytics_data', 'SOURCE:MAIN_FLOW']);
                    return reject(error);
                } else {
                    self.logger.log("message published successfully", {
                        topic: publisherObject[0].topic,
                        messages: JSON.parse(publisherObject[0].messages), // Parse the stringified messages
                        key: publisherObject[0].key,
                        partition: publisherObject[0].partition,
                        attributes: publisherObject[0].attributes
                    }, service);
                    return resolve();
                }
            }, [200, 800]);
        })
    }

    getDate() {
        return MOMENT().format('YYYY-MM-DD HH:mm:ss');
    }



}
export default BillFetchAnalytics;
