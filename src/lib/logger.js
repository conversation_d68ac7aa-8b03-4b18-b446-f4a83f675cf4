import AES256Encryption from '../utils/aes256encryption';
import _ from 'lodash';
import L from 'lgr';
import utility from '../lib';
var env = (process.env.NODE_ENV || 'development').toLowerCase();

class Logger {
  constructor(options) {
    this.aes256Encryption = new AES256Encryption(options);
    this.config = _.get(options,'config');
    this.sensitiveFields = _.get(this.config, ['DYNAMIC_CONFIG', 'CCBP', 'ENCRYPTION_CONFIG', 'SENSITIVE_FIELDS'], ['userData_recharge_number', 'recharge_number', 'dueAmount', 'dueDate', 'due_date', 'customer_mobile', 'referenceId', 'rechargeNumber', 'due_amount', 'amount', 'recharge_number_2', 'recharge_number_3', 'recharge_number_4', 'recharge_number_5', 'recharge_number_6', 'subscriberNumber', 'subscriberName', 'subscriberEmailId', 'subscriberDOB', 'subscriberAddress', 'subscriberAltNumber', 'subscriberGender', 'subscriberCity', 'minReloadAmount', 'currentBillAmount', 'currentMinBillAmount', 'billDueDate', 'billDueAmount', 'lastCC', 'rawLastCC','rawlastcc', 'debugKey', 'current_outstanding_amount', 'currentOutstandingAmount', 'lastDueDt', 'lastAmount', 'customerMobile', 'customerEmail', 'dueAmt', 'totalAmt', 'recharge_number_for_display', 'key', 'smsBody', 'card_no', 'date', 'last_paid_amount', 'recipient', 'recipients', 'deepLinkObj', 'total_due', 'last_four_digits', 'minimum_due', 'lastFourDigits', 'mobileNumber', 'rechargeNumber5', 'rechargeNumber6', 'mobileNo', 'reminderID', 'last4Digit', 'userData', 'extra', 'customerOtherInfo', 'traceKey', 'value', 'billAmount', 'extraParams', 'extraCommonParams', 'url', 'cassandraKey', 'notificationUniqueKey', 'notificationUniqueKeyNONRU', 'record_key']);
    // encrypt extra as well, it has multiple fields like lastduedate, lastamount
    this.loggerConfig = _.get(this.config, ['DYNAMIC_CONFIG', 'CCBP', 'LOGGER_CONFIG', 'EXCLUSION_SERVICE_LIST'], [
      'subscriberMode',
      'updateUserConsentConsumer',
      'smsParsingBillPayment',
      'smsParsingBillPaymentDwhRealTime',
      'csvCron',
      'customNotificationCron',
      'fallbackCustomerIdIngester',
      'ccIngestion',
      'ccIngestionKafka',
      'userScoreingestionConsumer',
      'userScoreCSVIngest',
      'whatsappCustomerWhitelistCron',
      'whatsappCustomerWhitelistConsumer',
      'generalSmsParser',
      'custIdRnMappingIngestion',
      'cronUserScoreIngestion',
      'expiredCAPublisher',
      'statusReport',
      'notificationStatus',
      'batch',
      'planValidityNotification',
      'rechargeNudgeRechargeConsumer',
      'rechargeNudgeValidationConsumer',
      'rechargeNudgeServiceValidationConsumer',
      'rechargeNudgeServiceRechargeConsumer',
      'notificationService',
      'notificationReport',
      'syncReminder',
      'billDuePublisherPaytmPostpaid',
      'airtelPrepaidPublisher',
      'smsParsingFasTag',
      'planValidityNotificationSubscriber',
      'removeExpiredPlanValidity',
      'oldBillDuePublisher',
      'prepaidHistoricRecords',
      'planValidityRMQConsumer',
      'allTransactionsConsumer',
      'emiDueConsumer',
      'emiDueCommonConsumer',
      'billReminderCylinderConsumer',
      'operatorUpNotification',
      'syncAutomaticRecent',
      'electricitysmsParsingBillPayment',
      'electricitysmsParsingBillPaymentDwhRealtime',
      'dthsmsParsingBills',
      'rentsmsParsingBillPayment',
      'smsParsingLoanEmi',
      'paytmPostpaid',
      'realtimeSmsParsingPrepaid',
      'realtimeSmsParsingPostpaid',
      'airtelKafkaPublisher',
      'airtelPrepaidIngest',
      'airtelBillFetchConsumer',
      'airtelPrepaidBllDuePublisher',
      'activePaytmUsersConsumer',
      'checkActiveUsersConsumer',
      'genericSmsParsingRealTime',
      'billDuePrepaidPublisher'
    ]);
    this.L = L;
    this.encryptionFlag = _.get(this.config, ['DYNAMIC_CONFIG', 'CCBP', 'ENCRYPTION_CONFIG', 'ENCRYPTION_FLAG'], 1);
    this.encryptionFlag = _.get(this.config, ['DYNAMIC_CONFIG', 'CCBP', 'ENCRYPTION_CONFIG', 'ENCRYPTION_FLAG'], 1);
  }

  containsXXXX(value) {
    return /XXXX XXXX/.test(value) || /XX XXXX/.test(value) || /BBPS_/.test(value);
  }

  containsDate(value) {
    return /\d{4}-\d{2}-\d{2}/.test(value);
  }

  containsFloat(value) {
    return /^\d+\.\d+$/.test(value);
  }

  processSensitiveFields(obj, serviceName) {

    if (serviceName !== 'financial services' || typeof obj !== 'object' || obj === null) {
      return obj;
    }
  
    const processedObj = Object.keys(obj).reduce((acc, key) => {
      const value = obj[key];
      if (this.sensitiveFields.includes(key)) {
        acc[key] = serviceName === 'financial services' ? this.aes256Encryption.encrypt(value) : value;
      } else if (typeof value === 'object') {
        acc[key] = this.processSensitiveFields(value, serviceName);
      } else {
        let valueStr = String(value);
        if (this.containsXXXX(valueStr) || this.containsFloat(valueStr)) {
          acc[key] = serviceName === 'financial services' ? this.aes256Encryption.encrypt(valueStr) : valueStr;
        } else {
          acc[key] = value;
        }
      }
      return acc;
    }, {});
  
    return processedObj;
  }

  info(message, data, service) {
    this.logData(message, data, service, 'info');
  }

  error(message, data, service) {
    this.logData(message, data, service, 'error');
  }
  
  log(message, data, service) {
    this.logData(message, data, service, 'log');
  }

  critical(message, data, service) {
    this.logData(message, data, service, 'critical');
  }

  warn(message, data, service) {
    this.logData(message, data, service, 'warn');
  }

  debug(message, data, service) {
    this.logData(message, data, service, 'debug');
  }

  verbose(message, data, service) {
    this.logData(message, data, service, 'verbose');
  }
  

  logData(message, data, service, logLevel) {
    if(this.encryptionFlag == 0) {
      const stringifiedData = typeof data === 'object' ? 
        `'${JSON.stringify(data)}'` : 
        String(data);
      return this.L[logLevel](message, stringifiedData);
    }

    if(!logLevel) {
      return;
    }
    if(env != 'production' && this.encryptionFlag == 0) {
      const stringifiedData = typeof data === 'object' ? 
        `'${JSON.stringify(data)}'` : 
        String(data);
      return this.L[logLevel](message, stringifiedData);
    }
    
    let encryptedData = {};
    if (typeof data === 'string') {
      try {
        const jsonData = JSON.parse(data);
        encryptedData = this.processSensitiveFields(jsonData, service);
      } catch (e) {
        if (this.containsXXXX(data) || this.containsFloat(data)) {
          encryptedData = this.aes256Encryption.encrypt(data);
        } else {
          encryptedData = data;
        }
      }
    }else if (typeof data === 'object') {
      encryptedData = this.processSensitiveFields(data, service);
    }
    
    // Force string conversion before passing to logger
    const stringifiedData = typeof encryptedData === 'object' ? 
      `'${JSON.stringify(encryptedData)}'` : 
      String(encryptedData);
    
    this.L[logLevel](message, stringifiedData);
  }

  overrideLoggerMethods(parentService) {
    const methods = ['log', 'info', 'error', 'warn', 'debug', 'critical'];

    // Add a flag to prevent recursive logging
    if (!parentService || !this.loggerConfig.includes(parentService) || this.encryptionFlag == 0) {
      console.log(`Logger overrides not enabled for service: ${parentService}`);
      return;
    }
    const isLoggingInProgress = Symbol('isLoggingInProgress');

    const overrideMethod = (originalMethod, context) => {
      return (...args) => {
        try {
          const containsKafkaConsumerPollRequest = args.some(arg => typeof arg === 'string' && arg.includes('Kafka Consumer Poll Request'));
          // Prevent recursive calls
          if (context[isLoggingInProgress]) {
            return originalMethod.apply(context, args);
          }
          try {
            context[isLoggingInProgress] = true;
            
            const processedArgs = args.map((arg) => {
              if (typeof arg === 'string') {
                try {
                  const jsonArg = JSON.parse(arg);
                  const processed = this.processSensitiveFields(jsonArg, _.get(arg, 'service', null));
                  return typeof processed === 'object' ? 
                    `'${JSON.stringify(processed)}'` : 
                    String(processed);
                } catch (e) {
                  return this.containsXXXX(arg) ? this.aes256Encryption.encrypt(arg) : arg;
                }
              } else if (typeof arg === 'object' && arg !== null) {
                const processed = this.processSensitiveFields(arg, _.get(arg, 'service', null));
                return typeof processed === 'object' ? 
                  `'${JSON.stringify(processed)}'` : 
                  String(processed);
              }
              return arg;
            });
    
            if (!containsKafkaConsumerPollRequest) {
              utility._sendMetricsToDD(1, ['REQUEST_TYPE:LOGGER', `SERVICE:${parentService}`]);
            }
          
            return originalMethod.apply(context, processedArgs);
          } finally {
            // Always clean up the flag
            delete context[isLoggingInProgress];
          }
        } catch (error) {
          return originalMethod.apply(context, args);
        }
      };
    };

    try {
      // Override self.L.log methods
      methods.forEach((method) => {
        const originalMethod = this.L[method];
        this.L[method] = overrideMethod(originalMethod, this.L);
      });
    
      // Override console methods
      methods.forEach((method) => {
        const originalConsoleMethod = console[method];
        console[method] = overrideMethod(originalConsoleMethod, console);
      });
    } catch (error) {
      //console.error('Failed to override logger methods:', error);
    }
  }
}

export default Logger;