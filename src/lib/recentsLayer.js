
import _ from 'lodash';
import utility from '../lib';
import REQUEST from 'request';
import VA<PERSON><PERSON><PERSON>OR from 'validator';

class RecentsLayer {

    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.maxRetryCount = _.get(this.config, ['DYNAMIC_CONFIG', 'RECENTS', 'UPDATE_URL', 'RETRYCOUNT'], 1);
        this.retryErrors = ["ETIMEDOUT", "EAI_AGAIN", "ECONNREFUSED", "ESOCKETTIMEDOUT"];
        this.retriableStatusCodes = ['5XX'];
    }

    /**
     * 
     * @param {*} done
     * @param {*} queryParam  
     * @param {*} fieldName 
     * @param {*} fieldValue 
     * @param {*} source 
     * @param {*} retryCount 
     */

    update(done, queryParam, fieldName, fieldValue, source = "defaultSource", retryCount = 0) {
        return done(null, 0) // Mongo API is not available 
    //     let self = this,
    //         operator = _.get(queryParam,'operator', null);

    //     if(self.isOperatorBlacklistedToUpdateRecents(operator)) {
    //         self.L.error('RecentsLayer::', `Operator ${operator} is blacklisted...skipping it`);
    //         utility._sendMetricsToDD(1, [
    //             'REQUEST_TYPE:RECENTS_UPDATE',
    //             'SOURCE:' + source,
    //             'OPERATOR:' + operator,
    //             'STATUS:BLACKLIST_OPERATOR' 
    //         ]);
    //         return done(`RecentsLayer:: Operator ${operator} is blacklisted...skipping it`);
    //     }

    //     let requestBody = {
    //         data: [
    //             {
    //                 fieldName,
    //                 fieldValue
    //             }
    //         ]
    //     };

    //     _.extend(requestBody, queryParam);
    //    // convert service and paytype to lowercase
    //    requestBody.service = _.toLower(requestBody.service);
    //    requestBody.paytype = _.toLower(requestBody.paytype);
       
    //     // parse customerId to integer if it is a number
    //     if (requestBody.customer_id) {
    //         requestBody.customer_id = typeof (requestBody.customer_id) === 'string' ? VALIDATOR.toInt(requestBody.customer_id) : requestBody.customer_id;   
    //     }

    //     let apiOpts = {
    //         "uri": _.get(self.config, ['MONGO', 'RECENT_UPDATE_URL']),
    //         "method": 'PUT',
    //         "timeout": 60000,
    //         'json': requestBody
    //     };

    //     var latencyStart = new Date().getTime();
    //     this.L.verbose('MongoAPI:: update api call specs ', JSON.stringify(apiOpts));
    //     REQUEST(apiOpts, (error, response, body) => {
    //         utility._sendLatencyToDD(latencyStart, {
    //             'REQUEST_TYPE': 'RECENTS_UPDATE',
    //             'URL': '/v1/recentupdate/admin',
    //             'SOURCE': source
    //         });
            
    //         utility._sendMetricsToDD(1, [
    //             'REQUEST_TYPE:RECENTS_UPDATE',
    //             'STATCODE:' + _.get(response, 'statusCode', _.get(error, 'code', '5XX')),
    //             'API_STATCODE:' + _.get(body, 'status_code', 'XX'),
    //             'RETRY_COUNT:' + retryCount,
    //             'SOURCE:' + source,
    //             'FIELDNAME:' + fieldName,
    //             'OPERATOR:' + _.get(queryParam,'operator','No_OPERATOR_PASSED')
    //         ]);

    //         if (body && typeof body === 'string') {
    //             try {
    //                 body = JSON.parse(body);
    //             } catch (e) {
    //                 this.L.error('RecentsLayer:: Error while parsing response body queryParam: ' + JSON.stringify(queryParam) + JSON.stringify(e));
    //                 return done('Parsing response body error');
    //             }
    //         }

    //         let apiStatCode = _.get(response, 'statusCode', '5XX') + '',
    //             maskedStatCode = apiStatCode[0] + 'XX';
    //         if (error || self.retriableStatusCodes.indexOf(maskedStatCode) > -1) {
    //             this.L.error(`RecentsLayer:: Error from Mongo API apiOpts: `, JSON.stringify(apiOpts) + ' error:' + error);
    //             return self.shouldRetry(done, error, queryParam, fieldName, fieldValue, source, retryCount, apiStatCode);
    //         }

    //         if (!body) {
    //             this.L.error(`RecentsLayer:: Response body not there! apiOpts:`, JSON.stringify(apiOpts));
    //             utility._sendMetricsToDD(1, [`REQUEST_TYPE:RECENT_UPDATE_BODY_NOT_FOUND`, 'STATUS:ERROR']);
    //             return done('request Body Not found');
    //         }

    //         if (body.status_code === '00') {
    //             this.L.log(`RecentsLayer:: Successfully updated in Recents!`, ` query: ${JSON.stringify(apiOpts)} rowsUpdated:${JSON.stringify(body.data)}`);
    //             return done(null, _.get(body, ['data', 'rows_updated'], 0));
    //         }

    //         this.L.error(`RecentsLayer:: Validation failure error apiOpts : `, JSON.stringify(apiOpts) + 'response body :' + JSON.stringify(body));
    //         return done(null, _.get(body, ['data', 'rows_updated'], 0));
    //     });

    }
/*
    recentFetch(done,params,source="defaultSource",retryCount=0){
        let self=this;
        let apiOpts;
        let invalidParams = [];
        let mandatoryParams;

        let customerId = _.get(params,'customer_id',null);
        if(customerId == null){
            mandatoryParams = ['recharge_number', 'service', 'operator'];
            mandatoryParams.forEach(function (key) {
                if (!params[key]) invalidParams.push(key);
            });
            if (invalidParams.length > 0) 
                return  done(`Mandatory Params ${invalidParams} is Missing / Invalid`);
            apiOpts={
                uri: _.get(self.config, ['MONGO', 'FETCH_RECENT_URL']),
                method: 'POST',
                timeout: 60000,
                json: {
                    recharge_number: _.get(params, 'recharge_number', null),
                    service: _.get(params, 'service', null),
                    operator: _.get(params, 'operator', null)
                }
            }
        }
        else{
            if(_.get(params, 'paytype', null) == "credit card"){
                mandatoryParams = ['customer_id', 'service', 'paytype'];
            }
            else{
                mandatoryParams = ['customer_id','recharge_number', 'service','paytype'];
            }
            mandatoryParams.forEach(function (key) {
                if (!params[key]) invalidParams.push(key);
            });
            if (invalidParams.length > 0) 
                return  done(`Mandatory Params ${invalidParams} is Missing / Invalid`);
            apiOpts={
                uri: _.get(self.config, ['MONGO', 'FETCH_RECENT_URL']),
                method: 'POST',
                timeout: 60000,
                json: {
                    customerId: _.get(params,'customer_id',null),
                    service: _.get(params, 'service', null),
                    recharge_number: _.get(params, 'recharge_number', null),
                    paytype: _.get(params,'paytype',null),
                    operator: _.get(params, 'operator', null)
                }
            }
            let enableNewFormatMCN = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'ENABLE_NEW_FORMAT_MCN', 'SMS_PARSING'], 0)

            if(enableNewFormatMCN){
                delete apiOpts.json.recharge_number;
                delete apiOpts.json.reference_id;
            }
        }

        console.log("printing the apiOpts",apiOpts);

        var latencyStart = new Date().getTime();
        this.L.verbose('MongoAPI:: FETCH_RECENT_API', JSON.stringify(apiOpts));

        REQUEST(apiOpts, (error, response, body) => {
            utility._sendLatencyToDD(latencyStart, {
                'REQUEST_TYPE': 'FETCH_RECENT_DETAILS',
                'URL': '/v1/fetchRecents',
                'SOURCE': source
            });
    
            utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:FETCH_RECENT_DETAILS',
                    'STATCODE:' + _.get(response, 'statusCode', _.get(error, 'code', '5XX')),
                    'API_STATCODE:' + _.get(body, 'status_code', 'XX'),
                    'RETRY_COUNT:' + retryCount,
                    'SOURCE:' + source
            ]);

            console.log("printing the response from saga",body);
    
                if (body && typeof body === 'string') {
                    try {
                        body = JSON.parse(body);
                    } catch (e) {
                        this.L.error('RecentsLayer::recentFetch`,`Error while parsing response body for: ' + JSON.stringify(apiOpts.json) + JSON.stringify(e));
                        return done('Parsing response body error');
                    }
                }
                let apiStatCode = _.get(response, 'statusCode', '5XX') + '',
                    maskedStatCode = apiStatCode[0] + 'XX';
    
                if (error || self.retriableStatusCodes.indexOf(maskedStatCode) > -1) {
                    this.L.error(`RecentsLayer::recentFetch`, `Error from Mongo API apiOpts: ` + JSON.stringify(apiOpts.json) + ' error:' + error);
                    let retry = self.shouldRetryAPI(error, retryCount, maskedStatCode);
                    if (retry) return self.recentFetch(done, params, source, retryCount + 1);
                }
    
                if (!body) {
                    this.L.error(`RecentsLayer::recentFetch`, `Response body not there! apiOpts:` + JSON.stringify(apiOpts.json));
                    utility._sendMetricsToDD(1, [`REQUEST_TYPE:FETCH_RECENT_DETAILS_BODY_NOT_FOUND`, 'STATUS:ERROR']);
                    return done('request Body Not found');
                }
    
                if (apiStatCode === '200') {
                    this.L.verbose(`RecentsLayer::recentFetch`, `Successful fetch from Recents for ${JSON.stringify(apiOpts.json)}`);
                    return done(null, _.get(body, ['recents'], []));
                }

                if(body.errorCode == "ERR_VL_001" || "ERR_VL_002" || "ERR_VL_003"){
                    this.L.error(`RecentsLayer::recentFetch ` + body.errorMessage + ` ` + + JSON.stringify(apiOpts.json) + 'response body : ' + JSON.stringify(body.data));
                    return done("API Failure, body.status_code != '200'");
                }
    
                return done("API Failure, body.status_code != '200'");
            });
    }

    getCCDetailsByMcnCustIdNew(done,params,source = "defaultSource", retryCount = 0){
        let self=this;
        
        self.recentFetch(function(error,data){
            if(!error){
                for (let index = 0; index < data.length; index++){
                    data[index].panUniqueReference=data[index].par;
                    delete data[index].par;
                }
                return done(null,data);
            }
            else{
                return done(error);
            }
        },params,source,retryCount);
    }
*/
    getCCDetailsByMcnCustId(done, params, source = "defaultSource", retryCount = 0) {
        return (done, null) // Mongo API is not available
        // let self = this;
        // let enableNewFormatMCN = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'ENABLE_NEW_FORMAT_MCN', 'SMS_PARSING'], 0)
        // let apiOpts = {
        //     uri: _.get(self.config, ['MONGO', 'RECENT_FETCH_URL']),
        //     method: 'GET',
        //     timeout: 60000,
        //     json: {
        //         recharge_number: _.get(params, 'recharge_number', null),
        //         operator: _.get(params, 'operator', null),
        //         customer_id: _.get(params, 'customer_id', null),
        //         paytype: _.get(params, 'paytype', null),
        //         service: _.get(params, 'service', null),
        //         reference_id: _.get(params, 'reference_id', null)
        //     }
        // };
        // if(enableNewFormatMCN){
        //     delete apiOpts.json.recharge_number;
        //     delete apiOpts.json.reference_id;
        // }

        // var latencyStart = new Date().getTime();
        // this.L.verbose('MongoAPI:: getCCDetailsByMcnCustId api call', JSON.stringify(apiOpts));
        // REQUEST(apiOpts, (error, response, body) => {
        //     utility._sendLatencyToDD(latencyStart, {
        //         'REQUEST_TYPE': 'RECENT_FETCHCCDETAILS',
        //         'URL': '/v1/fetchCCDetail/admin',
        //         'SOURCE': source
        //     });

        //     utility._sendMetricsToDD(1, [
        //         'REQUEST_TYPE:RECENT_FETCHCCDETAILS',
        //         'STATCODE:' + _.get(response, 'statusCode', _.get(error, 'code', '5XX')),
        //         'API_STATCODE:' + _.get(body, 'status_code', 'XX'),
        //         'RETRY_COUNT:' + retryCount,
        //         'SOURCE:' + source
        //     ]);

        //     if (body && typeof body === 'string') {
        //         try {
        //             body = JSON.parse(body);
        //         } catch (e) {
        //             this.L.error('RecentsLayer::getCCDetailsByMcnCustId`,`Error while parsing response body for: ' + JSON.stringify(apiOpts.json) + JSON.stringify(e));
        //             return done('Parsing response body error');
        //         }
        //     }
        //     let apiStatCode = _.get(response, 'statusCode', '5XX') + '',
        //         maskedStatCode = apiStatCode[0] + 'XX';

        //     if (error || self.retriableStatusCodes.indexOf(maskedStatCode) > -1) {
        //         this.L.error(`RecentsLayer::getCCDetailsByMcnCustId`, `Error from Mongo API apiOpts: ` + JSON.stringify(apiOpts.json) + ' error:' + error);
        //         let retry = self.shouldRetryAPI(error, retryCount, maskedStatCode);
        //         if (retry) return self.getCCDetailsByMcnCustId(done, params, source, retryCount + 1);
        //     }

        //     if (!body) {
        //         this.L.error(`RecentsLayer::getCCDetailsByMcnCustId`, `Response body not there! apiOpts:` + JSON.stringify(apiOpts.json));
        //         utility._sendMetricsToDD(1, [`REQUEST_TYPE:RECENT_RECENT_FETCHCCDETAILS_BODY_NOT_FOUND`, 'STATUS:ERROR']);
        //         return done('request Body Not found');
        //     }

        //     if (body.status_code === '00') {
        //         this.L.verbose(`RecentsLayer::getCCDetailsByMcnCustId`, `Successful fetch from Recents for ${JSON.stringify(apiOpts.json)}`);
        //         return done(null, _.get(body, ['data'], []));
        //     }

        //     this.L.error(`RecentsLayer::getCCDetailsByMcnCustId`, `Validation failure error apiOpts : ` + JSON.stringify(apiOpts.json) + 'response body :' + JSON.stringify(body.data));
        //     return done("API Failure, body.status_code != '00'");
        // });
    }

    cacheCleanByCustId(done, params, source = "defaultSource", retryCount = 0) {
        let self = this;

        let apiOpts = {
            uri: _.get(self.config, ['MONGO', 'RECENT_CLEAR_CACHE']),
            method: 'POST',
            timeout: 60000,
            json: params
        };

        var latencyStart = new Date().getTime();
        self.L.verbose('MongoAPI:: cacheCleanByCustId api call', JSON.stringify(apiOpts)); 
        REQUEST(apiOpts, (error, response, body) => {
            utility._sendLatencyToDD(latencyStart, {
                'REQUEST_TYPE': 'RECENT_CACHE_CLEAN',
                'URL': '/api/customer/clearCache',                 
                'SOURCE': source
            });

            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE: RECENT_CACHE_CLEAN',
                'STATCODE:' + _.get(response, 'statusCode', _.get(error, 'code', '5XX')),
                'RETRY_COUNT:' + retryCount,
                'SOURCE:' + source
            ]);

            if (body && typeof body === 'string') {
                try {
                    body = JSON.parse(body);
                } catch (e) {
                    this.L.error('RecentsLayer::cacheCleanByCustId`,`Error while parsing response body for: ' + JSON.stringify(apiOpts.json) + JSON.stringify(e));
                    return done('Parsing response body error');
                }
            }
            let apiStatCode = _.get(response, 'statusCode', '5XX') + '',
                maskedStatCode = apiStatCode[0] + 'XX';

            if (error || self.retriableStatusCodes.indexOf(maskedStatCode) > -1) {
                this.L.error(`RecentsLayer::cacheCleanByCustId`, `Error from Mongo API apiOpts: ` + JSON.stringify(apiOpts.json) + ' error:' + error);
                let retry = self.shouldRetryAPI(error, retryCount, maskedStatCode);
                if (retry) return self.cacheCleanByCustId(done, params, source, retryCount + 1);
            }

            if (!body) {
                this.L.error(`RecentsLayer::cacheCleanByCustId`, `Response body not there! apiOpts:` + JSON.stringify(apiOpts.json));
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:RECENT_CLEAR_CACHE_NULL_BODY_RESPONSE' ,'TYPE:BODY_NOT_FOUND', 'STATUS:ERROR']);
                return done('request Body Not found');
            }

            if (body.statusCode === 'OK') {
                this.L.verbose(`RecentsLayer::cacheCleanByCustId`, `Successful Cache clean for ${JSON.stringify(apiOpts.json)}`);
                return done(null, _.get(body, 'statusCode', ''));
            }

            this.L.error(`RecentsLayer::cacheCleanByCustId`, `Validation failure error apiOpts : ` + JSON.stringify(apiOpts.json) + 'response body :' + JSON.stringify(body));
            return done("API Failure, body.statusCode != 'OK'");
        });
    }

    shouldRetry(done, error, queryParam, fieldName, fieldValue, source, retryCount, statusCode) {
        let self = this;
        statusCode = statusCode + '';
        let maskedStatCode = statusCode[0] + 'XX'
        if ((self.retriableStatusCodes.indexOf(maskedStatCode) > -1 || (error && error.code && self.retryErrors.indexOf(error.code) > -1)) && retryCount < self.maxRetryCount) {
            retryCount += 1;
            return self.update(done, queryParam, fieldName, fieldValue, source, retryCount);
        }

        return done(`Invalid retry error code ${error && error.code} or Max ${retryCount} retryCount reached.`);
    }

    /**
     * returns true/false to retry or no retry
     * @param {*} error 
     * @param {*} retryCount 
     */
    shouldRetryAPI(error, retryCount, maskedStatCode) {
        let self = this;
        if (((self.retriableStatusCodes.indexOf(maskedStatCode) > -1) || (error && error.code && self.retryErrors.indexOf(error.code) > -1)) && retryCount < self.maxRetryCount) {
            return true;
        }
        return false;
    }

    removeRecentsAPI(params, source = "defaultSource", retryCount = 0, callback) {
        return callback(null) // Mongo API is not available
        // let self = this;

        // if (!params || !params.customerId || !params.rechargeNumber) {
        //     this.L.error(`RecentsLayer::removeRecents`, `Invalid params:${JSON.stringify(params)} passed for removeRecents call`);
        //     return callback(`INVALID_PARAMS`);
        // }

        // let customerId = params.customerId,
        //     rechargeNumber = params.rechargeNumber;

        // let apiOpts = {
        //     uri: _.get(self.config, ['MONGO', 'REMOVE_RECENTS']),
        //     method: 'POST',
        //     headers: {
        //         'X-USER-ID': customerId
        //     },
        //     timeout: 60000,
        //     json: {
        //         "objectsToBeDeleted": [{
        //             "recharge_number" : rechargeNumber
        //         }]
        //     }
        // };

        // var latencyStart = new Date().getTime();
        // this.L.verbose('recentsLayer::removeRecents','api call', JSON.stringify(apiOpts));
        // REQUEST(apiOpts, (error, response, body) => {
        //     utility._sendLatencyToDD(latencyStart, {
        //         'REQUEST_TYPE': 'REMOVE_RECENTS',
        //         'URL': '/user/favourite/v2/removerecent',
        //         'SOURCE': source
        //     });

        //     utility._sendMetricsToDD(1, [
        //         'REQUEST_TYPE:REMOVE_RECENTS',
        //         'STATCODE:' + _.get(response, 'statusCode', _.get(error, 'code', '5XX')),
        //         'API_STATCODE:' + _.get(body, 'status_code', 'XX'),
        //         'RETRY_COUNT:' + retryCount,
        //         'SOURCE:' + source
        //     ]);

        //     if (body && typeof body === 'string') {
        //         try {
        //             body = JSON.parse(body);
        //         } catch (e) {
        //             self.L.error('recentsLayer::removeRecents',`Parsing error while api hit for apiOpts:${JSON.stringify(apiOpts)}, Response body:${body}, error:${JSON.stringify(e)}`);
        //             return callback(`PARSING_ERROR`)
        //         }
        //     }
        //     let apiStatCode = _.get(response, 'statusCode', '5XX') + '',
        //         maskedStatCode = apiStatCode[0] + 'XX';

        //     if (error || self.retriableStatusCodes.indexOf(maskedStatCode) > -1) {
        //         this.L.error(`RecentsLayer::removeRecents`, `Error from Mongo API apiOpts: ` + JSON.stringify(apiOpts.json) + ' error:' + error);
        //         let retry = self.shouldRetryAPI(error, retryCount, maskedStatCode);
        //         if (retry) return self.removeRecentsAPI(params, source, retryCount + 1, callback);
        //     }

        //     if (!body) {
        //         this.L.error(`RecentsLayer::removeRecents`, `Response body not there! apiOpts:` + JSON.stringify(apiOpts.json));
        //         utility._sendMetricsToDD(1, [`REQUEST_TYPE:REMOVE_RECENTS`, 'STATUS:ERROR']);
        //         return callback('REQUEST_BODY_NOT_FOUND');
        //     }

        //     if (body.code === 200) {
        //         return callback(null, body);
        //     }

        //     return callback("API_FAILURE");
        // });
    }
    async removeRecents(params, source = "defaultSource", retryCount = 0) {
        let self = this;

        return new Promise((resolve, reject) => {
            self.removeRecentsAPI(params, source = "defaultSource", retryCount = 0, (error, response)=>{
                if(error) {
                    this.L.error('recentsLayer::removeRecents','error:', `${error}_params:${params}`);
                    utility._sendMetricsToDD(1, [`REQUEST_TYPE:REMOVE_RECENTS_API`, 'STATUS:ERROR', `TYPE:${error}`]);
                    resolve(null);
                } else {
                    utility._sendMetricsToDD(1, [`REQUEST_TYPE:REMOVE_RECENTS_API`, 'STATUS:SUCCESS']);
                    this.L.log('recentsLayer::removeRecents','api call', JSON.stringify(response));
                    resolve(response);
                }
            }) 
        });
    }

    /**
     * 
     * @param {*} operator 
     * @returns boolean
     */
    isOperatorBlacklistedToUpdateRecents(operator) {
        let self = this;
        if(operator && _.get(self.config, ['COMMON', 'BLACKLIST_RECENT_OPERATOR', operator], 0)) {
            return true;
        }
        return false;
    }

}

/*
    Sample payload
    {
    "recharge_number" : "4862 XXXX XXXX 8588",//mandatory
    "operator":"visa",//mandatory
    "customer_id" : 12973,//optional
    "paytype":"prepaid", //mandatory,
    "service":"mobile", // mandatory
    "reference_id" : "234567nbhvc45676543456",//optional
    "data":[
        {
            "fieldName":"bills",
            "fieldValue":[{
                "due_date":"2020-10-15 23:59:59",
                "bill_date":"2020-10-08 12:59:59",
                "amount" : 1000.00,
                "label":""
            }]
        }
    ]
    }
*/

/*
Sample payload
{
   "recharge_number" : "4862 XXXX XXXX 8588",//mandatory
   "operator":"visa",//mandatory
   "customer_id" : 12973,//optional
   "paytype":"prepaid", //mandatory,
   "service":"mobile", // mandatory
   "reference_id" : "234567nbhvc45676543456",//optional
   "data":[
       {
           "fieldName":"bills",
           "fieldValue":[{
               "due_date":"2020-10-15 23:59:59",
               "bill_date":"2020-10-08 12:59:59",
               "amount" : 1000.00,
               "label":""
           }]
       }
   ]
}
*/

export default RecentsLayer;
