"use strict";

import _ from 'lodash'
import AWS from 'aws-sdk'
import { parseStream } from 'fast-csv';
const csv = require('fast-csv')
import utility from './'
import MOMENT from 'moment'
import fs from 'fs';
import path from 'path';

let FILE_NAMES=['aa.csv','bb.csv'];
const FS = require('fs');
class AWSCsvIngester {

    constructor(options,updateProgress=()=>{}) {
        this.L = options.L,
        this.config = options.config;
        this.totalProcessedRecordsForCurrentDay = 0;
        this.isRecordProcessingCapped = _.get(options,'isRecordProcessingCapped',false);
        this.cappingNumberForCurrentDay = _.get(options,'cappingNumberForCurrentDay');
        this.allowedCronExecutionTime = _.get(options,'allowedCronExecutionTime', false);
        this.updateProgress = updateProgress 
    }


    configure(bucketName,serviceName="",batchSize = 1){
        let self = this;
        this.s3Params = {
            Bucket: _.get(this.config, 'AWS.Bucket', null),
            ClientInfo: _.get(this.config, 'AWS.ClientInfo', null),
        };
        this.bucketName = bucketName
        this.batchSize = batchSize;
        self.batchRecords = []
        try{

            this.s3 = new AWS.S3(this.s3Params.ClientInfo)
        }catch(error){
            this.L.error("Error while initializing S3",error)
        }
       
        this.serviceName = serviceName
    }

    getFileNames(folderPath , callback){
        let self = this;
        let files = []
        this.s3.listObjects({
            Bucket : 'digital-reminder',
            Delimiter: '/',
            Prefix: `${folderPath}/`
        }, function(err, data) {
            if (err) {
                self.L.critical("Error while Retreiving Data")
                return callback(null,FILE_NAMES)
            } else {
              data.Contents.forEach(function(obj,index) {
                console.log(obj)
                files.push(obj.Key)
              })
              self.L.info(self.serviceName , "files ::",files)
              return callback(null , files)
            }
          })
    }

    pushTestDataFileToS3() {

    const data = `customer_id,service,payment_date
1,electricity,2025-01-01 14:32:10
1,electricity,2025-02-02 08:23:45
1,electricity,2025-01-04 11:50:55
2,electricity,2025-01-02 09:45:21
2,electricity,2025-01-02 10:34:58
1,electricity,2025-01-03 16:27:33
1,electricity,2025-01-02 19:12:06
2,electricity,2025-01-03 13:03:42
2,electricity,2025-01-03 15:11:30
2,electricity,2025-01-03 12:06:05
1,mobile,2025-01-01 07:18:39
1,mobile,2025-02-02 21:02:14
1,loan,2025-01-04 17:56:22
2,mobile,2025-01-02 14:59:03
2,mobile,2025-01-02 16:17:51
1,mobile,2025-01-03 20:14:11
1,mobile,2025-01-02 22:29:47
2,mobile,2025-01-03 18:21:27
2,loan,2025-01-03 09:33:11
2,loan,2025-01-03 11:47:56
`;

    let csvForServer = "/var/log/digital-reminder/Smart_Fetch_Test$2025-02-04.csv";
    let csvForS3 = "digital-reminder/ACTIVE_PAYTM_USER_INGEST_TEST/activePaytm$2025-02-04.csv";

    fs.writeFile(csvForServer, data, "utf-8", (err) => {
        if (err) {
            console.log(err);
        } else {
            console.log("Data saved");
        }
    });

    fs.readFile(csvForServer, (err, data) => {
        if (err) {
            throw err;
        }

        const params = {
            Bucket: 'digital-reminder',
            Key: csvForS3,
            Body: data
        };
        
        this.s3.upload(params, (s3Err, data) => {
            if (s3Err) {
                throw s3Err;
            }
            console.log(`File uploaded successfully at ${data.Location}`);
        });
    });
}

    start(processBatch ,filename ,  callback ,skipRows=0){    

        let self = this;
        // checking if capping number or capping time is reached then stopping the execution.
        if(self.isRecordProcessingCapped && ((self.totalProcessedRecordsForCurrentDay == self.cappingNumberForCurrentDay) || (self.allowedCronExecutionTime && self.allowedCronExecutionTime.isBefore(MOMENT())))) {
            self.L.info(this.serviceName ,filename, "Daily limit of processing reached. Ending the processing for today");
            return callback(null);
        }

        let params =  {
            Bucket:  "digital-reminder",
            Key: filename
        }
        self.totalRecords = skipRows
        let s3Stream,csvStream

        
        self.L.info(self.serviceName , "Starting Processing for file :",filename)
        
        try{
            s3Stream = this.s3.getObject(params).createReadStream();
            //s3Stream = FS.createReadStream(path.resolve("/home/<USER>/digital-reminder",filename));
            s3Stream.on('error',(error)=>{
                self.L.critical("Error while configuring s3Stream - ",error)
                if(self.csvStream){
                    self.csvStream.destroy()
                }else{
                    return callback("Error")
                }
            }) 
            this.csvStream = csv.parseStream(s3Stream,{headers:headers => headers.map(h => h.trim()),skipRows , trim:true});   
        }catch(error){
            self.L.critical("Error while configuring s3Stream",error)
        }

        let recordsProcessedOfCurrentFile = 0;

        this.csvStream
            .on('data', async function processRow(data) {
                    recordsProcessedOfCurrentFile++;
                    if(self.isRecordProcessingCapped && ((self.totalProcessedRecordsForCurrentDay >= self.cappingNumberForCurrentDay - 1) || (self.allowedCronExecutionTime && self.allowedCronExecutionTime.isBefore(MOMENT())))) {
                        self.totalRecords++;
                        self.batchRecords.push(data);
                        self.totalProcessedRecordsForCurrentDay++;
                        self.L.info(this.serviceName ,filename, "Daily limit of processing reached. Ending the processing for today");
                        self.csvStream.removeListener('data',processRow);
                        self.csvStream.end();
                    } else {    
                        self.totalRecords++;
                        self.totalProcessedRecordsForCurrentDay++;
                        self.batchRecords.push(data)
                        if(self.batchRecords.length == 1000){
                            self.csvStream.pause();
                            self.L.info(this.serviceName ,filename, "Processing batch of length ",self.batchRecords.length)
                            try{
                                await processBatch(self.batchRecords)

                            }catch(err){
                                self.L.error(this.serviceName ,filename,  "Error while processing batch",err)
                            }
                            self.L.info(this.serviceName , filename, "Finished processing batch ")
                            self.updateProgress(filename , self.totalRecords)
                            self.batchRecords = []
                            self.csvStream.resume()
                        
                        }
                    }
            })
            .on('end', async (rowCount) => {
                /** Process the last batch */
                if(self.batchRecords.length > 0)
                {
                    self.L.info(this.serviceName ,filename, "Processing batch of length ",self.batchRecords.length)
                    try{
                        await processBatch(self.batchRecords)
                    }catch(err){
                        self.L.error(this.serviceName ,filename,  "Error while processing batch",err)
                    }
                    self.L.info(this.serviceName , filename, "Finished processing batch ")
                    self.batchRecords = []
                }
                if(recordsProcessedOfCurrentFile == rowCount) {
                    utility._sendMetricsToDD(rowCount || "0" ,['REQUEST_TYPE:CSV_PROCESSED',`FILE:${filename}`])
                    self.L.info(this.serviceName ,filename,  "Reached end of csv file , rowCount",rowCount)
                    self.updateProgress(filename , -1)
                } else if(self.isRecordProcessingCapped && ((self.totalProcessedRecordsForCurrentDay >= self.cappingNumberForCurrentDay) || (self.allowedCronExecutionTime && self.allowedCronExecutionTime.isBefore(MOMENT())) )) {
                    utility._sendMetricsToDD(rowCount || "0" ,['REQUEST_TYPE:DAILY_LIMIT_BREACHED',`FILE:${filename}`])
                    self.L.info(this.serviceName ,filename,  "DAILY LIMIT BREACHED, rowCount",rowCount);
                    self.updateProgress(filename , self.totalRecords)
                }
                return callback(null)
               
            })
            .on('error', error => {
                utility._sendMetricsToDD(1 ,['REQUEST_TYPE:CSV_ERROR',`FILE:${filename}`])
                self.L.error(self.serviceName , filename,"Error in CSV Stream : ", error)
                if(self.csvStream){
                    self.csvStream.destroy()
                }
                return callback(error)
            });

    }

}

export default AWSCsvIngester;

