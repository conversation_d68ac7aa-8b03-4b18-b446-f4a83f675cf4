"use strict";


import _ from 'lodash';
import UTIL from 'util'
import uuidv1 from 'uuidv1'
import RequestWrapper from './requestWrapper'
import REQUEST from 'request'
import utility from '../lib';


class DigitalCatalog {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;

        this.requestWraper = new RequestWrapper({
            requestType: "DIGITAL_CATALOG",
            retryCount: 3
        });

        this.appName = options.appName;
    }

    async getPlanDetail({ categoryId, operator, circle, amount }) {
        let responseObject = {
            status: true,
            data: {}
        };

        try {
            let { endPoint, API } = this.config.DIGITAL_CATALOG; // check impact 

            let url = endPoint + API.getPlan;

            url = UTIL.format(url, categoryId, amount, encodeURI(this._capitalize(operator)), encodeURI(this._capitalize(circle)));

            let apiOpts = {
                method: 'GET',
                url: url,
                json: true,
                timeout: 10000,
                headers: {
                    "x-app-rid": uuidv1() + "-" + this.appName
                }
            };

            try {
                this.L.log("getPlanDetail, requestOption: ", apiOpts);

                responseObject.data = await this.requestWraper.hitRequest(apiOpts);
            } catch (error) {
                this.L.log("getPlanDetail, error:", error, " , for requestOption: ", apiOpts);

                responseObject.status = false;
                responseObject.data = error;
            }

        } catch (error) {
            this.L.error("src/lib/digitalCatalog/getPlanDetail, error", error);
            
            responseObject.status = false;
            responseObject.data = error;
        }

        return responseObject;
    }

    _capitalize(inputString) {
        let strArr = inputString.split(" ");
        
        for(let i = 0; i < strArr.length; i++) {
            let str= strArr[i];
            strArr[i] = str.charAt(0).toUpperCase() + str.slice(1);
        }

        return strArr.join(" ");
    }
    getCategoryProductDetail(cb){
        let self = this,
            GET_CATEGORY_FROM_CATEGORY_ID_API_URL = self.config.DIGITAL_CATALOG.GET_CATEGORY_FROM_CATEGORY_ID_API_URL,
            apiOpts = {
                url: GET_CATEGORY_FROM_CATEGORY_ID_API_URL, 
                method: 'GET',
                json: true,
                timeout: 10000,
                headers: {
                    "x-app-rid": uuidv1() + "-" + self.appName
                }                
            };
        self.L.verbose('PGAPI:: getCreditCardDataFromPG api call', JSON.stringify(apiOpts));
        let latencyStart = new Date().getTime();    
        REQUEST(apiOpts, (error, response, body) =>{
            let latencyTime = new Date().getTime() - latencyStart;
            utility._sendMetricsToDD(latencyTime, ['REQUEST_TYPE:GET_CATEGORY_DATA_FROM_DCAT', `URL:${GET_CATEGORY_FROM_CATEGORY_ID_API_URL}`]);
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:GET_CATEGORY_DATA_FROM_DCAT', 
                `URL:${GET_CATEGORY_FROM_CATEGORY_ID_API_URL}`,
                'STATCODE:' + _.get(response, 'statusCode', _.get(error, 'code', '5XX'))
            ]);
            if (!error && body) {
                let res = body;
                return cb(null, res);
            } else {
                return cb(error);
            }
        });
    }

    get_product_list(cb, operator, circle) {
        let self = this,
            GET_PRODUCTLIST_URL = self.config.DIGITAL_CATALOG.GET_PRODUCTLIST_URL;
        operator = self._capitalize(operator);
        circle = self._capitalize(circle);
        let apiOpts = {
            method: 'POST',
            url: GET_PRODUCTLIST_URL,
            qs: { channel: 'web', version: '200.200.200', locale: 'en-in' },
            headers:
            {
                'content-type': 'application/json'
            },
            body:
            {
                filters:
                    [{ key: 'operator', value: operator },
                    { key: 'circle', value: circle }],
                ignoreVisibility: true,
                itemsPerPage: 20,
                pageCount: 1
            },
            json: true,
            timeout: 10000,
        }
        self.L.verbose('PGAPI:: GET_PRODUCTLIST_URL api call', JSON.stringify(apiOpts));
        let latencyStart = new Date().getTime();

        REQUEST(apiOpts, (error, response, body) => {
            let latencyTime = new Date().getTime() - latencyStart;
            utility._sendMetricsToDD(latencyTime, ['REQUEST_TYPE:GET_PRODUCTLIST_URL', `URL:${GET_PRODUCTLIST_URL}`]);
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:GET_PRODUCTLIST_URL',
                `URL:${GET_PRODUCTLIST_URL}`,
                'STATCODE:' + _.get(response, 'statusCode', _.get(error, 'code', '5XX'))
            ]);
            if (!error && body) {
                let res = body;
                return cb(null, res);
            } else {
                return cb(error);
            }
        });

    }

    get_circle_from_mnp(cb, recharge_number) {
        let self = this,
            GET_CIRCLE_FROM_MNP_URL = self.config.DIGITAL_CATALOG.GET_CIRCLE_FROM_MNP_URL,
            apiOpts = {
                url: GET_CIRCLE_FROM_MNP_URL,
                method: 'GET',
                 headers:
                {
                'servicename': 'SMS_PARSING'
                },
                qs: { number: recharge_number },
                timeout: 1000,
            };
        self.L.verbose('PGAPI:: GET_CIRCLE_FROM_MNP_URL api call', JSON.stringify(apiOpts));
        let latencyStart = new Date().getTime();
        

        REQUEST(apiOpts, (error, response, body) => {
            let latencyTime = new Date().getTime() - latencyStart;
            utility._sendMetricsToDD(latencyTime, ['REQUEST_TYPE:GET_CIRCLE_FROM_MNP_URL', `URL:${GET_CIRCLE_FROM_MNP_URL}`]);
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:GET_CIRCLE_FROM_MNP_URL',
                `URL:${GET_CIRCLE_FROM_MNP_URL}`,
                'STATCODE:' + _.get(response, 'statusCode', _.get(error, 'code', '5XX'))
            ]);
            let res = null;
            if (!error && body) res = body;
            return cb(error, res);
        });
    }
    get_validity_from_rps(cb, operator, amount, rechargeNumber, productId) {
        operator = this._capitalize(operator);

        let self = this;
        var options = {
            method: 'POST',
            url: self.config.DIGITAL_CATALOG.GET_PLAN_RPS_OTT,
            qs: { version: '111.99', channel: 'ios', locale: 'en-in' },
            headers:
            {
                'content-type': 'application/json'
            },
            body:
            {
                filters:
                    [{ key: 'operator', value: operator },
                    { key: 'price', value: amount }],    // amount = 399 for statging
                searchFilters: [{ key: 'price', value: amount }],
                userPlansActive: true,
                rechargeNumber: rechargeNumber,
                productId: productId
            },
            json: true
        };

        REQUEST(options, function (error, response, body) {
            cb(error, body);
        });
    }


    get_plan_mapping(cb, page, size) {
        let self = this,
            GET_PLAN_MAPPING_URL = self.config.DIGITAL_CATALOG.GET_PLAN_DETAILS_URL,
            apiOpts = {
                url: GET_PLAN_MAPPING_URL,
                method: 'GET',
                qs: {page: page, size: size},
                headers:{
                    'content-type': 'application/json'
                },
                timeout: 1000,
            };
            self.L.log("get_plan_mapping, requestOption: ", apiOpts);
            let latencyStart = new Date().getTime();
            REQUEST(apiOpts, (error, response, body) => {
                let latencyTime = new Date().getTime() - latencyStart;
                utility._sendMetricsToDD(latencyTime, ['REQUEST_TYPE:GET_PLAN_MAPPING', `URL:${GET_PLAN_MAPPING_URL}`]);
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:GET_PLAN_MAPPING',
                    `URL:${GET_PLAN_MAPPING_URL}`,
                    'STATCODE:' + _.get(response, 'statusCode', _.get(error, 'code', '5XX'))
                ]);
                if (!error && body) {
                    let res = body;
                    return cb(null, res);
                } else {
                    return cb(error);
                }
            });
    }
}

export default DigitalCatalog;
