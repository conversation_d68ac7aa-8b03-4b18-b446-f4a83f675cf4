import _ from 'lodash';
import utility from './index.js';
import ASY<PERSON> from 'async';
import MOMENT from 'moment';

class CombinedNotificationUtil {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
    }

    /**
     * Format epoch timestamp to YYYY-MM-DD format
     * @param {number} epochTimestamp - Epoch timestamp in milliseconds
     * @returns {string|null} - Formatted date string or null if invalid
     */
    formatEpochToDate(epochTimestamp) {
        let self = this;
        
        try {
            // Check if epochTimestamp is a valid number
            if (!epochTimestamp || isNaN(epochTimestamp)) {
                self.L.log('formatEpochToDate :: Invalid epoch timestamp:', epochTimestamp);
                return null;
            }
            
            // Convert to moment object and validate
            let momentDate = MOMENT(Number(epochTimestamp));
            if (!momentDate.isValid()) {
                self.L.log('formatEpochToDate :: Invalid moment date for epoch:', epochTimestamp);
                return null;
            }
            
            // Format to YYYY-MM-DD
            let formattedDate = momentDate.format('YYYY-MM-DD');
            self.L.log(`formatEpochToDate :: Converted ${epochTimestamp} to ${formattedDate}`);
            
            return formattedDate;
        } catch (error) {
            self.L.error('formatEpochToDate :: Error formatting epoch timestamp:', error, 'epochTimestamp:', epochTimestamp);
            return null;
        }
    }

    /**
     * Calculate statistics from combined data array
     * @param {Array} combinedData - Array of bill objects
     * @returns {Object} - Object with totalDueAmount, billCount, minDueDate, maxDueDate
     */
    calculateCombinedDataStats(combinedData) {
        let self = this;
        
        if (!Array.isArray(combinedData) || combinedData.length === 0) {
            return {
                totalDueAmount: 0,
                billCount: 0,
                minDueDate: null,
                maxDueDate: null
            };
        }

        let totalDueAmount = 0;
        let billCount = combinedData.length;
        let dueDates = [];

        // Process each bill object in the array
        combinedData.forEach(bill => {
            // Sum up due amounts
            if (bill.dueAmount && !isNaN(bill.dueAmount)) {
                totalDueAmount += parseFloat(bill.dueAmount);
            }
            
            // Collect due dates
            if (bill.dueDate) {
                dueDates.push(bill.dueDate);
            }
        });

        // Calculate min and max due dates
        let minDueDate = dueDates.length > 0 ? Math.min(...dueDates) : null;
        let maxDueDate = dueDates.length > 0 ? Math.max(...dueDates) : null;

        // Format dates to YYYY-MM-DD
        let formattedMinDueDate = minDueDate ? self.formatEpochToDate(minDueDate) : null;
        let formattedMaxDueDate = maxDueDate ? self.formatEpochToDate(maxDueDate) : null;

        let stats = {
            totalDueAmount: totalDueAmount,
            billCount: billCount,
            minDueDate: formattedMinDueDate,
            maxDueDate: formattedMaxDueDate
        };

        self.L.log('calculateCombinedDataStats :: stats = ' + JSON.stringify(stats, null, 2));
        
        return stats;
    }

    validateDataToProcessForNotification(callback, kafkaPayload) {
        let self = this;
        self.L.log('1. validateDataToProcessForNotification :: convert payload to record and validate :: 1');
        let record = self.convertKafkaPayloadToRecord(kafkaPayload);
        
        if (record == null) {
            utility.sendNotificationMetricsFromCreate(record,{},"COMBINED_INVALID_PAYLOAD")
            return callback('unable to get valid data from kafka payload', record);
        }

        // Root level required fields validation
        const correlationId = _.get(record, 'correlationId', null);
        if (!correlationId || typeof correlationId !== 'string' || correlationId.trim() === '') {
            utility.sendNotificationMetricsFromCreate(record,{},"COMBINED_INVALID_CORRELATION_ID")
            return callback('correlationId is required and must be a non-empty string', record);
        }

        const notificationType = _.get(record, 'notificationType', null);
        if (notificationType !== 'COMBINED') {
            utility.sendNotificationMetricsFromCreate(record,{},"COMBINED_INVALID_NOTIFICATION_TYPE")
            return callback('notificationType must be "COMBINED"', record);
        }

        const source = _.get(record, 'source', null);
        if (!source || typeof source !== 'string' || source.trim() === '') {
            utility.sendNotificationMetricsFromCreate(record,{},"COMBINED_INVALID_SOURCE")
            return callback('source is required and must be a non-empty string', record);
        }

        const data = _.get(record, 'data', null);
        if (!data || typeof data !== 'object') {
            utility.sendNotificationMetricsFromCreate(record,{},"COMBINED_INVALID_DATA")
            return callback('data is required and must be an object', record);
        }

        // data.* required fields validation
        const customerId = _.get(data, 'customer_id', null);
        if (!customerId || typeof customerId !== 'number' || customerId <= 0 || !Number.isInteger(customerId)) {
            utility.sendNotificationMetricsFromCreate(record,{},"COMBINED_INVALID_CUSTOMER_ID")
            return callback('customer_id is required and must be a positive integer', record);
        }

        const service = _.get(data, 'service', null);
        if (!service || typeof service !== 'string' || service.trim() === '') {
            utility.sendNotificationMetricsFromCreate(record,{},"COMBINED_INVALID_SERVICE")
            return callback('service is required and must be a non-empty string', record);
        }

        const combinedData = _.get(data, 'combined_data', null);
        if (!combinedData || !Array.isArray(combinedData) || combinedData.length === 0) {
            utility.sendNotificationMetricsFromCreate(record,{},"COMBINED_INVALID_COMBINED_DATA")
            return callback('combined_data is required and must be a non-empty array', record);
        }

        if (combinedData.length > 5) {
            utility.sendNotificationMetricsFromCreate(record,{},"COMBINED_DATA_TOO_LARGE")
            return callback('combined_data array length must not exceed 5 items', record);
        }

        // Validate each item in combined_data array
        for (let i = 0; i < combinedData.length; i++) {
            const item = combinedData[i];
            const itemIndex = `combined_data[${i}]`;

            // rechargeNumber validation
            const rechargeNumber = _.get(item, 'rechargeNumber', null);
            if (!rechargeNumber || typeof rechargeNumber !== 'string' || rechargeNumber.trim() === '') {
                utility.sendNotificationMetricsFromCreate(record,{},"COMBINED_INVALID_RECHARGE_NUMBER")
                return callback(`${itemIndex}.rechargeNumber is required and must be a non-empty string`, record);
            }

            // operator validation
            const operator = _.get(item, 'operator', null);
            if (!operator || typeof operator !== 'string' || operator.trim() === '') {
                utility.sendNotificationMetricsFromCreate(record,{},"COMBINED_INVALID_OPERATOR")
                return callback(`${itemIndex}.operator is required and must be a non-empty string`, record);
            }

            // dueDate validation
            const dueDate = _.get(item, 'dueDate', null);
            if (!dueDate || typeof dueDate !== 'number') {
                utility.sendNotificationMetricsFromCreate(record,{},"COMBINED_INVALID_DUE_DATE")
                return callback(`${itemIndex}.dueDate is required and must be a number (epoch timestamp)`, record);
            }

            // dueAmount validation
            const dueAmount = _.get(item, 'dueAmount', null);
            if (dueAmount === null || dueAmount === undefined || typeof dueAmount !== 'number') {
                utility.sendNotificationMetricsFromCreate(record,{},"COMBINED_INVALID_DUE_AMOUNT")
                return callback(`${itemIndex}.dueAmount is required and must be a number`, record);
            }

            // productId validation
            const productId = _.get(item, 'productId', null);
            if (!productId || typeof productId !== 'number' || productId <= 0 || !Number.isInteger(productId)) {
                utility.sendNotificationMetricsFromCreate(record,{},"COMBINED_INVALID_PRODUCT_ID")
                return callback(`${itemIndex}.productId is required and must be a positive integer`, record);
            }

            // userType validation
            const userType = _.get(item, 'userType', null);
            if (!userType || !['RU', 'NON_RU'].includes(userType)) {
                utility.sendNotificationMetricsFromCreate(record,{},"COMBINED_INVALID_USER_TYPE")
                return callback(`${itemIndex}.userType is required and must be either "RU" or "NON_RU"`, record);
            }

            // service validation (should match parent service)
            const itemService = _.get(item, 'service', null);
            if (!itemService || typeof itemService !== 'string' || itemService.trim() === '') {
                utility.sendNotificationMetricsFromCreate(record,{},"COMBINED_INVALID_ITEM_SERVICE")
                return callback(`${itemIndex}.service is required and must be a non-empty string`, record);
            }
            if (itemService !== service) {
                utility.sendNotificationMetricsFromCreate(record,{},"COMBINED_SERVICE_MISMATCH")
                return callback(`${itemIndex}.service must match parent data.service`, record);
            }

            // status validation (should not be 7 or 13)
            const status = _.get(item, 'status', null);
            if (status === null || status === undefined || typeof status !== 'number' || !Number.isInteger(status)) {
                utility.sendNotificationMetricsFromCreate(record,{},"COMBINED_INVALID_STATUS")
                return callback(`${itemIndex}.status is required and must be an integer`, record);
            }
            if (status === 7 || status === 13) {
                utility.sendNotificationMetricsFromCreate(record,{},"COMBINED_INVALID_STATUS_VALUE")
                return callback(`${itemIndex}.status cannot be 7 or 13`, record);
            }

            // notification_status validation (should not be 0)
            const notificationStatus = _.get(item, 'notification_status', null);
            if (notificationStatus === null || notificationStatus === undefined || typeof notificationStatus !== 'number' || !Number.isInteger(notificationStatus)) {
                utility.sendNotificationMetricsFromCreate(record,{},"COMBINED_INVALID_NOTIFICATION_STATUS")
                return callback(`${itemIndex}.notification_status is required and must be an integer`, record);
            }
            if (notificationStatus === 0) {
                utility.sendNotificationMetricsFromCreate(record,{},"COMBINED_INVALID_NOTIFICATION_STATUS_VALUE")
                return callback(`${itemIndex}.notification_status cannot be 0`, record);
            }
        }

        self.L.log('validateDataToProcessForNotification :: All validations passed successfully');
        return callback(null, record);
    }

    convertKafkaPayloadToRecord(kafkaPayload) {
        let self = this;
        let kafkaPayloadData;

        try {
            kafkaPayloadData = JSON.parse(_.get(kafkaPayload, 'value', null));
            if(_.get(kafkaPayloadData, 'correlationId', null)){
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:COMBINED_NOTIFICATION_CORRELATION_ID', `ID:${_.get(kafkaPayloadData, 'correlationId', null)}`]);
            }
        } catch (error) {
            if (error) {
                self.L.critical('convertKafkaPayloadToRecord', `Invalid Kafka record received`, kafkaPayload);
                return null;
            }
        }

        return kafkaPayloadData;
    }

    /**
     * Process template selection for combined notification
     * @param {Object} record - The notification record
     * @param {Object} payload - The payload containing notification data 
     * @param {string} tableName - The table name
     * @param {string} notificationType - The type of notification (should be 'COMBINED')
     * @param {string} type - The notification type (SMS, PUSH, EMAIL, etc.)
     * @returns {number|null} - The template ID or null if not found
     */
    processForCombinedTemplate(record, payload, tableName, notificationType, type) {
        try {
            let self = this,
                dayValue = '',
                templateId = null;
            
            // Handle null/undefined record gracefully
            if (!record) {
                self.L.log("processForCombinedTemplate :: record is null/undefined, returning null");
                return null;
            }
            
            self.L.log("processForCombinedTemplate :: getTemplateId :: dayValue: ", dayValue, "operator : ", _.get(record, 'operator', null), " type = ", type);

            let serviceBasedKey = `BR_${_.toUpper(_.get(payload, 'service'))}_${dayValue}${notificationType}_${type}`;

            // Base key for combined template
            let serviceBasedKeyForCombined = `BR_${_.toUpper(_.get(payload, 'service'))}_${notificationType}_${type}`;
            
            // Get payload data for determining the case
            const billCount = _.get(payload, 'billCount', 0);
            const minDueDate = _.get(payload, 'minDueDate');
            const maxDueDate = _.get(payload, 'maxDueDate');
            
            // Determine case and append appropriate suffix
            let caseSuffix = '';
            if (billCount === 1) {
                caseSuffix = '_SINGLEBILL';
            } else if (billCount > 1) {
                if (minDueDate === maxDueDate) {
                    caseSuffix = '_MULTIBILL_SAMEDUEDATES';
                } else {
                    caseSuffix = '_MULTIBILL_DIFFDUEDATES';
                }
            }
            
            // Generate specific key with case suffix
            let specificKey = serviceBasedKeyForCombined + caseSuffix;
            
            self.L.log("processForCombinedTemplate :: getTemplateId :: serviceBasedKeyForCombined: ", serviceBasedKeyForCombined);
            if (caseSuffix) {
                self.L.log("processForCombinedTemplate :: getTemplateId :: specificKey: ", specificKey);
            }
            
            // Try to get templateId from specific key first, then fallback to base key
            templateId = _.get(record, ['templates', type]);
            if (!templateId) {
                // First try specific key if case suffix exists
                if (caseSuffix) {
                    templateId = _.get(this.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG','COMBINED_TEMPLATE_ID_BY_SERVICE', specificKey]);
                    self.L.log("processForCombinedTemplate :: getTemplateId :: templateId from specificKey: ", templateId);
                }
                
                // Fallback to base key if specific key didn't return a templateId
                if (!templateId) {
                    templateId = _.get(this.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG','COMBINED_TEMPLATE_ID_BY_SERVICE', serviceBasedKeyForCombined]);
                    self.L.log("processForCombinedTemplate :: getTemplateId :: templateId from baseKey: ", templateId);
                }
            }
            
            self.L.log("processForCombinedTemplate :: getTemplateId :: final templateId: ", templateId);
            return templateId || null;
        }
        catch (error) {
            this.L.error(`processForCombinedTemplate::Error while fetching templateId for combined bill notification: ${error}`);
            return null;
        }
    }

    /**
     * Prepare payment cache parameters from individual bill in combinedParams
     * @param {Object} bill - Individual bill object from combinedParams
     * @param {Object} notifyService - Reference to notify service for context
     * @returns {Object} - Parameters object for payment cache checking
     */
    prepareCombinedBillParams(bill, notifyService) {
        let self = this;
        let recharge_number = _.get(bill, 'recharge_number', _.get(bill, 'rechargeNumber', null));
        let paytype = _.toLower(_.get(bill, 'paytype', 'dummy'));
        let service = _.toLower(_.get(bill, 'service', null));
        let operator = _.toLower(_.get(bill, 'operator', null));
        let bank_name = _.toLower(_.get(bill, 'bank_name', null));
        let customer_id = _.get(bill, 'customer_id', null);
        let card_network = _.toLower(_.get(bill, 'card_network', null));

        return {
            paytype: paytype,
            service: service,
            operator: service == 'financial services' && notifyService.notificationBillSource == 'NONRU' ? bank_name : operator,
            recharge_number: service == 'financial services' ? notifyService.getRNForCCCache(recharge_number, customer_id, bank_name, card_network) : recharge_number,
            customer_id: customer_id
        };
    }

    /**
     * Validate payments for combined notification and update payload
     * @param {Object} row - Notification row
     * @param {Object} notifyService - Reference to notify service
     * @param {Function} callback - Callback function
     */
    validateAndUpdateCombinedPayments(row, notifyService, callback) {
        let self = this;
        
        try {
            let data = typeof row.data == 'string' ? JSON.parse(row.data) : row.data;
            let combinedParams = _.get(data, 'dynamicParams.combinedParams', []);
            
            if (!combinedParams || !Array.isArray(combinedParams) || combinedParams.length === 0) {
                self.L.log('validateAndUpdateCombinedPayments:: No combinedParams found, proceeding with regular flow');
                return callback(null);
            }

            self.L.log(`validateAndUpdateCombinedPayments:: Validating payments for ${combinedParams.length} bills ${JSON.stringify(combinedParams)}`);
            
            let validBills = [];
            let totalAmount = 0;
            let paidBillsCount = 0;

            // Use ASYNC.each to check payment status for each bill
            ASYNC.each(combinedParams, (bill, next) => {
                // Prepare parameters for this bill
                let params = self.prepareCombinedBillParams(bill, notifyService);
                
                // Check payment status using the refactored function
                notifyService.checkPaymentCacheByParams(params, (err) => {
                    if (err) {
                        // Payment found or remind later set - skip this bill
                        paidBillsCount++;
                        self.L.log(`validateAndUpdateCombinedPayments:: Bill payment found for recharge_number: ${params.recharge_number}, skipping`);
                    } else {
                        // No payment found - include this bill
                        validBills.push(bill);
                        totalAmount += parseFloat(_.get(bill, 'dueAmount', 0));
                    }
                    next();
                });
            }, (err) => {
                if (err) {
                    self.L.error('validateAndUpdateCombinedPayments:: Error during payment validation:', err);
                    return callback(err);
                }

                self.L.log(`validateAndUpdateCombinedPayments:: Payment validation complete. Valid bills: ${validBills.length}, Paid bills: ${paidBillsCount}`);

                // Check if all bills are paid
                if (validBills.length === 0) {
                    self.L.log('validateAndUpdateCombinedPayments:: All bills paid, dropping notification');
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:COMBINED_NOTIFICATION_PAYMENT_VALIDATION',
                        'STATUS:ALL_BILLS_PAID',
                        'BILL_SOURCE_TYPE:' + notifyService.notificationBillSource
                    ]);
                    return callback({
                        status: _.get(notifyService.config, ['NOTIFICATION', 'status', 'CANCELED'], 2),
                        error_msg: 'All bills in combined notification already paid'
                    });
                }

                // Update payload with remaining bills
                self.updateCombinedPayload(row, data, validBills, totalAmount, paidBillsCount, notifyService, callback);
            });

        } catch (error) {
            self.L.error('validateAndUpdateCombinedPayments:: Error:', error);
            return callback(error);
        }
    }

    /**
     * Update combined notification payload with remaining valid bills
     * @param {Object} row - Notification row
     * @param {Object} data - Parsed notification data
     * @param {Array} validBills - Array of bills that haven't been paid
     * @param {number} totalAmount - Recalculated total amount
     * @param {number} paidBillsCount - Number of bills that were paid
     * @param {Object} notifyService - Reference to notify service
     * @param {Function} callback - Callback function
     */
    updateCombinedPayload(row, data, validBills, totalAmount, paidBillsCount, notifyService, callback) {
        let self = this;
        
        try {
            // Update combinedParams with valid bills only
            _.set(data, 'dynamicParams.combinedParams', validBills);
            _.set(data, 'dynamicParams.totalDueAmount', totalAmount);
            _.set(data, 'dynamicParams.billCount', validBills.length);

            // Recalculate min/max due dates from remaining bills
            let dueDates = validBills.map(bill => _.get(bill, 'dueDate')).filter(date => date);
            if (dueDates.length > 0) {
                let minDueDate = Math.min(...dueDates);
                let maxDueDate = Math.max(...dueDates);
                
                // Format dates to YYYY-MM-DD
                let formattedMinDueDate = self.formatEpochToDate(minDueDate);
                let formattedMaxDueDate = self.formatEpochToDate(maxDueDate);
                
                _.set(data, 'dynamicParams.minDueDate', formattedMinDueDate);
                _.set(data, 'dynamicParams.maxDueDate', formattedMaxDueDate);
            }

            // Update row data
            row.data = data;

            self.L.log(`updateCombinedPayload:: Updated payload - validBills: ${validBills.length}, totalAmount: ${totalAmount}, paidBillsCount: ${paidBillsCount}`);

            // Send metrics about the update
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:COMBINED_NOTIFICATION_PAYMENT_VALIDATION',
                'STATUS:PAYLOAD_UPDATED',
                `VALID_BILLS:${validBills.length}`,
                `PAID_BILLS:${paidBillsCount}`
            ]);

            // Re-evaluate template based on updated bill count and due dates
            self.reEvaluateCombinedTemplate(row, data, validBills, notifyService, callback);

        } catch (error) {
            self.L.error('updateCombinedPayload:: Error updating payload:', error);
            return callback(error);
        }
    }

    /**
     * Re-evaluate combined template based on updated bill data
     * @param {Object} row - Notification row
     * @param {Object} data - Parsed notification data
     * @param {Array} validBills - Array of remaining valid bills
     * @param {Object} notifyService - Reference to notify service
     * @param {Function} callback - Callback function
     */
    reEvaluateCombinedTemplate(row, data, validBills, notifyService, callback) {
        let self = this;
        
        try {
            // Create a temporary record for template evaluation
            let tempRecord = {
                templates: _.get(row, 'templates', {}),
                operator: _.get(data, 'dynamicParams.operator', null)
            };

            // Create payload for template evaluation
            let tempPayload = {
                billCount: validBills.length,
                minDueDate: _.get(data, 'dynamicParams.minDueDate', null),
                maxDueDate: _.get(data, 'dynamicParams.maxDueDate', null),
                service: _.get(data, 'dynamicParams.service', null)
            };

            // Re-evaluate template using our own method
            let newTemplateId = self.processForCombinedTemplate(
                tempRecord, 
                tempPayload, 
                null, // tableName 
                'COMBINED', 
                row.type
            );

            // Check if template changed
            if (newTemplateId && newTemplateId !== row.template_id) {
                self.L.log(`reEvaluateCombinedTemplate:: Template changed from ${row.template_id} to ${newTemplateId}`);

                let templateName = _.get(self.config, ['DYNAMIC_CONFIG', 'TEMPLATE_CONFIG', newTemplateId, 'TEMPLATE_NAME'], null);
                if(templateName) {
                    _.set(data, 'templateName', templateName);
                    _.set(data, 'additional_data.templateName', templateName);
                    // Update template_id in row and data
                    _.set(row, 'original_template_id', row.template_id);
                    row.template_id = newTemplateId;
                    _.set(data, 'template_id', newTemplateId);

                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:COMBINED_NOTIFICATION_TEMPLATE_REEVALUATION',
                        'STATUS:TEMPLATE_CHANGED',
                        `OLD_TEMPLATE:${row.template_id}`,
                        `NEW_TEMPLATE:${newTemplateId}`
                    ]);
                } else {
                    self.L.log(`reEvaluateCombinedTemplate:: Template name not found for template_id: ${newTemplateId}`);
                }
                
            } else {
                self.L.log(`reEvaluateCombinedTemplate:: Template remains same: ${row.template_id}`);
            }

            // Continue with regular notification flow
            callback(null);

        } catch (error) {
            self.L.error('reEvaluateCombinedTemplate:: Error during template re-evaluation:', error);
            // Continue with original template in case of error
            callback(null);
        }
    }

    /**
     * Publish combined notification to RU DWH event topic
     * @param {Object} preparedKafkaPayload - The prepared kafka payload
     * @param {Object} reminderKafkaNotificationServicePublisher - The reminder kafka publisher
     * @param {Object} config - Configuration object
     * @param {Function} publisherCallbackFunction - Callback function (optional)
     */
    publishCombinedNotificationToRuDwh(preparedKafkaPayload, reminderKafkaNotificationServicePublisher, config) {
        let self = this;
        let kafkaStartTime = new Date().getTime();
        
        // Create payload for ru_dwh_event topic
        let ruDwhTopic = _.get(config, 'KAFKA.SERVICES.RU_DWH_EVENT.TOPIC', 'ru_dwh_event');
        let ruDwhPublisherObject = [{
            topic: ruDwhTopic,
            messages: JSON.stringify(preparedKafkaPayload.payload),
            key: String(_.get(preparedKafkaPayload, 'payload.data.customer_id', ''))
        }];
        
        self.L.log(`publishCombinedNotificationToRuDwh:: Publishing combined notification to ${ruDwhTopic}`);
        
        // Publish to ru_dwh_event topic using reminderKafkaNotificationServicePublisher
        reminderKafkaNotificationServicePublisher.publishData(ruDwhPublisherObject, (error) => {
            let kafkaEndTime = new Date().getTime();
            let kafkaDuration = kafkaEndTime - kafkaStartTime;
            self.L.log(`kafka ru_dwh_event latency - publish - ${kafkaDuration} milliseconds`);
            
            if (error) {
                self.L.error(`publishCombinedNotificationToRuDwh:: Error publishing to ${ruDwhTopic}:`, error);
            } else {
                self.L.log(`publishCombinedNotificationToRuDwh:: Successfully 000  published to ${ruDwhTopic}`);
                utility._sendMetricsToDD(1, [
                    'STATUS:PUBLISHED',
                    'TOPIC:' + ruDwhTopic,
                    'SOURCE_ID:' + _.get(preparedKafkaPayload, 'payload.source_id', 0),
                    'CATEGORY_ID:' + _.get(preparedKafkaPayload, 'payload.category_id', 0),
                    'TYPE:COMBINED_RU_DWH'
                ]);
            }
        }, [200, 800]);
    }

    /**
     * Check if a combined notification already exists for the given record
     * This validation is used in normal single notification flow to skip notifications
     * when a combined notification is already created for the same recharge number
     * 
     * @param {Object} record - The notification record from normal flow
     * @param {Object} cassandraBillsModel - Instance of cassandraBills model
     * @param {Function} callback - Callback function (error, shouldSkip)
     */
    /**
     * Check if customer is eligible for combined notification existence check
     * This includes customer ID whitelisting, notification type, realtime flags, and due date check
     */
    isEligibleForCombinedNotificationExistenceCheck(custId, notificationType, isRealtime, isNonRuRealtime, dueDate) {
        let self = this;
        let cugCustIds = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'COMBINED_NOTIFICATION_VALIDATION_CONFIG', 'CUG_CUSTOMERID_LIST'], []);
        let percentageRollout = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'COMBINED_NOTIFICATION_VALIDATION_CONFIG', 'PERCENTAGE_ROLLOUT'], 0);

        self.L.log(`isEligibleForCombinedNotificationExistenceCheck:: Checking if Customer ${custId} is part of cugCustIds: ${cugCustIds} and percentageRollout: ${percentageRollout}`);

        // Check if notification type is DUEDATE or BILLDUE
        if (!notificationType || (notificationType !== 'DUEDATE' && notificationType !== 'BILLDUE')) {
            self.L.log(`isEligibleForCombinedNotificationExistenceCheck:: NotificationType ${notificationType} not eligible - must be DUEDATE or BILLDUE`);
            return false;
        }

        // Check if it's non-realtime (both realtime flags should be false)
        if (isRealtime === true || isNonRuRealtime === true) {
            self.L.log(`isEligibleForCombinedNotificationExistenceCheck:: Realtime processing not eligible - isRealtime: ${isRealtime}, isNonRuRealtime: ${isNonRuRealtime}`);
            return false;
        }

        // Check if dueDate is current date - if yes, proceed with normal flow (skip combined validation)
        if (dueDate) {
            let currDate = MOMENT().startOf('day');
            let dueDateMoment = MOMENT(dueDate).utc().startOf('day');
            let dueDate_currDate_diff = dueDateMoment.diff(currDate, 'days');
            
            if (dueDate_currDate_diff === 0) {
                self.L.log(`isEligibleForCombinedNotificationExistenceCheck:: DueDate is current date (${dueDateMoment.format('YYYY-MM-DD')}), proceeding with normal flow`);
                return false;
            }
        }

        // Check customer eligibility
        if (cugCustIds.includes(custId) || Number(custId) % 100 < Number(percentageRollout)) {
            return true;
        }

        return false;
    }

    validateCombinedNotificationExists(record, cassandraBillsModel, isRealtime, isNonRuRealtime, callback) {
        let self = this;
        
        try {
            // Extract required parameters from record
            let customerid = _.get(record, 'customer_id', null);
            let rechargenumber = _.get(record, 'recharge_number', null);
            let service = _.get(record, 'service', null);
            let operator = _.get(record, 'operator', null);
            let notificationType = _.get(record, 'notificationType', null);
            let dueDate = _.get(record, 'due_date', null);
            
            // Validate required fields
            if (!customerid || !rechargenumber || !service || !operator) {
                self.L.log('validateCombinedNotificationExists:: Missing required fields, proceeding with normal flow');
                return callback(null, false);
            }
            
            // Check if customer is eligible for combined notification existence check
            if (!self.isEligibleForCombinedNotificationExistenceCheck(customerid, notificationType, isRealtime, isNonRuRealtime, dueDate)) {
                self.L.log(`validateCombinedNotificationExists:: Customer ${customerid} not eligible for combined notification validation, proceeding with normal flow`);
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:NORMAL_NOTIFICATION_VALIDATION',
                    'STATUS:CUSTOMER_NOT_ELIGIBLE',
                    'SOURCE:COMBINED_NOTIFICATION'
                ]);
                return callback(null, false);
            }
            
            // Prepare parameters for cassandra query
            let params = {
                customerid: String(customerid),
                rechargenumber: rechargenumber,
                service: service,
                operator: operator
            };
            
            self.L.log(`validateCombinedNotificationExists:: Checking combined notification for customer_id: ${customerid}, recharge_number: ${rechargenumber}, service: ${service}, operator: ${operator}`);
            
            // Check if combined notification key exists
            cassandraBillsModel.checkCombinedNotificationKey(params, (error, result) => {
                if (error) {
                    // Combined notification key exists - skip normal notification
                    self.L.log(`validateCombinedNotificationExists:: Combined notification exists, skipping normal notification for ${JSON.stringify(params)}`);
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:NORMAL_NOTIFICATION_VALIDATION',
                        'STATUS:SKIPPED_DUE_TO_COMBINED',
                        'SOURCE:COMBINED_NOTIFICATION'
                    ]);
                    return callback(null, true); // shouldSkip = true
                } else {
                    // No combined notification found - proceed with normal flow
                    self.L.log(`validateCombinedNotificationExists:: No combined notification found, proceeding with normal notification for ${JSON.stringify(params)}`);
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:NORMAL_NOTIFICATION_VALIDATION',
                        'STATUS:PROCEEDING_WITH_NORMAL',
                        'SOURCE:COMBINED_NOTIFICATION'
                    ]);
                    return callback(null, false); // shouldSkip = false
                }
            });
            
        } catch (error) {
            self.L.error('validateCombinedNotificationExists:: Error during validation:', error);
            // In case of error, proceed with normal flow (don't block notification)
            return callback(null, false);
        }
    }
}

export default CombinedNotificationUtil; 