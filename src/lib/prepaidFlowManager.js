"use strict";

import _ from 'lodash'
import VA<PERSON><PERSON><PERSON><PERSON> from 'validator'
import MOMENT from 'moment'
import BIL<PERSON> from '../models/bills';
import async from 'async';
import utility from '../lib';
import billSubscriber from '../services/billSubscriber';
import RecentBills from './recentBills';
class PrepaidFlowManager{
    constructor(options) {
        let self=this;
        this.L = options.L;
        this.config = options.config;
        this.dbInstance = options.dbInstance;
        this.bills = new BILLS(options);
        this.billSubscriber = new billSubscriber(options);
        this.recentBills = new RecentBills(options);
        this.prepaidElectricityNotificationThreshold = _.get(this.config, ['DYNAMIC_CONFIG', 'PREPAID_BILL_FETCH_CONFIG', 'MIN_BALANCE', 'AMOUNT'], 2000);
        this.allowedOperatorForPrepaidBillFetch = _.get(this.config, ['DYNAMIC_CONFIG', 'PREPAID_BILL_FETCH_CONFIG', 'PREPAID_ENABLED_OPERATORS', 'OPERATORS'], []);

        // Set interval for reinitialization
        setInterval(() => {
            this.initializeVariables();
        }, _.get(this.config, 'COMMON.INTERVAL_FOR_REINITIALIZE', 15) * 60 * 1000);
    }

    initializeVariables() {
        this.L.verbose("Reinitializing PrepaidFlowManager variables");
        this.allowedOperatorForPrepaidBillFetch = _.get(this.config, ['DYNAMIC_CONFIG', 'PREPAID_BILL_FETCH_CONFIG', 'PREPAID_ENABLED_OPERATORS', 'OPERATORS'], []);
    }

    handlePrepaidRecord(done, record) {
        let self = this,
        postpaidTable = self.getTableName(_.get(record, 'productId', null), _.get(record, 'operator', null)),
        prepaidTable = postpaidTable + '_prepaid';
        
        if (!record || (record.isPrepaid == 0)) {
            self.L.error("handlePrepaidRecord", "Record is not prepaid or invalid");
            return done(new Error("Record is not prepaid or invalid")); // Return early with error
        }

        if(!postpaidTable) {
            self.L.error(`handlePrepaidRecord :: tableName not found in OPERATOR_TABLE_REGISTRY.`);
            return done(new Error("Postpaid tableName not found in OPERATOR_TABLE_REGISTRY."));
        }

        let postpaidDbRecord = _.get(record, 'dbData', []);
        let postpaidRecordStatus = postpaidDbRecord.some(item => item.status !== 13 && item.status !== 7);

        let tasks = [];
    
        self.L.log(`prepaidFlowManager :: start processing for ${record.debugKey}, Flags :: isRecordExist: ${record.isRecordExist}, postpaidRecordStatus: ${postpaidRecordStatus}, recordFoundOfSameCustId: ${record.recordFoundOfSameCustId}, level_2_category: ${record.dwh_classId}`);

        // Task 1: Update postpaid table with status 13 if exist.
        if (record.isRecordExist && postpaidRecordStatus) {
            tasks.push((callback) => {
            self.updatePostpaidTableWithStatus13((err) => {
                    if (err) {
                        self.L.error("Error in updating " + postpaidTable + " table, error :: " + err);
                        utility._sendMetricsToDD(1, [
                            'REQUEST_TYPE:SMS_PARSING_POSTPAID',
                            `SERVICE:${_.get(record, 'category', null)}`,
                            'STATUS:SQL_UPDATE_ERROR',
                            'OPERATOR:' + record.operator,
                            'TYPE:STOP_BILLS_FETCH_FOR_PREPAID',
                            `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`
                        ]);
                        return callback(err);
                    }
                    self.L.log(`handlePrepaidRecord :: updatePostpaidTableWithStatus13 :: successfully updated record with status 13 ${record.debugKey}`);
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:SMS_PARSING_POSTPAID',
                        `SERVICE:${_.get(record, 'category', null)}`,
                        'STATUS:SQL_UPDATE_SUCCESS',
                        'OPERATOR:' + record.operator,
                        'TYPE:STOP_BILLS_FETCH_FOR_PREPAID',
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`
                    ]);
                    callback(null);
                }, postpaidTable, record);
            });
        }
    
        // Task 2: Insert into prepaid bills table (always execute this)
        tasks.push((callback) => {
            self.createSmsParsingPrepaidBill((err) => {
                if (err) {
                    self.L.error("Error in inserting in " + prepaidTable + " table, error :: " + err);
                    return callback(err);
                }
                self.L.log(`handlePrepaidRecord :: createSmsParsingPrepaidBill :: function successfully executed ${record.debugKey}`);
                callback(null);
            }, prepaidTable, record);
        });
    
        async.parallel(tasks, (err) => {
            if (err) {
                return done(err);
            }
            done(null, record);
        });
    }    

    updatePostpaidTableWithStatus13(cb, tableName, record) {
        const self = this;
        const operator = _.get(record, 'operator');
        const service = _.get(record, 'service', _.get(record, 'category', null));
        const rechargeNumber = _.get(record, 'rechargeNumber');
        
        const query = `UPDATE ${tableName} 
        SET status = 13, reason = 'PREPAID_IDENTIFIED' 
        WHERE recharge_number = ? 
        AND service = ? 
        AND operator = ?`;
        
        const queryParams = [
            rechargeNumber,
            _.toLower(service),
            _.toLower(operator)
        ];
    
        const latencyStart = new Date().getTime();
        self.L.log(`updatePostpaidTableWithStatus13 :: going to update status to 13 for ${record.debugKey}, queryParams: ${queryParams}`);
        self.dbInstance.exec((err, data) => {
            utility._sendLatencyToDD(latencyStart, { 
                'REQUEST_TYPE': 'DB_QUERY',
                'TYPE': 'setPostpaidStatus13WhenPrepaid',
                'STATUS': 'SUCCESS'
            });
    
            if (err || !data) {
                utility._sendMetricsToDD(1, [
                    "REQUEST_TYPE:DB_QUERY", 
                    'STATUS:ERROR', 
                    `TYPE:setPostpaidStatus13WhenPrepaid`
                ]);
                self.L.critical('updatePostpaidTableWithStatus13::', 'Error occurred while updating data in DB:', err);
            } else {
                self.L.log(`updatePostpaidTableWithStatus13:: Successfully updated ${data.affectedRows} rows.`);
            }
    
            return cb(err);
        }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
    }

    createSmsParsingPrepaidBill(cb, tableName, record) {
        let self = this,
            operator = record.operator,
            service_id = 0;
        let postpaidDbExistingData = _.get(record, 'dbData', []);
    
        if (record.service_id) {
            service_id = record.service_id;
        }
    
        if (!record.service) record.service = _.get(self.config, ['CVR_DATA', record.productId, 'service'], '');
    
        let checkQuery = `SELECT * FROM ${tableName} WHERE operator = ? AND service = ? AND recharge_number = ?`;
        let checkParams = [operator, record.service, record.rechargeNumber];

        self.dbInstance.exec(function(err, existingData) {
            if (err) {
                return cb(err);
            }
    
            if (existingData && existingData.length > 0) {
                self.L.log(`createSmsParsingPrepaidBill :: record exist in prepaid table, updating details ${record.debugKey}`);
                utility._sendMetricsToDD(1, [
                    "REQUEST_TYPE:SMS_PARSING_POSTPAID", 
                    `SERVICE:${_.get(record, 'category', null)}`, 
                    'STATUS:TRAFFIC', 
                    'TYPE:UPDATE_BILLS_PREPAID',
                    "OPERATOR:" + record.operator,
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                    `APP_VERSION:${_.get(record,'appVersion', null)}`
                ]);

                let recordOfSameCustIdPrepaid = existingData.filter((dataValue) => dataValue.customer_id == record.customerId).length > 0 ? true : false;
                _.set(record, 'recordFoundOfSameCustIdPrepaid', recordOfSameCustIdPrepaid);
                _.set(record, 'isPrepaidDataFound', true);

                async.eachSeries(existingData, (item, callback) => {
                    let clonedRecord;
                    if (record.billsData) {
                        clonedRecord = _.cloneDeep(record.billsData);
                    } else {
                        clonedRecord = _.cloneDeep(record);
                    }
                    clonedRecord.customerId = _.get(item, 'customer_id', null);
                    clonedRecord.customerMobile = _.get(item, 'customer_mobile', null);
                    clonedRecord.customerEmail = _.get(item, 'customer_email', null);
                    clonedRecord.customerOtherInfo = self.custInfoMerged(clonedRecord, item);
                    
                    self.bills.updateBillForSameRechargeNumPostpaid((err) => {
                        _.set(self.timestamps,'RUupdatesDbTime',new Date().getTime());
                        if (err) {
                            self.L.error("Error in updating in " + tableName + " table, error :: " + err + " for record " + record.debugKey + " and customer_id " + _.get(item, 'customer_id', null));
                            utility._sendMetricsToDD(1, [
                                "REQUEST_TYPE:SMS_PARSING_POSTPAID", 
                                `SERVICE:${_.get(record, 'category', null)}`, 
                                'STATUS:ERROR', 
                                "TYPE:SQL_UPDATE_BILLS_PREPAID",
                                "OPERATOR:" + record.operator,
                                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`
                            ]);
                            return callback(err);
                        }
                        
                        self.L.log(`createSmsParsingPrepaidBill :: updateBillForSameRechargeNumPostpaid :: successfully updated record in prepaid table for ${record.debugKey}, customerId: ${_.get(item, 'customer_id', null)}`);
                        utility._sendMetricsToDD(1, [
                            "REQUEST_TYPE:SMS_PARSING_POSTPAID", 
                            `SERVICE:${_.get(record, 'category', null)}`, 
                            'STATUS:SUCCESS', 
                            'TYPE:SQL_UPDATE_BILLS_PREPAID', 
                            "OPERATOR:" + record.operator,
                            `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`
                        ]);
                        
                        callback(null);
                    }, tableName, clonedRecord);
                }, (err) => {
                    if (err) {
                        return cb(err);
                    }
                    cb(null);
                });
                            
            } else {
                _.set(record, 'isPrepaidDataFound', false);
                let completedCount = 0;
                let hasErrorOccurred = false;
                if (_.get(record, 'isRecordExist', false) && postpaidDbExistingData.length > 0) {
                    self.L.log(`createSmsParsingPrepaidBill :: record does not exist in prepaid table, and record exist in postpaid table, creating entry in prepaid bills table ${tableName}, ${record.debugKey}`);
                    
                    async.eachSeries(postpaidDbExistingData, (item, callback) => {
                        if (item.status == 13 || item.status == 7) {
                            self.L.log(`createSmsParsingPrepaidBill :: record with status 13 or 7 ${record.debugKey}, db id ${_.get(item, 'id', null)}`);
                            completedCount++;
                            return callback();
                        }
                
                        let clonedRecord;
                        if (record.billsData) {
                            clonedRecord = _.cloneDeep(record.billsData);
                        } else {
                            clonedRecord = _.cloneDeep(record);
                        }
                        clonedRecord.customerId = _.get(item, 'customer_id', null);
                        clonedRecord.customerMobile = _.get(item, 'customer_mobile', null);
                        clonedRecord.customerEmail = _.get(item, 'customer_email', null);
                        clonedRecord.gateway = _.get(item, 'gateway', null);
                        clonedRecord.productId = _.get(item, 'product_id', null);
                        clonedRecord.operator = _.get(item, 'operator', null);
                        clonedRecord.service = _.get(item, 'service', null);
                        clonedRecord.paytype = _.get(item, 'paytype', null);
                        clonedRecord.circle = _.get(item, 'circle', null);
                        clonedRecord.paymentChannel = _.get(item, 'payment_channel', null);
                        clonedRecord.customerOtherInfo = self.custInfoMerged(clonedRecord, item);
                        self.bills.createSmsParsingPostpaidBill((err) => {
                            if (err) {
                                self.L.error("Error in inserting in " + tableName + " table, error :: " + err);
                                utility._sendMetricsToDD(1, [
                                    "REQUEST_TYPE:SMS_PARSING_POSTPAID", 
                                    `SERVICE:${_.get(record, 'category', null)}`, 
                                    'STATUS:ERROR', 
                                    'TYPE:SQL_INSERT_BILLS_PREPAID', 
                                    "OPERATOR:" + record.operator,
                                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`
                                ]);
                                if (!hasErrorOccurred) {
                                    hasErrorOccurred = true;
                                    return callback(err);
                                }
                            } else {
                                self.L.log(`handlePrepaidRecord :: createSmsParsingPrepaidBill :: successfully created record in prepaid table ${tableName}`);
                                utility._sendMetricsToDD(1, [
                                    "REQUEST_TYPE:SMS_PARSING_POSTPAID", 
                                    `SERVICE:${_.get(record, 'category', null)}`, 
                                    'STATUS:SUCCESS', 
                                    'TYPE:SQL_INSERT_BILLS_PREPAID', 
                                    "OPERATOR:" + record.operator,
                                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`
                                ]);
                                completedCount++;
                                callback();
                            }
                        }, tableName, clonedRecord);
                    }, (err) => {
                        if (err) {
                            cb(err);
                        } else {
                            cb(null);
                        }
                    });
                } else {
                    self.L.log(`createSmsParsingPrepaidBill :: record does not exist in prepaid table, and record does not exist in postpaid table ${record.debugKey}`);
                    return cb(null);
                }
            }
        }, 'DIGITAL_REMINDER_MASTER', checkQuery, checkParams);
    }

    getTableName(catalogProductID, operator) {
        let
            self = this,
            tableName = _.get(self.config, ['OPERATOR_TABLE_REGISTRY', catalogProductID], null) ||
                _.get(self.config, ['OPERATOR_TABLE_REGISTRY', operator], null);

        return tableName;
    }

    getOriginOfPayloadCurrentlyBeingProcessed(record) {
        let self = this;
        if(self.smsParsingBillsDwhRealtime) {
            return "SMS_PARSING_DWH_REALTIME"
        }
        return _.get(record,'isRuSmsParsing', false)==true? 'SMS_PARSING_REALTIME':'SMS_PARSING_DWH';
    }

    custInfoMerged(clonedRecord, item) {
        let self = this;
        let dbCustomerOtherInfo = {};
        let recordCustomerOtherInfo = {};
        let filteredCustInfo = {};
        try {
            dbCustomerOtherInfo = JSON.parse(_.get(item , 'customerOtherInfo', {}));
            recordCustomerOtherInfo = JSON.parse(_.get(clonedRecord, 'customerOtherInfo', {}));

        } catch (error) {
            self.L.error(`custInfoMerge :: Error while parsing customerOtherInfo for ${clonedRecord.debugKey}, customerId: ${_.get(item, 'customer_id', null)}`);
        }
        let mergedCustomerOtherInfo = _.merge(dbCustomerOtherInfo, recordCustomerOtherInfo);
        filteredCustInfo = JSON.stringify(mergedCustomerOtherInfo, function (key, value) {
            if (key && typeof (value) === 'string')
                return value.replace(/[?']/g, "");
            else
                return value;
        })
        return filteredCustInfo;
    }

    composePrepaidTableName(record) {
        let self = this;
        let postpaidTableName = self.recentBills.getPostpaidTableNameFromConfig(record);
        let prepaidTableName = postpaidTableName + '_prepaid';
        return prepaidTableName;
    }

    async updatePostpaidTableWithStatus13AndReasonPrepaid(tableName, record) {
        const self = this;
        const operator = _.get(record, 'operator');
        const service = _.get(record, 'service');
        const rechargeNumber = _.get(record, 'rechargeNumber');
        
        const query = `UPDATE ${tableName} SET status = 13, reason = 'PREPAID_IDENTIFIED' WHERE recharge_number = ? AND service = ? AND operator = ?`;
        const queryParams = [
          rechargeNumber,
          _.toLower(service),
          _.toLower(operator)
        ];
    
        const latencyStart = new Date().getTime();
        self.L.log(`updatePostpaidTableWithStatus13AndReasonPrepaid :: going to update status to 13 for ${record.debugKey}, queryParams: ${queryParams}`);
        
        return new Promise((resolve, reject) => {
          self.dbInstance.exec((err, data) => {
            if (err || !data) {
              utility._sendMetricsToDD(1, [
                "REQUEST_TYPE:DB_QUERY", 
                'STATUS:ERROR', 
                `TYPE:setPostpaidStatus13WhenPrepaid`
              ]);
              self.L.critical('updatePostpaidTableWithStatus13AndReasonPrepaid::', 'Error occurred while updating data in DB:', err);
              return reject(err);
            } else {
              utility._sendLatencyToDD(latencyStart, { 
                'REQUEST_TYPE': 'DB_QUERY',
                'TYPE': 'setPostpaidStatus13WhenPrepaid',
                'STATUS': 'SUCCESS'
              });
              self.L.log(`updatePostpaidTableWithStatus13AndReasonPrepaid:: Successfully updated ${data.affectedRows} rows.`);
              return resolve(data);
            }
          }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
        });
      }

    isPrepaidFlowAllowed(record) {
        return  _.get(record, 'isPrepaid', "0") == "1" && 
               this.allowedOperatorForPrepaidBillFetch.length > 0 &&
               this.allowedOperatorForPrepaidBillFetch.includes(_.get(record, 'operator', null)) &&
               (_.get(record, 'is_automatic', 0) || 0) == 0;
    }
}

export default PrepaidFlowManager;
