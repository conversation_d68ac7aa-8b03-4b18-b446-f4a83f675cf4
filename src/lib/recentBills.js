"use strict";

import _ from 'lodash'
import VA<PERSON>ID<PERSON>OR from 'validator'
import MOMENT from 'moment'
import utility from '../lib'
import ReminderFlowManager from '../lib/reminderFlowManager'
import DigitalCatalog from '../lib/digitalCatalog'

class RecentBillsLibrary {

    constructor(options) {
        let self=this;
        this.L = options.L,
        this.config = options.config;
        this.reminderFlowManager = new ReminderFlowManager(options);


        self.includedOperatorList = _.get(self.config, ['DYNAMIC_CONFIG', 'OPERATOR_MONTHLY_PAYMENT', 'COMMON', 'INCLUDE_OPERATOR'], 'rent payment');
        console.log("includedOperatorList ", self.includedOperatorList)
        self.includedOperator = self.includedOperatorList.split(',').map((e) => e.trim());
        self.lowBalancePrepaidElectricityAllowedOperators = _.get(this.config, ['DYNAMIC_CONFIG', 'PREPAID_BILL_FETCH_CONFIG', 'PREPAID_ENABLED_OPERATORS', 'OPERATORS'], []);
    }


    /** 
     * @param {} recentBillsOperators contains config data to check, if bill is remindable or not 
     * @param {} billTable contains config bill_remindable table mapping
     */

    _initRecentBillSpecificData(billTable, recentBillsOperators) {
        let recentBillSpecificData = {};
        // this.L.log(`_initRecentBillSpecificData, billTable - ${billTable?JSON.stringify(billTable):''}`);
        // this.L.log(`_initRecentBillSpecificData, recentBillsOperators - ${recentBillsOperators?JSON.stringify(recentBillsOperators):''}`);
        _.extend(recentBillSpecificData, billTable, recentBillsOperators);
        // this.L.log(`_initRecentBillSpecificData, recentBillSpecificData - ${JSON.stringify(recentBillSpecificData)}`);
        return recentBillSpecificData;
    }

    /**
     * 
     * @param {object} rechargeData recharge data received from kafka topic
     * @param {object} recent_bills_operators recent bills operator object list
     */

    _isRemindable(rechargeData, recent_bills_operators, excluded) {
        let operator = _.get(rechargeData, 'productInfo_operator', ''),
            paytype = _.get(rechargeData, 'productInfo_paytype', ''),
            customer_id = _.get(rechargeData, 'customerInfo_customer_id', ''),
            remindable_operator = _.get(recent_bills_operators, operator, null),
            excluded_ids = _.get(excluded, 'CUSTOMER_IDS', []),
            excluded_paytype = _.get(excluded, 'PAYTYPE', []),
            paytypes = [];

        if (excluded_ids.indexOf(customer_id) > -1) {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:NON_REMINDABLE', `TYPE:CUST_ID`]);
            return false;
        }
        if(excluded_paytype.indexOf(paytype) > -1){
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:NON_REMINDABLE', `TYPE:PAYTYPE`]);
            return false;
        }
        if (remindable_operator) {
            paytypes = _.get(remindable_operator, 'paytypes', ['postpaid']);
            return paytypes.indexOf(paytype) < 0 ? false : true;
        } else {
            let supported_paytype = ['postpaid', 'fee payment', 'insurance', 'loan'];
            return supported_paytype.indexOf(paytype) < 0 ? false : true;
        }
    }

    /**
     * * @param {object} rechargeData contains recharges data
     * * @param {object} recent_bills_operators billReminder operators details
     * * @param {object} userData contains user data
     */

    prepareRecentBillData(rechargeData, recent_bills_operators, userData, billSubscriberObj) {
        let self = this,
            billsObjs = [],
            isPrepaid = self.getPrepaidFlagValue(rechargeData),
            operator = _.get(rechargeData, 'productInfo_operator', ''),
            service = _.get(rechargeData, 'productInfo_service', ''),
            nextBillFetchDateObj = self.decideNextBillFetchDate(rechargeData, recent_bills_operators, billSubscriberObj),
            amount = _.get(rechargeData, 'userData_amount', 0),
            customerId = _.get(rechargeData, 'customerInfo_customer_id', ''),
            productId = _.get(rechargeData, 'catalogProductID', 0),
            serviceId = _.get(recent_bills_operators, [operator, 'serviceId'], 0),
            metaData  = _.get(rechargeData,'metaData',"{}"),
            dueDate = self.decideNextDueDate(recent_bills_operators, operator, amount),
            retryCount = 0,
            reason = null,
            validity = null,
            pns      = 0,
            extra = {},
            bankAttributes,bankName, cardNetwork,
            payType = _.get(rechargeData, 'productInfo_paytype', ''),
            billDate = utility.getFilteredDate(_.get(rechargeData, 'userInfoResponse.billDate', null));
            billDate = billDate.value ? MOMENT(billDate.value).format('YYYY-MM-DD') : MOMENT().format('YYYY-MM-DD');
            extra.customer_type = _.get(rechargeData, 'customerInfo_customer_type', null);
            extra.last_paid_amount = amount
            extra.order_id = _.get(rechargeData, 'orderInfo_order_id', null);
        try {
            if((_.get(rechargeData,'allowedPrepaidDthOperator',[]).indexOf(_.toLower(operator))>-1)){
                amount = 0;
            }else if (serviceId == _.get(self.config, 'COMMON.PREPAID_SERVICE_ID', 4)) {
                amount = (typeof (amount) === 'string' ? VALIDATOR.toFloat(amount) : amount);
            } else {
                amount = -(typeof (amount) === 'string' ? VALIDATOR.toFloat(amount) : amount);
            }

            customerId = typeof (customerId) === 'string' ? VALIDATOR.toInt(customerId) : customerId;
            productId = typeof (productId) === 'string' ? VALIDATOR.toInt(productId) : productId;
        } catch (error) {
            self.L.error("RecentBillsLibrary", "prepareRecentBillData::", "Fail to validate recent bills data ", error);
        }
        billsObjs = {
            customerId: customerId,
            rechargeNumber: _.get(rechargeData, 'userData_recharge_number', ''),
            productId: productId,
            operator: _.get(rechargeData, 'productInfo_operator', ''),
            gateway: _.get(rechargeData, 'currentGw', ''),
            nextBillFetchDate: _.get(nextBillFetchDateObj, 'nextBillFetchDate', null),
            coolOff: _.get(nextBillFetchDateObj, 'coolOff', false),
            service: _.get(rechargeData, 'productInfo_service', ''),
            paytype: payType,
            circle: _.get(rechargeData, 'productInfo_circle', ''),
            customerMobile: _.get(rechargeData, 'customerInfo_customer_phone'),
            customerEmail: _.get(rechargeData, 'customerInfo_customer_email', ''),
            paymentChannel: _.get(rechargeData, 'customerInfo_channel_id', ''),
            amount: amount,
            user_data: JSON.stringify(userData),
            paymentDate: MOMENT().format('YYYY-MM-DD HH:mm:ss'),
            service_id: serviceId,
            dueDate: dueDate,
            retryCount: retryCount,
            reason: reason,
            extra: JSON.stringify(extra),
            billDate: billDate,
            updateAllCustIdRecords : payType === 'credit card'? false : _.get(recent_bills_operators, [operator, 'updateAllCustIdRecords'], true),
            notificationStatus: _.get(self.config, ['COMMON', 'notification_status', 'ENABLED'], 1),
            validity: validity,
            pns: pns,
            orderId: _.get(rechargeData, 'orderInfo_order_id', ''),
        };

        if (billsObjs.service.toLowerCase() === 'electricity' && this.lowBalancePrepaidElectricityAllowedOperators.includes(billsObjs.operator)){
            billsObjs.isValidPrepaidElectricityOperator = true;
            billsObjs.prepaidAmount = billsObjs.amount;
            const now = MOMENT();
            billsObjs.prePaidDueDate = null;
            const nfbDaysOffset = _.get(self.config, ['DYNAMIC_CONFIG', 'PREPAID_BILL_FETCH_CONFIG', 'NBFD_CYCLE_BILLPAID', billsObjs.operator], 20);
            billsObjs.prePaidNextBillFetchDate = now.add(nfbDaysOffset, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss');
            billsObjs.coolOff = false;
            self.updateIsPrepaidFlag(billsObjs, isPrepaid);
            self.L.info(`prepareRecentBillData, billsObjs - ${JSON.stringify(billsObjs)}`);
        }

        if (_.toLower(billsObjs.service) == 'financial services') {
            //RN1: MCN, RN2: Token, RN3: CIN, RN4: TIN, metadata.PAR:parId

            let referenceId = null,
                parId = null;

             try {
                if (_.isEmpty(_.get(rechargeData, 'userData_recharge_number_3'))) {
                    if (!_.isEmpty(_.get(rechargeData, 'userData_recharge_number_2')) && _.get(rechargeData, 'userData_recharge_number_2').startsWith('CIN_')) {
                        referenceId = _.get(rechargeData, 'userData_recharge_number_2').substring('CIN_'.length);
                    } else {
                        let operatorRecentData = _.get(rechargeData, 'recentData', {});
                        operatorRecentData = typeof operatorRecentData === 'string' ? JSON.parse(operatorRecentData) : operatorRecentData;
                        referenceId = _.get(operatorRecentData, 'creditCardId');
                    }
                } else {
                    referenceId = _.get(rechargeData, 'userData_recharge_number_3');
                }
            } catch (error) {
                self.L.error("prepareRecentBillData::issue in getting CIN", error);
            }
            try {
                let metaData = _.get(rechargeData, 'metaData', '');
                metaData = typeof metaData === 'string' ? JSON.parse(metaData) : metaData;
                parId = _.get(metaData, 'panUniqueReference', '');
            } catch(error) {
                self.L.error("prepareRecentBillData::issue in getting PAR", error);
            }
            const TIN = _.get(rechargeData, 'userData_recharge_number_4', '');
            try{
                bankAttributes = JSON.parse(_.get(self.config, ['CVR_DATA', productId, 'attributes'] , '{}'))
                bankName = _.toLower(_.get(bankAttributes, ['bank_code'] , ''));
                cardNetwork = _.toLower(_.get(bankAttributes, ['card_network'] , ''));
            } catch (err) {
                self.L.error("RecentBillsLibrary", "prepareRecentBillData::", "Trouble reading attributes data ", err);
            }
            
            if(billsObjs.rechargeNumber && billsObjs.rechargeNumber.indexOf("X") == 0) {
                _.set(billsObjs, 'newFormatMCN', true);  // starts with X instead of number
            }
            _.set(billsObjs, 'isCreditCardOperator', true);
            _.set(billsObjs, 'bankName', bankName);
            _.set(billsObjs, 'cardNetwork', cardNetwork);
            if(!referenceId && parId && parId != '') {
                _.set(billsObjs, 'parId', parId);
                _.set(billsObjs, 'tin', TIN);
                _.set(billsObjs, 'tokenisedCreditCard', true);
                _.set(billsObjs, 'newFormatMCN', false);
                _.set(billsObjs, 'referenceId', parId); // referenceId is part of sql table's unique constraint
            } else {
                _.set(billsObjs, 'referenceId', referenceId);
            }

        }
        return billsObjs;
    }

    /**
     * 
     * @param {*} rechargeData  contains recharges data object
     * @param {*} billDelay     contains delay for NBFD
     */
    decideNextBillFetchDate(rechargeData, recent_bills_operators, billSubscriberObj, currentAutomaticStatus=null) {

        let
            self = this,
            operator = _.get(rechargeData, 'productInfo_operator', null);
            console.log("--------operator------------", operator);
        if(_.get(rechargeData , 'productInfo_service',_.get(rechargeData,'service',null))== "financial services"){
            if(operator)operator = _.toLower(operator)
        }
        
        let
            nextBillFetchDate = null,
            billDelay = self.getConfigByKeysRecentBills({
                name: recent_bills_operators,
                node: operator,
                keyname: 'firstBillDelay',
                default: 5
            }, {
                currentAutomaticStatus: currentAutomaticStatus,
                prefix: 'AUTOPAY_',
                prefixToKey: 'keyname'
            }),

            coolOff = false,

            billDelayTimeUnit = self.getConfigByKeysRecentBills({
                name: recent_bills_operators,
                node: operator,
                keyname: 'BILL_DELAY_TIME_UNIT',
                default: 'days'
            }, {
                currentAutomaticStatus: currentAutomaticStatus,
                prefix: 'AUTOPAY_',
                prefixToKey: 'keyname'
            }),

            mandCoolOffFlag   = self.getConfigByKeysRecentBills({
                name: recent_bills_operators,
                node: operator,
                keyname: 'mandatoryUpdateNbfd',
                default: false
            }, {
                currentAutomaticStatus: currentAutomaticStatus,
                prefix: 'AUTOPAY_',
                prefixToKey: 'keyname'
            })

        let nbfdFromGateway = utility.getFilteredDate(_.get(rechargeData, 'userInfoResponse.nextBillFetchDate', null));

        
        
        if(mandCoolOffFlag) {
            nextBillFetchDate =  billDelay < 0 ?  MOMENT().add(Math.abs(Number(billDelay)), 'months').format('YYYY-MM-DD HH:mm:ss'): MOMENT().add(billDelay, billDelayTimeUnit).format('YYYY-MM-DD HH:mm:ss');
            coolOff = true;
        } else if (nbfdFromGateway && nbfdFromGateway.value) {
            nextBillFetchDate = nbfdFromGateway.value;

        } else if (self.getConfigByKeysRecentBills({
            name: recent_bills_operators,
            node: operator,
            keyname: 'CONSTANT_NBFD',
            default:null
        },{
            currentAutomaticStatus: currentAutomaticStatus,
            prefix:'AUTOPAY_',
            prefixToKey: 'keyname'
        })){

            let date = self.getConfigByKeysRecentBills({
                name: recent_bills_operators,
                node: operator,
                keyname: 'CONSTANT_NBFD',
                default:null
            },{
                currentAutomaticStatus: currentAutomaticStatus,
                prefix:'AUTOPAY_',
                prefixToKey: 'keyname'
            })

            nextBillFetchDate = (MOMENT() >= MOMENT().date(date).startOf('day') ? MOMENT().add(1, 'months').date(date).startOf('day').format('YYYY-MM-DD HH:mm:ss') : MOMENT().date(date).startOf('day').format('YYYY-MM-DD HH:mm:ss'));
        }
        if (!nextBillFetchDate) {
            console.log("------Out Here-------");
            let billDate = utility.getFilteredDate(_.get(rechargeData, 'userInfoResponse.billDate', _.get(rechargeData , 'billDate',null) && MOMENT(_.get(rechargeData , 'billDate',null)).format('YYYY-MM-DD')));
            billDate = billDate.value;

            let dueDate = utility.getFilteredDate(_.get(rechargeData, 'userInfoResponse.dueDate', _.get(rechargeData , 'billDueDate',null) && MOMENT(_.get(rechargeData , 'billDueDate',null)).format('YYYY-MM-DD')));
            dueDate = dueDate.value;

            console.log("------billDate , dueDate --------------", billDate , "  ", dueDate);

            let billDateBasedGateways = _.get(self.config, 'SUBSCRIBER_CONFIG.BILL_FETCH_BASED_ON_BILL_DATE_GATEWAYS', []);

            console.log("-----billSubscriberObj.getFirstBillFetchInterval(operator)---------------", billSubscriberObj.getFirstBillFetchInterval(operator));

            if (billDate || dueDate) {
                let dateToBeUsedForNbfd = billDateBasedGateways.indexOf(operator) > -1 && billDate ? billDate : dueDate;
                console.log("-------dateToBeUsedForNbfd--------", dateToBeUsedForNbfd);
                if(dateToBeUsedForNbfd){
                    dateToBeUsedForNbfd = billSubscriberObj.getFirstBillFetchInterval(operator, currentAutomaticStatus) < 0 ? dateToBeUsedForNbfd.add(Math.abs(Number(billSubscriberObj.getFirstBillFetchInterval(operator, currentAutomaticStatus))), 'months') : dateToBeUsedForNbfd.add(billSubscriberObj.getFirstBillFetchInterval(operator,currentAutomaticStatus), billDelayTimeUnit);
                }
                else dateToBeUsedForNbfd = null;
                if (dateToBeUsedForNbfd && dateToBeUsedForNbfd.diff(MOMENT(), billDelayTimeUnit) > 0) {
                    nextBillFetchDate = dateToBeUsedForNbfd.format('YYYY-MM-DD HH:mm:ss');
                }
            }
        }

        if (!nextBillFetchDate) {
            nextBillFetchDate = billDelay < 0 ? MOMENT().add(Math.abs(Number(billDelay)), 'months').format('YYYY-MM-DD HH:mm:ss'):  MOMENT().add(billDelay, billDelayTimeUnit).format('YYYY-MM-DD HH:mm:ss');
            coolOff = true;
        }

        return {
            nextBillFetchDate: nextBillFetchDate,
            coolOff: coolOff
        };
    }

    /**
     * 
     * @param {*} rechargeData  contains recharges data object
     */
    getUserData(rechargeData) {
        let
            userData = {};

        [
            'recharge_number_2',
            'recharge_number_3',
            'recharge_number_4',
            'recharge_number_5',
            'recharge_number_6',
            'recharge_number_7',
            'recharge_number_8',
        ].forEach((key) => {
            if (_.get(rechargeData, 'userData_' + key, null) && _.get(rechargeData, 'userData_' + key, null) !== "") {
                _.set(userData, key, _.get(rechargeData, 'userData_' + key));
            }
        });

        return userData;
    }

    /**
     * library function is used to check validity of data
     * @param {*} rechargeData  contains recharges data object
     */

    isCreateBillDataValid(rechargeData) {
        let customer_id = _.get(rechargeData, "customerInfo_customer_id", ''),
            product_id = _.get(rechargeData, "catalogProductID", ''),
            rechargeNumber = _.get(rechargeData, 'userData_recharge_number', null),
            operator = _.get(rechargeData, 'productInfo_operator', null);

        try {
            customer_id = typeof (customer_id) === 'string' ? VALIDATOR.toInt(customer_id) : customer_id;
            product_id = typeof (product_id) === 'string' ? VALIDATOR.toInt(product_id) : product_id;
        } catch (err) {
            self.L.error("isCreateBillDataValid", "Failed to validate recharges data", err);
            return false;
        }

        if (isNaN(customer_id) || isNaN(product_id) || !rechargeNumber || !operator) {
            return false;
        } else {
            return true;
        }
    }

    decideNextDueDate(recent_bills_operators, operator, amount) {
        let self=this;
        var daysToNextDueDate = _.get(recent_bills_operators, [operator, 'daysToNextDueDate'], null);
        let deducedDueDate = null;
        /*
        if (operator == "rent payment") {
            let rentPaymentObj = this.reminderFlowManager.getFlowInstance({operator : operator, paytype : 'postpaid'});
            deducedDueDate = rentPaymentObj.getHeauristicDueDateBasedOnTxn(amount);
        }else 
        */

        if (self.includedOperator.includes(operator)) {

            let monthlyPaymentObj = this.reminderFlowManager.getFlowInstance({operator : operator, paytype : 'postpaid'});
            deducedDueDate = monthlyPaymentObj.getHeauristicDueDateBasedOnTxn(amount);
        } else if (daysToNextDueDate) {
            deducedDueDate = MOMENT().add(daysToNextDueDate, 'days').format('YYYY-MM-DD');
        }
        return deducedDueDate;
    }

     _calculateCurrentExpiryDateForPlan(planRecords) {
        console.log("plasdas _calculateCurrentExpiryDateForPlan ",planRecords)
        if (!planRecords.length) {
            return null;
        }
        let record = planRecords[0];
        let validity_expiry_date = MOMENT(record.due_date);
        if (validity_expiry_date < MOMENT()) {
            return null
        }
        return validity_expiry_date.format('YYYY-MM-DD');;
    }

    _getValidityExpiryDate(validity, order_date, cur_validity){

        if(!validity) return null;
        order_date = cur_validity ? cur_validity : order_date;
        if(!validity) {
            return null
        }
        validity    = validity.trim();
        let validityArray = validity.split(' '),
            number      = VALIDATOR.toInt(validityArray[0]),
            hasExistingRecharge = (cur_validity!=null);
        if(isNaN(number)) return null;

        if(validityArray.length == 1){    // eg. by default number is in days
            let validity = hasExistingRecharge ? number : (number-1);
            return MOMENT(order_date,'YYYY-MM-DD').add(validity,'d').format('YYYY-MM-DD');
        }
        if ( (validity.toLowerCase().indexOf('day') > -1) || (validity.toLowerCase().indexOf('days') > -1) || (validity.toLowerCase().indexOf('night') > -1) || (validity.toLowerCase().indexOf('nights') > -1) ) {
            let validity = hasExistingRecharge ? number : (number-1);
            return MOMENT(order_date,'YYYY-MM-DD').add(validity,'d').format('YYYY-MM-DD');
        }
        if ( (validity.toLowerCase().indexOf('month') > -1) || (validity.toLowerCase().indexOf('months') > -1) ) {
            let due_date = MOMENT(order_date,'YYYY-MM-DD').add(number,'M');
            if(!hasExistingRecharge) due_date = due_date.subtract(1, 'd');
            due_date = due_date.format('YYYY-MM-DD');
            return due_date;
        }
        if ( (validity.toLowerCase().indexOf('year') > -1) || (validity.toLowerCase().indexOf('years') > -1) ) {
            let due_date = MOMENT(order_date,'YYYY-MM-DD').add(number,'y');
            if(!hasExistingRecharge) due_date = due_date.subtract(1, 'd').format('YYYY-MM-DD');
            due_date = due_date.format('YYYY-MM-DD');
            return due_date;
        }
        return null;
    }

    async calculate_due_date_for_ott(done, recentBillsData){
        let self=this,
            cur_validity_expiry_date = self._calculateCurrentExpiryDateForPlan(recentBillsData.transactionHistory);
        if(recentBillsData.validity) {
            recentBillsData.dueDate  = self._getValidityExpiryDate(recentBillsData.validity, recentBillsData.billDate, cur_validity_expiry_date);
            return done();
        }
        self.digitalCatalog.get_validity_from_rps(function(err, response){
            if(err || !response.productList || !response.productList.length) {
                console.log("Getting Error or no product from get_validity_from_rps", err, response);
                return done("no validity from rps");
            }
            let productList = response.productList[0];
            recentBillsData.validity  = productList.validity;
            recentBillsData.dueDate  = self._getValidityExpiryDate(recentBillsData.validity, recentBillsData.billDate, cur_validity_expiry_date);
            done();
        }, recentBillsData.operator, recentBillsData.amount, recentBillsData.rechargeNumber, recentBillsData.productId);
    }
    getConfigByKeysRecentBills(configObject, extraVariableObject){
        let self=this,
        currentAutomaticStatus = _.get(extraVariableObject, 'currentAutomaticStatus', null),
        prefix = _.get(extraVariableObject, 'prefix', ''),
        prefixToKey = _.get(extraVariableObject, 'prefixToKey', null);

        let name = _.get(configObject, 'name', null),
        node = _.get(configObject, 'node', null),
        nodeWithPrefix = prefixToKey=='node'? prefix+node : node,
        keyname = _.get(configObject, 'keyname', null),
        keynameWithPrefix = prefixToKey=='keyname'? prefix+keyname : keyname,
        defaultValue = _.get(configObject, 'default', null);



        if(currentAutomaticStatus){
            return _.get(name, [nodeWithPrefix,keynameWithPrefix], 
                _.get(name, [node,keyname],defaultValue))
        }else{
            return _.get(name, [node,keyname],defaultValue)
        }
    }

    getPostpaidTableNameFromConfig(record){
        let self = this;
        let postpaidTable = _.get(self.config, ['OPERATOR_TABLE_REGISTRY', record.productId], null) ||
                        _.get(self.config, ['OPERATOR_TABLE_REGISTRY', record.operator], null) ||
                        _.get(self.config, ['OPERATOR_TABLE_REGISTRY', record.service], null);
        return postpaidTable;
    }

    updateIsPrepaidFlag(record, isPrepaid) {
        if (record.extra === null) {
            record.extra = {};
        } else if (typeof record.extra === 'string') {
            try {
                record.extra = JSON.parse(record.extra);
            } catch (e) {
                self.L.error(`updateIsPrepaidFlag :: Error parsing extra field: ${record.extra}, error: ${e}`);
                record.extra = {};
            }
        }
        _.set(record, 'extra.isPrepaid', isPrepaid);
        record.extra = JSON.stringify(record.extra);
    }

    checkIfIsPrepaidIsSet(record) {
        if (record.extra === null) {
            return false;
        }
        let extra;
        if (typeof record.extra === 'string') {
            try {
                extra = JSON.parse(record.extra);
            } catch (e) {
                self.L.error(`isPrepaid :: Error parsing extra field: ${record.extra}, error: ${e}`);
                return false;
            }
        } else {
            extra = record.extra;
        }
        return ((_.get(extra, 'isPrepaid', false) === true) || (_.get(extra, 'isPrepaid', '0') === '1'));
    }

    getPrepaidFlagValue(payload) {
        const isPrepaid = _.get(payload, 'is_prepaid', "0");
        if((isPrepaid === true || isPrepaid === 'true' || isPrepaid === 1 || isPrepaid === '1'))
            return "1";
        return "0";
    }
}

export default RecentBillsLibrary;
