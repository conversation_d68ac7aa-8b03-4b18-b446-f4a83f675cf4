"use strict";

import _ from 'lodash'
import AWS from 'aws-sdk'
import fs from 'fs';
import { parseStream } from 'fast-csv';
const csv = require('fast-csv')
import utility from './'


class AWSS3StreamUtils {

    constructor(options, updateProgress = () => { }, customNotification) {
        this.L = options.L,
            this.config = options.config;
        this.updateProgress = updateProgress
        this.customNotification = customNotification;
        this.isProcessing = false;
        this.isProcessingFirstRecord = false;

    }


    configure(bucketName, serviceName = "", batchSize = 1) {
        this.s3Params = {
            Bucket: _.get(this.config, 'AWS.Bucket', null),
            ClientInfo: _.get(this.config, 'AWS.ClientInfo', null),
        };
        this.bucketName = bucketName
        this.batchSize = batchSize;
        this.batchRecords = []
        this.batchofTestRecord = []
        try {
            if (this.isProcessing) {
                this.<PERSON>.warn("Cannot reconfigure while processing is active");
                return;
            }

            this.s3 = new AWS.S3(this.s3Params.ClientInfo)
        } catch (error) {
            this.L.error("Error while initializing S3", error)
        }

        this.serviceName = serviceName
    }

    getFileNames(folderPath, callback) {
        let self = this;
        let files = []
        this.s3.listObjects({
            Bucket: 'billdesk-staging',
            Delimiter: '/',
            Prefix: `${folderPath}/`
        }, function (err, data) {
            if (err) {
                self.L.critical("Error while Retreiving Data")
                return callback(err)
            } else {
                data.Contents.forEach(function (obj, index) {
                    console.log(obj)
                    files.push(obj.Key)
                })
                self.L.info(self.serviceName, "files ::", files)
                return callback(null, files)
            }
        })
    }
    getFileNamesWithBucket(folderPath, bucket, callback) {
        let self = this;
        let files = []
        this.s3.listObjects({
            Bucket: bucket,
            Delimiter: '/',
            Prefix: `${folderPath}/`
        }, function (err, data) {
            if (err) {
                self.L.critical("Error while Retreiving Data")
                return callback(err)
            } else {
                data.Contents.forEach(function (obj, index) {
                    console.log(obj)
                    files.push(obj.Key)
                })
                self.L.info(self.serviceName, "files ::", files)
                return callback(null, files)
            }
        })
    }

    start(processBatch, filename, record, callback, skipRows = 0) {

        let self = this;
        this.isProcessing = true;
        let params = {
            Bucket: "billdesk-staging",
            Key: filename
        }
        self.totalRecords = skipRows
        let s3Stream, csvStream

        //print the error in this table if egtting error in reading csv file
        self.L.info(self.serviceName, "Starting Processing for file :", filename)
        try {
            s3Stream = this.s3.getObject(params).createReadStream();
            //s3Stream=fs.createReadStream(filePath);
            s3Stream.on('error', (error) => {
                self.L.critical("Error while configuring s3Stream - ", error)
                if (self.csvStream) {
                    self.csvStream.destroy()
                } else {
                    return callback("Error")
                }
            })
            this.csvStream = csv.parseStream(s3Stream, { headers: headers => headers.map(h => h.trim()), skipRows, trim: true });
        } catch (error) {
            self.L.critical("Error while configuring s3Stream", error)
        }



        this.csvStream
            .on('data', async (data) => {
                this.totalRecords++;
                this.batchRecords.push(data)
                if (this.batchRecords.length == 2000) {
                    self.csvStream.pause();
                    self.L.info(this.serviceName, filename, "Processing batch of length ", this.batchRecords.length)
                    try {

                        await processBatch(this.batchRecords, record)


                    } catch (err) {
                        self.L.error(this.serviceName, filename, "Error while processing batch", err)
                        if (self.csvStream) {
                            self.csvStream.destroy()
                        }
                        return callback(err)
                    }
                    self.L.info(this.serviceName, filename, "Finished processing batch ")
                    if (this.totalRecords == -1) {
                        this.isProcessing = false;

                    }

                    self.updateProgress(filename, this.totalRecords, null, null, record)
                    this.batchRecords = []
                    self.csvStream.resume()

                }
            })
            .on('end', async (rowCount) => {
                /** Process the last batch */
                //it can either process twice
                if (this.batchRecords.length > 0) {
                    self.L.info(this.serviceName, filename, "Processing batch of length ", this.batchRecords.length)
                    try {
                        await processBatch(this.batchRecords, record)
                    } catch (err) {
                        self.L.error(this.serviceName, filename, "Error while processing batch", err)
                    }
                    self.L.info(this.serviceName, filename, "Finished processing batch ")
                    this.batchRecords = []
                }
                utility._sendMetricsToDD(rowCount || "0", ['REQUEST_TYPE:CSV_PROCESSED', `FILE:${filename}`])
                self.L.info(this.serviceName, filename, "Reached end of csv file , rowCount", rowCount)
                self.updateProgress(filename, -1, this.customNotification, this.totalRecords, record)
                return callback(null)

            })
            .on('error', error => {
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:CSV_ERROR', `FILE:${filename}`])
                self.L.error(self.serviceName, filename, "Error in CSV Stream : ", error)
                if (self.csvStream) {
                    self.csvStream.destroy()
                }
                return callback(error)
            });

    }
    //do not read empty fields record
    //is it ggoing sequentially
    //Progress file does not exist
    //{"status":"error","action":"drop"}]}}}
    //do not give approve option if test record noti not sent

    readFirstRecord(fileRecord, process, callback) {
        let self = this;

        this.s3Params = {
            Bucket: _.get(this.config, 'AWS.Bucket', null),
            ClientInfo: _.get(this.config, 'AWS.ClientInfo', null),
        };

        try {

            this.s3 = new AWS.S3(this.s3Params.ClientInfo)
        } catch (error) {
            this.L.error("Error while initializing S3", error)
        }

        let params = {
            Bucket: "billdesk-staging",
            Key: fileRecord.file_name
        }
        let isFirstRecord = true;

        //fs
        let stream = this.s3.getObject(params)
            .createReadStream()
            .pipe(csv.parse({ headers: true }))
            .on('data', async (data) => {
                this.batchofTestRecord.push(data);
                if (this.batchofTestRecord.length >= 1) {
                    try {
                        stream.pause();
                        console.log("reading first rescord from stream ", data);
                        if (isFirstRecord) {
                            console.log("isFirstRecord is ", isFirstRecord);
                            //what if two records enter here
                            isFirstRecord = false;
                            this.isProcessingFirstRecord = true;
                            try {
                                await process(this.batchofTestRecord[0], fileRecord);

                            }
                            catch (error2) {
                                console.log("error is in readFirstRecord ", error2);
                                this.isProcessingFirstRecord = false;
                                this.batchofTestRecord = []
                                stream.removeAllListeners(); 
                                stream.destroy(); // Stop processing after first record
                                return callback(error2);

                            }

                            this.isProcessingFirstRecord = false;
                            this.batchofTestRecord = []
                            stream.removeAllListeners(); 
                            stream.destroy(); // Stop processing after first record
                            return  callback();
                        }
                    }
                    catch (err) {
                        self.L.error(this.serviceName, fileRecord.file_name, "Error while processing record", err);
                        stream.removeAllListeners(); 
                        stream.destroy(err);
                        return callback(err); // Pass the error to the callback
                    }

                }
            })
            .on('end', async () => {
                console.log('End of CSV file');
                if (this.batchofTestRecord.length >= 1) {
                    try {
                        console.log("reading first rescord from stream ", data);
                        if (isFirstRecord) {
                            console.log("isFirstRecord is ", isFirstRecord);
                            //what if two records enter here
                            isFirstRecord = false;
                            try {
                                await process(this.batchofTestRecord[0], fileRecord);

                            }
                            catch (error2) {
                                console.log("error is in readFirstRecord ", error2);
                                this.batchofTestRecord = []
                                stream.removeAllListeners(); 
                                stream.destroy(); // Stop processing after first record
                                return callback(error2);

                            }
                            this.batchofTestRecord = []
                            stream.removeAllListeners(); 
                            stream.destroy();
                            return  callback();
                        }
                    }
                    catch (err) {
                        self.L.error(this.serviceName, fileRecord.file_name, "Error while processing record", err);
                        stream.removeAllListeners(); 
                        stream.destroy(err);
                        return callback(err); // Pass the error to the callback
                    }

                }
                if (!this.isProcessingFirstRecord && isFirstRecord) {
                    stream.removeAllListeners(); 
                    stream.destroy();
                    return callback(); // Call the callback without an error

                }

            })
            .on('error', (error) => {
                console.error('Error reading CSV:', error);
                stream.removeAllListeners(); 
                stream.destroy(error);
                return callback(error); // Pass the error to the callback
            });

    }


}

export default AWSS3StreamUtils;

