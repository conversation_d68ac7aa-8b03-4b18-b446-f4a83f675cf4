import _ from 'lodash'
import ASYNC from 'async'
import utility from '../lib'
//import marketOauth    from 'market-oauth'
/*
    if this module required again please iclude below line in package.json and uncomment _getUserDetailsFromAuth if required
    "market-oauth": "git+ssh://*****************:paytmteam/market-oauth.git",
*/
import REQUEST from 'request'

class RemindableUsersLibrary {

    constructor(options) {
        this.L = options.L;
        this.rechargeConfig = options.rechargeConfig;
        this.authConfig = _.get(this.rechargeConfig, 'auth', {});
        this.authorizationKey = "Basic " + new Buffer.from(this.authConfig.clientKey + ":" + this.authConfig.clientSecretAuth,'utf-8').toString('base64');
    }

    _getUserDetails(callback, data, resolvePromise = null , timeout = null) {
        let self = this;

        if (!data.customer_id) {
            if(resolvePromise){
                return resolvePromise(false);
            }
            return callback(null, false);
        }

        let apiOpts = {
            method: 'GET',
           // url: self.authConfig.endpoint + self.authConfig.fetch_user_api + '?fetch_strategy=BASIC&user_id=' + _.get(data, 'customer_id', null),
           url: 'http://inmockjava.nonprod.onus.paytmdgt.io/auth/v1/getUserDetails?customer_id=' +  _.get(data, 'customer_id', null),
            
           headers: {
                authorization: self.authorizationKey,
                verification_type: 'service_token'
            },
            json: true
        };

        if(timeout!=null){
            apiOpts['timeout'] = timeout
        }

        REQUEST(apiOpts, (error, response, body) => {
            if (body && typeof body === 'string') {
                try {
                    body = JSON.parse(body);
                } catch (error) {
                    self.L.error('RemindableUsersLibrary:: _getUserDetails::  not able to parse response from /v2/user api:', _.get(data, 'customer_id', null), error);
                    utility._sendMetricsToDD(1, ['REQUEST_TYPE:GETUSERDETAIL_V2_USER_API', 'STATUS:ERROR']);
                    if(resolvePromise){
                        return resolvePromise(false);
                    }
                    return callback(null, false);
                }
            }
            if (error || (body && !body.basicInfo)) {
                self.L.error('RemindableUsersLibrary:: _getUserDetails:: ', 'Error in fetching user data for the customerId - ' + _.get(data, 'customer_id', null) + error, body);
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:GETUSERDETAIL_V2_USER_API', 'STATUS:ERROR']);
                if(resolvePromise){
                    return resolvePromise(false);
                }
                return callback(null, false);
            } else {
                //self.L.log('RemindableUsersLibrary:: _getUserDetails::  /v2/user api response:', _.get(data, 'customer_id', null), JSON.stringify(body));  common function for all categories, commenting due to sensitive info
                if (_.get(body, 'basicInfo.phone', null) || _.get(body, 'basicInfo.email', null)) {
                    data['customer_mobile'] = _.get(body, 'basicInfo.phone', null);
                    data['customer_email'] = _.get(body, 'basicInfo.email', null);
                    data['customer_name'] = (_.get(body, 'basicInfo.firstName', '') ? _.get(body, 'basicInfo.firstName', '') : '') + " " + (_.get(body, 'basicInfo.lastName', '') ? _.get(body, 'basicInfo.lastName', '') : '');
                    if(resolvePromise){
                        return resolvePromise(true);
                    }
                    return callback(null, true);
                } else {
                    if(resolvePromise){
                        return resolvePromise(false);
                    }
                    return callback(null, false);
                }
            }
        });
    }

    /*
    _getUserDetailsFromAuth(cb,data){
        var self = this;

        ASYNC.waterfall([(callback) => {

            if (self.authToken == null) {
                marketOauth.configure(self.rechargeConfig.subscription.oauth);
                let mktO = marketOauth.auth;
                mktO.getToken((err, token) => {
                    if (!err && token) {
                        self.authToken = token;
                    } else {
                        self.L.error('RemindableUsersLibrary:: _getUserDetailsFromAuth:: not able to fetch token and existing token value is: ', self.authToken);
                    }
                    callback(null);
                });
            } else {
                callback(null);
            }
        },
        (callback) => {

            if (self.authToken) {
                self.L.log('RemindableUsersLibrary:: _getUserDetailsFromAuth:: ', 'Fetching user data for the customerId: ' + _.get(data, 'customer_id', null) + ' with token: ' + self.authToken);
                marketOauth.user.fetch(self.authToken, _.get(data, 'customer_id', null), (error, res) => {
                    if (error || !res) {
                        self.L.error('RemindableUsersLibrary:: _getUserDetailsFromAuth:: ', 'Error in fetching user data for the customerId - ' + _.get(data, 'customer_id', null) + ' for token - ' + self.authToken + ' ' + error);
                        self.authToken = null;
                        callback(null, false);
                    } else {
                        if (_.get(res, 'mobile', null) != null || _.get(res, 'email', null) != null) {
                            data['customer_mobile'] = _.get(res, 'mobile', null);
                            data['customer_email'] = _.get(res, 'email', null);
                            data['customer_name'] = _.get(res, ['firstName'], '') + " " + _.get(res, ['lastName'], '');
                            callback(null, true);
                        } else {
                            data['customer_name'] = _.get(res, ['firstName'], '') + " " + _.get(res, ['lastName'], '');
                            callback(null, false);
                        }
                    }
                });
            } else {
                callback(null, false);
            }
        }],
            (error, res) => {
                if (error) {
                    self.L.error('RemindableUsersLibrary:: _getUserDetailsFromAuth:: unable to fetch customer info');
                }
                cb(null, res);
            }
        );

    }
    */
    _shouldRemindUser(customerMobile,rechargeNumber,operator,checkableOperators){

        if (checkableOperators.indexOf(operator) > -1) {
            if (customerMobile != rechargeNumber) {
                return false;
            }
        }
        return true;
    }

}
export default RemindableUsersLibrary;


(function () {
    if (require.main === module) {
        var commander = require('commander');
        commander
            .version('0.0.1')
            .option('-c, --cust_id <value>', 'customer_id', Number)
            .parse(process.argv);

        if (!commander.cust_id) {
            console.log('Please enter customer_id whom you wanna fetch basic details!');
            process.exit(1);
        }

        startup.init({
            exclude: {
                cvr: true,
                mongoDb: true,
            }
        }, function (err, options) {
            if (err) {
                console.log(err);
                process.exit(1);
            }

            let data = {
                "customer_id": commander.cust_id
            };
            let serviceInstance = new RemindableUsersLibrary(options);
            serviceInstance._getUserDetails((err, res) => {
                console.log('User info appended in Data ', data);
                console.log('Response of the func is ', err, res);
            }, data);
        });
    }
})();