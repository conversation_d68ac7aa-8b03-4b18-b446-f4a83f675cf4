/*
    jshint
    esversion: 8
*/

"use strict";

import _ from 'lodash';

import Lock from './lock'
import RequestWrapper from './requestWrapper'

/**
 * only fetchCustomerDetail function will has tested by the QA.
 */


class OAuth {
    constructor(options) {
        this.authCode = null;
        this.authToken = null;
        this.rechargeConfig = options.rechargeConfig;
        this.authConfig = this.rechargeConfig.auth;
        this.authorizationKey = "Basic " + new Buffer.from(this.authConfig.clientKey + ":" + this.authConfig.clientSecretAuth,'utf-8').toString('base64');
        
    
        this.lock = new Lock({batchSize: options.batchSize});
        
        this.requestWraper = new RequestWrapper({
            requestType: "OAuth",
            retryCount: 3
        });
    }

    async fetchCode() {

        let apiOpts = {
            method: 'POST',
            url: _.get(rechargeConfig, 'auth.endpointReminder', '') + _.get(rechargeConfig, 'auth.create_token_api', ''),
            headers: {
                authorization: this.authorizationKey,
                'content-type': 'application/x-www-form-urlencoded',
                'cache-control': 'no-cache'
            },
            body: 'response_type=code&client_id=' + this.authConfig.clientKey + '&do_not_redirect=true&scope=paytm&username=' + this.authConfig.auth_username + '&password=' + this.authConfig.auth_password,
            timeout: 10000
        };
        try {
            let body = await this.requestWraper.hitRequest(apiOpts);

            this.authCode = body.code;
        } catch (error) {
            throw error;
        }
    }

    async fetchToken() {
        let apiOpts = {
            method: 'POST',
            url: this.authConfig.endpointReminder + this.authConfig.get_token_api,
            //json: true,
            headers: {
                authorization: this.authorizationKey,
                'content-type': 'application/x-www-form-urlencoded'
            },
            body: 'grant_type=authorization_code&code=' + this.authCode + '&client_id=' + this.authConfig.clientKey + '&scope=paytm&client_secret=' + this.authConfig.clientSecretAuth,
            timeout: 10000
        };

        try {
            let body = await this.requestWraper.hitRequest(apiOpts);
            this.authToken = body.access_token;

        } catch (error) {
            throw error;
        }

    }

    async fetchNewToken() {
        await this.lock.acquire();

        if (!this.authToken) {
            try {
                await this.fetchCode();
                await this.fetchToken();

                this.lock.release();
            } catch (error) {
                this.lock.release();
                throw error;
            }
        }

        return this.authToken;
    }

    async getAuthToken() {
        if (this.authToken) return this.authToken;
        return await this.fetchNewToken();
    }

    async fetchCustomerDetail(qs) {
        if (!qs) {
            throw new Error("there is no query string for a request.");
        }

        let apiOpts = {
            method: 'GET',
            //For automation and testing
            //url: this.authConfig.endpoint + this.authConfig.fetch_user_api + qs,
            url:'http://inmockjava.nonprod.onus.paytmdgt.io/v2/oauth/fetch'+qs,
            headers: {
                authorization: this.authorizationKey,
                verification_type: 'service_token',
            },
            timeout: 10000,
            json: true
        };

        return this.requestWraper.hitRequest(apiOpts);
    }
}

export default OAuth;