"use strict";

import _ from 'lodash'
import REQUEST from 'request'
import utility from '.';

class SAGA {

    constructor(options) {
        this.L = options.L,
        this.config = options.config;
    }
    
    getCreditCardDataFromSaga(cb, customerId){ 
        let self = this,
          url = _.get(self.config, 'COMMON.SAGA_SAVED_CARDS_BY_USER_ID_API_URI', '') + customerId,  
        //url = _.get(self.config, 'COMMON.SAGA_SAVED_CARDS_BY_USER_ID_API_URI', '') + customerId + '/recents',
            apiOpts = {
                url: url, 
                method: 'POST',
                timeout: 10000,
                headers: {
                    "content-type": "application/json"
                },
                'json': {
                    type: "savedCard",
                    product : {
                        cardType: "CC",
                        service: "credit card"
                    }
                }             
            };
        let latencyStart = new Date().getTime();    
        self.L.log('SAGA_SAVED_CARDS_BY_USER_ID_API::getCreditCardDataFromSAGA ApiOpts', `${JSON.stringify(apiOpts)}_having_customer_id ${customerId}`);
        REQUEST(apiOpts, (error, response, body) =>{
            let latencyTime = new Date().getTime() - latencyStart;
            utility._sendMetricsToDD(latencyTime, ['REQUEST_TYPE:SAGA_SAVED_CARDS_BY_USER_ID_API', `URL:${url}`]);
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:SAGA_SAVED_CARDS_BY_USER_ID_API', 
                `URL:${url}`,
                'STATCODE:' + _.get(response, 'statusCode', _.get(error, 'code', '5XX'))
            ]);
            if (!error && body && body.recents) {
                let res = body.recents;
                this.L.log('SAGA_SAVED_CARDS_BY_USER_ID_API::getCreditCardDataFromSaga Response', `${JSON.stringify(body)}_having_customer_id ${customerId}`);
                return cb(null, res);
            } else {
                this.L.error('SAGA_SAVED_CARDS_BY_USER_ID_API::getCreditCardDataFromSaga Error', `${error}_having_ApiOpts${JSON.stringify(apiOpts)}having_customer_id ${customerId}`);
                return cb(`SAGA_SAVED_CARDS_BY_USER_ID_API Error Msg: ${error}`);
            }
        });
    }
}

export default SAGA;