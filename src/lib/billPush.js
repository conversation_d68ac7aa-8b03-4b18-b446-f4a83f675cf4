"use strict";


import _ from 'lodash';
import UTIL from 'util'
import uuidv1 from 'uuidv1'
import RequestWrapper from './requestWrapper'
import MOMENT from 'moment';
import ASYNC from 'async';
import REQUEST from 'request'
import utility from '.';
import Logger from './logger';


class BillPush {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.client = options.cassandraDbClient;
        this.infraUtils = _.get(options, 'INFRAUTILS', null);

        this.requestWraper = new RequestWrapper({
            requestType: "BILL_PUSH",
            retryCount: 3
        });

        this.appName = options.appName;
        this.logger = new Logger(options);
    }

    pushToRegistrationProcess(ref, record, payLoad, source, flag = 'reminder') {
        let self = this;
        let finalPayload = self.preparePayload(record, payLoad, source);
        const blockedPaytype = _.get(self.config, ['DYNAMIC_CONFIG', 'VALIDATION_SYNC', 'COMMON', 'BLOCKED_PAYTYPE'], ['prepaid']);
        if (blockedPaytype.includes(_.get(finalPayload, 'paytype', null))) {
            self.L.log(`pushToRegistrationProcess:: Skipping bill push with paytype ${_.get(record, 'paytype', null)} for recharge number ${_.get(finalPayload, 'rechargeNumber', null)
                }`);
            return;
        } else {
            self.publishBillPushUser(ref, finalPayload, source, flag);
        }
    }

    preparePayload(record, payLoad, source) {
        let userData = _.get(record, 'user_data', null);
        if (typeof userData == 'string') {
            try {
                userData = JSON.parse(userData);
            } catch (e) {
                userData = {};
            }
        }

        let productId = _.get(record, 'productId', _.get(record, 'product_id', null));

        let newRecord = {
            "operation": "Create",
            "productId": _.get(record, 'productId', _.get(record, 'product_id', null)),
            "rechargeNumber": _.get(record, 'rechargeNumber', _.get(record, 'recharge_number', null)),
            "source": source ? source : "reminder",
            "operator": _.get(record, 'operator', null),
            "onboardingOperator": "euronet",
            "service": _.get(record, 'service', null),
            "customerId": _.get(record, 'customerId', _.get(record, 'customer_id', null)),
            "paytype": _.get(record, 'paytype', null),
            "rechargeNumber2": _.get(userData, 'recharge_number_2', null),
            "rechargeNumber3": _.get(userData, 'recharge_number_3', null),
            "rechargeNumber4": _.get(userData, 'recharge_number_4', null),
            "rechargeNumber5": _.get(userData, 'recharge_number_5', null),
            "rechargeNumber6": _.get(userData, 'recharge_number_6', null),
            "rechargeNumber7": _.get(userData, 'recharge_number_7', null),
            "rechargeNumber8": _.get(userData, 'recharge_number_8', null),
            "mobileNo": _.get(record, 'customerMobile', _.get(record, 'customer_mobile', null)),
            "circle": _.toLower(_.get(this.config, ['CVR_DATA', productId, 'circle'], null)),
        }
        return newRecord;
    }

    getKeyForKafka(payload) {
        // RECHARGE_NO_SERVICE_OPERATOR_PAYTYPE_CIRCLE_OPERATION
        let key = ''; 
        if (_.get(payload, 'rechargeNumber')) key += _.get(payload, 'rechargeNumber');
        if (_.get(payload, 'service')) key += (key ? '_' : '') + _.get(payload, 'service');
        if (_.get(payload, 'operator')) key += (key ? '_' : '') + _.get(payload, 'operator');
        if (_.get(payload, 'paytype')) key += (key ? '_' : '') + _.get(payload, 'paytype');
        if (_.get(payload, 'circle')) key += (key ? '_' : '') + _.get(payload, 'circle');
        if (_.get(payload, 'operation')) key += (key ? '_' : '') + _.get(payload, 'operation');
        return key;
    }

    publishBillPushUser(ref, payload, source, flag) {
        let self = this;
        let key = self.getKeyForKafka(payload);
        if (payload != null || payload != undefined) {
            if (ref.upmsPublisher) {
                ref.upmsPublisher.publishData([{
                    topic: _.get(self.config.KAFKA, 'SERVICES.UPMS_PUBLISHER.TOPIC', ''),
                    messages: JSON.stringify(payload),
                    key: key,
                    attributes: 1
                }], (error) => {
                    if (error) {
                        const errorMessage = error.message || 'UPMS Publisher error'; 
                        utility._sendMetricsToDD(1, [
                            "REQUEST_TYPE:UMPS_SUBCRIPTION",
                            `SERVICE:${_.get(payload, 'service', null)}`,
                            'STATUS:ERROR',
                            "TYPE:KAFKA_PUBLISH",
                            "TOPIC:UMPS_SUBCRIPTION",
                            "OPERATOR:" + _.get(payload, 'operator', null),
                            "SOURCE:" + source,
                            "FLAG:" + flag
                        ]);
                        self.logger.critical(`billPush :: publishRecordInKafka Error while publishing message in Kafka ${error} - MSG:- `, payload, _.get(payload, 'service', null));
                    } else {
                        utility._sendMetricsToDD(1, [
                            "REQUEST_TYPE:UMPS_SUBCRIPTION",
                            `SERVICE:${_.get(payload, 'service', null)}`,
                            'STATUS:PUBLISHED',
                            "TYPE:KAFKA_PUBLISH",
                            "TOPIC:UMPS_SUBCRIPTION",
                            "OPERATOR:" + _.get(payload, 'operator', null),
                            "SOURCE:" + source,
                            "FLAG:" + flag
                        ]);
                        self.logger.log(`billPush :: publishRecordInKafka Message published successfully in Kafka - MSG:- for key ${key} with payload::`, payload,);
                    }
                    return;
                }, [200, 800]);
            } else {
                utility._sendMetricsToDD(1, [
                    "REQUEST_TYPE:UMPS_SUBCRIPTION",
                    `SERVICE:${_.get(payload, 'service', null)}`,
                    'STATUS:ERROR',
                    "TYPE:KAFKA_PUBLISHER_NOT_FOUND",
                    "TOPIC:UMPS_SUBCRIPTION",
                    "OPERATOR:" + _.get(payload, 'operator', null)
                ]);
                self.insertFailedPayloadIntoDB(ref, payload, "upmsPublisher not found");
            }
        }

    }

    insertFailedPayloadIntoDB(ref, payload, error){
        let self = this;
        let params={
            "operation": _.get(payload, 'operation', 'Create'),
            "productId": _.get(payload, 'productId', null),
            "rechargeNumber": _.get(payload, 'rechargeNumber', null),
            "source": _.get(payload, 'source', 'reminder'),
            "operator": _.get(payload, 'operator', null),
            "onboardingOperator": _.get(payload, 'onboardingOperator','euronet'),
            "service": _.get(payload, 'service', null),
            "customerId": _.get(payload, 'customerId', null),
            "paytype": _.get(payload, 'paytype', null),
            "rechargeNumber2": _.get(payload, 'rechargeNumber2', null),
            "rechargeNumber3": _.get(payload, 'rechargeNumber3', null),
            "rechargeNumber4": _.get(payload, 'rechargeNumber4', null),
            "rechargeNumber5": _.get(payload, 'rechargeNumber5', null),
            "rechargeNumber6": _.get(payload, 'rechargeNumber6', null),
            "rechargeNumber7": _.get(payload, 'rechargeNumber7', null),
            "rechargeNumber8": _.get(payload, 'rechargeNumber8', null),
            "mobileNo": _.get(payload, 'mobileNo', null),
            "error": error
        }

        const query = `INSERT INTO failed_subscription (operation, customer_id, product_id, recharge_number, source, operator, onboarding_operator, service, paytype, recharge_number_2, recharge_number_3, recharge_number_4, recharge_number_5, recharge_number_6, recharge_number_7, recharge_number_8, mobile_no, error, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);`; 

        const queryParams = [
            params.operation,
            params.customerId,
            params.productId,
            params.rechargeNumber,
            params.source,
            params.operator,
            params.onboardingOperator,
            params.service,
            params.paytype,
            params.rechargeNumber2,
            params.rechargeNumber3,
            params.rechargeNumber4,
            params.rechargeNumber5,
            params.rechargeNumber6,
            params.rechargeNumber7,
            params.rechargeNumber8,
            params.mobileNo,
            params.error,
            MOMENT().format('YYYY-MM-DD HH:mm:ss'),
            MOMENT().format('YYYY-MM-DD HH:mm:ss')
        ];

        self.client.execute(query, queryParams, { prepare : true }, (err, result) => {
            if(!err){
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:UPMS_SUBSCRIPTION_FAILURE", 'STATUS:SUCCESS','TYPE:DB_INSERTION']);
            } else {
                self.L.error('insertFailedPayloadIntoDB :: DB exception! Params: ' + JSON.stringify(queryParams) + "Error :: " + err);
            }
        })
        payload.error = error;
        payload.created_at = MOMENT().format('YYYY-MM-DD HH:mm:ss');
        payload.updated_at = MOMENT().format('YYYY-MM-DD HH:mm:ss');
        self.publishToDwh(payload)
    }

    publishToDwh(payload){
        let self = this;
        self.configureKafka(function (error) {
            if (error) {
                self.L.critical('billPush :: start', 'unable to configure kafka for dwh', error);
                process.exit(0);
            }
            else {
                self.L.log('billPush :: start', 'Kafka Configured successfully for dwh !!');
            }
        });

        if (self.dwhPublisher) {
            self.dwhPublisher.publishData([{
                topic: _.get(self.config.KAFKA, 'SERVICES.UPMS_SUBSCRIPTION_FAILURE_DWH_PUBLISHER.TOPIC', ''),
                messages: JSON.stringify(payload)
            }], (error) => {
                if (error) {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:UPMS_SUBSCRIPTION_FAILURE",
                        'STATUS:ERROR',
                        "TYPE:DWH_PUBLISH",
                        "TOPIC:UPMS_SUBSCRIPTION_FAILURE_DWH"
                    ]);

                    self.L.critical('billPush :: publishToDwh', 'Error while publishing payload in dwh Kafka - MSG:- ' + JSON.stringify(payload), error);
                } else {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:UPMS_SUBSCRIPTION_FAILURE",
                        'STATUS:PUBLISHED',
                        "TYPE:DWH_PUBLISH",
                        "TOPIC:UPMS_SUBSCRIPTION_FAILURE_DWH"
                    ]);
                    self.L.log('billPush :: ', 'Message published successfully in Dwh Kafka', ' on topic UPMS_SUBSCRIPTION_FAILURE_DWH' + JSON.stringify(payload));
                }
                return;
            }, [200, 800]);
        } else {
            self.L.critical("Dwh Publisher not found.....");
        }

    }

    configureKafka(done) {
        let self = this;
        ASYNC.waterfall([
            next => {
                self.dwhPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.UPMS_SUBSCRIPTION_FAILURE_DWH_PUBLISHER.HOSTS
                });
                self.dwhPublisher.initProducer('high', function (error) {
                    if (error) {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:UPMS_SUBSCRIPTION_FAILURE", 'STATUS:ERROR', 'TYPE:UPMS_SUBSCRIPTION_FAILURE_DWH_PUBLISHER', 'SOURCE:MAIN_FLOW']);
                    }
                    return next(error)
                });
            }
        ], function (error) {
            if (error) {
                self.L.critical('configureKafka', 'Could not initialize Kafka', error);
                return done(error)
            }
            return done(null);
        });
    }

}

export default BillPush;