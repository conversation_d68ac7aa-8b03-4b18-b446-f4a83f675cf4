const { google } = require('googleapis');
const AWS = require('aws-sdk');
const logger = require('../lib/logger');
const creds = require('../config/googleSheetCreds.json');

const s3 = new AWS.S3();
const BUCKET_NAME = 'digital-reminder';

class GoogleSheetsService {
    constructor() {
        // Initialize auth with your service account credentials
        this.auth = new google.auth.GoogleAuth({
            keyFile: creds,
            scopes: ['https://www.googleapis.com/auth/spreadsheets']
        });
        
        this.sheets = google.sheets({ version: 'v4' });
        this.spreadsheetId = '1Jnz4uEETur9F-J4FEMVH4VsfzEc-Lquq9dt96Aq6kJ8';
    }

    async createDailySheet(date = new Date()) {
        try {
            const sheetTitle = date.toISOString().split('T')[0];
            
            // Create new sheet
            await this.sheets.spreadsheets.batchUpdate({
                auth: await this.auth.getClient(),
                spreadsheetId: this.spreadsheetId,
                requestBody: {
                    requests: [{
                        addSheet: {
                            properties: {
                                title: sheetTitle,
                                gridProperties: {
                                    rowCount: 1000,
                                    columnCount: 5
                                }
                            }
                        }
                    }]
                }
            });

            // Add headers
            await this.sheets.spreadsheets.values.update({
                auth: await this.auth.getClient(),
                spreadsheetId: this.spreadsheetId,
                range: `${sheetTitle}!A1:E1`,
                valueInputOption: 'RAW',
                requestBody: {
                    values: [['Title', 'Total_Occurrences', 'Firings', 'Resolutions', 'Priority']]
                }
            });

            return sheetTitle;
        } catch (error) {
            logger.error('Error creating daily sheet:', error);
            throw error;
        }
    }

    async processAlerts(date) {
        try {
            const s3Path = `slack-alerts/${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}/slack-alerts.json`;
            
            // Get all alerts from S3
            const response = await s3.listObjects({
                Bucket: BUCKET_NAME,
                Prefix: s3Path
            }).promise();

            // Process each alert file
            const alertMap = new Map();
            
            for (const object of response.Contents) {
                const data = await s3.getObject({
                    Bucket: BUCKET_NAME,
                    Key: object.Key
                }).promise();

                const alert = JSON.parse(data.Body.toString());
                this.processAlert(alertMap, alert);
            }

            return Array.from(alertMap.values());
        } catch (error) {
            logger.error('Error processing alerts:', error);
            throw error;
        }
    }

    processAlert(alertMap, alert) {
        const key = alert.title;
        if (!alertMap.has(key)) {
            alertMap.set(key, {
                title: alert.title,
                total_occurrences: 0,
                firings: 0,
                resolutions: 0,
                priority: this.extractPriority(alert)
            });
        }

        const record = alertMap.get(key);
        record.total_occurrences++;
        
        // Determine if it's a firing or resolution
        if (alert.title.includes('[FIRING')) {
            record.firings++;
        } else {
            record.resolutions++;
        }
    }

    extractPriority(alert) {
        // Extract priority from alert description or annotations
        const priorityMatch = alert.description?.match(/priority\s*=\s*(P[0-9])/i);
        return priorityMatch ? priorityMatch[1] : 'Unknown';
    }

    async updateDailySheet(date = new Date()) {
        try {
            const sheetTitle = date.toISOString().split('T')[0];
            
            // Create new sheet for the day
            await this.createDailySheet(date);

            // Process alerts
            const alerts = await this.processAlerts(date);

            // Prepare values for sheet
            const values = alerts.map(alert => [
                alert.title,
                alert.total_occurrences,
                alert.firings,
                alert.resolutions,
                alert.priority
            ]);

            // Update sheet with values
            if (values.length > 0) {
                await this.sheets.spreadsheets.values.append({
                    auth: await this.auth.getClient(),
                    spreadsheetId: this.spreadsheetId,
                    range: `${sheetTitle}!A2`,
                    valueInputOption: 'RAW',
                    insertDataOption: 'INSERT_ROWS',
                    requestBody: { values }
                });
            }

            logger.info(`Updated Google Sheet for ${sheetTitle} with ${values.length} alerts`);
        } catch (error) {
            logger.error('Error updating daily sheet:', error);
            throw error;
        }
    }
}

module.exports = new GoogleSheetsService();