import _ from 'lodash'
import utility from '../lib'
import VAL<PERSON><PERSON><PERSON> from 'validator'
import <PERSON>Y<PERSON> from 'async'
import OS from 'os'
import MOMENT from 'moment'
import BILLS from '../models/bills'
import digitalUtility from 'digital-in-util'
import Q from 'q'
import DroppedTransactions from '../models/droppedTransactions'
import NOTIFIER from '../services/notify'
import KafkaConsumerChecks from '../lib/kafkaConsumerChecks';


class OperatorUpNotification {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.activePidLib = options.activePidLib;
        this.infraUtils = options.INFRAUTILS;
        this.tableName = 'dropped_transactions';
        this.droppedTransactions = new DroppedTransactions(options);
        this.commonLib = new utility.commonLib(options);
        this.reminderUtils = new digitalUtility.ReminderUtils();
        this.kafkaBatchSize = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'OPERATOR_UP_NOTIFY', 'BATCHSIZE'], 2) : 500;
        this.kafkaBatchDelay = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'OPERATOR_UP_NOTIFY', 'DELAY'], 5 * 60 * 1000) : 0;
        this.notify = new NOTIFIER(options);
        this.kafkaConsumerChecks = new KafkaConsumerChecks(options);
        this.mode = options.mode;
        this.recordMap = {};
    }

    start() {
        let self = this;
        self.configureKafka(function (error) {
            if (error) {
                self.L.critical('start :: operatorUpNotification', 'unable to configure kafka', error);
                process.exit(0);
            } else {
                self.L.log('start :: operatorUpNotification', 'Kafka Confugured successfully !!');
            }
        });
    }

    configureKafka(done) {
        /**
         * maintain this sequence
         * 1) Initialize all publisher
         * 2) Initialize all consumers
         */
        let self = this;
        ASYNC.series([
            next => {
                self.notify.configureKafkaPublisher((error) => {
                    return next(error);
                });
            },
            next => {
                self.L.log('configureKafka', 'Going to initialize Kakfa Consumer for topic : OPERATOR_HEALTH');
                // Initialize consumer of topic REMINDER_Cylinder
                self.L.log("configureKafka", "host name : ", _.get(self.config.KAFKA, 'TOPICS.RECHARGE.HOSTS'), " topic name: ",  _.get(self.config.KAFKA, 'SERVICES.OPERATOR_HEALTH.TOPIC'));

                self.kafkaConsumer = new self.infraUtils.kafka.consumer({
                    "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.REMINDER_KAFKA_CLUSTER.HOSTS'),
                    "groupId": "operatorHealth-consumer",
                    "topics": _.get(self.config.KAFKA, 'SERVICES.OPERATOR_HEALTH.TOPIC'),
                    "id": 'operatorHealthConsumer_' + OS.hostname() + '_' + process.pid,
                    "fromOffset": "latest",
                    "autoCommit": false,
                    "batchSize": self.kafkaBatchSize
                });

                self.kafkaConsumer.initConsumer(self.execSteps.bind(self), (error) => {
                    if (error) {
                        self.L.critical('configureKafka', 'Could not initialize Kafka', error);
                    }
                    self.L.log("configureKafka", "consumer of topic : OPERATOR_HEALTH Configured");
                    return next(error);
                });         
            }
        ], function (error) {
            if (error) {
                self.L.critical('configureKafka', 'Could not initialize Kafka', error);
                return done(error)
            }
            return done(null);
        });
    }

    execSteps(records) {
        let self = this,
            chunkSize = 50,
            currentPointer = 0, lastMessage;

        let startTime = new Date().getTime();

        if (records && _.isArray(records)) {
            lastMessage = records[records.length - 1];
            self.kafkaConsumer._pauseConsumer();
        } else {
            self.L.critical('execSteps', `No valid kafka records found. Received records-`, records);
            return;
        }
        self.L.log('execSteps:: ', `Processing ${records.length} Bill Fetch data !!`);
        utility._sendMetricsToDD(records.length, ['REQUEST_TYPE:OPERATOR_UP_NOTIFY']);
        ASYNC.whilst(
            () => {
                return currentPointer < records.length;
            },
            (callback) => {
                let nextChunk = records.slice(currentPointer, (currentPointer + chunkSize));
                currentPointer += chunkSize;
                self.processBatch(nextChunk, () => {
                    setTimeout(() => {
                        callback();
                    }, 200);
                });
            },
            (err) => {
                self.kafkaConsumerChecks.findOffsetDuplicates("OperatorUpNotification", records);
                
                self.recordMap = {};
                self.kafkaConsumer.commitOffset(lastMessage, (error) => {
                    if (error) {
                        self.L.error('execSteps::', 'Error while commit for-', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                    }
                    else {
                        self.L.log('execSteps::', 'Commit success for offset :', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                    }

                    // Resume consumer now

                    let endTime = new Date().getTime();
                    let executionTime = (endTime - startTime) / 1000;      //in seconds
                    executionTime = Math.round(executionTime);
                    self.L.log('execSteps::', 'per chunkSize record Execution time :', executionTime, 'seconds');
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:OPERATOR_UP_NOTIFY", 'STATUS:SUCCESS', "SOURCE:OPERATOR_UP_NOTIFICATION", "TIME_TAKEN:" + executionTime]);

                    setTimeout(function () {
                        self.kafkaConsumer._resumeConsumer();
                    }, self.kafkaBatchDelay);
                });
            }
        );
    }

    processBatch(records, done) {
        let self = this,
            currentPointer = 0;
        ASYNC.whilst(
            () => {
                return currentPointer < records.length;
            },
            (callback) => {
                let record = records[currentPointer];
                currentPointer = currentPointer + 1;
                self.processRecord(record, (err) => {
                    setTimeout(() => {
                        callback();
                    }, 1);
                });
            },
            (err) => {
                return done()
            }
        );
    }

    processRecord(payLoad, done) {
        let self = this;
        try {
            let record = self.convertKafkaPayloadToRecord(payLoad);
            let errorResponse = self.validateKafkaPayload(record);
            if (errorResponse) {
                // Invalid record count send to promethius
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:OPERATOR_UP_NOTIFY", 'STATUS:VALIDATION_FAILURE']);
                self.L.error(`processRecord:: Invalid Record record: ${JSON.stringify(record)} error:${errorResponse}`);
                return done(errorResponse);
            }

            let result = true,
                pageNumber = 0,
                MAX_FETCH = 5000;

            ASYNC.whilst(
                () => {
                    return result===true || result.length > MAX_FETCH;
                },
                (callback) => {
                    result = false;
                    self.droppedTransactions.getDroppedTransaction((err, customerDetails) => {
                        if (err) {
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:OPERATOR_UP_NOTIFY", 'STATUS:DB_READ_FAILURE']);
                            self.L.error(`processRecord:: unable to fetch Record::: error:${err}`);
                        }

                        result = customerDetails

                        if(result.length > MAX_FETCH) self.sendAndDelete(result,callback);
                        else callback();
                    }, record.operator, record.gateway, pageNumber++, MAX_FETCH);
                },
                (err) => {
                    self.sendAndDelete(result,done);
                }
            );

        }
        catch (err) {
            self.L.error('processRecord:: ', err);
            return done(err);
        }
    }

    sendAndDelete(customerDetails, done) {
        let self = this;
        ASYNC.eachLimit(customerDetails, 3, function (customerDetail, eachCb) {
            ASYNC.waterfall([
                cb => {
                    
                    self.sendNotification((err) => {
                        if(err) {
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:OPERATOR_UP_NOTIFY", 'STATUS:NOTIFICATION_FAILURE']);
                        }
                        return cb(err);
                    }, customerDetail);
                },
                cb => {
                    self.droppedTransactions.updatedDroppedTransaction((err) => {
                        if (err) {
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:OPERATOR_UP_NOTIFY", 'STATUS:DB_UPDATE_FAILURE']);
                            self.L.error(`processRecord:: error in updating dropped transaction, error:${err}`);
                        } else {
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:OPERATOR_UP_NOTIFY", 'STATUS:SUCCESS']);
                        }
                        return cb(err);
                    }, customerDetail);
                }
            ], function (err, result) {
                if (err) {
                    self.L.error(`processRecord:: unable to send notification customer id:${customerDetail.customer_id}  :: error:${err}`);
                }
                return eachCb(err);
            });

        }, function (err) {
            return done(err);
        });
    }

    validateKafkaPayload(record) {

        let self = this;

        if (!record || !record.operator || !record.gateway) {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:OPERATOR_UP_NOTIFY", 'STATUS:ERROR', 'TYPE:VALIDATION_FAIL']);
            return 'Invalid record!';
        }

        if (record.isOperatorDown!==false || record.reqType.toLowerCase() != 'recharge') {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:OPERATOR_UP_NOTIFY", 'STATUS:ERROR', 'TYPE:SKIP']);
            return 'operator is not up yet!';
        }

        if(_.get(self.recordMap,[record.operator,record.gateway], false)) {
            return 'Duplicate record! already processed in this batch!';
        }

        _.set(self.recordMap,[record.operator,record.gateway], true);

    }

    convertKafkaPayloadToRecord(payLoad) {
        let self = this;
        try {
            payLoad = JSON.parse(_.get(payLoad, 'value', null));
        } catch (error) {
            if (error) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:OPERATOR_UP_NOTIFY", 'STATUS:ERROR', 'TYPE:RECORD_PARSING']);
                self.L.critical('convertKafkaPayloadToRecord', `Invalid Kafka record received`, payLoad);
                return;
            }
        }

        payLoad = _.omitBy(payLoad, _.isNull);

        let operator = _.get(payLoad, 'operator', '');
        let gateway = _.get(payLoad, 'gateway_name', '');

        let isOperatorDown = _.get(payLoad, 'operator_down', true);
        let reqType = _.get(payLoad, 'request_type', '');

        let isValidationDown = _.get(payLoad, 'validation_down', null);
        if(isValidationDown !== null) {
            isOperatorDown = isValidationDown || isOperatorDown;
        }
        let record = {
            "operator": operator,
            "isOperatorDown": isOperatorDown,
            "gateway": gateway,
            "reqType": reqType
        };

        return record;
    }

    sendNotification(callback, record) {

        let self = this;
        let payLoad = {
            recharge_number: _.get(record, 'recharge_number', null),
            operator: _.get(record, 'operator', null),
            merchant_name: _.get(record, 'operator', null),
            category_id: _.get(record, 'category_id', null),
            service: _.get(record, 'service', null)
        };

        let userData = _.get(record, 'user_data', {});
        if (typeof userData === 'string') {
            try {
                userData = JSON.parse(userData);
            } catch (e) {
                self.L.error('sendNotification:: unable to parse user data operator up :: error:', e);
                userData = {};
            }
        }

        const amount = parseFloat(_.get(userData, 'amount', null));
        if (!isNaN(amount)) {
            payLoad.amount = amount;
        }
        
        let notificationRecord = {
            type: 'PUSH',
            recipients: _.get(record, 'customer_id', null),
            template_id: _.get(this.config, ['DYNAMIC_CONFIG', 'OPERATOR_UP_NOTIFY', _.get(payLoad, 'category_id'), 'TEMPLATE_ID'], _.get(this.config, ['NOTIFICATION', 'OPERATOR_UP_NOTIFY_TEMPLATE', 'DEFAULT', 'd'], null))
        };
        utility.sendNotificationMetricsFromSource(payLoad,"INITIATED","OPUP")
        utility.sendNotificationMetricsFromCreate(payLoad,notificationRecord , "RECVD")
        if (!_.get(notificationRecord, 'template_id', null)) {
            utility.sendNotificationMetricsFromCreate(payLoad,notificationRecord , "INVALID_TEMPLATE_ID")
            return callback('Template_id is null');
        }

        else if (_.get(notificationRecord, 'recipients', null) != null) {
            let
                notificationData = {
                    "template_type": _.get(notificationRecord, 'type', 'PUSH').toLowerCase(),
                    "template_id": _.get(notificationRecord, 'template_id', null),
                    "options": {
                        "notificationOpts": {
                            "recipients": VALIDATOR.toString(_.get(notificationRecord, 'recipients', null))
                        },
                        "type": "async",
                        "data": payLoad
                    }
                };
            let url_type = "external";
            let landing_path = _.get(this.config, ['NOTIFICATION', 'category_url_type_map', _.get(payLoad, 'category_id', null)], "utility");
            let deeplink_url = _.get(this.config, ['NOTIFICATION', 'notificationapi', 'DIGITAL_DEEPLINK_URL'], null);
            let deeplink_api = _.get(this.config, ['NOTIFICATION', 'notificationapi', 'DEEPLINK_API'], null) + "/" + _.get(payLoad, 'category_id', null);

            let url = `paytmmp://${landing_path}?url=${deeplink_url}${deeplink_api}`;

            let deepLinkData = {
                "channel_id": "both",
                "deepLinkObj": {
                    "extra": {
                        "url": url + "?product_id=" + _.get(record, 'product_id', null) + "$recharge_number=" + _.get(record, 'recharge_number', null) + (_.has(payLoad, 'amount') ? "$price=" + _.get(payLoad, 'amount', null) : "") + "$" + this._utmParams(this.mode, '$'),
                        "url_type": url_type
                    }
                },
                "noRich": false
            };

            _.extend(notificationData.options.notificationOpts, deepLinkData);

            // We are waiting till morning for notification adding minimum hours to current time
            let
                date = new Date(),
                timeToSend = date.getHours(),
                sendAt,
                timeRange = {
                    gte: _.get(this.config, 'RECHARGE_NUDGE_CONFIG.blockedNotificationStartHour', 0),
                    lte: _.get(this.config, 'RECHARGE_NUDGE_CONFIG.blockedNotificationEndHour', 7)
                };

            // We are sending notification just after 7+ extra hours after 00:00 AM
            if (timeToSend >= timeRange.gte && timeToSend < timeRange.lte) {
                sendAt = MOMENT().add(timeRange.lte, "hours").format("YYYY-MM-DD HH:mm:ss");
            } else {
                sendAt = MOMENT().format("YYYY-MM-DD HH:mm:ss");
            }

            let
                apiOpts = {
                    "uri": _.get(this.config, 'NOTIFICATION.notificationapi.DIGITALNOTIFICATIONAPI', null),
                    "method": "POST",
                    "timeout": 500,
                    "json": {
                        "source_id": 3,
                        "category_id": 3,
                        "recharge_number": _.get(record, 'recharge_number', null),
                        "product_id": _.get(record, 'product_id', null),
                        "max_retry_count": 2,
                        "retry_interval": 30,
                        "type": _.get(notificationRecord, 'type', null),
                        "template_id": _.get(notificationRecord, 'template_id', null),
                        "recipient": _.get(notificationRecord, 'recipients', null),
                        "send_at": sendAt,
                        "data": notificationData,
                        "rules": {
                            "condition": `category_id=3 and source_id=3 and recharge_number='${_.get(record, 'value.userData_recharge_number', null)}' and product_id=${_.get(record, 'value.catalogProductID', null)} 
                                   and type='${_.get(notificationRecord, 'type', null)}' and template_id=${_.get(notificationRecord, 'template_id', null)} and recipient='${_.get(record, 'customer_id', null)}'`,
                            "actions": [
                                {
                                    "status": "pending",
                                    "action": "drop"
                                },
                                {
                                    "status": "sent",
                                    "action": "drop"
                                },
                                {
                                    "status": "error",
                                    "action": "drop"
                                }
                            ]
                        }
                    }
                };


            self.notify.__createNotification((err, res) => {
                utility.sendNotificationMetricsFromCreate(payLoad,notificationRecord ,"CREATED")
                return callback(err);
            }, apiOpts.json);
        }
        else {
            utility.sendNotificationMetricsFromCreate(payLoad,notificationRecord , "INVALID_RECP")
            // recipients not found, sending error
            callback("Recipients not found while sending notification");
        }
    }

    _utmParams(mode, delimiter = '&') {
        let utm = _.get(this.config, 'NOTIFICATION.RECHARGE_NUDGE_UTM.' + mode, null);
        if (!utm) {
            this.L.error(`UTM config not found for template: ${mode}`);
            utm = _.get(this.config, ['NOTIFICATION', 'RECHARGE_NUDGE_UTM', 'notfound'], '');
        }
        return utm.replace(/&/g, delimiter);
    }

    suspendOperations() {
        var self = this,
            deferred = Q.defer();
        self.L.log(`OperatorUpNotification::suspendOperations kafka consumer shutdown initiated`);

        Q()
            .then(function () {
                self.kafkaConsumer.close(function (error, res) {
                    if (error) {
                        self.L.error(`OperatorUpNotification::stopConsumer :: Error Stopping Consumer Err : ${error}`);
                        return error;
                    }
                    self.L.info(`OperatorUpNotification::stopConsumer :: Consumer Stopped!  Response : ${res}`);
                })
            })
            .then(function () {
                self.L.log(`OperatorUpNotification::suspendOperations kafka consumer shutdown successful`);
                deferred.resolve();
            })
            .catch(function (err) {
                self.L.error(`OperatorUpNotification::suspendOperations error in shutting kafka consumer`, err);
                deferred.reject(err);
            });
        return deferred.promise;
    }
}



export default OperatorUpNotification;