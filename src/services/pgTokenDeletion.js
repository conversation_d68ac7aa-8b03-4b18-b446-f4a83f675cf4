"use strict";
import _, { reject } from 'lodash'
import utility from '../lib'
import RecentsLayerLib from '../lib/recentsLayer'
import VA<PERSON>IDATOR from 'validator'
import ASYNC from 'async'
import OS from 'os'
import MOMENT from 'moment'
import BILLS from '../models/bills'
import MODELS from '../models'
import digitalUtility from 'digital-in-util'
import CATALOGVERTICALRECHARGE from '../models/catalogVerticalRecharge';
import SmsParsingSyncCCBillLibrary from '../lib/smsParsingSyncCCBills';
import PG from '../lib/pg'
import DigitalCatalog from '../lib/digitalReminderConfig'
import RemindableUsersLibrary from '../lib/remindableUser'
import uuidv1 from 'uuidv1'
import recentBillLibrary from '../lib/recentBills'
import BILLSUBSCRIBER from './billSubscriber'

import Q from 'q'

class pgTokenDeletion {
    constructor(options) {
        this.L = options.L;
        this.infraUtils = options.INFRAUTILS;
        this.config = options.config;
        this.bills = new BILLS(options);
        this.billsModel = new MODELS.Bills(options)
        this.commonLib = new utility.commonLib(options);
        this.recentsLayerLib = new RecentsLayerLib(options); // TODO
        this.billSubscriber = new BILLSUBSCRIBER(options);
        this.bills_operator_table = _.get(this.config, 'OPERATOR_TABLE_REGISTRY', {});
        this.recentBillLibrary = new recentBillLibrary(options);
        this.infraUtils = _.get(options, 'INFRAUTILS', null);
        this.cc_bills_operators = this.recentBillLibrary._initRecentBillSpecificData(this.bills_operator_table, this.ccOperators);
        this.paymentGatewayUtils = new PG(options);
        this.smsParsingSyncCCBillLib = new SmsParsingSyncCCBillLibrary(options);
        this.digitalCatalogLib = DigitalCatalog;
        this.remindableUsersLibrary = new RemindableUsersLibrary(options);
        this.tableName = 'bills_creditcard';
        this.refereshIntervalForCategoryData = 15 * 60 * 1000; // DCAT getCategoryProductDetail API refresh interval 
        this.reminderUtils = new digitalUtility.ReminderUtils();
        this.catalogVerticalRecharge = new CATALOGVERTICALRECHARGE(options);
        this.activePidLib = options.activePidLib;
        this.greyScaleEnv = _.get(options, 'greyScaleEnv', false);
        this.commonLib = new utility.commonLib(options);
        this.kafkaBatchSize = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'SMS_PARSING_CC_BILLS', 'BATCHSIZE'], 2) : 500;
        this.kafkaBatchDelay = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'SMS_PARSING_CC_BILLS', 'DELAY'], 5 * 60 * 1000) : 0;
    }

    start() {
        let self=this;
        self.smsParsingSyncCCBillLib.refreshDCATCacheData(self.refereshIntervalForCategoryData);
        ASYNC.waterfall([
            next=>{
                self._configureAndStartConsumer((error) => {
                    if (error) {
                        self.L.error("pgTokenDeletion consumer", "Failed to initialize pgTokenDeletion consumer service");
                        process.exit(1);
                    }
                });
            }
        ],function (err) {
            process.exit(0);
        })
    }

    _configureAndStartConsumer() {
        let
            self = this;

        self.L.log('_configureAndStartConsumer', `Going to initialize Kakfa Consumer for topic ${_.get(self.kafkaConsumerConfig, 'TOPIC', null)}`);

        // Initialize consumer of topic : PG_Deleted_AMW
        let topic = _.get(self.config.KAFKA, 'SERVICES.PG_TOKEN_DELETION.TOPIC');
        let kafkaHost = _.get(self.config.KAFKA, 'TOPICS.PG_TOKEN_DELETION.HOSTS');

        self.kafkapgTokenDeletionConsumer = new self.infraUtils.kafka.consumer({
            "kafkaHost": kafkaHost,
            "groupId": "pgTokenDeletionConsumer",
            "topics": topic,
            "id": 'pgTokenDeletionConsumer_' + OS.hostname(),
            "fromOffset": "earliest",
            "autoCommit": false,
            "batchSize": self.kafkaBatchSize
        });

        self.kafkapgTokenDeletionConsumer.initConsumer(self.execSteps.bind(self), (error) => {
            if (error) {
                self.L.critical("_configureAndStartConsumer", "error while configuring consumer", error);
            } else {
                self.L.log("_configureAndStartConsumer", `consumer of topic : ${topic} Configured`);
            }
            return;
        });
    }

    execSteps(records) {
        let self = this,
            chunkSize = 2,   // ?
            currentPointer = 0, lastMessage;
      
        let startTime = new Date().getTime();
        if (records && _.isArray(records)) {
            lastMessage = records[records.length - 1];
            self.kafkapgTokenDeletionConsumer._pauseConsumer();
            utility._sendMetricsToDD(records.length, [
                'STATUS:PG_TOKEN_DELETION_CONSUMED',
                'TOPIC:' + _.get(self.config.KAFKA, 'SERVICES.PG_TOKEN_DELETION.TOPIC'),
                'REQUEST_TYPE:PG_TOKEN_DELETION_CONSUMER'
            ])
        } else {
            self.L.critical('execSteps', `No valid kafka records found. Received records-`, records);
            return;
        }

        self.L.log('execSteps:: ', `Processing ${records.length} PG Deleted Tokens data !!`);

        console.log('The records are ',records);

        ASYNC.whilst(
            () => {
                return currentPointer < records.length;
            },
            (callback) => {
                let nextChunk = records.slice(currentPointer, (currentPointer + chunkSize));
                currentPointer += chunkSize;
                self.processBatch(nextChunk, () => {
                    setTimeout(() => {
                        callback();
                    }, 1);
                });
            },
            (err) => {
                try{
                    self.kafkapgTokenDeletionConsumer.commitOffset(lastMessage, (error) => {
                        if (error) {
                            self.L.error('execSteps::', 'Error while commit for-', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                        }
                        else {
                            self.L.log('execSteps::', 'Commit success for offset :', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                        }
    
                        // Resume consumer now
                        let endTime = new Date().getTime();
                        let executionTime = (endTime - startTime) / 1000;      //in seconds
                        executionTime = Math.round(executionTime);
                        self.L.log('execSteps::', 'per batchSize record Execution time :', executionTime, 'seconds', 'recordLength :',records.length);
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_STATUS", 'STATUS:SUCCESS', "SOURCE:PG_TOKEN_DELETION", "TIME_TAKEN:" + executionTime]);
                      

                        if(self.greyScaleEnv) {
                            setTimeout(function(){
                                self.kafkapgTokenDeletionConsumer._resumeConsumer();
                            },_.get(self.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'BILL_REMINDER_NOTIFICATION', 'BATCH_DELAY'],10));   //?
                        } else {
                            self.kafkapgTokenDeletionConsumer._resumeConsumer();
                        }
                    });
                } catch (err){
                    self.L.error('execSteps::', 'Error while commit for-', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                    self.kafkapgTokenDeletionConsumer._resumeConsumer();
                }
            }
        );
    }

    processBatch(records, done) {
        let self = this;
        ASYNC.map(
            records,
            (record, next) => {
                self.processData(() => {
                    next();
                }, record);
            },
            err => {
                done();
            }
        )
    }

    processData(done, data) {
        let self = this,
            record, 
            debugKey = data;

        ASYNC.waterfall([
            (callback) => {
                self.convertKafkaPayloadToRecord(function (error, result) {
                    if (error) {
                        return callback(`Unable to validate data, error - ${error}`);
                    } else {
                        record = result;
                        return callback();
                    }
                }, data);
            },
            (callback) => {
                self.processRecordAndInsert((error) => {
                    if (error) {
                        return callback(error);
                    } else {
                        return callback(null);
                    }
                }, record, this.tableName);
            }], (error) => {
                if (error) {
                    self.L.error('pgTokenDeletion', 'debugKey', debugKey, 'error', error);
                }
                return done();
            }
        );
    }

    convertKafkaPayloadToRecord(callback , kafkaPayload){
        let
            self = this,
            kafkaPayloadData;

        try {
            kafkaPayloadData = JSON.parse(_.get(kafkaPayload, 'value', null));
        } catch (error) {
            if (error) {
                self.L.critical('convertKafkaPayloadToRecord', `Invalid Kafka record received`, kafkaPayload);
                callback(error);
            }
        }

        let record={};

        record.customer_id= _.get(kafkaPayloadData, 'customer_id', null);
        record.bank_name = _.get(kafkaPayloadData, 'bank_name', null);
        record.card_network = _.get(kafkaPayloadData, 'card_network', null);
        record.lastCC = _.get(kafkaPayloadData, 'lastCC',null);
        record.tin = _.get(kafkaPayloadData,'tin',null);
        record.par= _.get(kafkaPayloadData,'par',null);

        callback (null,record);
    }

    async processRecordAndInsert(done , record, tableName){

        let self =this;

        ASYNC.waterfall([
            next => {
                let customerId = _.get(record, 'customer_id');
                return self.billsModel.getBillByCustomer(next, tableName, customerId) ;
            },
            async(rows, next) => {
                self.L.log('processRecordAndInsert', `Found ${rows.length} transaction history for the customerId:${_.get(record,'customer_id',null)}`)
                _.set(record, 'transactionHistory', rows); 

                await self.findAndDeleteMatchingMCNs(record);

                return next();
            }
        ], function (error) {
            if (error) {
                self.L.error(`_prepareDataToInsert`, `Failed with error ${error} for data ${record}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:PG_TOKEN_DELTION", 'STATUS:ERROR','SOURCE:MAIN_FLOW_EXECUTION']);
            }
            return done();
        });
    }

    async findAndDeleteMatchingMCNs(record) {
        // console.log('The record is at findAndDeleteAllMatchingMCNs ', record);
        let self = this;
        const historicalData = _.get(record,'transactionHistory',[]);
        let parPresentInHistory=false;

        try{
            for(let row of historicalData) {
                let customerId=_.get(row,'customer_id',null);
                let bankName =_.get(row,'bank_name',null);
                let cardNetwork =_.get(row,'card_network',null);
                let rechargeNumber =_.get(row,'recharge_number',null);
                if(row.reference_id === record.par && customerId  && bankName && cardNetwork && rechargeNumber&& customerId!='' && bankName!='' && cardNetwork!='' && rechargeNumber!=''){
                    let new_reference_id=null, lastCC=null;
                    try{
                        lastCC=rechargeNumber.replace(/\s/g, '').substr(-4);
        
                        new_reference_id = `${lastCC}_${bankName}_${cardNetwork}`;
                        self.L.log('The  new ref id is ', new_reference_id);
                    }catch (error) {
                        self.L.error("findAndDeleteMatchingMCNs::issue in getting lastCC", error);
                    }

                    if(new_reference_id !== null) {
                        parPresentInHistory=true;
                    }

                    await self.billsModel.deleteAllMatchingMCNspgTokenDeletion({...row},this.tableName);

                    row.reference_id= new_reference_id;
                    row.tin=null;

                    try{
                        let extra = _.get(row, 'extra', null);
                        let tempExtra = extra ? JSON.parse(extra) : {};
                        tempExtra.type = 'PG_Deleted_AMW';
                        tempExtra.updated_source='sms';
                        tempExtra.pgCardId=new_reference_id;
                        row.extra = JSON.stringify(tempExtra);
                    }catch(error){
                        self.L.error("findAndDeleteMatchingMCNs::issue in setting extra", error);
                    }

                    await self.bills.createCCBillforpgTokenDeletion(this.tableName, row);
                }
            }

            if(parPresentInHistory == false) {

                for(let row of historicalData) {
                    let customerId=_.get(row,'customer_id',null);
                    let bankName =_.get(row,'bank_name',null);
                    let cardNetwork =_.get(row,'card_network',null);
                    let rechargeNumber =_.get(row,'recharge_number',null);

                    let lastCC=null;
                    try{
                        lastCC=rechargeNumber.replace(/\s/g, '').substr(-4);
                    }catch (error) {
                        self.L.error("findAndDeleteMatchingMCNs::issue in getting lastCC", error);
                    }

                    if (customerId  && bankName && cardNetwork && rechargeNumber&& customerId!='' && bankName!='' && cardNetwork!='' && rechargeNumber!='' && lastCC == record.lastCC && _.toLower(bankName) == _.toLower(record.bank_name) && _.toLower(cardNetwork) == _.toLower(record.card_network)){
                        parPresentInHistory=true;
                        break;
                    }
                }
            }

            if(parPresentInHistory == false) {  // create a new entry in bills_creditcard and show it as a PG_Deleted_AMW card . 

                let prefix ='XXXX XXXX XXXX ';
                record.recharge_number = `${prefix}${record.lastCC}`;
                record.reference_id = `${record.lastCC}_${record.bank_name}_${record.card_network}`;

                let extra = {};
                extra.type = 'PG_Deleted_AMW';
                extra.updated_source='PG_Deleted_AMW';
                extra.created_source='PG_Deleted_AMW';
                extra.pgCardId = record.recharge_number;

                try{
                    record.extra = JSON.stringify(extra);
                }catch (error){
                    self.L.error("findAndDeleteMatchingMCNs::issue in stringifying extra", error);
                }

                record.is_automatic=0;
                record.service_id=0;
                record.service='financial services';
                record.paytype='credit card';
                record.status=4;
                // record.par_id=record.par;

                let sagaSavedCCDataUniqueKey = self.smsParsingSyncCCBillLib.getUniqueKeyForSavedCardsData({
                    isPaytmFirstCard : "0",
                    bankName : record.bank_name,
                    cardScheme : record.card_network
                });

                self.L.log(`findAndDeleteMatchingMCNs`, `sagaSavedCCDataUniqueKey : ${sagaSavedCCDataUniqueKey}`);

                await self.smsParsingSyncCCBillLib.getFinancialServicesPID(function (error, productId) {
                    if(error) {
                        self.L.error(`getFinancialServicesPID`, `Error Msg: ${error} for sagaSavedCCDataUniqueKey ${sagaSavedCCDataUniqueKey}`);    
                        reject(error);
                    } else {
                        self.L.log(`getFinancialServicesPID`, `productId: ${productId} for sagaSavedCCDataUniqueKey ${sagaSavedCCDataUniqueKey}`);
                        record.product_id=productId;  
                    }
                }, sagaSavedCCDataUniqueKey);

                if(!_.has(self.config, ['CVR_DATA', record.product_id, 'operator'])) {
                    self.L.error('createCCBillAndSendNotification', `CVR data not exists for productId:${productId}`);
                    reject(`CVR data not exists for productId:${record.product_id}`);
                }

                record.operator=_.get(self.config, ['CVR_DATA', record.product_id, 'operator'],null);

                await self.bills.createCCBillforpgTokenDeletion(this.tableName, record);
            }

        } catch (error){
            self.L.error("findAndDeleteMatchingMCNs:: Error while deleting creditcard history ", error);
        }
        return new Promise((resolve, reject) => {
            resolve();
        })

    }
}

export default pgTokenDeletion;