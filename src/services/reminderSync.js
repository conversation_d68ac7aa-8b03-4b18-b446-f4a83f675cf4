#!/usr/bin/env node
"use strict";

import _ from 'lodash'
import INFRAUTILS from 'infra-utils'
import OS from 'os'
import BILLS from '../models/bills'
import startup from '../lib/startup'
import utility from '../lib'
import RecentsLayerLib from '../lib/recentsLayer';
import BillSubscriber from './billSubscriber'
import ASYNC from 'async'
import digitalUtility from 'digital-in-util'
import CATALOGVERTICALRECHARGE from '../models/catalogVerticalRecharge';
import Q from 'q';
import MOMENT from 'moment';
import KafkaConsumerChecks from '../lib/kafkaConsumerChecks';

class ReminderSync {

    constructor(options) {
        this.dbInstance = options.dbInstance;
        this.L = options.L;
        this.bills = new BILLS(options);
        this.billSubscriber = new BillSubscriber(options);
        this.config = options.config;
        this.reminderUtils = new digitalUtility.ReminderUtils();
        this.catalogVerticalRecharge = new CATALOGVERTICALRECHARGE(options);
        this.commonLib = new utility.commonLib(options);
        this.recentsLayerLib = new RecentsLayerLib(options);
        this.kafkaConsumerChecks = new KafkaConsumerChecks(options);
        this.exclusionServiceList = _.get(this.config, ['DYNAMIC_CONFIG', 'REMINDER_SYNC_CONFIG', 'EXCLUSION_LIST', 'SERVICE_PAYTYPE_MAP'], {});
        setInterval(() => {
            this.initializeVariable();
        }, _.get(this.config, 'COMMON.INTERVAL_FOR_REINITIALIZE', 15) * 60 * 1000);

    }
    
    startDummyLogs() {
        let self = this;
        let dummyLogs = setInterval(function () {
            self.L.log('startDummyLogs', 'dummy logs...');
        }, 30000);
    }

    initializeVariable(){
        this.exclusionServiceList = _.get(this.config, ['DYNAMIC_CONFIG', 'REMINDER_SYNC_CONFIG', 'EXCLUSION_LIST', 'SERVICE_PAYTYPE_MAP'], {});
    }
    
    start() {
        let self = this;
        self.startDummyLogs();
        self.L.log("Service started on topic REMINDER_SYNC ");
        self.configureKafka(function (error) {
            if (error) {
                self.L.critical('REMINDER_SYNC :: start', 'unable to configure kafka', error);
                process.exit(0);
            }
            else {
                self.L.log('REMINDER_SYNC :: start', 'Kafka Configured successfully !!');
            }
        });
    }

    configureKafka(done) {
        let self = this;
        ASYNC.waterfall([
            next => {
                /**
                 * Kafka publisher to publish events to CT publisher pipeline 
                 */
                 self.ctKafkaPublisher = new INFRAUTILS.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.CT_EVENTS_PUBLISHER.HOSTS
                });
                this.ctKafkaPublisher.initProducer('high', function (error) {
                    if (!error)
                        self.L.log("_initializectKafkaPublisher : ctKafkaPublisher publisher Configured");
                    return next(error)
                });
            },
            next => {
                self.L.log('configureKafka', 'Going to initialize Kakfa Consumer for topic : REMINDER_SYNC');

                // Initialize consumer of topic REMINDER_SYNC
                 self.consumer = new INFRAUTILS.kafka.consumer({
                    "kafkaHost": _.get(self.config, 'KAFKA.TOPICS.REMINDER_SYNC.HOSTS'),
                    "groupId": "reminderSync-consumer",
                    "topics": _.get(self.config.KAFKA, 'SERVICES.REMINDER_SYNC.REMINDER_SYNC_TOPICS', []),
                    "id": 'reminderSync_' + OS.hostname(),
                    "fromOffset": "earliest"
                });
                self.consumer.initConsumer(self.syncDigitalReminder.bind(self), (error) => {
                    if (error)
                    self.L.critical("_initializeRecentBillConsumer : reminderSync consumer cannot start.", error);
                else
                    self.L.log("_initializeRecentBillConsumer : reminderSync consumer Configured");
                });
                return next()
            }
        ], function (error) {
            if (error) {
                self.L.critical('REMINDER_SYNC::configureKafka', 'Could not initialize Kafka', error);
            }
            return done(error);
        });
    }

    _getRechargeNumberCondition(rechargeNumber, operatorName) {
        let self = this;
        let [isOperatorPrefixEnabled, alternateRechargeNumber] = self.commonLib.getAlternateRechargeNumber(rechargeNumber, operatorName);
        
        if(isOperatorPrefixEnabled) {
            return ` and recharge_number in (${JSON.stringify(rechargeNumber)}, ${JSON.stringify(alternateRechargeNumber)})`;
        }
        return ` and recharge_number = ${JSON.stringify(rechargeNumber)}`;
    }

    prepareConditionForUpdateAccToAutomaticStatus(kafkaData) {
        let self = this,
            rechargeNumber = _.get(kafkaData, 'rechargeNumber', null),
            operatorName = _.get(kafkaData, 'operatorName', null),
            condition = `operator = ${JSON.stringify(operatorName)} and customer_id = ${kafkaData.customerId}`,
            updateCondition = `operator = ${JSON.stringify(operatorName)}`;

        let rechargeNumberCondition = self._getRechargeNumberCondition(rechargeNumber, operatorName);
        condition += rechargeNumberCondition;
        updateCondition += rechargeNumberCondition;

        condition += ` and service = ${JSON.stringify(kafkaData.service)}`;
        updateCondition += ` and service = ${JSON.stringify(kafkaData.service)}`;

        if (self.commonLib.isCreditCardOperator(kafkaData.service)) {
            if (!kafkaData.referenceId) {
                self.L.critical('syncDigitalReminder', 'Invalid referenceId or customerId for data : ', kafkaData);
                return [null, null];
            }
            condition += ` and reference_id = ${JSON.stringify(kafkaData.referenceId)};`;
            updateCondition += ` and reference_id = ${JSON.stringify(kafkaData.referenceId)};`;
        }
        return [condition, updateCondition];
    }

    prepareQueryForOtherCustIds(kafkaData, params) {
        let self = this;
        let operatorName = _.get(kafkaData, 'operatorName', null);
        let customerId = _.get(kafkaData, 'customerId', null);
        let rechargeNumber = _.get(kafkaData, 'rechargeNumber', null);
        let service = _.get(kafkaData, 'service', null);
        let tableName = _.get(params, 'tableName', null);

        let query = `UPDATE ${tableName} set is_automatic = 2 where operator = ${JSON.stringify(operatorName)}`;
        query += self._getRechargeNumberCondition(rechargeNumber, operatorName);
        query += ` and customer_id != ${customerId} and service = ${JSON.stringify(service)}`;
        
        return query;
    }

    syncDigitalReminder(data) {
        let
            self = this,
            notificationStatus = 1;
        if (!_.get(data, 'value', null))
            return;
        try {
            self.kafkaConsumerChecks.findOffsetDuplicates("ReminderSync", [data]);

            let tableName = null;
            let kafkaData = JSON.parse(data.value);
            let productId = kafkaData.productId;
            let extraDetails = '{}';

            if (!kafkaData || !kafkaData.operatorName || !kafkaData.rechargeNumber || !kafkaData.customerId || !kafkaData.service || !kafkaData.paytype) {
                self.L.critical('Invalid kafkaData :: ', JSON.stringify(kafkaData));
                return;
            }

            if(_.has(_.get(self, 'exclusionServiceList', {}), kafkaData.service) && _.get(self, ['exclusionServiceList', kafkaData.service] ,'') == kafkaData.paytype){
                self.L.log('syncDigitalReminder',`Reminder Not enabled for service ${kafkaData.service} and paytype ${kafkaData.paytype} for recharge_number ${kafkaData.rechargeNumber}`);
                return;
            }

            let debugKey = `OP_RN_CID_RID-${kafkaData.operatorName}_${kafkaData.rechargeNumber}_${kafkaData.customerId}_${_.get(kafkaData, 'referenceId', '')}`;
            tableName = _.get(self.config, ['OPERATOR_TABLE_REGISTRY', kafkaData.operatorName], null);
            if(_.toLower(_.get(kafkaData,'service',''))=='paytm postpaid'){
                tableName='bills_paytmpostpaid'
            }

            if (!tableName) {
                self.L.critical('NO TABLE FOUND FOR DATA :: ', debugKey, data.value);
                return;
            }

            let [condition, updateCondition] = self.prepareConditionForUpdateAccToAutomaticStatus(kafkaData);
            if(!condition || !updateCondition) {
                self.L.critical('Invalid condition or updateCondition for data', debugKey, data.value);
                return;
            }

            ASYNC.waterfall([
                async next => {
                    if(_.get(kafkaData,'service',null) == 'financial services') {

                        let dbRecord = false;

                        self.bills.getBillByCustomer(async (err, data) => {
                            if (err || !data || !_.isArray(data) || data.length < 1) {
                                return next(null , []);
                            }

                            console.log("--------data from bills_creidtcard---------", data);


                            for (let row of data) {
                                if (row.recharge_number === kafkaData.rechargeNumber && row.reference_id === kafkaData.referenceId) {
                                    dbRecord = row;
                                } else {
                                    const last4_MCN = row.recharge_number.replace(/\s+/g, '').substr(-4);  // removing white space from RN. 1234 XXXX XX12 12 <-- to handle scenarioes like this
                                    if (last4_MCN == kafkaData.rechargeNumber.replace(/\s+/g, '').substr(-4)) {

                                        let attributes_row = JSON.parse(_.get(self.config, ['CVR_DATA', row.product_id, 'attributes'], '{}'))
                                        let attributes_kafkaData = JSON.parse(_.get(self.config, ['CVR_DATA', kafkaData.productId, 'attributes'], '{}'))
                                        let bankNme_kafkaData = _.toLower(_.get(attributes_kafkaData, ['bank_code'], ''));
                                        let bankName_row = _.toLower(_.get(attributes_row, ['bank_code'], ''));

                                        if (bankName_row == bankNme_kafkaData) {
                                            console.log("-----deleting for the matching MCN----", row);
                                            await this.bills.deleteAllMatchingMCNs({...row});
                                        }
                                    }
                                }
                            }
                        }, 'bills_creditcard', kafkaData.customerId);
                        if(!dbRecord) {
                            return next (null ,[]);
                        } else {
                            console.log("--------dbRecord---------", dbRecord);
                            return next (null, [dbRecord]);
                        }
                    } else {
                        self.bills.getRecord(function (error, records) {
                            return next(error, records);
                        }, condition, 'is_automatic, product_id,notification_status,extra', tableName, 'DIGITAL_REMINDER_MASTER');
                    }
                },
                (records,next) => {
                    if(records[0] && records[0].notification_status) {
                        notificationStatus = records[0].notification_status;
                    }
                    
                    if(records.length == 0) {
                        self.L.error(`Record not found for ${debugKey}`);
                        utility._sendMetricsToDD(1, ['REQUEST_TYPE:REMINDER_SYNC_RECORD_NOT_FOUND', 'STATUS:ERROR']);

                    }

                    if (!records.length && kafkaData.is_automatic !== 0) {
                        // check extra mandatory params for record creation
                        if (!kafkaData.productId || !kafkaData.paytype) {
                            self.L.error(`Mandatory params productId or paytype missing to create record for data`, data.value);
                            utility._sendMetricsToDD(1, ['REQUEST_TYPE:REMINDER_SYNC_MANDATORY_PARAMS_MISSING', 'STATUS:ERROR']);
                            return next();
                        }

                        let params = self.createParamsForNewRecord(kafkaData);
                        self.billSubscriber.createBill(function (error) {
                            if (error) {
                                self.L.error(`Unable to create record for ${debugKey} error:`, error);
                                utility._sendMetricsToDD(1, ['REQUEST_TYPE:REMINDER_SYNC_CREATE_RECORD', 'STATUS:ERROR']);
                            }
                            return next();
                        }, params);
                    } else {
                        let subsMetaData = _.get(kafkaData, 'metaData', {});
                            extraDetails = _.get(records[0], 'extra', '{}') || '{}';
                    
                        if(records[0] && records[0].product_id && !kafkaData.productId){
                            productId = records[0].product_id        
                        }

                        if(subsMetaData.editable){
                            try {
                                if (typeof extraDetails == 'string')
                                    extraDetails = JSON.parse(extraDetails);
                            }
                            catch (err) {
                                self.L.error('_processRecords', 'Error in parsing extra details', err);
                            }
                            _.set(extraDetails, 'isPaymodeChanged', 1);
                            extraDetails = JSON.stringify(extraDetails);
                        }

                        if (records.length > 0 && kafkaData.is_automatic !== 0) {
                            // Handling scenario for prepaid to postpaid and where postpaid record has same custId that of kafka record.
                            let params = self.createParamsForNewRecord(kafkaData);
                            let prepaidTableName = tableName + '_prepaid';
                            self.L.log('reminderSync', 'kafka record with same customer id in postpaid bills table, check prepaidToPostpaid migration for ', debugKey);
                            _.set(params, 'sameCustIdInPostpaid', true);
                            self.billSubscriber.handlePrepaidToPostpaidMigration(prepaidTableName, tableName, params, (error) => {
                                if (error) {
                                    self.L.error(`Unable to migrate records for ${debugKey} error:`, error);
                                    utility._sendMetricsToDD(1, ['REQUEST_TYPE:REMINDER_SYNC_MIGRATE_RECORDS', 'STATUS:ERROR']);
                                }
                                return next();
                            });
                        } else {
                            return next();
                        }
                    }
                },
                next => {
                    // Now update records for updateCondition
                    let query = self.prepareQueryForUpdateAccToAutomaticStatus(kafkaData, {
                        "tableName": tableName,
                        "extraDetails": extraDetails
                    });
                    utility._sendMetricsToDD(1, [
                        'STATUS:UPDATE_RECORD',
                        'REQUEST_TYPE:REMINDER_SYNC',
                        'OPERATOR:' + kafkaData.operatorName
                    ])
                    self.updateOperatorTable(query);
                    return next();
                },
                next => {
                    if (kafkaData.is_automatic == 1 || kafkaData.is_automatic == 5 || kafkaData.is_automatic == 8) {
                        let queryForAllCustIds = self.prepareQueryForOtherCustIds(kafkaData, {
                            "tableName": tableName
                        });
                        self.updateOperatorTable(queryForAllCustIds);
                        return next();
                    }else return next();
                },
                next => {
                    self.commonLib.getRetailerData((error) => {
                        if(error) {
                            return next(error)
                        } else {
                            return next(null)
                        }
                    }, kafkaData.customerId, kafkaData);
                },
                next => {
                    self.commonLib.getCvrData((error) => {
                        if(error) {
                            return next(error)
                        } else {
                            return next(null)
                        }
                    }, productId, kafkaData);
                },
                next => {                  
                    let eventName = _.get(self.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENTS', 'AUTOMATIC_ENABLED'], 'automaticEnabled')
                    let debugKey = `rech:${kafkaData.rechargeNumber}::cust:${kafkaData.customerId}::op:${kafkaData.operatorName}`;
                    if(kafkaData.is_automatic === 0){
                        eventName = _.get(self.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENTS', 'AUTOMATIC_DISABLED'], 'automaticDisabled')
                    }
                    if(self.commonLib.isCTEventBlocked(eventName)){
                        self.L.info(`Blocking CT event ${eventName}`)
                        return next()
                    }

                    if (kafkaData.is_automatic === 5) {
                        eventName = _.get(self.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENTS', 'SUBS_RENEW'], 'SUBS_RENEW')
                    }
                    let mappedData = self.reminderUtils.createCTPipelinePayload(kafkaData, eventName, debugKey);
                    let clonedData = _.cloneDeep(mappedData); // needed for correct async logging
                    if (!notificationStatus ) {
                        return next(`stop publishing data on CleverTap via the Kafka pipeline for notification status : ${notificationStatus} for Kafka - MSG : ${JSON.stringify(clonedData)}`);
                    }else {
                        self.ctKafkaPublisher.publishData([{
                            topic: _.get(self.config.KAFKA, 'SERVICES.CT_EVENTS_PUBLISHER.TOPIC', ''),
                            messages: JSON.stringify(mappedData)
                        }], (error) => {
                            if (error) {
                                utility._sendMetricsToDD(1, ["REQUEST_TYPE:REMINDER_SYNC", 'STATUS:ERROR', "TYPE:CT_EVENTS", "OPERATOR:" + kafkaData.operatorName]);
                                self.L.critical('reminderSync :: publishCtEvents', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(clonedData), error);
                            } else {
                                utility._sendMetricsToDD(1, ["REQUEST_TYPE:REMINDER_SYNC", 'STATUS:PUBLISHED', "TYPE:CT_EVENTS", "OPERATOR:" + kafkaData.operatorName,`EVENT_NAME:${eventName}`]);
                                self.L.log('reminderSync :: publishCtEvents', 'Message published successfully in Kafka', ' on topic REMINDER_CT_EVENTS', JSON.stringify(clonedData));
                            }
                            return next(error);
                        }, [200, 800]);
                    }
                },
                // next => {
                //     let billsObj = {},
                //         source = 'syncAutomaticRecent';
                //     _.set(billsObj, 'automaticState', kafkaData.is_automatic);
                //     _.set(billsObj, 'automaticDate', null);// curently not getting in kafka payload
                //     let queryParameters = {
                //         customer_id: kafkaData.customerId,
                //         recharge_number: kafkaData.rechargeNumber,
                //         operator: kafkaData.operatorName,
                //         service: kafkaData.service,
                //         paytype: kafkaData.paytype
                //     };
                    
                //     let fieldValue = [billsObj];
                //     self.recentsLayerLib.update(function (error) {
                //         self.L.log('updateBillsInRecent::recentsLayerLib.update', `update recents request completed for ${debugKey},error if any is:${error}`);
                //         return next();
                //     }, queryParameters,'automaticDetails',fieldValue,source);
                
                // },
            ], function (error) {
                if(error)
                    self.L.critical('Error occured for data', debugKey, error);
            });
        } catch (err) {
            self.L.critical("syncDigitalReminder :: Error in processing records for sync in reminder: for data",data, err);
        }
    }

    prepareQueryForUpdateAccToAutomaticStatus(kafkaData, params) {
        let self = this;
        let automaticStatus = _.get(kafkaData, 'is_automatic', null);
        let tableName = _.get(params, 'tableName', null);
        let extraDetails = _.get(params, 'extraDetails', null);

        let [condition, updateCondition] = self.prepareConditionForUpdateAccToAutomaticStatus(kafkaData);
        let query = `UPDATE  ${tableName} set is_automatic = ${automaticStatus}`;
        query += ([0,5,8].includes(automaticStatus) ? '' : `, extra = ${JSON.stringify(extraDetails)}`);
        query += ` where ${condition}`;
        return query;
    }

    createParamsForNewRecord(kafkaData) {
        return {
            customerId: _.get(kafkaData, 'customerId', null),
            rechargeNumber: _.get(kafkaData, 'rechargeNumber', null),
            productId: _.get(kafkaData, 'productId', null),
            operator: _.get(kafkaData, 'operatorName', null),
            service: _.get(kafkaData, 'service', null),
            paytype: _.get(kafkaData, 'paytype', null),
            circle: _.get(kafkaData, 'circle', null),
            nextBillFetchDate: _.get(kafkaData, 'nextBillFetchDate', MOMENT()),
            customerMobile: _.get(kafkaData, 'customerMobile', null),
            customerEmail: _.get(kafkaData, 'customerEmail', null),
            user_data: _.get(kafkaData, 'user_data', null),
            referenceId: _.get(kafkaData, 'referenceId', null),
            sourceType: 'reminderSync'
        };
    }

    updateOperatorTable(query) {
        let
            self = this;
        self.dbInstance.exec(function (err, res) {
            if (err)
                self.L.critical('error while executing query:', query, err)
            else {
                self.L.log(query, JSON.stringify(res));
            }
        }, 'DIGITAL_REMINDER_MASTER', query);
    }

       suspendOperations(){
        var self        = this,
        deferred = Q.defer();

        self.L.log(`ReminderSync::suspendOperations kafka consumer shutdown initiated`);
    
        Q()
        .then(function(){
            self.consumer.close(function(error, res){
                if(error){
                    self.L.error(`ReminderSync::stopConsumer :: Error Stopping Consumer Err : ${error}`);
                    return error;
                }
                self.L.info(`ReminderSync::stopConsumer :: Consumer Stopped!  Response : ${res}`);
            })
        })
        .then(function(){
            self.L.log(`ReminderSync::suspendOperations kafka consumer shutdown successful`);
            deferred.resolve();
        })
        .catch(function(err){
            self.L.error(`ReminderSync::suspendOperations error in shutting kafka consumer`, err);
            deferred.reject(err);
        });
        return deferred.promise;
    }
}

export default ReminderSync;

(function () {
    if (require.main === module) {
        startup.init({
            exclude: {
                cvr: false,
                mongoDb: true,
                ruleEngine: true,
                activePidLib: true,
                dynamicConfig: true
            }
        }, function (err, options) {
            if (err) {
                console.log(`err: ${err}`);
            }
            let reminderSync = new ReminderSync(options);
            reminderSync.syncDigitalReminder({
                value: JSON.stringify({
                    operatorName: 'jaipur vidyut vitran nigam ltd.(jvvnl)',
                    rechargeNumber: '210462002786',
                    service: 'electricity',
                    customerId: 1181452793,
                    is_automatic: 1,
                    paytype: "postpaid",
                })
            });
        });
    }
}());
