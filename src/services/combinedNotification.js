import BillReminderNotification from './billReminderNotification.js';
import CombinedNotificationUtil from '../lib/combinedNotificationUtil.js';

import NOTIFICATION from '../models/notification'
import CATALOGVERTICALRECHARGE from '../models/catalogVerticalRecharge'
import BILLS from '../models/bills'
import nonPaytmBills from "../models/nonPaytmBills";
import utility from '../lib'
import REQUEST from 'request'
import MOMENT from 'moment'
import ASYNC from 'async'
import _ from 'lodash'
import RemindableUsersLibrary from '../lib/remindableUser'
import OS from 'os'
import Q from 'q'
import billsLib from '../lib/bills'
import KafkaConsumerChecks from '../lib/kafkaConsumerChecks';
import EncryptionDecryptioinHelper from '../lib/EncryptionDecryptioinHelper';
import Logger from '../lib/logger';

import EncryptorDecryptor from 'encrypt_decrypt';
const env = (process.env.NODE_ENV || 'development').toLowerCase();

const LOAN_SERVICE = "LOAN";

class CombinedNotification {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.dbInstance = options.dbInstance;
        this.activePidLib = options.activePidLib;
        this.notification = new NOTIFICATION(options);
        this.catalogVerticalRecharge = new CATALOGVERTICALRECHARGE(options);
        this.bills = new BILLS(options);
        this.nonPaytmBillsModel  = new nonPaytmBills(options);
        this.operatorTableMap = _.get(this.config, 'OPERATOR_TABLE_REGISTRY', {});
        this.operatorNotificationConfig = _.get(this.config, 'OPERATOR_NOTIFICATION_CONFIG', {});
        this.operatorTemplateMappingConfig = _.get(this.config, 'OPERATOR_TEMPLATE_MAPPING');
        this.notificationConfig = _.get(this.config, 'NOTIFICATION');
        this.notificationExpiryPeriod = _.get(this.config, 'OPERATOR_NOT_IN_USE_CONFIG');
        this.operatorsBillsConfig = _.get(this.config, ['RECENT_BILL_CONFIG', 'OPERATORS']);
        this.cvrData = {};
        this.offsetIdMap = {};
        this.authToken = null;
        this.blackListOperators = null;
        this.blackListCustomers = null;
        this.remindableUsersLibrary = new RemindableUsersLibrary(options);
        this.infraUtils = options.INFRAUTILS;
        this.commonLib = new utility.commonLib(options);
        this.categoryId = 1; // Bill Reminder Category which is used in notification table to segregate data
        this.greyScaleEnv = options.greyScaleEnv;
        this.kafkaBatchSize = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'BILL_REMINDER_NOTIFICATION', 'BATCHSIZE'],2) : 100;
        this.allowedTemplatesForPromoCodes = new Set(_.get(this.config, ['DYNAMIC_CONFIG','NOTIFICATION_CONFIG','PROMO_CODES_CONFIG','ALLOWED_TEMPLATE_IDS_FOR_PROMOCODE'],[]));
        this.minPrepaidBalanceCheck = _.get(this.config, ['DYNAMIC_CONFIG', 'PREPAID_BILL_FETCH_CONFIG', 'MIN_BALANCE', 'AMOUNT'], 2000);
        this.ctPromoCodeUtil = new utility.CtPromoCodes();
        this.disabledSourcesList = [];
        this.billsLib = new billsLib(options);
        this.kafkaConsumerChecks = new KafkaConsumerChecks(options);
        this.encryptionDecryptioinHelper = new EncryptionDecryptioinHelper(options);
        this.logger = new Logger(options);
        this.cryptr = new EncryptorDecryptor();
        /** For CC we may need to disable some Credit card record depending upon its data_source */
        let disabledSources = _.get(this.config,['DYNAMIC_CONFIG','CC_PUBLISHER_CONFIG','SOURCE_DISABLED_FOR_NOTIFY'],{})

        Object.keys(disabledSources).forEach(source=>{
            if(disabledSources[source] == 1){
                this.disabledSourcesList.push(source)
            }
        })

        setInterval(() => {
            this.initializeVariable();
        }, _.get(this.config, 'COMMON.INTERVAL_FOR_REINITIALIZE', 15) * 60 * 1000);
        
        // Create utility instance of BillReminderNotification for reusing functions
        this.utilityInstance = new BillReminderNotification(options);
        
        // Create utility instance of CombinedNotificationUtil for validation functions
        this.combinedNotificationUtil = new CombinedNotificationUtil(options);
        
        this.logger.info('Logger initialized for service: combinedNotification');
    }

    // Class-level constant for dummy values as specified in requirements
    DUMMY_VALUE = "dummy";

    /**
     * This method will intialize the dynamic operator config specific variables after certain interval
     */
    initializeVariable() {
        let self = this;
        self.L.log("initializeVariable :: combinedNotification", "Re-initializing variable after interval");
        self.operatorsBillsConfig = _.get(this.config, ['RECENT_BILL_CONFIG', 'OPERATORS']);
        self.operatorTableMap = _.get(this.config, 'OPERATOR_TABLE_REGISTRY', {});
        self.operatorNotificationConfig = _.get(this.config, 'OPERATOR_NOTIFICATION_CONFIG', {});
        self.operatorTemplateMappingConfig = _.get(this.config, 'OPERATOR_TEMPLATE_MAPPING');
        self.notificationExpiryPeriod = _.get(this.config, 'OPERATOR_NOT_IN_USE_CONFIG');
        self.notificationConfig = _.get(this.config, 'NOTIFICATION');
        self.blackListOperators = _.get(self.notificationConfig, 'BlackListOperator', null);
        self.blackListCustomers = _.get(self.config, ['DYNAMIC_CONFIG','NOTIFICATION_EXCEPTIONS','BLOCK_CUSTOMER_NOTIFICATIONS','CUSTOMER_ID'],null);
        
        /** For CC we may need to disable some Credit card record depending upon its data_source */
        let disabledSources = _.get(self.config,['DYNAMIC_CONFIG','CC_PUBLISHER_CONFIG','SOURCE_DISABLED_FOR_NOTIFY'],{})

        self.disabledSourcesList = []

        Object.keys(disabledSources).forEach(source=>{
            if(disabledSources[source] == 1){
                self.disabledSourcesList.push(source)
            }
        })
    }

    start() {
        let self = this;
        self.L.log("start", "CombinedNotification create service started");

        self.blackListOperators = _.get(self.notificationConfig, 'BlackListOperator', null);
        self.blackListCustomers = _.get(self.config, ['DYNAMIC_CONFIG','NOTIFICATION_EXCEPTIONS','BLOCK_CUSTOMER_NOTIFICATIONS','CUSTOMER_ID'],null);
        self.operatorsSendingNotificationToRegisteredUser = _.get(self.notificationConfig, 'registeredUserNotificationOperator', null);
        
        // set cvr data
        self.cvrData = _.get(self.config, 'CVR_DATA', {});

        if (_.isEmpty(self.cvrData)) {
            self.L.critical('CombinedNotification service: CVR data is empty');
            process.exit(0);
        }
        ASYNC.waterfall([
            (callback) => {
                // initialize kafka consumer
                self.L.log('start', 'Going to configure Kafka');
                self.configureKafka(function (error) {
                    if (error) {
                        self.L.critical('CombinedNotification :: start', 'unable to configure kafka', error);
                        callback(error);
                    }
                    else {
                        self.L.log('CombinedNotification :: start', 'Kafka Configured successfully !!');
                        callback(null);
                    }
                });
            }
        ], (error, result) => {
            if(error){
                this.L.critical('CombinedNotification :: start', 'Error:', error);
                // exiting in case of error
                process.exit(0);
            }
        });
    }

    configureKafka(done) {

        /**
         * maintain this sequence
         * 1) Initialize all publisher
         * 2) Initialize all consumers
         */
        let self = this;
        ASYNC.waterfall([
            next => {
                self.L.log('configureKafkaPublisher', 'Going to initialize Kakfa Publisher');
                return self.utilityInstance.notify.configureKafkaPublisher(next);
            },
            next => {
                self.L.log('configureKafka', 'Going to initialize Kakfa Consumer for topic : COMBINED_NOTIFICATION');

                // Initialize consumer of topic COMBINED_NOTIFICATION
                self.kafkaBillFetchConsumer = new self.infraUtils.kafka.consumer({
                    "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.REMINDER_COMBINED_CONFIG.HOSTS'),
                    "groupId": "combined-notification-consumer",
                    "topics": _.get(self.config.KAFKA, 'SERVICES.REMINDER_COMBINED_CONFIG.COMBINED_NOTIFICATION'),
                    "id": 'combinedNotificationConsumer_' + OS.hostname(),
                    "fromOffset": "earliest",
                    "autoCommit": false,
                    "batchSize": self.kafkaBatchSize
                });

                self.kafkaBillFetchConsumer.initConsumer(self.execSteps.bind(self), (error) => {
                    if (!error)
                        self.L.log("configureKafka", "consumer of topic : COMBINED_NOTIFICATION Configured");
                    return next(error);
                });
                
            }
        ], function (error) {
            if (error) {
                self.L.critical('configureKafka', 'Could not initialize Kafka', error);
            }
            return done(error);
        });
    }

    execSteps(records) {
        let self = this,
            chunkSize = _.get(self.config, ['NOTIFICATION', 'notificationapi', 'BR_CHUNKSIZE'], 50),
            currentPointer = 0, lastMessage,processingTimeout;
      
        let startTime = new Date().getTime();
        

        if (records && _.isArray(records)) {
            lastMessage = records[records.length - 1];
            self.kafkaBillFetchConsumer._pauseConsumer();
            utility._sendMetricsToDD(records.length, [
                'STATUS:CONSUMED',
                'TOPIC:' + _.get(self.config.KAFKA, 'SERVICES.REMINDER_COMBINED_CONFIG.COMBINED_NOTIFICATION'),
                'REQUEST_TYPE:COMBINED_NOTIFICATION_CONSUMER'
            ])
        } else {
            self.L.critical('execSteps', `No valid kafka records found. Received records-`, records);
            return;
        }

        self.L.log('execSteps:: ', `Processing ${records.length} Bill Fetch data !!`);

        ASYNC.whilst(
            () => {
                return currentPointer < records.length;
            },
            (callback) => {
                let nextChunk = records.slice(currentPointer, (currentPointer + chunkSize));
                currentPointer += chunkSize;
                self.processBatch(nextChunk, () => {
                    setTimeout(() => {
                        callback();
                    }, 100);
                });
            },
            (err) => {
                try{
                    
                    self.kafkaConsumerChecks.findOffsetDuplicates("CombinedNotification", records);
                    
                    self.kafkaBillFetchConsumer.commitOffset(lastMessage, (error) => {
                        if (error) {
                            self.L.error('execSteps::', 'Error while commit for-', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                        }
                        else {
                            self.L.log('execSteps::', 'Commit success for offset :', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                        }
    
                        // Resume consumer now
                        let endTime = new Date().getTime();
                        let executionTime = (endTime - startTime) / 1000;      //in seconds
                        executionTime = Math.round(executionTime);
                        self.L.log('execSteps::', 'per batchSize record Execution time :', executionTime, 'seconds', 'recordLength :',records.length);
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_STATUS", 'STATUS:SUCCESS', "SOURCE:COMBINED_NOTIFICATION", "TIME_TAKEN:" + executionTime]);
                      

                        if(self.greyScaleEnv) {
                            setTimeout(function(){
                                self.kafkaBillFetchConsumer._resumeConsumer();
                            },_.get(self.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'COMBINED_NOTIFICATION', 'BATCH_DELAY'],10));
                        } else {
                            self.kafkaBillFetchConsumer._resumeConsumer();
                        }
                    });
                } catch (err){
                    self.L.error('execSteps::', 'Error while commit for-', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                    self.kafkaBillFetchConsumer._resumeConsumer();
                }
            }
        );
    }

    processBatch(records, done) {
        let self = this;
        ASYNC.map(
            records,
            (record, next) => {
                self.processNotification(() => {
                    next();
                }, record);
            },
            err => {
                done();
            }
        )
    }

    processNotification(done, data) {
        let self = this,
            record=null, tableName,
            debugKey = data;

        utility.sendNotificationMetricsFromCreate({},{},"COMBINED_RECVD")
        ASYNC.waterfall([
            (callback) => {
                self.validateDataToProcessForNotification(function (error, result) {
                    record = result;
                    if (error) {
                        return callback(`Unable to validate data, error - ${error}`);
                    } else {
                        return callback();
                    }
                }, data);
            },
            (callback) => {
                self.L.log('2. processNotification', `Processing notification for ${_.get(record, 'debugKey', null)}`);
                tableName = _.get(self.config, ['OPERATOR_TABLE_REGISTRY', _.get(record, 'product_id', null)], null) ||
                    _.get(self.config, ['OPERATOR_TABLE_REGISTRY', _.get(record, 'operator', null)], null);

                    if(_.toLower(_.get(record, 'service', ''))=='paytm postpaid'){
                        tableName='bills_paytmpostpaid'
                    }

                    if(_.get(record, 'notificationType', null) == 'PREPAID_LOW_BALANCE'){
                        tableName = tableName + '_prepaid'
                    }

                    self.L.log('2. processNotification', `Processing notification tableName is found as ${tableName}`);

                    self.prepareNotification((error) => {
                        if (error) {
                            return callback(error, false);
                        } else {
                            return callback(null, true);
                        }
                        
                    }, record, tableName);

                //callback(null, false);
            }], (error) => {
                if (error) {
                    return self.utilityInstance.notify.insertRejectedNotifications(done, self.utilityInstance.billsLib.createErrorMessage(error), record, null);
                } else {
                    return done();
                }
            }
        );
    }

    validateDataToProcessForNotification(callback, kafkaPayload) {
        return this.combinedNotificationUtil.validateDataToProcessForNotification(callback, kafkaPayload);
    }

    convertKafkaPayloadToRecord(kafkaPayload) {
        return this.combinedNotificationUtil.convertKafkaPayloadToRecord(kafkaPayload);
    }

    async prepareNotification(callback, kafkaPayload, tableName) {
        let self = this;
        self.L.log('prepareNotification :: Processing combined notification record');

        self.L.log('=== COMBINED NOTIFICATION RECORD ===');
        self.L.log(JSON.stringify(kafkaPayload, null, 2));
        self.L.log('=== END RECORD ===');

        self.L.log('=== debug0000 ===');

        try {
            let record = _.get(kafkaPayload, 'data', null);
            // Extract combined data from record
            const combinedData = _.get(record, 'combined_data', []);
            const service = _.get(record, 'service', null);
            const customerId = _.get(record, 'customer_id', null);

            _.set(record, "source", _.get(kafkaPayload, "source", -1));
            _.set(record, "product_id", -1);    //dummy product_id as it is part of primary key notification table.
            _.set(record, "recharge_number", service); 

            self.L.log('record == ' + JSON.stringify(record, null, 2));

            self.L.log('=== debug1111 ===');

            if (!combinedData || !Array.isArray(combinedData) || combinedData.length === 0) {
                return callback('Invalid or empty combined_data');
            }

            if (!customerId) {
                return callback('customer_id is required');
            }
            // Use first item for basic record structure (dummy values as specified)
            const firstItem = combinedData[0];
            
            // Create payLoad following billReminderNotification pattern
            
            let payLoad = {
                recharge_number: service,
                operator: service,
                service: service,
                customer_id: _.get(record, 'customer_id', null),
                paytype: _.get(record, 'paytype', self.DUMMY_VALUE),
                time_interval:  _.get(record, 'time_interval', null),
                refId : _.get(record, "refId", null),
                rtspId : _.get(record, "rtspId", null),
                
            };
            self.L.log('=== debug2222 ===');

            // Get emoji data from config and extend payLoad (following billReminderNotification pattern)
            let emojiData = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'EMOJI_JAVA_ESCAPE_CODES'], null);
            if (emojiData) {
                _.extend(payLoad, emojiData);
            }

            if(combinedData) {
                _.set(payLoad, 'combinedParams', combinedData);
                
                // Calculate additional statistics from combined data
                let combinedStats = self.combinedNotificationUtil.calculateCombinedDataStats(combinedData);
                _.set(payLoad, 'totalDueAmount', combinedStats.totalDueAmount);
                _.set(payLoad, 'billCount', combinedStats.billCount);
                _.set(payLoad, 'minDueDate', combinedStats.minDueDate);
                _.set(payLoad, 'maxDueDate', combinedStats.maxDueDate);
            }

            self.L.log('=== debug2222 000 === emojiData = ' + JSON.stringify(emojiData, null, 2));

            // Set notification type for combined notifications
            const notificationType = _.get(kafkaPayload, 'notificationType', 'COMBINED');

            self.L.log('=== debug2222 1111 ===' + notificationType);

            // Get templates using utility instance
            let templates = self.utilityInstance.getTemplates(record, notificationType, payLoad, tableName);
            self.L.log('=== debug3333 === templates = ' + JSON.stringify(templates, null, 2));

            // Create notification records - only PUSH as specified
            let notificationRecords = [
                {
                    type: 'PUSH',
                    recipients: _.get(record, 'customer_id', null) ? (_.get(record, 'customer_id', null)).toString() : null,
                    notificationType: notificationType,
                    template_id: _.get(templates, 'PUSH', null)
                }
            ];
            self.L.log('=== debug4444 ===');

            // Process each notification record
            for (let notificationRecord of notificationRecords) {
                if (_.get(notificationRecord, 'template_id', null)) {
                    await new Promise((resolve, reject) => {
                        self.sendNotification(record, notificationRecord, payLoad, (err) => {
                            if (err) {
                                self.L.error('prepareNotification :: Error in sendNotification:', err);
                                reject(err);
                            } else {
                                resolve();
                            }
                        });
                    });
                } else {
                    self.L.log('prepareNotification :: No template_id found for notification type:', _.get(notificationRecord, 'type'));
                }
            }

            callback(null, 'Combined notification processed successfully');

        } catch (error) {
            self.L.error('prepareNotification :: Error processing combined notification:', error);
            callback(error.message || error);
        }
    }

    sendNotification(record, notificationRecord, payLoad, callback) {
        let self = this;

        self.L.log('=== debug5555 ===');


        if (!_.get(notificationRecord, 'recipients', null) || !_.get(notificationRecord, 'template_id', null)) {
            self.L.error('sendNotification :: Invalid recipients or template_id');
            return callback('Invalid recipients or template_id');
        }
        self.L.log('=== debug66666 ===');

        // Only handle PUSH notifications as specified
        if (_.get(notificationRecord, 'type', null) !== 'PUSH') {
            return callback('Only PUSH notifications are supported for combined notifications');
        }

        self.L.log('=== debug77777===');

        try {
            let deepLinkData = {};
            let url_type = "external";
            let landing_path = _.get(self.config, ['NOTIFICATION', 'category_url_type_map', _.get(payLoad, 'category_id', null)], null);
            
            // Get deeplink configuration
            let deeplink_url = _.get(self.config, ['NOTIFICATION', 'notificationapi', 'DIGITAL_DEEPLINK_URL'], null);
            let deeplink_api = _.get(self.config, ['NOTIFICATION', 'notificationapi', 'DEEPLINK_API'], null) + "/" + _.get(payLoad, 'category_id', null); // Using dummy category

            let product_service = _.get(payLoad, 'service', null) != null ? _.get(payLoad, 'service', null).toLowerCase() : null;
            let paytype = _.get(payLoad, 'paytype', null) != null ? _.get(payLoad, 'paytype', null).toLowerCase() : null;
            let short_operator_name = _.get(payLoad, "short_operator_name", self.DUMMY_VALUE);
            let template_id = _.get(notificationRecord, "template_id", null);
            let operator = _.get(payLoad, "operator", self.DUMMY_VALUE);
            
            // Get UTM parameters using utility function
            let utm = this.utilityInstance._utmByTemplateId(template_id, short_operator_name, '$', product_service, operator);
            if (!landing_path) {
                if (product_service === 'mobile') {
                    landing_path = "mobile_postpaid";
                } else if (product_service === 'dth') {
                    landing_path = "dth";
                } else {
                    landing_path = 'utility';
                }
            }

            // Create base URL
            let url = `paytmmp://${landing_path}?url=${deeplink_url}${deeplink_api}`;

            // Get parameters for URL using utility function
            let paramsForUrl = this.utilityInstance.getParamsForChatAndPush(record, payLoad);
            
            // Create complete URL using utility functions
            let completeUrl = url + "?" + this.utilityInstance.getQueryParams(paramsForUrl, '$') + 
                             this.utilityInstance.getExtraRechargeNum(record, '$') + utm;

            // Append promo code using utility function
            this.utilityInstance.appendPromoCodeInNotification(payLoad, notificationRecord, (promoCampaignErr, promoCampaign) => {
                if (promoCampaignErr == null) {
                    if (_.get(promoCampaign, "promo_text", null) != null && _.get(promoCampaign, "promocode", null) != null) {
                        if (completeUrl.includes("utm_campaign")) {
                            completeUrl = completeUrl + `_P-${_.get(promoCampaign, "promocode")}`;
                        }
                        _.set(payLoad, "promo_content", _.get(promoCampaign, "promo_text", null));
                        _.set(payLoad, "promo_code", _.get(promoCampaign, "promocode", null));
                        self.L.log(`appendPromoCodeInNotification :: URL: ${completeUrl} and promo_content: ${_.get(payLoad,'promo_content')}`);
                    }
                }

                // Create deeplink data structure
                deepLinkData = {
                    "payLoad": payLoad,
                    "extra": {
                        "url": completeUrl,
                        "url_type": url_type
                    },
                    "paramsForUrl": paramsForUrl
                };

                // Get push notification data using utility function
                let pushNotificationData = this.utilityInstance.notificationLibrary.getPushNotiData(deepLinkData, notificationRecord, 1);

                // Send processed notification using utility function
                self.L.log('combined notification final pushNotificationData = ' + JSON.stringify(pushNotificationData, null, 2));
                this.utilityInstance.sendProcessedNotification(record, pushNotificationData, notificationRecord, callback);
                //return callback(null, pushNotificationData);
            });

        } catch (error) {
            self.L.error('sendNotification :: Error creating notification:', error);
            callback(error.message || error);
        }
    }

    // Utility functions that leverage billReminderNotification functions
    getTemplateId(type, record, notificationType, payload = {}, dueDate, tableName) {
        if (this.utilityInstance) {
            return this.utilityInstance.getTemplateId(type, record, notificationType, payload, dueDate, tableName);
        }
        throw new Error('Utility instance not available');
    }

    replaceUrlVariables(urlTemplate, paramsForUrl) {
        if (this.utilityInstance) {
            return this.utilityInstance.replaceUrlVariables(urlTemplate, paramsForUrl);
        }
        throw new Error('Utility instance not available');
    }

    getParamsForChatAndPush(record, payLoad) {
        if (this.utilityInstance) {
            return this.utilityInstance.getParamsForChatAndPush(record, payLoad);
        }
        throw new Error('Utility instance not available');
    }
    
    // Graceful shutdown method
    suspendOperations(){
        var self = this,
        deferred = Q.defer();
        self.L.log(`combinedNotification::suspendOperations kafka consumer shutdown initiated`);
        Q()
        .then(function(){
            self.kafkaBillFetchConsumer.close(function(error, res){
                if(error){
                    self.L.error(`combinedNotification::stopConsumer :: Error Stopping Consumer Err : ${error}`);
                    return error;
                }
                self.L.info(`combinedNotification::stopConsumer :: Consumer Stopped!  Response : ${res}`);
            })
        })
        .then(function(){
            self.L.log(`combinedNotification::suspendOperations kafka consumer shutdown successful`);
            deferred.resolve();
        })
        .catch(function(err){
            self.L.error(`combinedNotification::suspendOperations error in shutting kafka consumer`, err);
            deferred.reject(err);
        });
        return deferred.promise;
    }
}

export default CombinedNotification; 