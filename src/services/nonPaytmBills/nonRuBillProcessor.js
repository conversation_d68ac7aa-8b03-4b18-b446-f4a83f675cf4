import _ from 'lodash';
import MOMENT from 'moment';
import EncryptorD<PERSON>ryptor from 'encrypt_decrypt';
import utility from '../../lib/index.js'
import AnalyticsHandler from './AnalyticsHandler.js';
import NonPaytmBillsModel from '../../models/nonPaytmBills.js';
import BillDataValidator from './nonRuBillDataValidator.js';
import BillRecordComparator from './nonRuBillRecordComparator/BillRecordComparatorManager.js';

// Import all preprocessing strategies directly
import MobilePostpaidStrategy from './nonRuPreProcessingStrategy/MobilePostpaidStrategy.js';
import MobilePrepaidStrategy from './nonRuPreProcessingStrategy/MobilePrepaidStrategy.js';
import ElectricityStrategy from './nonRuPreProcessingStrategy/ElectricityStrategy.js';
import CreditCardStrategy from './nonRuPreProcessingStrategy/CreditCardStrategy.js';
import DTHStrategy from './nonRuPreProcessingStrategy/DTHStrategy.js';
import FasTagStrategy from './nonRuPreProcessingStrategy/FasTagStrategy.js';
import RentPreProcessingStrategy from './nonRuPreProcessingStrategy/RentPreProcessingStrategy.js';
import DefaultStrategy from './nonRuPreProcessingStrategy/DefaultStrategy.js';
import UpsertStrategy from './nonRuDatabaseEventStrategy/UpsertStrategy.js';
import FastagOperatorUpsertStrategy from './nonRuDatabaseEventStrategy/FastagUpsertStrategy.js';
import DeleteStrategy from './nonRuDatabaseEventStrategy/DeleteStrategy.js';
import FindAndCreateStrategy from './nonRuDatabaseEventStrategy/FindAndCreateStrategy.js';
import FindAndUpdateStrategy from './nonRuDatabaseEventStrategy/FindAndUpdateStrategy.js';

/**
 * Enhanced BillPreprocessor that combines data processing, validation, preprocessing, and database event strategy management
 * Eliminates the need for separate BillDataProcessor, BillPreprocessingStrategyFactory, and DatabaseEventStrategy
 */
class BillPreprocessor {
    constructor(options) {
        this.config = options.config;
        this.L = options.L;
        this.analyticsManager = new AnalyticsHandler(options);
        this.validator = new BillDataValidator(options);

        // Initialize preprocessing strategies directly (eliminating factory pattern)
        this.preprocessingStrategies = new Map();
        this.defaultStrategy = null;
        this._initializePreprocessingStrategies(options);

        // Initialize database event strategies
        this.databaseEventStrategies = new Map();
        this._initializeDatabaseEventStrategies(options);

        this.L.log('BillPreprocessor', 'Initialized with integrated bill processing, preprocessing, and database event strategies');
        this.L.log('BillPreprocessor', `Initialized with ${this.preprocessingStrategies.size} preprocessing strategies: ${Array.from(this.preprocessingStrategies.keys()).join(', ')}`);
        this.L.log('BillPreprocessor', `Initialized with ${this.databaseEventStrategies.size} database event strategies: ${Array.from(this.databaseEventStrategies.keys()).join(', ')}`);
    }

    /**
     * Initialize all available preprocessing strategies
     * @private
     */
    _initializePreprocessingStrategies(options) {
        // Create strategy instances
        const strategies = [
            new MobilePostpaidStrategy(options),
            new MobilePrepaidStrategy(options),
            new ElectricityStrategy(options),
            new CreditCardStrategy(options),
            new DTHStrategy(options),
            new FasTagStrategy(options),
            new RentPreProcessingStrategy(options)
        ];

        // Register strategies with their service and paytype combinations
        strategies.forEach(strategy => {
            const key = this._createStrategyKey(strategy.getServiceType(), strategy.getPaytype());
            this.preprocessingStrategies.set(key, strategy);

            this.L.log('BillPreprocessor',
                `Registered preprocessing strategy: ${strategy.constructor.name} for key: ${key}`);
        });

        // Set default strategy
        this.defaultStrategy = new DefaultStrategy(this);
        this.L.log('BillPreprocessor',
            `Registered default preprocessing strategy: ${this.defaultStrategy.constructor.name}`);
    }

    /**
     * Initialize all available database event strategies
     * @private
     */
    _initializeDatabaseEventStrategies(options) {
        const strategies = {
            'upsert': new UpsertStrategy(options),
            'upsertWithRead': new FastagOperatorUpsertStrategy(options),
            'delete': new DeleteStrategy(options),
            'findAndUpdateData': new FindAndUpdateStrategy(options),
            'findAndCreate': new FindAndCreateStrategy(options),
            'updateMultipleRecordsWithSameRN': new UpsertStrategy(options)
        };

        // Register database event strategies
        Object.entries(strategies).forEach(([eventType, strategy]) => {
            this.databaseEventStrategies.set(eventType, strategy);
            this.L.log('BillPreprocessor',
                `Registered database event strategy: ${strategy.constructor.name} for event: ${eventType}`);
        });
    }

    /**
     * Create a strategy key from service and paytype
     * @param {string} service - Service type
     * @param {string} paytype - Payment type
     * @returns {string} Strategy key
     * @private
     */
    _createStrategyKey(service, paytype) {
        return paytype ? `${service}:${paytype}` : service;
    }

    /**
     * Get the appropriate preprocessing strategy for the given service and paytype
     * @param {string} service - Service type
     * @param {string} paytype - Payment type
     * @returns {Object} Strategy instance
     */
    _getPreprocessingStrategy(service, paytype) {
        const key = this._createStrategyKey(service, paytype);
        const strategy = this.preprocessingStrategies.get(key);

        if (strategy) {
            return strategy;
        }

        // Try with service only (for strategies that don't specify paytype)
        const serviceOnlyStrategy = this.preprocessingStrategies.get(service);
        if (serviceOnlyStrategy) {
            return serviceOnlyStrategy;
        }

        // Return default strategy if no specific strategy found
        this.L.log('BillPreprocessor',
            `No specific preprocessing strategy found for service: ${service}, paytype: ${paytype}. Using default strategy.`);
        return this.defaultStrategy;
    }

    /**
     * Get the appropriate database event strategy for the given event type
     * @param {string} dbEvent - Database event type
     * @returns {Object} Strategy instance
     */
    _getDatabaseEventStrategy(dbEvent) {
        const strategy = this.databaseEventStrategies.get(dbEvent);
        if (!strategy) {
            this.L.error('getDatabaseEventStrategy', `No valid database event strategy found for event: ${dbEvent}`);
            throw new Error(`No valid database event strategy found for event: ${dbEvent}`);
        }
        return strategy;
    }

    /**
     * Main method to process bill data - combines validation, preprocessing, and database operations
     * This replaces the functionality of the old BillDataProcessor
     * @param {Object} billsKafkaRow - Bill data to process
     * @returns {Promise<void>}
     */
    async processBillData(billsKafkaRow) {
        const dbEvent = _.get(billsKafkaRow, 'dbEvent');
        // Validate bill data
        const validationResult = this.validator.validateBillsData(billsKafkaRow, dbEvent);
        if (!validationResult.isValid) {
            this.L.log('processBillData', `Validation failed for bill data for debugKey: ${billsKafkaRow.debugKey} with error: ${validationResult.error}`);
            utility.sendNonPaytmBillsMetrics('ERROR', 'INVALID_BILL_DATA', 'VALIDATE_BILL_DATA', billsKafkaRow, validationResult.error);
            return this.analyticsManager.handleInvalidBillData(billsKafkaRow, validationResult.error);
        }
        // Preprocess the bill data
        try {
            billsKafkaRow = await this._preprocessBillData(billsKafkaRow);
        } catch (error) {
            utility.sendNonPaytmBillsMetrics('ERROR', 'INVALID_BILL_DATA', 'PREPROCESS_BILL_DATA', billsKafkaRow, error.message);
            this.L.error('processBillData', `Failed to preprocess bill data for debugKey: ${billsKafkaRow.debugKey} with error: ${error.message}`);
            return this.analyticsManager.handleInvalidBillData(billsKafkaRow, error.message);
        }
        // Process based on database event using strategy pattern
        try {
            const databaseStrategy = this._getDatabaseEventStrategy(dbEvent);
            await databaseStrategy.execute(billsKafkaRow);
        } catch (error) {
            utility.sendNonPaytmBillsMetrics('ERROR', 'INVALID_BILL_DATA', 'DATABASE_EVENT_STRATEGY', billsKafkaRow, error.message);
            this.L.error('processBillData', `Failed to process bill data for debugKey: ${billsKafkaRow.debugKey} with error: ${error}`);
            return this.analyticsManager.handleInvalidBillData(billsKafkaRow, error.message);
        }
    }

    /**
     * Preprocess bill data based on service type using Strategy pattern
     * @param {Object} billsKafkaRow - Bill data to preprocess
     * @returns {Promise<Object>} Preprocessed bill data
     */
    async _preprocessBillData(billsKafkaRow) {
        const service = _.toLower(_.get(billsKafkaRow, 'service', ''));
        const paytype = _.toLower(_.get(billsKafkaRow, 'paytype', ''));
        try {
            // Get the appropriate strategy for this service and paytype
            const strategy = this._getPreprocessingStrategy(service, paytype);
            this.L.log('preprocessBillData',
                `Using preprocessing strategy: ${strategy.constructor.name} for service: ${service}, paytype: ${paytype}, debugKey: ${billsKafkaRow.debugKey}`);
            // Execute service-specific preprocessing using the strategy
            return await strategy.process(billsKafkaRow);
        } catch (error) {
            this.L.error('preprocessBillData',
                `Failed to preprocess bill data for service: ${service}, paytype: ${paytype}, debugKey: ${billsKafkaRow.debugKey}, error: ${error}`);
            throw error;
        }
    }

    /**
     * Register a new preprocessing strategy
     * This allows for dynamic addition of new bill types without modifying the core class
     * @param {Object} strategy - Strategy instance to register
     * @throws {Error} If strategy is invalid
     *
     * @example
     * // Adding a new strategy for gas bills
     * const gasStrategy = new GasStrategy(this);
     * this.registerPreprocessingStrategy(gasStrategy);
     */
    registerPreprocessingStrategy(strategy) {
        try {
            if (!strategy || typeof strategy.preprocess !== 'function' || typeof strategy.getServiceType !== 'function') {
                throw new Error('Invalid strategy: must have preprocess() and getServiceType() methods');
            }

            const key = this._createStrategyKey(strategy.getServiceType(), strategy.getPaytype());
            this.preprocessingStrategies.set(key, strategy);

            this.L.log('BillPreprocessor', `Successfully registered new preprocessing strategy: ${strategy.constructor.name} for key: ${key}`);
        } catch (error) {
            this.L.error('BillPreprocessor', `Failed to register preprocessing strategy: ${error}`);
            throw error;
        }
    }

    /**
     * Register a new database event strategy
     * This allows for dynamic addition of new database event types without modifying the core class
     * @param {string} eventType - Database event type
     * @param {Object} strategy - Strategy instance to register
     * @throws {Error} If strategy is invalid
     *
     * @example
     * // Adding a new strategy for archive events
     * const archiveStrategy = new ArchiveStrategy(options);
     * this.registerDatabaseEventStrategy('archive', archiveStrategy);
     */
    registerDatabaseEventStrategy(eventType, strategy) {
        try {
            if (!strategy || typeof strategy.execute !== 'function') {
                throw new Error('Invalid strategy: must have execute() method');
            }

            this.databaseEventStrategies.set(eventType, strategy);

            this.L.log('BillPreprocessor', `Successfully registered new database event strategy: ${strategy.constructor.name} for event: ${eventType}`);
        } catch (error) {
            this.L.error('BillPreprocessor', `Failed to register database event strategy: ${error}`);
            throw error;
        }
    }
}

export default BillPreprocessor;