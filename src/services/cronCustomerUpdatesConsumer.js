import _        from 'lodash';
import <PERSON>YNC    from 'async';
import MOMEN<PERSON>   from 'moment';
import OS from 'os';

import utility from '../lib';
import KafkaConsumer from '../lib/KafkaConsumer';
import KafkaConsumerChecks  from '../lib/kafkaConsumerChecks';
import cassandraBills       from '../models/cassandraBills'

const DATE_FORMAT = "YYYY-MM-DD HH:mm:ss";

class CronCustomerUpdatesConsumer {
    constructor(options) {
        let self = this;
        self.L = options.L;
        self.config = options.config;
        self.kafkaConsumerChecks = new KafkaConsumerChecks(options);
        self.cassandraBills = new cassandraBills(options);
        self.infraUtils = options.INFRAUTILS;
        self.kafkaResumeTimeout = self.greyScaleEnv ? _.get(self.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'CONSUMER_WHATSAPP_CUSTOMER_WHITELIST_INGESTOR', 'BATCH_DELAY'], 5*60*1000) : 500;
    }
    start() {
        let self = this;
        self.L.log('cronCustomerUpdatesConsumer::', `Starting service`);
        ASYNC.waterfall([
            
            (next) => {
                try {
                    self.consumer = new KafkaConsumer({
                        "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.REMINDER_KAFKA_CLUSTER.HOSTS'), 
                        "groupId": 'customerUpdatesConsumer',
                        "topics": _.get(self.config.KAFKA, 'TOPICS.CUSTOMER_UPDATES.TOPIC', 'customer-updates'),
                        "id": "customer-updates-consumer-" + OS.hostname(),
                        "maxBytes" : _.get(self.config,'DYNAMIC_CONFIG.KAFKA_CONFIG.CUSTOMER_UPDATES.BATCHSIZE',1000000),
                        sessionTimeout:_.get(self.config,'DYNAMIC_CONFIG.KAFKA_CONFIG.SESSION_TIMEOUT',2*60*1000),
                        maxProcessTimeout:_.get(self.config,'DYNAMIC_CONFIG.KAFKA_CONFIG.MAX_PROCESS_TIMEOUT.CUSTOMER_UPDATES',30*60*1000)
                    });
        
                    self.consumer.initConsumer(self._processKafkaData.bind(self), (error) => {
                        if (error)
                            self.L.critical("customerUpdatesConsumer:: consumer Configured cannot start.", error);
                        else if (!error)
                            self.L.log("customerUpdatesConsumer:: consumer Configured");
                        return next(error);
                    });
                } catch (error) {
                    return next(error)
                }
            }
        ], (error) => {
            if (error) {
                self.L.critical('customerUpdatesConsumer :: start', 'Error while starting service...', error);
                process.exit(0)
            } else {
                self.L.log('customerUpdatesConsumer:: start', 'Service started....');
            }
        })
    }
    _processKafkaData(records, resolveOffset, topic, partition, cb) {
        let startTime = new Date().getTime();
        let self = this;
        let chunkSize = _.get(self.config, ['DYNAMIC_CONFIG', 'CONSUMER_CUSTOMER_UPDATES', 'COMMON', 'CHUNKSIZE'], 1);
        let lastMessage,
        rowData = null,
        recordsToProcess = [];
        
        if (records && _.isArray(records)) {
            lastMessage = records[records.length -1];
            self.L.log('c::_processKafkaData received data from kafka ', records.length);
        } else {
            self.L.critical('CronCustomerUpdatesConsumer ::_processKafkaData error while reading kafka');
            return cb();
        }
        
        records.forEach(row => {
            if(row && row.value) {
                try {
                    rowData = JSON.parse(row.value);
                    recordsToProcess.push(rowData);
                } catch (error) {
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:CONSUMER_CUSTOMER_UPDATES', 
                        'STATUS:INVALID_JSON_PAYLOAD'
                    ]);
                    self.L.error("CronCustomerUpdatesConsumer::_processKafkaData", "Failed to parse whatsapp customer whitelist ingestion data topic, partition, offset, timestamp ::", row.topic, row.partition, row.offset, row.timestamp + ", " + error);
                }
            } else {
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:CONSUMER_CUSTOMER_UPDATES', 
                    'STATUS:INVALID_PAYLOAD'
                ]);
                self.L.error("CronCustomerUpdatesConsumer::_processKafkaData", "Unable to get valid data from kafka topic, partition, offset, timestamp ::", row.topic, row.partition, row.offset, row.timestamp);
            }
        });

        self.L.log('cronCustomerUpdatesConsumer::_processKafkaData', `Processing ${recordsToProcess.length} out of ${records.length} data !!`);
        
        ASYNC.eachLimit(recordsToProcess, chunkSize, self.processCustomerUpdatesRecord.bind(self), async (err) => {
            self.L.log('cronCustomerUpdatesConsumer::_processKafkaData', 'Processing completed !!');
            
            self.kafkaConsumerChecks.findOffsetDuplicates("cronCustomerUpdatesConsumer", records,topic , partition);
            
            if (err) {
                self.L.error("cronCustomerUpdatesConsumer::_prepareDataToInsert Error: ", err );
                setTimeout(() => {
                    // Resume consumer now
                    return cb();
                }, self.kafkaResumeTimeout);
            } else {
                await resolveOffset(lastMessage.offset)
                self.L.log('_processKafkaData::', 'Commit success for offset :', _.get(lastMessage, 'offset') + ', topic :' + topic + ', partition :' + partition + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                recordsToProcess = [];
                let endTime = new Date().getTime();
                let executionTime = (endTime - startTime) / 1000;      //in seconds
                executionTime = Math.round(executionTime);
                self.L.log('execSteps::', 'per chunkSize record Execution time :', executionTime, 'seconds ',records.length);
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:CONSUMER_STATUS', 
                    'STATUS:SUCCESS', 
                    "SOURCE:CRON_CUSTOMER_UPDATES", 
                    "TIME_TAKEN:" + executionTime
                ]);
                setTimeout(() => {
                    // Resume consumer now
                    return cb();
                }, self.kafkaResumeTimeout);
            }
        });
    } 
    async processCustomerUpdatesRecord(record, done) {
        let self = this;
        let activeUserDays = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'WHATSAPP_NOTIFICATION', 'ACTIVE_USER_DAYS'], 10);

        const customer_id = record["customer_id"];
        const updated_at = record["updated_at"];
        const file_name = record["file_name"] || "";
        const date_folder = record["date_folder"] || "";
        
        if (!customer_id || _.isEmpty(customer_id) || !updated_at || _.isEmpty(updated_at)) {
            self.L.error("processCustomerUpdatesRecord::", "error while processing record:", JSON.stringify(record));
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:CRON_CUSTOMER_UPDATES", 'STATUS:ERROR', 'TYPE:INVALID_RECORD']);
            return done();
        }
        
        try {
            // Check if a record already exists for this customer
            const existingRecords = await self.cassandraBills.getCustomerUpdates(customer_id, activeUserDays);
            
            // Get current timestamp for created_at/modified_at fields
            const currentTimestamp = MOMENT().format(DATE_FORMAT);
            
            if (existingRecords && existingRecords.length > 0) {
                const existingRecord = existingRecords[0];
                
                // Compare the dates - only update if the new updated_at is more recent
                const existingDate = MOMENT(existingRecord.updated_at);
                const newDate = MOMENT(updated_at);
                
                if (newDate.isAfter(existingDate)) {
                    // The new record has a more recent updated_at, so update the existing record
                    self.L.info("processCustomerUpdatesRecord::", 
                        `Updating customer ${customer_id} with newer update date ${updated_at} (previous: ${existingRecord.updated_at})`);
                    
                    // Parameters for update: [updated_at, customer_id]
                    const updateParams = [updated_at, customer_id];
                    
                    await self.cassandraBills.updateCustomerUpdate(updateParams, activeUserDays);
                    
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:CRON_CUSTOMER_UPDATES', 
                        'STATUS:SUCCESS', 
                        'TYPE:CUSTOMER_RECORD_UPDATED'
                    ]);
                } else {
                    // The existing record is already up to date or more recent
                    self.L.info("processCustomerUpdatesRecord::", 
                        `Skipping update for customer ${customer_id} - existing date ${existingRecord.updated_at} is more recent than ${updated_at}`);
                    
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:CRON_CUSTOMER_UPDATES', 
                        'STATUS:SUCCESS', 
                        'TYPE:CUSTOMER_RECORD_SKIPPED'
                    ]);
                }
            } else {
                // No existing record, insert a new one
                self.L.info("processCustomerUpdatesRecord::", `Creating new record for customer ${customer_id} with update date ${updated_at}`);
                
                // Parameters for insert: [customer_id, updated_at]
                // Since we're using TTL, we only need to pass the essential fields
                const insertParams = [customer_id, updated_at];
                
                await self.cassandraBills.insertCustomerUpdate(insertParams, activeUserDays);
                
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:CRON_CUSTOMER_UPDATES', 
                    'STATUS:SUCCESS', 
                    'TYPE:CUSTOMER_RECORD_INSERTED'
                ]);
            }
            
            return done();
        } catch (error) {
            self.L.error("processCustomerUpdatesRecord::", "Error processing record:", error.stack);
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:CRON_CUSTOMER_UPDATES', 
                'STATUS:ERROR', 
                'TYPE:DB_OPERATION_FAILED'
            ]);
            return done(error);
        }
    }
}
export default CronCustomerUpdatesConsumer;