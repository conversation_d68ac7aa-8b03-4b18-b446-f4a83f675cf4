import OS from 'os'
import _ from 'lodash'
import MODELS from '../models'
import MOMENT from 'moment'
import utility from '../lib'
import RemindableUsersLibrary from '../lib/remindableUser'
import NOTIFIER from '../services/notify'
import { resolve } from 'path'
import cassandraBills from '../models/cassandraBills'
import KafkaConsumerChecks from '../lib/kafkaConsumerChecks';

class NotificationFallBackConsumer {

    /**
    * @param { object } options Contains configuration and dependencies
    * @param { object } options.L Paytm Logger (lgr) object
    * @param { object } options.config Local config object
    * @param { object } options.INFRAUTILS Contains util libraries like kafka
    */

    constructor(options) {
        this.greyScaleEnv = options.greyScaleEnv;
        this.L = options.L;
        this.config = options.config;
        this.status_list = _.get(this.config, ['NOTIFICATION', 'status']);
        this.infraUtils = options.INFRAUTILS;
        this.kafkaBatchSize = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'NOTIFICATION_FALLBACK', 'BATCHSIZE'], 2) : 500;
        this.kafkaResumeTimeout = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'NOTIFICATION_FALLBACK', 'BATCH_DELAY'], 5 * 60 * 1000) : 100;
        this.notification = new MODELS.Notification(options);
        this.cassandraDb = new MODELS.CassandraDb(options);
        this.remindableUsersLibrary = new RemindableUsersLibrary(options);
        this.notify = new NOTIFIER(options);
        this.cassandraBills = new cassandraBills(options);
        this.kafkaConsumerChecks = new KafkaConsumerChecks(options);

        this.notifyFallbackTable = 'notification_fallback_events';
        this.fallbackCustomerIdTable = 'fallback_customer_id';
        this.notificationRecordExists = false;
    }

    start() {
        this._initializeNotifyFallbackPubSub((error) => {
            if (error) {
                this.L.error("notificationFallbackConsumer :: start", "Failed to initialize notification fallback service");
            }
        });
    }

    async _initializeNotifyFallbackPubSub(cb) {
        try {
            await this.cassandraDb.createTableIfNotExist(this.notifyFallbackTable, this.getSchema(), ['NOTIFICATION_FALLBACK']);
            this.cassandraDb.createIndexes(this.notifyFallbackTable, this.getIndexingFields(), ['NOTIFICATION_FALLBACK']);

            this.kafkaNotificationServicePublisher = new this.infraUtils.kafka.producer({
                "kafkaHost": this.config.KAFKA.TOPICS.REMINDER_BILLFETCH_PIPELINE.HOSTS
            });

            this.reminderKafkaNotificationServicePublisher = new this.infraUtils.kafka.producer({
                "kafkaHost": this.config.KAFKA.TOPICS.REMINDER_KAFKA_CLUSTER.HOSTS
            });

            this.consumer = new this.infraUtils.kafka.consumer({
                "kafkaHost": _.get(this.config.KAFKA, 'TOPICS.NOTIFICATION_FALLBACK.HOSTS'),
                "groupId": "reminder-notify_fallback-consumer",
                "topics": _.get(this.config.KAFKA, 'SERVICES.NOTIFICATION_FALLBACK.TOPIC'),
                "id": 'reminder_notification_fallback' + OS.hostname(),
                "fromOffset": "earliest",
                "autoCommit": false,
                "batchSize": this.kafkaBatchSize
            });

            this.kafkaNotificationServicePublisher.initProducer('high', (error) => {
                if (error) {
                    this.L.critical("notificationFallbackConsumer :: _initializeNotifyFallbackPubSub", "reminderKafkaNotificationServicePublisher failed while configuring.");
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_FALLBACK", 'STATUS:ERROR', 'TYPE:KAFKA_PRODUCER_1']);
                    return cb(error);;
                }

                this.L.log("notificationFallbackConsumer :: _initializeNotifyFallbackPubSub", "kafkaNotificationServicePublisher Configured");

                this.reminderKafkaNotificationServicePublisher.initProducer('high', (error) => {
                    if (error) {
                        this.L.critical("notificationFallbackConsumer :: _initializeNotifyFallbackPubSub", "reminderKafkaNotificationServicePublisher failed while configuring.");
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_FALLBACK", 'STATUS:ERROR', 'TYPE:KAFKA_PRODUCER_2']);
                        return cb(error);;
                    }

                    this.L.log("notificationFallbackConsumer :: _initializeNotifyFallbackPubSub", "reminderKafkaNotificationServicePublisher Configured");

                    this.notify.configureRejectNotificationKafkaPublisher((error) => {
                        if (error) {
                            this.L.critical('notificationFallbackConsumer:: _initializeNotifyFallbackPubSub', `rejectNotificationKafkaPublisher failed while configuring.`);
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_FALLBACK", 'STATUS:ERROR', 'TYPE:KAFKA_PRODUCER_3']);
                            return cb(error);;
                        }

                        this.L.log("notificationFallbackConsumer :: _initializeNotifyFallbackPubSub", "rejectNotificationkafkaPublisher Configured");
                        
                        this.L.log("Service started on topics::" + _.get(this.config.KAFKA, 'SERVICES.NOTIFICATION_FALLBACK.TOPIC'));
                        this.consumer.initConsumer(this._processNotifyFallbackData.bind(this), (error) => {
                            if (error) {
                                this.L.critical("notificationFallbackConsumer :: _initializeNotifyFallbackPubSub", "Notification Fallback consumer Configured cannot start. ", error);
                                utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_FALLBACK", 'STATUS:ERROR', 'TYPE:KAFKA_CONSUMER']);
                            }
                            else if (!error) {
                                this.L.log("notificationFallbackConsumer :: _initializeNotifyFallbackConsumer", "Notification Fallback consumer Configured");
                            }
                            return cb(error);
                        });
                    });
                });
            });
        } catch (error) {
            return cb(error);
        }
    }

    async startNotifyKafkaProducer(){
        let self = this;
        return new Promise((resolve, reject) => {
            try{
                self.notify.configureKafkaPublisher((error) => {
                    if (error) {
                        this.L.critical('notificationFallbackConsumer:: _initializeNotifyFallbackPubSub', `rejectNotificationKafkaPublisher failed while configuring.`);
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_FALLBACK", 'STATUS:ERROR', 'TYPE:KAFKA_PRODUCER_3']);
                        reject(error);
                    }
                    else {
                        resolve();
                    }
                });
            }
            catch(error){
                reject(error);
            }
        });
    }

    async startNotificationFallbackPublishers(){
        let self = this;
        return new Promise((resolve, reject) => {
            try{
                this.kafkaNotificationServicePublisher = new this.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.REMINDER_BILLFETCH_PIPELINE.HOSTS});

                this.reminderKafkaNotificationServicePublisher = new this.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.REMINDER_KAFKA_CLUSTER.HOSTS});
            
                this.kafkaNotificationServicePublisher.initProducer('high', (error) => {
                    if (error) {
                        this.L.critical("notificationFallbackConsumer :: _initializeNotifyFallbackPubSub", "reminderKafkaNotificationServicePublisher failed while configuring.");
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_FALLBACK", 'STATUS:ERROR', 'TYPE:KAFKA_PRODUCER_1']);
                        reject(error);
                    }
                    this.reminderKafkaNotificationServicePublisher.initProducer('high', (error) => {
                        if (error) {
                            this.L.critical("notificationFallbackConsumer :: _initializeNotifyFallbackPubSub", "reminderKafkaNotificationServicePublisher failed while configuring.");
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_FALLBACK", 'STATUS:ERROR', 'TYPE:KAFKA_PRODUCER_2']);
                            reject(error);
                        }
                        else {
                            resolve();
                        }
                    });
                });
            }
            catch(error){
                reject(error);
            }
        });
    }

    async _processNotifyFallbackData(records) {
        let self = this;
        let lastMessage;
        let startTime = new Date().getTime();
        if (records && _.isArray(records)) {
            lastMessage = records[records.length - 1];
            this.consumer._pauseConsumer();
        } else {
            this.L.critical('notificationFallbackConsumer :: _processNotifyFallbackData', `No valid kafka records found. Received records - `, records);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_FALLBACK", 'STATUS:ERROR', 'TYPE:NO_RECORDS_FOUND']);
            return;
        }

        self.kafkaConsumerChecks.findOffsetDuplicates("NotificationFallBackConsumer", records);

        records.forEach(async (recordObj) => {
            try {
                this.L.log("notificationFallbackConsumer :: _processNotifyFallbackData", `Record payload received:: ${JSON.stringify(recordObj)}\n from Topic: ${recordObj['topic']}\n on Partition: ${recordObj['partition']}\n having Timestamp: ${recordObj['timestamp']}`);//no sensitive fields in the payload
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_FALLBACK", 'STATUS:TRAFFIC', 'TYPE:RECORD_COUNT']);
                if (!recordObj || _.isEmpty(recordObj) || !recordObj['value'] || _.isEmpty(recordObj['value'])) {
                    this.L.log("notificationFallbackConsumer :: _processNotifyFallbackData", "Record payload received is null/empty.");
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_FALLBACK", 'STATUS:ERROR', 'TYPE:RECORD_PAYLOAD_FAILURE']);
                    return;
                }
                let record = JSON.parse(recordObj['value']); // Actual payload to be processed.

                let isFailedEvent = _.get(this.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_FALLBACK_CONFIG', _.get(record, ['eventType'], null), 'FAILED_EVENT'],
                    _.get(this.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_FALLBACK_CONFIG', _.get(record, ['eventType'], '').toLowerCase(), 'FAILED_EVENT'], 'false')) === 'true';

                let isRetryEvent = _.get(this.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_FALLBACK_CONFIG', _.get(record, ['eventType'], null), 'RETRY_EVENT'],
                    _.get(this.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_FALLBACK_CONFIG', _.get(record, ['eventType'], '').toLowerCase(), 'RETRY_EVENT'], 'false')) === 'true';

                if (isFailedEvent || isRetryEvent) {
                    let validatedPayload = await this.validateFallbackPayload(record);
                    if (validatedPayload) {
                        let notificationRecord = validatedPayload[0], templateName = validatedPayload[1], newTemplateId = validatedPayload[2];
                        if (isRetryEvent) {
                            if (notificationRecord['max_retry_count'] >= 3 && notificationRecord['retry_count'] >= notificationRecord['max_retry_count']) {
                                notificationRecord['template_id'] = newTemplateId;
                                utility._sendMetricsToDD(1, [
                                    `REQUEST_TYPE:NOTIFICATION_FALLBACK`,
                                    `STATUS:RETRY_COUNT_EXHAUSTED`,
                                    `TYPE:RETRY_FALLBACK_EVENT`
                                ]);
                                await this.processFallBackPayload(notificationRecord, templateName);
                            }
                            else {
                                await new Promise((resolve, reject) => {
                                    utility._sendMetricsToDD(1, [
                                        "REQUEST_TYPE:NOTIFICATION_FALLBACK", 
                                        'STATUS:RETRY', 
                                        'TYPE:RETRY_FALLBACK_EVENT'
                                    ]);
                                    this.updateNotification(() => {
                                        this.L.log("notificationFallbackConsumer :: _processNotifyFallbackData", "Notification is scheduled for retry for jobId: " + record['jobId']);
                                        resolve();
                                        
                                    }, ['retry_count', 'max_retry_count', 'status', 'retry_interval', 'send_at'],
                                    [notificationRecord['retry_count'] + 1, 3, _.get(this.status_list, 'RETRY_FALLBACK', 9), 15, MOMENT().format("YYYY-MM-DD HH:mm:ss")], 
                                    notificationRecord, _.get(this.status_list, 'RETRY_FALLBACK', 9))
                                });
                            }
                        } else {
                            await new Promise((resolve, reject) => {
                                utility._sendMetricsToDD(1, [
                                    "REQUEST_TYPE:NOTIFICATION_FALLBACK", 
                                    'STATUS:FAILED', 
                                    'TYPE:FAILED_FALLBACK_EVENT'
                                ]);
                                this.updateNotification(() => {
                                    this.L.log("notificationFallbackConsumer :: _processNotifyFallbackData", "Setting Notification's failed fallback status for jobId: " + record['jobId']);
                                    resolve();
                                    
                                }, ['status'], [_.get(this.status_list, 'FAILED_FALLBACK', 8)], notificationRecord, _.get(this.status_list, 'FAILED_FALLBACK', 8));
                            });

                            notificationRecord['template_id'] = newTemplateId;
                            await this.processFallBackPayload(notificationRecord, templateName);
                        }
                    }
                } else {
                    this.L.log("notificationFallbackConsumer :: _processNotifyFallbackData", "No retry/failed event found.");
                }
                await this.insertIntoCassandra(record);
            } catch (ex) {
                this.L.error("notificationFallbackConsumer :: _processNotifyFallbackData", "Error while processing Failed/retry events", ex);
            }
        });

        await new Promise((resolve, reject) =>
            this.consumer.commitOffset(lastMessage, (error) => {
                if (error) {
                    this.L.error('notificationFallbackConsumer :: _processNotifyFallbackData', 'Error while commit for-', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:REALTIME_SMS_PARSING", 'STATUS:ERROR', 'TYPE:OFFSET_COMMIT', `TOPIC:${_.get(lastMessage, 'topic', null)}`, `PARTITION:${_.get(lastMessage, 'partition', null)}`, 'SOURCE:MAIN_FLOW']);
                }
                else {
                    this.L.log('notificationFallbackConsumer :: _processNotifyFallbackData', 'Commit success for offset :', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                }

                let endTime = new Date().getTime();
                let executionTime = (endTime - startTime) / 1000;
                executionTime = Math.round(executionTime);

                this.L.log('notificationFallbackConsumer :: _processNotifyFallbackData ', 'per chunkSize record Execution time: ', executionTime, 'seconds');
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_FALLBACK", 'STATUS:SUCCESS', "TYPE:CONSUMER", "TIME_TAKEN:" + executionTime]);

                setTimeout(() => {
                    this.consumer._resumeConsumer();
                }, this.kafkaResumeTimeout);

                resolve();
            })
        );
    }

    async insertIntoCassandra(record) {
        this.L.info('notificationFallbackConsumer :: insertIntoCassandra', `Insertion in Cassandra started.....`);
        let eventConfig = _.get(this.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_FALLBACK_CONFIG', _.get(record, ['eventType'], null), 'NOTIFY_EVENT'],
            _.get(this.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_FALLBACK_CONFIG', _.get(record, ['eventType'], '').toLowerCase(), 'NOTIFY_EVENT'], null));

        if (!eventConfig) {
            this.L.info('notificationFallbackConsumer :: insertIntoCassandra', `Event ${record['eventType']} not found in event config`);
            return;
        }

        let params = [MOMENT().format("YYYY-MM-DD HH:mm:ss"), record['jobId'], record['eventType']];
        params.push(...eventConfig.split('-'));

        let templateName = _.get(this.config, ['DYNAMIC_CONFIG', 'TEMPLATE_CONFIG', _.get(record, 'templateId', null), 'TEMPLATE_NAME'], null);
        if (!templateName || _.isEmpty(templateName)) {
            this.L.log("notificationFallbackConsumer :: insertIntoCassandra", "Template Id not found");
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_FALLBACK", 'STATUS:VALIDATION_FAILED', 'TYPE:TEMPLATE_ID_NOT_FOUND']);
            return;
        }

        if (this.notificationRecordExists) {
            await this.cassandraDb.insertData(this.notifyFallbackTable,
                ['created_at', 'job_id', 'event', 'status', 'isRetryable'],
                params,
                ['NOTIFICATION_FALLBACK']);
            this.L.info('notificationFallbackConsumer :: insertIntoCassandra', `Insertion in Cassandra ended.....`);
        }
    }

    async getNotificationsByJobId(jobId) {
        let currentDay = new Date().getDate();
        let tableSuffix = Math.floor(currentDay/7) + 1;
        let jobIdNotificationMappingTable = `jobId_notification_mapping${tableSuffix}`;

        let notificationRecordKey = await this.cassandraBills.getJobIdNotificationMappingRecord(jobId, jobIdNotificationMappingTable);
        if (!notificationRecordKey || _.isEmpty(notificationRecordKey) || !notificationRecordKey[0] || _.isEmpty(notificationRecordKey[0])) {
            tableSuffix = tableSuffix === 1 ? 5 : tableSuffix - 1;
            let jobIdNotificationMappingFallbackTable = `jobId_notification_mapping${tableSuffix}`;
            notificationRecordKey = await this.cassandraBills.getJobIdNotificationMappingRecord(jobId, jobIdNotificationMappingFallbackTable);
            if (!notificationRecordKey || _.isEmpty(notificationRecordKey) || !notificationRecordKey[0] || _.isEmpty(notificationRecordKey[0])) {
                this.L.error('notificationFallbackConsumer::getNotificationsByJobId::', `notification record not found against job id ${jobId}`);
                return Promise.resolve(null);
            }
        }

        notificationRecordKey = notificationRecordKey[0];
        let whereCondition = `recharge_number = ? AND recipient = ? AND type = ? AND template_id = ? AND source_id = ? AND category_id = ? AND product_id = ? AND send_at = ?`;
        let queryParams = [
            _.get(notificationRecordKey, 'recharge_number', null), _.get(notificationRecordKey, 'recipient', null), _.get(notificationRecordKey, 'type', null), 
            _.get(notificationRecordKey, 'template_id', null), _.get(notificationRecordKey, 'source_id', null), _.get(notificationRecordKey, 'category_id', null),
            _.get(notificationRecordKey, 'product_id', null), _.get(notificationRecordKey, 'send_at', null)
        ]

        let sendAtDay = MOMENT(_.get(notificationRecordKey, 'send_at', null)).date();
        let notificationTableSuffix = Math.floor(sendAtDay/7) + 1;
        let notificationTable = `notification${notificationTableSuffix}`;

        let notificationRecords = await new Promise((resolve, reject) => {
            this.cassandraBills.getNotifications((err, result) => {
                err ? resolve(null) : resolve(result.rows);
            }, notificationTable, whereCondition, queryParams);
        });

        return Promise.resolve(notificationRecords);
    }

    updateNotification(cb, fields, params, record, status, checkInSql = true) {
        // for push notifications, customerId = recipient
        let sourceId = _.get(record, 'source_id', null);
        let customerId = _.get(record, 'recipient', null);
        let debugKey = `${_.get(record, 'recharge_number', null)}_${_.get(record, 'recipient', null)}_${_.get(record, 'type', null)}_${_.get(record, 'template_id', null)}_${_.get(record, 'source_id', null)}_${_.get(record, 'category_id', null)}_${_.get(record, 'product_id', null)}_${_.get(record, 'send_at', null)}`;
        this.L.log('fbConsumer::updateNotification::', `source id is ${sourceId} and customer id is ${customerId} for record ${debugKey}`);

        if (this.notify.isNotificationMigratedToCassandra(sourceId, customerId)) {
            this.L.log('fbConsumer::updateNotification::', `notification is migrated to cassandra db for sourceId ${sourceId}, customerId ${customerId}, record ${debugKey}`);
            utility._sendMetricsToDD(1, [
                `REQUEST_TYPE:FBCONSUMER_UPDATE_NOTIFICATION`,
                `DATABASE:CASSANDRA`,
                `STATUS:TRAFFIC`,
                `SOURCE_ID: ${_.get(record, 'source_id', 'UNKNOWN')}`
            ]);
            /**
             * if status = retry_fallback
             *  1. delete prev record from cassandra
             *  2. create new record in cassandra with new send_at
             *  3. create or update entry in mysql for retry_fallback
             * 
             * if status = failed_fallback
             *  1. updated record in cassandra
             *  2. delete entry from mysql if present
             */
            let whereCondition = `recharge_number=? AND recipient=? AND type=? AND template_id=? AND source_id=? AND category_id=? AND product_id=? AND send_at=?`;
            let whereConditionParams = [
                _.get(record, 'recharge_number', null), _.get(record, 'recipient', null), _.get(record, 'type', null), _.get(record, 'template_id', null), 
                _.get(record, 'source_id', null), _.get(record, 'category_id', null), _.get(record, 'product_id', null), _.get(record, 'send_at', null)
            ]
            let paramsCassandra =  params.concat(whereConditionParams);

            if (status === _.get(this.status_list, 'RETRY_FALLBACK', 9)) {
                this.cassandraBills.deleteNotification((err, result) => {
                    if (err) {
                        this.L.error('fbconsumer::updateNotification::deleteNotificationCb::', `error occurred while deleting record ${debugKey}`, err);
                    }
                    let isDeleted = _.get(result.rows[0], '[applied]', false);
                    if (isDeleted) {
                        this.L.log('fbconsumer::updateNotification::deleteNotificationCb::', `deleted prev record from cassandra for ${debugKey}`);
                    } else {
                        this.L.critical('fbconsumer::updateNotification::deleteNotificationCb::', `couldn't find prev record in cassandra hence no delete action occurred for ${debugKey}`);
                    }
                    try {
                        record.data = typeof record.data == 'string' ? JSON.parse(record.data) : record.data;
                    } catch(error) {
                        this.L.error('fbConsumer::updateNotification::deleteNotificationCb::', `error while parsing record data for ${JSON.stringify(record)}`, error);
                        return cb();
                    }
                    
                    let updatedRecord = _.cloneDeep(record);
                    _.set(updatedRecord, 'retry_count', params[0]);
                    _.set(updatedRecord, 'max_retry_count', params[1]);
                    _.set(updatedRecord, 'status', params[2]);
                    _.set(updatedRecord, 'retry_interval', params[3]);
                    _.set(updatedRecord, 'send_at', params[4]);
                    _.set(updatedRecord, 'notificationStatus', params[2]);
                    updatedRecord.record_key = `${_.get(updatedRecord, 'recharge_number', null)}_${_.get(updatedRecord, 'recipient', null)}_${_.get(updatedRecord, 'type', null)}_${_.get(updatedRecord, 'template_id', null)}_${_.get(updatedRecord, 'source_id', null)}_${_.get(updatedRecord, 'category_id', null)}_${_.get(updatedRecord, 'product_id', null)}_${_.get(updatedRecord, 'send_at', null)}`;

                    this.notify.createNotificationInCassandra(async (err) => {
                        if (err) {
                            this.L.error('fbconsumer::updateNotification::createNotificationInCassandraCb::', `error occurred during notification create for ${updatedRecord.record_key}`, err);
                            return cb();
                        }
                        let notificationRecordMysql = await this.notification.getNotificationsByJobId(_.get(record, 'job_id', null), 1);
                        let mysqlParams = [].concat(params);
                        if (notificationRecordMysql && notificationRecordMysql.length > 0) {
                            mysqlParams.push(notificationRecordMysql[0].id);
                            this.notification.updateNotification((err, data) => {
                                if (err) {
                                    this.L.error('fbconsumer::updateNotification::', `err occurred while updating notification in mysql for retry for record id ${notificationRecordMysql[0].id}`, err);
                                } else {
                                    this.L.log('fbconsumer::updateNotification::', `updated entry in mysql for retry for record id ${notificationRecordMysql[0].id}`);
                                }
                                return cb();
                            }, fields, 'id=?', mysqlParams);
                        } else {
                            this.notification.createNotificationForRetry((err, data, status) => {
                                if (err) {
                                    this.L.error('fbconsumer::createNotificationForRetryCb::', `error creating record in mysql for retry for record ${updatedRecord.record_key}`);
                                    return cb();
                                }
                                this.L.log('fbconsumer::createNotificationForRetryCb::', `successfully created record in mysql for retry for record ${updatedRecord.record_key}`);
                                return cb();
                            }, updatedRecord);
                        }
                    }, updatedRecord);
                }, whereCondition, whereConditionParams, _.get(record, 'send_at', null));

            } else if (status === _.get(this.status_list, 'FAILED_FALLBACK', 10)) {
                let updatedRecord = _.cloneDeep(record);
                _.set(updatedRecord, 'status', params[0]);
                this.notify.updateNotificationInCassandra(async (err) => {
                    if (err) {
                        this.L.error('fbconsumer::updateNotificationInCassandraCb::', `error while updating notification for record ${debugKey}`);
                    }
                    // delete entry from mysql if present
                    if(checkInSql){
                        let notificationRecordMysql = await this.notification.getNotificationsByJobId(_.get(record, 'job_id', null), 1); 
                        if (notificationRecordMysql && notificationRecordMysql.length > 0) {
                            this.L.log('updateNotificationInCassandraCb::', `found mysql entry for the record with id ${notificationRecordMysql[0].id}, executing delete!`);
                            this.notification.deleteNotification((err) => {
                                if (err) {
                                    this.L.error('fbconsumer::deleteNotificationCb::', `err while deleting entry for record ${debugKey}`, err);
                                }
                                return cb();
                            }, 'id = ?', [notificationRecordMysql[0].id]);
                        } else {
                            return cb();
                        }
                    }else{
                        return cb();
                    }
                }, fields, whereCondition, paramsCassandra, updatedRecord)
            }
        } else {
            utility._sendMetricsToDD(1, [
                `REQUEST_TYPE:FBCONSUMER_UPDATE_NOTIFICATION`,
                `DATABASE:MYSQL`,
                `STATUS:TRAFFIC`,
                `SOURCE_ID: ${_.get(record, 'source_id', 'UNKNOWN')}`
            ]);
            params.push(_.get(record, 'id', null));
            this.notification.updateNotification((err, data) => {
                if (err) {
                    this.L.error('fbconsumer::updateNotificationCb::', `error occurred in updateNotification for record id ${_.get(record, 'id', null)}`, err);
                } else {
                    this.L.log('fbconsumer::updateNotificationCb::', `successfully updated notification in mysql for record id ${_.get(record, 'id', null)}`);
                }
                return cb();
            }, fields, 'id=?', params);
        }
    }

    async validateFallbackPayload(record, skipTemplateNameCheck = false) {
        let templateName = _.get(this.config, ['DYNAMIC_CONFIG', 'TEMPLATE_CONFIG', _.get(record, 'templateId', null), 'TEMPLATE_NAME'], null);
        this.notificationRecordExists = false;
        if ( (!templateName || _.isEmpty(templateName)) && !skipTemplateNameCheck) {
            this.L.log("notificationFallbackConsumer :: validateFallbackPayload", "Template Id not found");
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_FALLBACK", 'STATUS:VALIDATION_FAILED', 'TYPE:TEMPLATE_ID_NOT_FOUND']);
            return Promise.resolve(null);
        }
        
        let notificationRecords;
        notificationRecords = await this.getNotificationsByJobId(record['jobId']);
        if (!notificationRecords || _.isEmpty(notificationRecords)) {
            notificationRecords = await this.notification.getNotificationsByJobId(record['jobId'], 1);
        }
        
        if (!notificationRecords || _.isEmpty(notificationRecords)) {
            this.L.log("notificationFallbackConsumer :: validateFallbackPayload", "Notification Record not found");
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_FALLBACK", 'STATUS:VALIDATION_FAILED', 'TYPE:NOTIFY_RECORD_NOT_FOUND']);
            return Promise.resolve(null);
        }

        let notificationRecord = notificationRecords[0];
        if (notificationRecord && !_.isEmpty(notificationRecord)) {
            this.notificationRecordExists = true;
        }

        if (!notificationRecord || !notificationRecord['template_id'] ||
            notificationRecord['template_id'] != _.get(record, 'templateId', null)) {
            this.L.log("notificationFallbackConsumer :: validateFallbackPayload", "Notification record is null/TemplateId or TemplateName mismatch between notification DB record and fallback payload");
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_FALLBACK", 'STATUS:VALIDATION_FAILED', 'TYPE:TEMPLATE_MISMATCH']);
            return Promise.resolve(null);
        }

        let newTemplateId = _.get(this.config, ['DYNAMIC_CONFIG', 'TEMPLATE_CONFIG', _.get(record, 'templateId', null), 'MAPPED_TEMPLATE_ID'], null);
        if (!newTemplateId || newTemplateId < 1) {
            this.L.log("notificationFallbackConsumer :: validateFallbackPayload", "Template Id mapped against the fallback payload templateId not found.");
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_FALLBACK", 'STATUS:VALIDATION_FAILED', 'TYPE:MAPPED_TEMPLATE_NOT_FOUND']);
            return Promise.resolve(null);
        }
        // notificationRecord['template_id'] = newTemplateId;

        templateName = _.get(this.config, ['DYNAMIC_CONFIG', 'TEMPLATE_CONFIG', newTemplateId, 'TEMPLATE_NAME'], null);
        if (!templateName || _.isEmpty(templateName)) {
            this.L.log("notificationFallbackConsumer :: validateFallbackPayload", "Template name not found for mapped template Id");
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_FALLBACK", 'STATUS:VALIDATION_FAILED', 'TYPE:TEMPLATE_NAME_NOT_FOUND']);
            return Promise.resolve(null);
        }

        return Promise.resolve([notificationRecord, templateName, newTemplateId]);
    }

    async processFallBackPayload(notificationRecord, templateName) {
        let notificationData = JSON.parse(notificationRecord['data']) || {}, newNotificationData = {};

        if (notificationData && !notificationData['dynamicParams'] && !notificationData['notificationReceiver']) {
            let pushNotiData = this.notify.getPushNotiData(notificationData, templateName,
                notificationRecord['category_id'], 'PUSH');
            notificationData = pushNotiData['json'];
        }

        if (!notificationData || _.isEmpty(notificationData)) {
            return Promise.resolve();
        }

        newNotificationData['templateName'] = templateName;
        newNotificationData['dynamicParams'] = notificationData['dynamicParams'];
        newNotificationData['notificationReceiver'] = notificationData['notificationReceiver']
        newNotificationData['dynamicParams']['whatsapp_short_link'] = await this.createTinyUrl(notificationData['extraCommonParams']['url'] || notificationData['messageCentrePush']['extraParams']['url']);
        newNotificationData['additional_data'] = notificationData['additional_data'];

        notificationData = {};// clearing the now irrelevant data from memory

        let customerData = { 'customer_id': _.toInteger(newNotificationData['notificationReceiver']['notificationReceiverIdentifier'][0]) }

        let custResult = await this.cassandraDb.selectData(this.fallbackCustomerIdTable, 'customer_id = ?', [customerData['customer_id']], ['NOTIFICATION_FALLBACK']);

        if (!custResult || custResult.length < 1) {
            this.L.log("notificationFallbackConsumer :: processFallBackPayload",
                `No Notification will be sent to this customer Id: ${customerData['customer_id']} because no records found for fallback customerId: ${customerData.customer_id} in Cassandra db, NotificationRecord :: ${JSON.stringify(notificationRecord)}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_FALLBACK", 'STATUS:FAILED', 'TYPE:CUST_ID_NOT_FOUND']);
            return;
        }

        newNotificationData['notificationReceiver']['notificationReceiverIdentifier'] = [];
        newNotificationData['notificationReceiver']['notificationReceiverType'] = 'MOBILENUMBER';

        return new Promise((resolve, reject) =>
            this.remindableUsersLibrary._getUserDetails(async (err, userDetailsSuccess) => {
                if (!userDetailsSuccess) {
                    this.L.log("notificationFallbackConsumer :: processFallBackPayload", "Unable to fetch user Details");
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_FALLBACK", 'STATUS:RETRY', 'TYPE:RETRY_FALLBACK_EVENT']);
                } else {
                    newNotificationData['notificationReceiver']['notificationReceiverIdentifier'].push(customerData['customer_mobile'])

                    notificationRecord['data'] = newNotificationData;
                    notificationRecord['type'] = 'WHATSAPP';
                    notificationRecord['send_at'] = MOMENT().format("YYYY-MM-DD HH:mm:ss");
                    notificationRecord['created_at'] = notificationRecord['updated_at'] = null

                    await this.createAndPublishNotification(notificationRecord);
                }
                resolve();
            }, customerData));
    }

    async createAndPublishNotification(notificationRecord) {
        let DBStartTime = new Date().getTime();
        return new Promise((resolve, reject) =>
            this.createNotification((error, data, status) => {
                let dbDuration = new Date().getTime() - DBStartTime;
                if (error) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_FALLBACK", 'STATUS:FAILED', 'TYPE:NOTIFICATION_RECORD_CREATION_FAILED']);
                    return resolve();
                }
                this.L.log("notificationFallbackConsumer :: createAndPublishNotification", `Database insertion latency - Querytook - ${dbDuration} milliseconds`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_FALLBACK", 'STATUS:LATENCY', 'TYPE:NOTIFICATION_RECORD_CREATION', 'TIME_TAKEN:' + dbDuration]);
                
                let cassandraKey = `${_.get(notificationRecord,'recharge_number','')}_${_.get(notificationRecord,'recipient','')}_${_.get(notificationRecord,'product_id','')}_${_.get(notificationRecord,'source_id','')}_${_.get(notificationRecord,'category_id','')}_${_.get(notificationRecord,'template_id','')}_${_.get(notificationRecord,'type','')}`;
                let notificationUniqueKey = `${_.get(notificationRecord,'recharge_number','')}_${_.get(notificationRecord,'recipient','')}_${_.get(notificationRecord,'template_id','')}`;
                let cassandraValue = {
                    send_at: _.get(notificationRecord, 'send_at', null),
                    priority: _.get(notificationRecord, 'priority', null),
                    status: _.get(notificationRecord, 'status', null)
                }
                this.notify.setDataInCassandraForDuplicacy({cassandraKey:cassandraKey, notificationUniqueKey:notificationUniqueKey}, cassandraValue, (err, value) => {
                    if (err) {
                        this.L.error('fbConsumer::createAndPublishNotification::', `error updating cassandra cache for key ${cassandraKey}`, err);
                        return resolve();
                    }
                    this.L.log('fbConsumer::createAndPublishNotification::', `updated cassandra cache successfully for key ${cassandraKey}`);
                    
                    _.set(notificationRecord, 'status', status)
                    _.set(notificationRecord, 'cassandraKey', cassandraKey);
                    _.set(notificationRecord, 'notificationUniqueKey', notificationUniqueKey);
                    let preparedKafkaPayload = this.notify.prepareKafkaPayload(notificationRecord, _.get(data, 'insertId', null));
                    
                    let kafkaStartTime = new Date().getTime();
                    
                    let this1 = this;
                    function publisherCallbackFunction(error) {
                        let kafkaDuration = new Date().getTime() - kafkaStartTime;
                        this1.L.log(`kafka v1/notify latency - publish - ${kafkaDuration} milliseconds`);
                        if (error) {
                            this1.L.critical('Error while publishing message in Kafka - MSG:- ' + JSON.stringify(preparedKafkaPayload.payload), error);
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_FALLBACK", 'STATUS:ERROR', 'TYPE:MSG_PUBLISH_FAILED']);
                        } else {
                            this1.L.log('Message published successfully in Kafka', ` on topic ${preparedKafkaPayload.topic}`, JSON.stringify(preparedKafkaPayload.payload));
                            this1.L.log('notificationFallbackConsumer :: createAndPublishNotification::', `STATUS:PUBLISHED_TOPIC:${preparedKafkaPayload.topic}_SOURCE_ID:${_.get(preparedKafkaPayload, 'payload.source_id', 0)}_CATEGORY_ID:${_.get(preparedKafkaPayload, 'payload.category_id', 0)}_TYPE:${_.get(preparedKafkaPayload, 'payload.type', 'NOT_PASSED')}_RECHARGE_NUMBER:${_.get(preparedKafkaPayload, 'payload.recharge_number', '')}`);
                            utility._sendMetricsToDD(1, [
                                'STATUS:SUCCESS',
                                'TYPE:MSG_PUBLISH_SUCCESS',
                                'TOPIC:' + preparedKafkaPayload.topic,
                                'SOURCE_ID:' + _.get(preparedKafkaPayload, 'payload.source_id', 0),
                                'CATEGORY_ID:' + _.get(preparedKafkaPayload, 'payload.category_id', 0),
                                'NOTIFY_TYPE:' + _.get(preparedKafkaPayload, 'payload.type', 'NOT_PASSED')
                            ])
                        }
                        resolve();
                    }
                    
                    var publisherObject = [
                        {
                            topic: preparedKafkaPayload.topic,
                            messages: JSON.stringify(preparedKafkaPayload.payload)
                        }
                    ];
                    
                    if (preparedKafkaPayload.topic.includes('NOTIFICATION_') || preparedKafkaPayload.topic.includes('RT_NOTIFICATION') || preparedKafkaPayload.topic.includes('HEURISTIC_NOTIFICATION')) {
                        this.reminderKafkaNotificationServicePublisher.publishData(publisherObject, publisherCallbackFunction, [200, 800]);
                    }
                    else {
                        this.kafkaNotificationServicePublisher.publishData(publisherObject, publisherCallbackFunction, [200, 800]);
                    }
                })
            },
                notificationRecord)
        );
    }

    createNotification(cb, record) {
        if (this.notify.isNotificationMigratedToCassandra(_.get(record, 'source_id', null), _.get(record, 'recipient', null))) {
            record['retry_count'] = 0;
            record['job_id'] = null;
            record['sent_at'] = null;
            record['error_msg'] = null;
            this.notify.createNotificationInCassandra((err) => {
                if (err) {
                    utility._sendMetricsToDD(1, [
                        `REQUEST_TYPE:FBCONSUMER_CREATE_NOTIFICATION`,
                        `DATABASE:CASSANDRA`,
                        `STATUS:ERROR`,
                        `SOURCE_ID:${_.get(record, 'source_id', 'UNKNOWN')}`
                    ]);
                } else {
                    utility._sendMetricsToDD(1, [
                        `REQUEST_TYPE:FBCONSUMER_CREATE_NOTIFICATION`,
                        `DATABASE:CASSANDRA`,
                        `STATUS:SUCCESS`,
                        `SOURCE_ID:${_.get(record, 'source_id', 'UNKNOWN')}`
                    ]);
                }
                cb(err, null, _.get(record, 'status', null));
            }, record);
        } else {
            this.notification.createNotification((err, data, status) => {
                if (err) {
                    utility._sendMetricsToDD(1, [
                        `REQUEST_TYPE:FBCONSUMER_CREATE_NOTIFICATION`,
                        `DATABASE:MYSQL`,
                        `STATUS:ERROR`,
                        `SOURCE_ID:${_.get(record, 'source_id', 'UNKNOWN')}`
                    ]);
                } else {
                    utility._sendMetricsToDD(1, [
                        `REQUEST_TYPE:FBCONSUMER_CREATE_NOTIFICATION`,
                        `DATABASE:MYSQL`,
                        `STATUS:SUCCESS`,
                        `SOURCE_ID:${_.get(record, 'source_id', 'UNKNOWN')}`
                    ]);
                }
                cb(err, data, status);
            }, record);
        }
    }

    createTinyUrl(url) {
        this.L.log("notificationFallbackConsumer :: createTinyUrl", `Creating short url for : ${url}`);
        return new Promise((resolve, reject) => new utility.TinyUrl().createShortUrl((err, shortLink) => {
            resolve(shortLink);
        }, _.get(this.config, ['TINYURL_CONFIG'], null), url));
    }

    async suspendOperations() {
        this.L.log("notificationFallbackConsumer :: suspendOperations", `kafka consumer shutdown initiated`);

        await new Promise((resolve, reject) => {
            this.consumer.close((error, res) => {
                if (error) {
                    this.L.error(`notificationFallbackConsumer :: stopConsumer :: Error Stopping Consumer Err : ${error}`);
                    this.L.error(`notificationFallbackConsumer :: suspendOperations error in shutting kafka consumer`, error);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_FALLBACK", 'STATUS:ERROR', 'TYPE:CONSUMER_CLOSE_FAILED']);
                    reject(error);
                }
                this.L.info(`notificationFallbackConsumer :: stopConsumer :: Consumer Stopped!  Response : ${res}`);
                this.L.log(`notificationFallbackConsumer :: suspendOperations kafka consumer shutdown successful`);
                resolve();
            })
        });
    }

    getSchema() {
        return [
            "job_id UUID PRIMARY KEY",
            "event TEXT",
            "status TEXT",
            "is_retryable INT",
            "created_at TIMESTAMP"
        ];
    }

    getIndexingFields() {
        return ['event', 'status'];
    }
}

export default NotificationFallBackConsumer;