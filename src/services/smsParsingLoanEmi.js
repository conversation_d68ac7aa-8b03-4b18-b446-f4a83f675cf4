import _ from 'lodash'
import utility from '../lib'
import RecentsLayerLib from '../lib/recentsLayer'
import ASYNC from 'async'
import OS from 'os'
import MOMENT from 'moment'
import BILLS from '../models/bills'
import digitalUtility from 'digital-in-util'
import CATALOGVERTICALRECHARGE from '../models/catalogVerticalRecharge';
import PG from '../lib/pg'
import SmsParsingSyncCCBillLibrary from '../lib/smsParsingSyncCCBills'
import DigitalCatalog from '../lib/digitalReminderConfig'
import RemindableUsersLibrary from '../lib/remindableUser'
import DynamicSmsParsingRegexExecutor from './smsParsingBillPayment/dynamicSmsParsingRegexExecutor'
import BillFetchAnalytics from '../lib/billFetchAnalytics.js';
import KafkaConsumerChecks from '../lib/kafkaConsumerChecks';
import BillsLibrary from '../lib/bills';
import SmsParsingLagDashboard from '../lib/smsParsingLagDashboard.js';
import InternalCustIdNonRUFlowTagger from '../lib/InternalCustIdNonRUFlowTagger'

class SmsParsingLoanEmi {
    constructor(options) {
        this.L = options.L;
        this.infraUtils = options.INFRAUTILS;
        this.config = options.config;
        this.bills = new BILLS(options);
        this.commonLib = new utility.commonLib(options);
        this.recentsLayerLib = new RecentsLayerLib(options); // TODO
        this.paymentGatewayUtils = new PG(options);
        this.smsParsingSyncCCBillLib = new SmsParsingSyncCCBillLibrary(options);
        this.digitalCatalogLib = DigitalCatalog;
        this.remindableUsersLibrary = new RemindableUsersLibrary(options);
        this.nonPaytmCCtableName = _.get(this.config, 'COMMON.NON_PAYTM_CC.tableName', 'bills_nonpaytm_creditcard');
        this.reminderUtils = new digitalUtility.ReminderUtils();
        this.catalogVerticalRecharge = new CATALOGVERTICALRECHARGE(options);
        this.activePidLib = options.activePidLib;
        this.refereshIntervalForCategoryData = 15 * 60 * 1000; // DCAT getCategoryProductDetail API refresh interval 
        this.greyScaleEnv = _.get(options, 'greyScaleEnv', false);
        this.commonLib = new utility.commonLib(options);
        this.billFetchAnalytics = new BillFetchAnalytics(options);
        this.kafkaConsumerChecks = new KafkaConsumerChecks(options);
        this.billsLib = new BillsLibrary(options);
        this.smsParsingLagDashboard = new SmsParsingLagDashboard(options);
        this.internalCustIdNonRUFlowTagger = new InternalCustIdNonRUFlowTagger(options);
        this.regexExecutor = new DynamicSmsParsingRegexExecutor(options);
        this.loadConfigs();
        this.startConfigRefresh();
        this.timestamps = {};
        this.RUreadsKafkaTime = new Date().getTime();
        this.SKIP_CONDITIONS = [
            'NBFD_IN_FUTURE',
            'smsParsingDisabled',
            'CATEGORY_NOT_ENABLED',
            'PARTIAL_RN_MATCH_FOUND',
            'DEFAULT_BILLS_LIMIT_REACHED',
            'DUE_DATE_IS_NULL'
        ];
    }

    loadConfigs() {
        try {
            // Load all dynamic configs
            this.enabledPredictedCategories = _.get(this.config, ['DYNAMIC_CONFIG', 'SMS_PARSING_LOAN_EMI', 'ENABLED_PREDICTED_CATEGORIES', 'CATEGORIES'], []);
            this.enabledServiceList = _.get(this.config, ['DYNAMIC_CONFIG', 'SMS_PARSING_LOAN_EMI', 'ENABLED_SERVICE_LIST', 'SERVICES'], []);
            this.enabledOperatorList = _.get(this.config, ['DYNAMIC_CONFIG', 'SMS_PARSING_LOAN_EMI', 'ENABLED_OPERATOR_LIST', 'OPERATORS'], []);
            this.minAmount = _.get(this.config, ['DYNAMIC_CONFIG', 'SMS_PARSING_LOAN_EMI', 'COMMON', 'MIN_AMOUNT'], 100);
            this.maxAmount = _.get(this.config, ['DYNAMIC_CONFIG', 'SMS_PARSING_LOAN_EMI', 'COMMON', 'MAX_AMOUNT'], 500000);
            this.dueDateNbfdDays = _.get(this.config, ['DYNAMIC_CONFIG', 'SMS_PARSING_LOAN_EMI', 'COMMON', 'DUEDATE_NBFD_DAYS'], 20);
            this.billDateNbfdDays = _.get(this.config, ['DYNAMIC_CONFIG', 'SMS_PARSING_LOAN_EMI', 'COMMON', 'BILLDATE_NBFD_DAYS'], 30);
            this.maskedRnMatchLength = _.get(this.config, ['DYNAMIC_CONFIG', 'SMS_PARSING_LOAN_EMI', 'COMMON', 'MASKED_RN_MATCH_LENGTH'], 2);
            this.toBeNotifiedRealtimeNonru = _.get(this.config, ['DYNAMIC_CONFIG', 'SMS_PARSING_LOAN_EMI', 'COMMON', 'TO_BE_NOTIFIED_REALTIME_NONRU'], 1);
            this.defaultBillsLimit = _.get(this.config, ['DYNAMIC_CONFIG', 'SMS_PARSING_LOAN_EMI', 'COMMON', 'DEFAULT_BILLS_LIMIT'], 50);
            this.ctEventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENTS', 'LOAN_SMS_PARSING'], 'reminderBillGen');
            this.kafkaBatchSize = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'SMS_PARSING_LOAN_BILLS', 'BATCHSIZE'], 2) : 500;
            this.kafkaBatchDelay = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'SMS_PARSING_LOAN_BILLS', 'DELAY'], 5 * 60 * 1000) : 0;
            this.smsParsingBillsDwhRealtime = false;

            this.L.log('SmsParsingLoanEmi :: loadConfigs', 'Successfully loaded dynamic configs');
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_LOAN_EMI", 'STATUS:SUCCESS', 'TYPE:CONFIG_REFRESH']);

        } catch (error) {
            this.L.error('SmsParsingLoanEmi :: loadConfigs', 'Error loading dynamic configs', error);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_LOAN_EMI", 'STATUS:ERROR', 'TYPE:CONFIG_REFRESH']);
        }
    }

    startConfigRefresh() {
        // Store interval reference for cleanup
        this.configRefreshInterval = setInterval(() => {
            this.L.log('SmsParsingLoanEmi :: startConfigRefresh', 'Refreshing dynamic configs');
            this.loadConfigs();
        },  _.get(this.config, 'COMMON.INTERVAL_FOR_REINITIALIZE', 15) * 60 * 1000);
    }

    async start() {
        const self = this;
        try {
            // Refresh DCAT cache data
            self.smsParsingSyncCCBillLib.refreshDCATCacheData(self.refereshIntervalForCategoryData);
            self.L.log('SmsParsingLoanEmi :: start', 'Going to configure Kafka..');
            await new Promise((resolve, reject) => {
                self.configureKafka((error) => {
                    if (error) {
                        reject(error);
                    } else {
                        resolve();
                    }
                });
            });

            self.L.log('SmsParsingLoanEmi :: start', 'Kafka Configured successfully !!');

        } catch (error) {
            self.L.critical('SmsParsingLoanEmi :: start', 'unable to configure kafka', error);
            process.exit(0);
        }
    }

    async configureKafka(done) {
        const self = this;
        
        try {
            // Initialize publishers sequentially
            for (const config of self.getPublisherConfigs()) {
                await self.initializePublisher(config);
            }

            // Initialize consumer
            await self.initializeConsumer();
            
            done(null);
        } catch (error) {
            self.L.critical('SmsParsingLoanEmi :: configureKafka', 'Could not initialize Kafka', error);
            done(error);
        }
    }

    getPublisherConfigs() {
        return [
            {
                name: 'nonPaytmKafkaPublisher',
                config: {
                    kafkaHost: this.config.KAFKA.TOPICS.NON_PAYTM_RECORDS.HOSTS
                },
                description: 'Kafka publisher to update events to non paytm bills pipeline',
                onError: () => utility._sendMetricsToDD(1, ["REQUEST_TYPE:LOAN_SMS_PARSING_DWH", 'STATUS:ERROR', 'TYPE:PRODUCER', 'TOPIC:NON_PAYTM_RECORDS', 'SOURCE:MAIN_FLOW'])
            },
            {
                name: 'billFetchKafkaPublisher',
                config: {
                    kafkaHost: this.config.KAFKA.TOPICS.REMINDER_BILLFETCH_PIPELINE.HOSTS
                },
                description: 'Kafka publisher to update reminder bill fetch',
                onError: () => utility._sendMetricsToDD(1, ["REQUEST_TYPE:LOAN_SMS_PARSING_DWH", 'STATUS:ERROR', 'TYPE:PRODUCER', 'TOPIC:REMINDER_BILL_FETCH', 'SOURCE:MAIN_FLOW'])
            },
            {
                name: 'billFetchRealTimeKafkaPublisher',
                config: {
                    kafkaHost: this.config.KAFKA.TOPICS.REMINDER_BILLFETCH_PIPELINE_REALTIME.HOSTS
                },
                description: 'Kafka publisher to update reminder bill fetch realtime',
                onError: () => utility._sendMetricsToDD(1, ["REQUEST_TYPE:LOAN_SMS_PARSING_DWH_REALTIME", 'STATUS:ERROR', 'TYPE:PRODUCER', 'TOPIC:REMINDER_BILL_FETCH_REALTIME', 'SOURCE:MAIN_FLOW'])
            },
            {
                name: 'ctKafkaPublisher',
                config: {
                    kafkaHost: this.config.KAFKA.TOPICS.CT_EVENTS_PUBLISHER.HOSTS
                },
                description: 'Kafka publisher to publish events to CT publisher pipeline',
                onError: () => utility._sendMetricsToDD(1, ["REQUEST_TYPE:LOAN_SMS_PARSING_DWH", 'STATUS:ERROR', 'TYPE:PRODUCER', 'TOPIC:cdo-ru-reminders-reminderEvents', 'SOURCE:MAIN_FLOW'])
            }
        ];
    }

    async initializePublisher(config) {
        const self = this;
        return new Promise((resolve, reject) => {
            self[config.name] = new self.infraUtils.kafka.producer(config.config);
            self[config.name].initProducer('high', error => {
                if (error) {
                    if (config.onError) {
                        config.onError();
                    }
                    reject(error);
                } else {
                    self.L.log('SmsParsingLoanEmi :: initializePublisher', `Initialized ${config.description}`);
                    resolve();
                }
            });
        });
    }

    async initializeConsumer() {
        const self = this;
        return new Promise((resolve, reject) => {
            self.L.log('SmsParsingLoanEmi :: initializeConsumer', 'Going to initialize Kakfa Consumer for topic : SMS_PARSING_LOANEMI');

            const kafkaConsumerObj = {
                kafkaHost: _.get(self.config.KAFKA, 'TOPICS.SMS_PARSING_LOAN_EMI.HOSTS'),
                groupId: "smsParsingLoanEmi-consumer",
                topics: _.get(self.config.KAFKA, 'SERVICES.SMS_PARSING_LOAN_EMI.TOPIC'),
                id: `smsParsingLoanEmi-consumer_${OS.hostname()}_${process.pid}`,
                fromOffset: "earliest",
                autoCommit: false,
                batchSize: self.kafkaBatchSize
            };

            self.kafkaSMSParsingLoanConsumer = new self.infraUtils.kafka.consumer(kafkaConsumerObj);
            self.kafkaSMSParsingLoanConsumer.initConsumer(self.execSteps.bind(self), error => {
                if (error) {
                    reject(error);
                } else {
                    self.L.log('SmsParsingLoanEmi :: initializeConsumer', 'Consumer initialized successfully');
                    resolve();
                }
            });
        });
    }

    execSteps(records) {
        let self = this,
            chunkSize = 50,
            startTime = new Date().getTime(),
            currentPointer = 0, lastMessage;

        if (records && _.isArray(records)) {
            lastMessage = records[records.length - 1];
            self.kafkaSMSParsingLoanConsumer._pauseConsumer();
        } else {
            self.L.critical('SmsParsingLoanEmi :: execSteps', `No valid kafka records found. Received records-`, records);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_LOAN_EMI", 'STATUS:INVALID_PAYLOAD', 'SOURCE:MAIN_FLOW']);
            return;
        }

        self.L.log('SmsParsingLoanEmi :: execSteps :: ', `Processing ${records.length} SMS Parsing Loan emi data !!`);
        utility._sendMetricsToDD(records.length, ['REQUEST_TYPE:SMS_PARSING_LOAN_EMI', 'STATUS:TRAFFIC', 'SOURCE:CONSUMED_RECORDS']);

        ASYNC.whilst(
            () => {
                return currentPointer < records.length;
            },
            (callback) => {
                let nextChunk = records.slice(currentPointer, (currentPointer + chunkSize));
                currentPointer += chunkSize;
                self.processBatch(nextChunk, () => {
                    setTimeout(() => {
                        callback();
                    }, 2);
                });
            },
            (err) => {
                self.kafkaConsumerChecks.findOffsetDuplicates("SmsParsingLoanEmi", records);

                self.kafkaSMSParsingLoanConsumer.commitOffset(lastMessage, (error) => {
                    if (error) {
                        self.L.error('SmsParsingLoanEmi :: execSteps::', 'Error while commit for-', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                    }else {
                        self.L.log('SmsParsingLoanEmi :: execSteps::', 'Commit success for offset :', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                    }
                let endTime = new Date().getTime();
                let executionTime = (endTime - startTime) / 1000;      //in seconds
                executionTime = Math.round(executionTime);
                self.L.log('SmsParsingLoanEmi :: execSteps::', 'per chunkSize record Execution time :', executionTime, 'seconds ','no. of records : ',records.length);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_STATUS", 'STATUS:SUCCESS', "SOURCE:SmsParsingLoanEmi", "TIME_TAKEN:" + executionTime]);

                    // Resume consumer now
                setTimeout(function () {
                        self.kafkaSMSParsingLoanConsumer._resumeConsumer();
                    }, self.kafkaBatchDelay);
                });
            }
        );
    }

    processBatch(records, done) {
        let self = this,
         currentPointer = 0;
        ASYNC.whilst(
            () => {
                return currentPointer < records.length;
            },
            (callback) => {
                let record = records[currentPointer];
                currentPointer = currentPointer+1;
                self.processData(record, () => {
                    setTimeout(() => {
                        callback();
                    }, 1);
                });
            },
            (err) => {
                return done()
            }
        );
    }

    processData(record, done) {
        let self = this;
        let published_time = Number(_.get(record, 'timestamp', null));

        try {
            record = JSON.parse(_.get(record, 'value', null));
            if(!record.data){
                self.L.critical('processData', `Invalid Kafka record received. data key is missing`, record);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_LOAN_EMI", 'STATUS:INVALID_PAYLOAD', 'TYPE:DATA_KEY_MISSING', 'SOURCE:MAIN_FLOW']);
                return done();
            }
        } catch (error) {
            if (error) {
                self.L.critical('processData', `Invalid Kafka record received`, record);
            }
            return done();
        }
        ASYNC.map(
            record.data,
            (smsData, next) => {
                _.set(smsData, 'published_time', published_time);
                self.processRecords(() => {
                    return next();
                }, smsData);
            },
            err => {
                done();
            }
        )
    }

    processRecords(done, record) {
        let self = this;
        try {
            ASYNC.waterfall([
                next => {
                    self.validateAndProcessRecord(record, function(err, response){
                        if (err) {
                            self.L.error('SmsParsingLoanEmi :: processRecords', `Invalid record received ${JSON.stringify(record)}, Error:`, err);
                            self.sendMetrics(response, 'VALIDATION_FAILURE', err, 'validateAndProcessRecord');
                            self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(
                                self.createRecordForAnalytics(record, self.getBillStatus(record), null),
                                err,
                                next
                            );
                        }else {
                            self.L.log('SmsParsingLoanEmi :: processRecords', `Validation successful record: ${JSON.stringify(response)} for ${response.debugKey}`);
                            self.sendMetrics(response, 'VALID_TRAFFIC', null, 'validateAndProcessRecord', [`APP_VERSION:${_.get(record,'appVersion', null)}`]);
                            next(null, response);
                        }
                    });
                },
                (processedRecord, next) =>{
                    const smsParsingEnabledStatus = self.isSmsParsingEnabled(processedRecord);

                    if (!smsParsingEnabledStatus) {
                        self.L.log('SmsParsingLoanEmi :: processRecords', `smsParsing disabled ${processedRecord.debugKey}, skip record.`);
                        self.sendMetrics(processedRecord, 'SKIPPED', 'SMS_PARSING_DISABLED', 'isSmsParsingEnabled');
                        return next('smsParsingDisabled');
                    } else {
                        self.L.log(`SmsParsingLoanEmi :: processRecords`, `smsParsing enabled ${processedRecord.debugKey}`);
                        self.getForwardActionFlow((err,action)=>{
                            if (err) {
                                self.L.error('SmsParsingLoanEmi :: processRecords', `Invalid action found for ${record.debugKey}, Error: `, err);
                                //Skipped records are treated as errors for analytics, while graceful error handling is managed in the waterfall's final callback.
                                self.sendMetrics(processedRecord, 'FAILURE', err, 'getForwardActionFlow');
                                self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(processedRecord,self.getBillStatus(processedRecord),null), err,next);
                            } else{
                                let billsData = self.getBillsData(processedRecord);
                                processedRecord.billsData = billsData;
                                self.L.log('SmsParsingLoanEmi :: processRecords', `Action: ${action} : ${processedRecord.debugKey}`);
                                next(null, action, processedRecord);
                            }
                        },processedRecord)
                    }
                },
                (action, processedRecord, next) => {
                    if(action == 'findAndUpdateToCassandra'){   
                        self.updateCassandra((err)=>{
                             if(err){
                                self.L.error(`SmsParsingLoanEmi :: processRecords`, `error in updateCassandra: ${processedRecord.debugKey} Error: ${err}`);
                                self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(processedRecord,self.getBillStatus(processedRecord),'NON_RU'), err,next);
                             } else {
                                self.L.log(`SmsParsingLoanEmi :: processRecords`, `updateCassandra success: ${processedRecord.debugKey}`);
                                next(null, action, processedRecord);
                             }
                        }, processedRecord);
                    }else{
                        next(null, action, processedRecord);
                    }
                },
                (action, processedRecord, next) => {
                    if(action == 'update'){   
                        self.checkEligibilityAndSetNBFD((err)=>{
                             if(err){
                                self.L.error(`SmsParsingLoanEmi :: checkEligibilityAndSetCustomParams ::`, `${processedRecord.debugKey} Error: ${err}`);
                                next(err, processedRecord);                       
                             }else{
                                self.L.log(`SmsParsingLoanEmi ::  checkEligibilityAndSetCustomParams`, `executed:: ${processedRecord.debugKey}`);
                                next(null, action, processedRecord);
                             }
                        }, processedRecord);
                    }else{
                        next(null, action, processedRecord);
                    }
                },
                (action, processedRecord, next) => {
                    if(action == 'update') {
                        self.updateDbRecord((err)=>{
                            if(err){
                                self.L.error(`SmsParsingLoanEmi :: updateDbRecord`, `error while updating for : ${processedRecord.debugKey}, error: ${err}`);
                                self.sendMetrics(processedRecord, 'FAILED', 'EXISTING_BILLS_UPDATE', 'updateDbRecord');
                                next(err,processedRecord);
                            } else {
                                self.L.log(`SmsParsingLoanEmi :: updateDbRecord`, `updated successfully for : ${processedRecord.debugKey}`);
                                self.sendMetrics(processedRecord, 'SUCCESS', 'EXISTING_BILLS_UPDATE', 'updateDbRecord');
                                next(null ,action ,processedRecord);
                            }
                        },processedRecord);
                    } else {
                        next(null, action, processedRecord);
                    }
                },
                (action, processedRecord, next) => {
                    if(action == 'update') {
                        self.publishInKafka((err)=>{
                            if(err){
                                self.L.critical('SmsParsingLoanEmi :: publishInKafka', `Failed to publish message for ${processedRecord.debugKey}`, err);
                                self.sendMetrics(processedRecord, 'FAILED', 'PUBLISH_FOR_PN_CT', 'publishInKafka');
                                next(err,processedRecord);
                            } else {
                                self.L.log('SmsParsingLoanEmi :: publishInKafka', `Message published successfully for ${processedRecord.debugKey}`);
                                self.sendMetrics(processedRecord, 'SUCCESS', 'publishInKafka');
                                next(null, processedRecord);
                            }
                        }, processedRecord);                        
                    } else {
                        next(null, processedRecord);
                    }
                },
                (processedRecord, next)=>{
                    let service = _.toUpper(_.get(processedRecord, 'service', null));
                    let operator = _.get(processedRecord, 'operator', null);
                    let source = `SMS_${service}_POSTPAID`;
                    self.smsParsingLagDashboard.publishDelaysMetrics((err)=>{
                        next(null,processedRecord);
                    },source,self.timestamps, operator, processedRecord);
                }
            ], function (error) {
                if (!error) {
                    self.L.log(`SmsParsingLoanEmi :: processRecords`, 
                        `Record processed successfully ${record.debugKey}`);
                    return done();
                } else if (self.SKIP_CONDITIONS && self.SKIP_CONDITIONS.includes(error)) {
                    self.L.log(`SmsParsingLoanEmi :: processRecords`, 
                        `Record processed and skipped due to ${error} condition for ${record.debugKey}`);
                    return done();
                } else {
                    self.L.error('SmsParsingLoanEmi :: processRecords', 
                        `Exception occurred for ${record.debugKey}`, error);
                    done(error);
                }
            });
        } catch(err) {
            self.L.critical(`processRecords`,`Exception occured for record:${record}`,err)
            done(err);
        }
    }

     /**
     * 
     * @param {*} record 
     */
      validateAndProcessRecord(record, cb) {
        let self = this;

        if (!record) {
            return cb('Invalid record', record);
        }

        try {
            ASYNC.waterfall([
                next => {
                    self.processTimestamps(record);
                    return next(null, record);
                },
                (record, next) => {
                    try {
                        let processedRecord = self.createProcessedRecord(record);
                        let debugKey = self.billsLib.generateDebugKey(processedRecord);
                        _.set(processedRecord, 'debugKey', debugKey);
                        _.set(record, 'debugKey', debugKey);
                        
                        utility._sendMetricsToDD(1, [
                            "REQUEST_TYPE:SMS_PARSING_LOAN_EMI",
                            "STATUS:RECEIVED",
                            "TYPE:OPERATOR_STATS",
                            `DWH_OPERATOR:${processedRecord.dwhOperator || 'unknown'}`,
                            `RU_OPERATOR:${processedRecord.operator || 'unknown'}`,
                            `PREDICTED_CATEGORY:${processedRecord.predicted_category || 'unknown'}`,
                        ]);

                        // Validate the processed record
                        let validationError = self.validateProcessedRecord(processedRecord);
                        if (validationError) {
                            return next(validationError, processedRecord);
                        }
                        

                        return next(null, processedRecord);
                    } catch (error) {
                        return next(error);
                    }
                }
            ], cb);
        } catch (err) {
            self.L.critical(`SmsParsingLoanEmi :: validateAndProcessRecord`, `Exception occurred for record:${JSON.stringify(record)}`, err);
            return cb(err, record);
        }
    }

    processTimestamps(record) {
        if (_.get(record, 'smsDateTime', null)) {
            if (record.smsDateTime.toString().length === 10) {
                record.smsDateTime = record.smsDateTime * 1000;
            }
        } else {
            _.set(record, 'smsDateTime', MOMENT().valueOf());
        }

        let smsDateTime_fromPayload = Number(record.smsDateTime); 
        this.RUreadsKafkaTime =  new Date().getTime(); // Time at which RU reads from the KAFKA

        this.timestamps = {
            data_smsDateTime: new Date(smsDateTime_fromPayload).getTime(),
            data_timestamp: new Date(record.timestamp).getTime(),
            data_deviceDateTime: new Date(record.deviceDateTime).getTime(),
            data_uploadTime: new Date(record.uploadTime).getTime(),
            collector_timestamp: new Date(record.collector_timestamp).getTime(),
            RUreadsKafkaTime: this.RUreadsKafkaTime,
            smsParsingEntryTime: new Date(record.producerEntryTime).getTime(),
            smsParsingExitTime: new Date(record.consumerExitTime).getTime(),
            dwhKafkaPublishedTime: new Date(record.published_time).getTime()
        };
    }

    createProcessedRecord(record) {
        let amount = _.get(record, 'emi_due', null) ? 
            utility.getFilteredAmount(_.get(record, 'emi_due', null)) : null;
        let dueDate = utility.getFilteredDate(_.get(record, 'due_date', null)).value;
        let dwhOperator = _.toLower(_.get(record, 'lender', ''));
        let mappedOperator = this.getMappedOperator(dwhOperator);
        let configProductId = _.get(this.config, ['DYNAMIC_CONFIG', 'SMS_PARSING_LOAN_EMI', mappedOperator, 'PRODUCT_ID'], null);
        let activePid = this.activePidLib.getActivePID(configProductId);

        // Get demerger operators list if applicable
        let demergerOperatorsList = _.get(this.config, ['DYNAMIC_CONFIG', 'SMS_PARSING_LOAN_EMI', mappedOperator, 'DEMERGER_OPERATOR'], null);

        return {
            customerId: this.billsLib.parseCustomerId(record.cId),
            rechargeNumber: _.get(record, 'account_no', ''),
            operator: mappedOperator,
            dwhOperator: dwhOperator,// Add the original operator
            paytype: 'loan',
            service: 'loan',
            productId: activePid,
            circle: 'all circles',
            status: _.get(this.config, ['COMMON', 'bills_status', 'BILL_FETCHED'], 4),
            billDate: this.billsLib.getSmsBillDate(record.smsDateTime),
            billFetchDate: MOMENT().format('YYYY-MM-DD HH:mm:ss'),
            dueDate: dueDate ? dueDate.endOf('day').format('YYYY-MM-DD HH:mm:ss') : null,
            amount: amount,
            msgId: _.get(record, 'msg_id', ''),
            sender_id: _.get(record, 'smsSenderID', ''),
            sms_id: _.get(record, 'sms_id', ''),
            sms_date_time: _.get(record, 'smsDateTime', ''),
            predicted_category: _.get(record, 'predicted_category', null),
            user_data: null,
            isDwhSmsParsingRealtime: this.smsParsingBillsDwhRealtime,
            appVersion: _.get(record, 'appVersion', null),
            partialRecordFound: !dueDate || !amount,
            dwhClassId: _.get(record, 'dwhClassId', _.get(record, 'dwh_classId', null)),
            rtspClassId: _.get(record, 'rtspClassId', null),
            dwhKafkaPublishedTime: _.get(record, 'published_time', null),
            demergerOperatorsList: demergerOperatorsList,
        };
    }

    async getForwardActionFlow(done, processedRecord) {
        const self = this;
        
        try {
            if (!self.shouldProcessRecord(processedRecord)) {
                self.L.log(`SmsParsingLoanEmi :: getForwardActionFlow:: Skipping record - Invalid rechargeNumber or non-RU operator for : ${processedRecord.debugKey}`);
                return done(null, 'findAndUpdateToCassandra', processedRecord);
            }

            const recordsFound = await new Promise((resolve, reject) => {
                self.getRecordsFromDb((err, found) => {
                    if (err) {
                        self.L.error(`SmsParsingLoanEmi :: getForwardActionFlow :: ERROR in getRecordsFromDb with error: ${err} for ${processedRecord.debugKey}`);
                        reject(err);
                    }
                    resolve(found);
                }, processedRecord);
            });

            if (!recordsFound) {
                self.L.log(`SmsParsingLoanEmi :: getForwardActionFlow:: No recordsFound in DB for : ${processedRecord.debugKey}`);
                return done(null, 'findAndUpdateToCassandra', processedRecord);
            }

            self.L.log(`SmsParsingLoanEmi :: recordsFound:: ${processedRecord.noOfFoundRecord}, activeRecord:: ${processedRecord.activeRecordsInDB}, ${processedRecord.debugKey}`);
            self.sendMetrics(processedRecord, 'FOUND', 'GET_RECORDS_FROM_DB');

            if (processedRecord.activeRecordsInDB > 0) {
                const matchResult = await self.findRechargeNumberMatch(processedRecord);
                if (matchResult.reason) {
                    return done(matchResult.reason);
                }
                return done(null, matchResult.action, processedRecord);
            } else {
                self.L.log(`SmsParsingLoanEmi :: getForwardActionFlow`, `No active records found for ${processedRecord.debugKey}`);
                return done(null, 'findAndUpdateToCassandra', processedRecord);
            }
        } catch (error) {
            self.L.error('SmsParsingLoanEmi :: getForwardActionFlow', `Error processing ${processedRecord.debugKey}`, error);
            return done(error);
        }
    }

    async findRechargeNumberMatch(processedRecord) {
        const self = this;
        const dbRecords = _.get(processedRecord, 'dbData', []);
        const recordLengthCustIdBased = _.get(processedRecord, 'noOfFoundRecord', 0);
        const smsRechargeNumber = processedRecord.rechargeNumber;
        const tableName = _.get(processedRecord, 'tableName', null);
        const exactMatches = self.hasExactRechargeNumberMatch(dbRecords, smsRechargeNumber);

        // Check if records hit default limit without a match
        if (exactMatches.length === 0 && recordLengthCustIdBased >= self.defaultBillsLimit) {
            self.L.log(`SmsParsingLoanEmi :: findRechargeNumberMatch :: Records hit default limit without match for ${processedRecord.debugKey}`);
            return { action: 'skip', reason: 'DEFAULT_BILLS_LIMIT_REACHED' };
        }


        if (exactMatches.length > 0 && tableName) {
            // Replace record.dbData with all custIds for matched RN
            try {
                self.L.log(`SmsParsingLoanEmi :: findRechargeNumberMatch`, `Exact RN match found :: getBillsOfSameRN: ${processedRecord.debugKey}`);
                
                if(_.isNull(processedRecord.dueDate)) {
                    //We do not update the record if the due date is null.
                    self.L.log(`SmsParsingLoanEmi :: findRechargeNumberMatch`, `Due date is null, skipping record ${processedRecord.debugKey}`);
                    return { action: 'skip', reason: 'DUE_DATE_IS_NULL' };
                }

                const newDbData = await self.getBillsOfSameRN(processedRecord);

                // Filter only active records
                const activeRecords = newDbData.filter(record => self.isActiveRecord(record));

                _.set(processedRecord, 'dbData', activeRecords);
                return { action: 'update' };
            } catch (error) {
                self.L.error('SmsParsingLoanEmi :: findRechargeNumberMatch', `Error getting bills of same recharge number for ${processedRecord.debugKey}`, error);
                return { reason: 'ERROR_GETTING_BILLS_OF_SAME_RN' };
            }
        }

        if (self.isMaskedNumber(smsRechargeNumber)) {
            const matchLength = self.maskedRnMatchLength;
            const lastDigits = self.getUnmaskedDigits(smsRechargeNumber, matchLength);
            
            if (lastDigits && !self.containsMaskedCharacters(lastDigits)) {
                if (self.hasMaskedRechargeNumberMatch(dbRecords, lastDigits, matchLength)) {
                    self.L.log(`SmsParsingLoanEmi :: Masked RN match found :: SMS RN: ${smsRechargeNumber}, ${processedRecord.debugKey}`);
                    return { action: 'skip', reason: 'PARTIAL_RN_MATCH_FOUND' };
                }
            }
        }

        self.L.log(`SmsParsingLoanEmi :: findRechargeNumberMatch`, `No matches found for ${processedRecord.debugKey}`);
        // No matches found
        return { action: 'findAndUpdateToCassandra' };
    }

    hasExactRechargeNumberMatch(dbRecords, smsRechargeNumber) {
        return dbRecords.filter(record => 
            this.isActiveRecord(record) && 
            _.toLower(record.recharge_number) === _.toLower(smsRechargeNumber)
        );
    }

    hasMaskedRechargeNumberMatch(dbRecords, lastDigits, matchLength) {
        return dbRecords.some(record => {
            if (this.isActiveRecord(record)) {
                const dbRNLastDigits = record.recharge_number.slice(-matchLength);
                return dbRNLastDigits === lastDigits;
            }
            return false;
        });
    }

    isActiveRecord(record) {
        return record.status !== _.get(this.config, 'COMMON.bills_status.DISABLED', 7) && record.status !== _.get(this.config, 'COMMON.bills_status.NOT_IN_USE', 13);
    }

    isMaskedNumber(rechargeNumber) {
        if (!rechargeNumber) return false;
        
        // Check for various masking patterns
        return rechargeNumber.includes('xxx') || 
               rechargeNumber.includes('XXX') || 
               rechargeNumber.includes('***') || 
               rechargeNumber.startsWith('XX');
    }

    getUnmaskedDigits(rechargeNumber, matchLength) {
        if (!rechargeNumber || !matchLength) return null;
        
        // Simply return the last matchLength digits
        return rechargeNumber.slice(-matchLength);
    }

    async getRecordsFromDb(done,record){
        let self = this;
        if (_.get(record, 'demergerOperatorsList', null) != null && _.isArray(record.demergerOperatorsList)) {
            _.set(record, 'isDemergerCase', true);
            _.set(record, 'isCreditCardOperator', false);
            _.set(record, 'isDuplicateCANumberOperator', false);
            let oldOperator = record.operator;
            self.iterateOnDbs(record, (err, foundInDb) => {
                if (err) {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:SMS_PARSING_LOAN_EMI_ITERATE_DB",
                        'STATUS:ERROR',
                        "OPERATOR:" + record.operator
                    ]);
                    _.set(record, 'operator', oldOperator);
                    self.L.log('getRecordsFromDb :: not found in all DBs');
                    return done(err, false);
                } else if (foundInDb == 0) {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:SMS_PARSING_LOAN_EMI_ITERATE_DB",
                        'STATUS:FAILURE',
                        "OPERATOR:" + record.operator,
                        "REASON:RECORD_NOT_FOUND"
                    ]);
                    _.set(record, 'operator', oldOperator);
                    self.L.log('getRecordsFromDb :: not found in all DBs');
                    return done(null, false);
                }
                return done(null, true);
            });
        } else {
            _.set(record, 'limit', self.defaultBillsLimit);
            self.bills.getBillsOfCustIdServiceOperator((err, data) => {
                if (err) {
                    self.L.error(`SmsParsingLoanEmi :: getBillsOfCustIdServiceOperator : Error on getting records from db with error: ${err}`);
                    return done(err, false);
                }
                if (!data || !_.isArray(data) || data.length < 1) return done(null, false);

                _.set(record, 'noOfFoundRecord', data.length);
                _.set(record, 'is_automatic', data[0].is_automatic);
                _.set(record, 'dbData', self.billsLib.getSortedDbData(data));
                _.set(record, 'activeRecordsInDB', self.billsLib.getActiveRecords(data));
                _.set(record, 'isRecordExist', true);
                return done(null, true);
            }, record.tableName, record);
        }
    }

    iterateOnDbs(record, cb) {
        let self = this;
        let recordFoundInDb = 0;
        ASYNC.eachSeries(record.demergerOperatorsList, (subOperator, next) => {
            // in case table config is disabled, set main operator as tablename
            let tableName = _.get(self.config, ['OPERATOR_TABLE_REGISTRY', subOperator], null);
            let clonedRecord = _.cloneDeep(record);
            _.set(clonedRecord, 'operator', subOperator);
            if (tableName != null) {
                try {
                    self.bills.getBillsOfSameRech((err, data) => {
                        if (err) {
                            utility._sendMetricsToDD(1, [
                                "REQUEST_TYPE:SMS_PARSING_LOAN_EMI_GETRECORDS",
                                'STATUS:ERROR',
                                "OPERATOR:" + record.operator,
                                "REASON:QUERY_ERROR_FROM_ITERATION_ON_TABLE",
                                "TABLE_NAME:" + tableName
                            ]);
                            return next(err);
                        }
                        if (data && data.length > 0) {
                            recordFoundInDb = 1;
                            _.set(record, 'noOfFoundRecord', data.length);
                            _.set(record, 'is_automatic', data[0].is_automatic);
                            _.set(record, 'dbData', self.billsLib.getSortedDbData(data));
                            _.set(record, 'isRecordExist', true);
                            _.set(record, 'activeRecordsInDB', self.billsLib.getActiveRecords(data));
                            _.set(record, 'operator', subOperator);
                            _.set(record, 'tableName', tableName);
                            return next('found');
                        }
                        return next(null);
                    }, tableName, clonedRecord);
                } catch (error) {
                    return next(error);
                }
            } else {
                return next(null);
            }
        }, (err) => {
            if (err === 'found') {
                return cb(null, recordFoundInDb);
            }
            return cb(err, recordFoundInDb);
        });
    }

    async updateCassandra(done, processedRecord) {
        let self = this;
        let isDummyRN = "1";
        
        try {
            if(processedRecord.demergerOperatorsList && processedRecord.demergerOperatorsList.length > 0){
                isDummyRN = "0"
            }

            const extra = {
                updated_data_source: "SMS_PARSING_DWH",
                created_source: "SMS_PARSING_DWH",
                source_subtype_2: _.get(processedRecord, 'partialRecordFound', false) ? 'PARTIAL_BILL' : 'FULL_BILL',
                eventState: "bill_gen",
                billSource: "sms_parsed",
                isDummyRN: isDummyRN,
                isDwhSmsParsing: true,
                smsRechargeNumber: _.get(processedRecord, 'rechargeNumber', null),
                demergerOperatorsList: _.get(processedRecord, 'demergerOperatorsList', null)
            };

            let dummyRechargeNumber = self.billsLib.generateDummyRechargeNumber(processedRecord);
            if(isDummyRN == "0"){
                dummyRechargeNumber = processedRecord.rechargeNumber;
            }
            const dataToBeInsertedInDB = self.prepareDataForCassandra(processedRecord, dummyRechargeNumber, extra);
            
            await self.publishToCassandra(dataToBeInsertedInDB, processedRecord);
            return done(null);
        } catch (error) {
            self.L.error(`SmsParsingLoanEmi :: updateCassandra failed for ${processedRecord.debugKey}, Error:`, error);
            self.sendMetrics(processedRecord, 'ERROR', 'PUBLISH_TO_NON_RU', 'updateCassandra');
            return done(error);
        }
    }

    prepareDataForCassandra(processedRecord, dummyRechargeNumber, extra) {
        return {
            source: 'SMS_PARSING_DWH',
            customerId: processedRecord.customerId,
            rechargeNumber: dummyRechargeNumber,
            productId: processedRecord.productId,
            operator: processedRecord.operator,
            amount: processedRecord.amount,
            dueDate: MOMENT(processedRecord.dueDate).isValid() ? MOMENT(processedRecord.dueDate).format('YYYY-MM-DD HH:mm:ss') : null,
            billDate: MOMENT().format('YYYY-MM-DD HH:mm:ss'),   
            billFetchDate: MOMENT(processedRecord.billFetchDate).format('YYYY-MM-DD HH:mm:ss'),
            paytype: processedRecord.paytype,
            service: processedRecord.service,
            circle: processedRecord.circle,
            categoryId: _.get(this.config, ['CVR_DATA', processedRecord.productId, 'category_id'], null),
            customer_mobile: null,
            customer_email: null,
            status: _.get(this.config, 'COMMON.bills_status.BILL_FETCHED', 4), 
            notificationStatus: _.get(this.config, ['COMMON', 'notification_status', 'ENABLED'], 1),  
            customerOtherInfo: _.get(processedRecord, 'billsData.customerOtherInfo', '{}'),  
            dbEvent: "upsert",
            nonpaytm_onBoardTime: new Date().getTime(),
            partialSmsFound: _.get(processedRecord, 'partialRecordFound', false),
            toBeNotified: this.toBeNotifiedRealtimeNonru,
            dwhKafkaPublishedTime: _.get(processedRecord, 'dwhKafkaPublishedTime', null),
            dwhClassId: _.get(processedRecord, 'dwhClassId', null),
            rtspClassId: _.get(processedRecord, 'rtspClassId', null),
            extra: JSON.stringify(extra),
            demergerOperatorsList: _.get(processedRecord, 'demergerOperatorsList', null)
        };
    }

    prepareExtraDetails(record, dbExtra, dbRecord) {
        const extraDetails = {
            ...dbExtra,
            billSource: 'sms_parsed',
            updated_source: 'sms',
            updated_data_source: 'SMS_PARSING_DWH',
            source_subtype_2: _.get(record, 'partialRecordFound', false) ? 'PARTIAL_BILL' : 'FULL_BILL',
        };

        extraDetails.lastSuccessBFD = _.get(dbRecord, 'bill_fetch_date', null);
        extraDetails.billFetchDate = MOMENT(record.smsTimeStamp).isValid() 
            ? MOMENT(record.smsTimeStamp).format('YYYY-MM-DD HH:mm:ss') 
            : MOMENT().format('YYYY-MM-DD HH:mm:ss');
        extraDetails.lastDueDt = _.get(dbRecord, 'due_date', null);
        extraDetails.lastBillDt = _.get(dbRecord, 'bill_date', null);
        extraDetails.lastAmount = _.get(dbRecord, 'amount', null);
        extraDetails.isDwhSmsParsing = true;

        // Reset error counters if present
        if (_.get(extraDetails, 'errorCounters', null)) {
            extraDetails.errorCounters = {};
        }

        return extraDetails;
    }

    async publishToCassandra(data, processedRecord) {
        let self = this;
        const topicName = _.get(self.config.KAFKA, 'SERVICES.NON_PAYTM_RECORDS_DWH.TOPIC', '');
        let nonRuDataToPublish = await self.internalCustIdNonRUFlowTagger.mapInternalCustIdToNonRUFlow(data);
        return new Promise((resolve, reject) => {
            self.nonPaytmKafkaPublisher.publishData([{
                topic: topicName,
                messages: nonRuDataToPublish
            }], (error) => {
                if (error) {
                    self.L.critical('SmsParsingLoanEmi :: updateCassandra', 'Error while publishing message in Kafka - MSG:- ' + nonRuDataToPublish, error);
                    reject('Error while publishing message in Kafka');
                } else {
                    self.sendMetrics(processedRecord, 'PUBLISHED', 'PUBLISH_TO_NON_RU', 'updateCassandra');
                    self.L.log(`SmsParsingLoanEmi :: updateCassandra', 'Message published successfully in Kafka on topic ${topicName}`, nonRuDataToPublish);
                    resolve();
                }
            });
        });
    }

    getBillsData(record) {
        
        const { dbExtra, dbCustomerOtherInfo, dbRecord} = this.parseDbData(record);
        
        const custInfoValues = this.prepareCustomerInfo(record, dbCustomerOtherInfo);
        const extraDetails = this.prepareExtraDetails(record, dbExtra, dbRecord);
        
        return {
            source: 'SMS_PARSING_DWH',
            commonAmount: record.amount,
            commonDueDate: record.dueDate ? MOMENT(record.dueDate).endOf('day').format('YYYY-MM-DD HH:mm:ss') : null,
            rechargeNumber: record.rechargeNumber,
            billDate: record.billDate,
            billFetchDate: record.billFetchDate,
            productId: record.productId,
            commonStatus: record.status,
            customerId: record.customerId,
            operator: record.operator,
            circle: record.circle,
            service: record.service,
            paytype: _.get(record, 'paytype', null),
            msgId: _.get(record, 'msgId', ''),
            user_data: record.user_data,
            is_automatic: _.get(record, 'is_automatic', 0),
            extra: JSON.stringify(extraDetails, function (key, value) {
                if (key && typeof (value) === 'string')
                    return value.replace(/[?']/g, "");
                else
                    return value;
            }),
            customerOtherInfo: JSON.stringify(custInfoValues, function (key, value) {
                if (key && typeof (value) === 'string')
                    return value.replace(/[?']/g, "");
                else
                    return value;
            }),
        };
    }

    parseDbData(record) {
        let dbExtra = {};
        let dbCustomerOtherInfo = {};
        let dbRecord = {};

        if (record.isRecordExist) {
            try {
                dbRecord = _.get(record, 'dbData[0]', {});
                dbExtra = JSON.parse(_.get(dbRecord, 'extra', '{}')) || {};
                dbCustomerOtherInfo = JSON.parse(_.get(dbRecord, 'customerOtherInfo', '{}')) || {};
            } catch (err) {
                this.L.error("SmsParsingLoanEmi :: getBillsData", "Error in JSON parsing", err);
            }
        }

        return { dbExtra, dbCustomerOtherInfo , dbRecord};
    }

    prepareCustomerInfo(record, dbCustomerOtherInfo) {
        return {
            ...dbCustomerOtherInfo,
            msgId: _.get(record, 'msgId', ''),
            sms_id: _.get(record, 'sms_id', ''),
            sms_date_time: _.get(record, 'sms_date_time', ''),
            sender_id: _.get(record, 'sender_id', ''),
            predicted_category: _.get(record, 'predicted_category', null)
        };
    }

    createRecordForAnalytics(record, source_subtype_2, user_type) {
        let self=this;
        let recordForAnalytics={};
        let extraInfo=self.getExtra(record);
        recordForAnalytics.source = _.get(extraInfo, 'updated_data_source', 'SMS_PARSING_DWH');
        recordForAnalytics.source_subtype_2 = source_subtype_2;
        recordForAnalytics.user_type = user_type;
        recordForAnalytics.customer_id = _.get(record, 'cId', null) !== null?_.get(record, 'cId', null) :_.get(record, 'customerId', null);
        recordForAnalytics.service = 'loan'
        recordForAnalytics.recharge_number = _.get(record, 'rechargeNumber', null) !==null?_.get(record, 'rechargeNumber', null):_.get(record, 'account_no', null);
        recordForAnalytics.operator = _.get(record, 'operator', null);
        recordForAnalytics.due_amount = _.get(record, 'emi_due', null)!==null?_.get(record, 'emi_due', null):_.get(record, 'amount', null);
        recordForAnalytics.additional_info = null;
        recordForAnalytics.sms_id = _.get(record, 'smsUUID', null) !== null?_.get(record, 'smsUUID', null):_.get(record, 'sms_id', null);
        recordForAnalytics.paytype = 'loan'
        recordForAnalytics.updated_at=_.get(record, 'updatedAt', null);
        recordForAnalytics.sender_id = _.get(record, 'smsSenderID', null) !== null ? _.get(record, 'smsSenderID', null) : _.get(record, 'senderId', null);
        recordForAnalytics.sms_date_time = _.get(record, 'sms_date_time', null) !== null ? _.get(record, 'sms_date_time', null) : _.get(record, 'smsDateTime', null);
        recordForAnalytics.predicted_category = _.get(record, 'predicted_category', null) !== null ? _.get(record, 'predicted_category', null) : _.get(record, 'dwhClassId', null);
        recordForAnalytics.due_date = _.get(record, 'dueDate', utility.getFilteredDate(_.get(record, 'due_date', null)).value);
        recordForAnalytics.bill_date = _.get(record, 'billDate', MOMENT(record.smsDateTime).isValid() ?  MOMENT(record.smsDateTime).format('YYYY-MM-DD HH:mm:ss') : MOMENT().format('YYYY-MM-DD HH:mm:ss'));
        recordForAnalytics.bill_fetch_date = _.get(record, 'billFetchDate', MOMENT().format('YYYY-MM-DD HH:mm:ss'));
        return recordForAnalytics;
    }

    getBillStatus(record){
        let dueAmount= _.get(record, 'emi_due', null)!==null?_.get(record, 'emi_due', null):_.get(record, 'amount', null);
        if (dueAmount == null || record.dueDate == null) {
            return "PARTIAL_BILL";
        }
        if(dueAmount>0 &&
         (MOMENT(record.dueDate).isValid()) && (MOMENT(record.dueDate).diff(MOMENT().endOf('day'), 'days') >= 0)) {
             return "FULL_BILL";
         }
         return  null;
    }

    getExtra(record) {
        let extra = _.get(record, 'extra', null)
        if (!extra || extra == 'null') {
            extra = {};
        } else if (typeof extra == 'string')
            extra = JSON.parse(extra);
        return extra;
    }

    validateProcessedRecord(record) {
        let self = this;
        let mandatoryParams = ['customerId', 'operator'];
        let fieldsNotPresent = [];
        
        if(!self.checkCategoryEnabled(record.predicted_category)) {
            self.sendMetrics(record, 'ERROR', 'CATEGORY_NOT_ENABLED', 'validateProcessedRecord', ['PREDCITED_CATEGORY:' + record.predicted_category]);
            return 'CATEGORY_NOT_ENABLED';
        }

        // Check operator mapping
        if (record.dwhOperator && !record.operator) {
            return 'OPERATOR_MAPPING_NOT_FOUND';
        }
        
        mandatoryParams.forEach(function(key) {
            if (!record[key]) fieldsNotPresent.push(key);
        });
        if (fieldsNotPresent.length > 0) {
            return `MANDATORY_PARAMS_MISSING: ${fieldsNotPresent.join(', ')}`;
        }
        
        if (record.amount != null) {
            if (record.amount < self.minAmount) {
                return 'AMOUNT_LESS_THAN_MIN_AMOUNT';
            }
            if (record.amount > self.maxAmount) {
                return 'AMOUNT_GREATER_THAN_MAX_AMOUNT';
            }
        }

        if (record.dueDate) {
            if (!MOMENT(record.dueDate).isValid()) {
                return 'INVALID_DUE_DATE';
            }
            
            if (MOMENT(record.dueDate).diff(MOMENT().startOf('day'), 'days') < 0) {
                return 'DUE_DATE_IN_PAST';
            }
        }
        
        if (record.rechargeNumber) {
            if (!self.regexExecutor.checkValidityOfRechargeNumberByRegex(record)) {
                self.L.log('smsParsingLoanEmi::validateProcessedRecord', `rechargeNumber Invalid (Regex) for debugKey:${record.debugKey}`);
                return 'RECHARGE_NUMBER_INVALID_REGEX';
            }
            self.L.log('smsParsingLoanEmi::validateProcessedRecord', `rechargeNumber Valid (Regex) for debugKey:${record.debugKey}`);
        }
        
        return null;
    }
    
    checkCategoryEnabled(category) {
        let self = this;
        if (!self.enabledPredictedCategories.includes(category)) {
            return false;
        }
        return true;
    }

    containsMaskedCharacters(str) {
        // Check for common masking patterns
        return str.includes('x') || 
               str.includes('X') || 
               str.includes('*') || 
               str.includes('#');
    }
    
    getMappedOperator(dwhOperator) {
        let self = this;
        if(!dwhOperator) return null;
        self.L.log('getMappedOperator', `DWH Operator: ${dwhOperator}`);
        let operator =  _.get(self.config, ['DYNAMIC_CONFIG', 'SMS_PARSING_LOAN_EMI', dwhOperator, 'RU_MAPPED_OPERATOR'], null);
        return _.toLower(operator);
    }

    shouldProcessRecord(record) {
        return record.rechargeNumber && 
               record.rechargeNumber !== '' && 
               this.billsLib.isRUOperator(record, 'SMS_PARSING_LOAN_EMI');
    }
    
    sendMetrics(record, status, type, source, extraTags) {
        let operator = _.get(record, 'operator', 'NoOperator');
        let service = _.get(record, 'service', 'NoService');
        let tagsArr = ['REQUEST_TYPE:SMS_PARSING_LOAN_EMI', 'STATUS:' + status, 'OPERATOR:' + operator, 'SERVICE:' + service];
        
        if(type) {
            tagsArr.push('TYPE:' + type);
        }
        
        if(source) {
            tagsArr.push('SOURCE:' + source);
        }
        
        if(extraTags && _.isArray(extraTags)) {
            tagsArr = tagsArr.concat(extraTags);
        }
        
        try {
            utility._sendMetricsToDD(1, tagsArr);
        } catch (error) {
            this.L.error('SmsParsingLoanEmi :: sendMetrics', `Error in sending metrics`, error);
        }
    }
    
    isSmsParsingEnabled(record) {
        let self = this;
        let service = _.toLower(_.get(record, 'service', null));
        let operator = _.toLower(_.get(record, 'operator', null));
        
        if (!service || !operator) {
            return false;
        }

        return self.enabledServiceList.includes(service) || self.enabledOperatorList.includes(operator);
    }
    
    async checkEligibilityAndSetNBFD(cb, processedRecord) {
        let self = this;
        let dbRecord = _.get(processedRecord, 'dbData', []),
        dbDueDate = _.get(dbRecord, '[0].due_date', null);
        
        try {
            // Check if NBFD from DB is in future
            let dbNBFD = _.get(dbRecord, '[0].next_bill_fetch_date', null);
            if (dbNBFD && MOMENT(dbNBFD).isValid() && MOMENT(dbNBFD).isAfter(MOMENT())) {
                self.L.log('SmsParsingLoanEmi :: checkEligibilityAndSetNBFD', `Skipping payload - NBFD in future for ${processedRecord.debugKey}`);
                self.sendMetrics(processedRecord, 'SKIPPED', 'NBFD_IN_FUTURE', 'checkEligibilityAndSetNBFD');
                return cb('NBFD_IN_FUTURE');
            }
            
            // Due Date validation
            if (processedRecord.dueDate) {
                let smsDueDate = MOMENT(processedRecord.dueDate).startOf('day');
                let dbDueDateMoment = MOMENT(dbDueDate).startOf('day');

                if(smsDueDate.isBefore(dbDueDateMoment)) {
                    self.L.error('SmsParsingLoanEmi :: checkEligibilityAndSetNBFD', `Invalid due date sequence for ${processedRecord.debugKey}`);
                    self.sendMetrics(processedRecord, 'ERROR', 'SMS_DUEDATE_BEFORE_DB_DUEDATE', 'checkEligibilityAndSetNBFD');
                    return cb('Sms Due Date before DB Due Date');
                }
            }

            // Calculate and set NBFD
            const nextBillFetchDate = self.calculateNextBillFetchDate(processedRecord);
            processedRecord.nextBillFetchDate = nextBillFetchDate;
            _.set(processedRecord, ['billsData', 'nextBillFetchDate'], nextBillFetchDate);
            
            self.L.log('SmsParsingLoanEmi :: checkEligibilityAndSetNBFD', `Set NBFD to ${nextBillFetchDate} ${processedRecord.debugKey}`);
            return cb(null);
        } catch (error) {
            self.L.error('SmsParsingLoanEmi :: checkEligibilityAndSetNBFD', `Error in processing ${processedRecord.debugKey}`, error);
            self.sendMetrics(processedRecord, 'ERROR', 'ELIGIBILITY_CHECK_FAILED', 'checkEligibilityAndSetNBFD');
            return cb(error);
        }
    }

    calculateNextBillFetchDate(processedRecord) {
        let self = this;
        
        // Calculate initial NBFD based on available dates
        let nbfd = this.getInitialNBFD(processedRecord);
        let nextBillFetchDate = nbfd.format('YYYY-MM-DD HH:mm:ss');

        // Ensure NBFD is not in the past
        if (MOMENT(nextBillFetchDate).isBefore(MOMENT())) {
            self.L.verbose(`getNextBillFetchDate:: Change NBFD, currently set in past. debugKey: ${processedRecord.debugKey}, NBFD: ${nextBillFetchDate}`);
            nextBillFetchDate = MOMENT().add(1, 'days').format('YYYY-MM-DD HH:mm:ss');
            self.sendMetrics(processedRecord, 'SUCCESS', 'NBFD_SETTING_IN_PAST', 'calculateNextBillFetchDate');
        }

        return nextBillFetchDate;
    }

    getInitialNBFD(processedRecord) {
        const self = this;
        // Priority 1: Due Date
        if (processedRecord.dueDate && MOMENT(processedRecord.dueDate).isValid()) {
            return MOMENT(processedRecord.dueDate).add(self.dueDateNbfdDays, 'days');
        }
        
        // Priority 2: Bill Date
        if (processedRecord.billDate && MOMENT(processedRecord.billDate).isValid()) {
            return MOMENT(processedRecord.billDate).add(self.billDateNbfdDays, 'days');
        }
        
        // Fallback: Current Date
        return MOMENT().add(self.billDateNbfdDays, 'days');
    }
    
    async updateDbRecord(done, record) {
        let self = this;
        self.L.log('SmsParsingLoanEmi :: updateDbRecord', `Starting update process for ${record.debugKey}`);
        
        try {
            if (record.activeRecordsInDB === 0) {
                self.L.log('SmsParsingLoanEmi :: updateDbRecord', `No active records in DB for ${record.debugKey}`);
                self.sendMetrics(record, 'ERROR', 'NO_ACTIVE_RECORDS_IN_DB', 'updateDbRecord');
                return done(null);
            }
            
            // Handle automatic records
            if (_.get(record, ['billsData','is_automatic'], 0) !== 0) {
                self.L.log('SmsParsingLoanEmi :: updateDbRecord', `Updating NBFD for automatic record ${record.debugKey}`);
                self.sendMetrics(record, 'AUTOMATIC_RECORD', 'UPDATE_BILLS_NBFD', 'updateDbRecord');
                return self.bills.updateBillsNBFD(done, record.tableName, record);
            }
            _.set(record, ['billsData', 'isDemergerCase'], _.get(record, 'isDemergerCase', false));
            // Handle non-automatic records
            self.L.log('SmsParsingLoanEmi :: updateDbRecord', `Updating records in SQL DB for ${record.debugKey}`);
            
            await new Promise((resolve, reject) => {
                self.bills.updateBillForSameRechargeNumPostpaid((err) => {
                    _.set(self.timestamps, 'RUupdatesDbTime', new Date().getTime());
                    if (err) {
                        reject(err);
                    } else {
                        self.L.log('SmsParsingLoanEmi :: updateRecord ::', `updateBillForSameRechargeNumPostpaid complete in smsParsing`);
                        resolve();
                    }
                }, record.tableName, record.billsData);
            });
            
            // Handle automatic failed cases
            if (record.is_automatic === 3 || record.is_automatic === 4) {
                self.L.log('SmsParsingLoanEmi :: updateRecord::', `resetIsAutomatic with tableName: ${record.tableName}, debugKey: ${record.debugKey}`);
                
                try {
                    await self.resetAutomaticStatus(record);
                } catch (err) {
                    self.L.error('SmsParsingLoanEmi :: updateRecord::', 'Error resetting automatic status', err);
                    throw err;
                }
            } else {
                self.L.log('SmsParsingLoanEmi :: updateRecord::', `resetIsAutomaticCustId is not required`);
            }

            return done(null);
            
        } catch (error) {
            self.L.error('SmsParsingLoanEmi :: updateDbRecord', `Error updating DB record for ${record.debugKey}`, error);
            return done(error);
        }
    }
    
    // Helper method to reset automatic status
    resetAutomaticStatus(record) {
        let self = this;
        return new Promise((resolve, reject) => {
            self.bills.resetIsAutomatic((err) => {
                _.set(self.timestamps, 'RUupdateRecentTime', new Date().getTime());
                if (err) {
                    reject(err);
                } else {
                    self.L.log('SmsParsingLoanEmi :: resetIsAutomaticCustId::', "reseting is_automatic complete in smsParsing");
                    resolve();
                }
            }, record, [3, 4], {isDemergerCase: record.isDemergerCase, customerId: record.customerId});
        });
    }
    
    async publishInKafka(done, record) {
        let self = this;
        ASYNC.parallel([
            function (cb) {
                self.publishInBillFetchKafka(function(err){
                    cb(err)
                },record)
            },
            function (cb) {
                self.publishCtEvents(function(err){
                    cb(err)
                },record)
            },
        ], function(error) {
            if (error) {
                self.L.error('SmsParsingLoanEmi :: publishInKafka', `Error occurred during parallel tasks`, error);
            }
            done(error);
        });
    }
    
    publishInBillFetchKafka(done,processedRecord){
        let self = this;
        let dbData = _.get(processedRecord, 'dbData', []);
        self.L.log(`SmsParsingLoanEmi :: publishInBillFetchKafka :: debugKey - ${processedRecord.debugKey}`);
        
        ASYNC.eachLimit(dbData, 3, (dataRow, cb) => {
            let dbDebugKey = `rech:${dataRow.recharge_number}::cust:${dataRow.customer_id}::op:${dataRow.operator}`;
            
            if(dataRow.notification_status == 0){
                self.L.error(`SmsParsingLoanEmi :: publishInBillFetchKafka :: stop publishing data on REMINDER_BILL_FETCH_REALTIME via the Kafka pipeline for notification status : ${dataRow.notification_status} debugKey::`, dbDebugKey);
                return cb();                         
            }
            
            if(dataRow.is_automatic != 0 && dataRow.is_automatic != 5 && dataRow.is_automatic != 8){
                self.L.error(`SmsParsingLoanEmi :: publishInBillFetchKafka :: stop publishing data on REMINDER_BILL_FETCH_REALTIME via the Kafka pipeline for automatic status : ${dataRow.is_automatic} debugKey::`, dbDebugKey);
                return cb();                         
            }

            if(dataRow.status == _.get(self.config , ['COMMON','bills_status','DISABLED'],7) || dataRow.status == _.get(self.config , ['COMMON','bills_status','NOT_IN_USE'],13)){
                self.L.error(`stop publishing data on REMINDER_BILL_FETCH_REALTIME as inactive record, status : ${dataRow.status} debugKey::`, dbDebugKey);
                return cb();
            }

            let { productId, circle } = fetchProductIdAndCircle(dataRow);
            
            let payload = {
                source: self.smsParsingBillsDwhRealtime == true ? "postpaidBillFetchDWHRealtime" : ((_.get(processedRecord,'isRuSmsParsing', false)==true) ? "postpaidBillFetchRealtime" : "postpaidBillFetchDWH"),
                notificationType: "BILLGEN",
                data: {
                    customerId: dataRow.customer_id,
                    rechargeNumber: dataRow.recharge_number,
                    productId: productId,
                    operator: processedRecord.operator,
                    amount: processedRecord.amount,
                    bill_fetch_date: MOMENT(),
                    paytype: _.get(processedRecord, 'paytype', 'loan'),
                    service: processedRecord.service,
                    circle: circle,
                    customerMobile:  dataRow.customer_mobile,
                    customerEmail: dataRow.customer_email,
                    status: self.config.COMMON.bills_status.BILL_FETCHED,
                    userData: dataRow.user_data,
                    billDate: processedRecord.billDate,
                    notification_status: dataRow.notification_status,
                    dueDate: processedRecord.dueDate,
                    customerOtherInfo: JSON.stringify(processedRecord),
                    planBucket: processedRecord.planBucket,
                    is_automatic: dataRow.is_automatic,
                    extra: _.get(processedRecord, ["billsData", "extra"], '{}'),
                    paymentDate : dataRow.paymentDate,
                    dwhKafkaPublishedTime: _.get(processedRecord, 'dwhKafkaPublishedTime', null),
                }
            }
            
            if(processedRecord.status == _.get(self.config, 'COMMON.bills_status.OLD_BILL_FOUND', 5) &&
            self.allowedServiceToSaveOldDueDate.indexOf(_.toLower(_.get(processedRecord, 'service', null))) > -1 && 
            !(self.oldBillFetchDueDateBlacklistedOperators.indexOf(_.toLower(_.get(processedRecord, 'operator', null))) > -1) 
            && _.get(processedRecord, 'oldBillFetchDate', null) == MOMENT().startOf('day').format('YYYY-MM-DD HH:mm:ss')){
                _.set(payload, "notificationType", "OLD_BILL_NOTIFICATION");
                _.set(payload, ["data", "status"], _.get(self.config, 'COMMON.bills_status.OLD_BILL_FOUND', 5));
            }
            
            let kafkaProducer;
            
            _.set(payload, ['data', 'billFetchReminder_onBoardTime'],new Date().getTime());
            
            if(_.get(processedRecord,'isDwhSmsParsingRealtime', false) === true && self.billFetchRealTimeKafkaPublisher !== undefined && self.billFetchRealTimeKafkaPublisher !== null) {
                kafkaProducer = self.billFetchRealTimeKafkaPublisher;
                self.L.log("SmsParsingLoanEmi :: publishInBillFetchKafka :: isDwhSmsParsingRealtime is true. Publishing in reminder kafka");
            } else {
                self.L.log("SmsParsingLoanEmi :: publishInBillFetchKafka :: isDwhSmsParsingRealtime is false. Publishing in recharges kafka");
                kafkaProducer = self.billFetchKafkaPublisher;
            }
            
            let topicName = (_.get(processedRecord,'isRuSmsParsing', false)==true || _.get(processedRecord,'isDwhSmsParsingRealtime', false)==true)? _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE_REALTIME.REMINDER_BILL_FETCH_REALTIME', '') : _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE.REMINDER_BILL_FETCH', '');
            
            self.L.log("SmsParsingLoanEmi :: publishInBillFetchKafka :: Pushing data in topic : ", topicName);
            utility.sendNotificationMetricsFromSource(payload)
            kafkaProducer.publishData([{
                topic: topicName,
                messages: JSON.stringify(payload)
            }], (error) => {
                if (error) {
                    utility.sendNotificationMetricsFromSource(payload,"ERROR")
                    self.sendMetrics(processedRecord, 'ERROR', 'KAFKA_PUBLISH', 'publishInBillFetchKafka', ['TOPIC:' + topicName, 'ORIGIN:' + self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord), 'APP_VERSION:' + _.get(processedRecord,'appVersion', null)]);
                    self.L.critical('SmsParsingLoanEmi :: publishInBillFetchKafka :: REMINDER_BILL_FETCH_REALTIME', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(payload), error);
                } else {
                    self.sendMetrics(processedRecord, 'PUBLISHED', 'KAFKA_PUBLISH', 'publishInBillFetchKafka', ['TOPIC:' + topicName, 'ORIGIN:' + self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord), 'APP_VERSION:' + _.get(processedRecord,'appVersion', null)]);
                    self.L.log('SmsParsingLoanEmi :: publishInBillFetchKafka :: ', 'Message published successfully in Kafka', ' on topic REMINDER_BILL_FETCH', JSON.stringify(payload));
                }
                return cb(null);
            }, [200, 800]);
        }, (error, res) => {
            if (error) {
                self.L.error("SmsParsingLoanEmi :: publishInBillFetchKafka :: ", "Error occured", error);
            }
            return done(error);
        });
        
        function fetchProductIdAndCircle(dataRow) {
            let productId, circle;
            if (dataRow.product_id && dataRow.circle && _.get(dataRow, "circle", "null").toLowerCase() != _.get(processedRecord, "circle", "null").toLowerCase()) {
                if(_.get(dataRow, "circle", "null").toLowerCase() != _.get(processedRecord, "circle", "null").toLowerCase()){
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:SMS_PARSING_LOAN_EMI", 
                        `SERVICE:${_.get(processedRecord, 'service', null)}`, 
                        'STATUS:PRODUCT_ID_UPDATE',
                        "OPERATOR:" + _.get(processedRecord,'operator',null),
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                        `APP_VERSION:${_.get(processedRecord,'appVersion', null)}`
                    ]);
                }
                self.L.info('Circle is updated from ', _.get(processedRecord, "circle"), ' to ', _.get(dataRow, "circle"), processedRecord.debugKey);
                productId = dataRow.product_id;
                circle = dataRow.circle;
            }
            else {
                productId = processedRecord.productId;
                circle = processedRecord.circle;
            }
            return { productId, circle };
        }
    }
    
    async publishCtEvents(done,record) {
        let self = this;
        self.L.log(`11. publishCtEvents:: Record Category - ${record.service}`);
        const productId = _.get(record, 'productId', 0);
        let dbData = _.get(record, 'dbData', []);
        let eventName = self.ctEventName;
        
        ASYNC.eachLimit(dbData, 3, (dataRow, cb) => {
            
            let dbDebugKey = `rech:${dataRow.recharge_number}::cust:${dataRow.customer_id}::op:${dataRow.operator}`;
            if ((dataRow.status == 13 || dataRow.status == 7)) {
                self.L.log('publishInKafka', `Skipping pulish CT for ${record.debugKey}, dbRow::${dbDebugKey} | due to inactice record`);
                return cb();
            }
            
            if(self.commonLib.isCTEventBlocked(eventName)){
                self.L.info(`Blocking CT event ${eventName}`)
                return cb()
            }
            
            let billsData = record.billsData;
            dataRow.due_date = MOMENT(record.dueDate, 'YYYY-MM-DD HH:mm:ss').startOf('day').format('YYYY-MM-DD HH:mm:ss');
            
            if(dataRow.notification_status == 0){
                self.L.error(`SmsParsingLoanEmi :: publishCtEvents :: stop publishing data on CleverTap via the Kafka pipeline for notification status : ${dataRow.notification_status} debugKey::`, dbDebugKey);
                return cb();                         
            }
            
            if(dataRow.notification_status == null)
                dataRow.notification_status = 1
            dataRow.bill_date = record.billDate ? MOMENT.utc(record.billDate, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD') : null;
            dataRow.amount = record.amount;
            dataRow.bill_fetch_date = MOMENT.utc(_.get(billsData, 'billFetchDate', null)).format('YYYY-MM-DD HH:mm:ss');
            dataRow.next_bill_fetch_date = MOMENT.utc(_.get(billsData, 'nextBillFetchDate', null)).format('YYYY-MM-DD HH:mm:ss');
            dataRow.status = _.get(billsData, 'commonStatus', 0);
            dataRow.extra = _.get(billsData, 'extra', null);
            dataRow.updated_at = MOMENT.utc().format('YYYY-MM-DD HH:mm:ss');
            dataRow.customerOtherInfo = _.get(billsData, 'customerOtherInfo', null);
            dataRow.rtspClassId = _.get(record, 'rtspClassId', null);
            dataRow.dwhClassId = _.get(record, 'dwhClassId', null);
            
            ASYNC.waterfall([
                next => {
                    self.commonLib.getRetailerData((error) => {
                        if(error) {
                            return next(error)
                        } else {
                            return next(null)
                        }
                    }, dataRow.customer_id, dataRow);
                },
                next => {
                    self.commonLib.getCvrData((error) => {
                        if(error) {
                            return next(error)
                        } else {
                            return next(null)
                        }
                    }, productId, dataRow);
                },
                next => {                    
                    let mappedData = self.reminderUtils.createCTPipelinePayload(dataRow, eventName, dbDebugKey);
                    let clonedData = _.cloneDeep(mappedData); // needed for correct async logging
                    self.ctKafkaPublisher.publishData([{
                        topic: _.get(self.config.KAFKA, 'SERVICES.CT_EVENTS_PUBLISHER.TOPIC', ''),
                        messages: JSON.stringify(mappedData)
                    }], (error) => {
                        if (error) {
                            utility._sendMetricsToDD(1, [
                                "REQUEST_TYPE:SMS_PARSING_LOAN_EMI", 
                                `SERVICE:${_.get(record, 'service', null)}`, 
                                'STATUS:ERROR', 
                                "TYPE:KAFKA_PUBLISH",
                                "TOPIC:CT_EVENTS", 
                                "OPERATOR:" + dataRow.operator,
                                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                                `APP_VERSION:${_.get(record,'appVersion', null)}`
                            ]);
                            self.L.critical('postpaidSmsParsing :: publishCtEvents', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(clonedData), error);
                        } else {
                            utility._sendMetricsToDD(1, [
                                "REQUEST_TYPE:SMS_PARSING_LOAN_EMI", 
                                `SERVICE:${_.get(record, 'service', null)}`, 
                                'STATUS:PUBLISHED', 
                                "TYPE:KAFKA_PUBLISH",
                                "TOPIC:CT_EVENTS", 
                                "OPERATOR:" + dataRow.operator,
                                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                                `APP_VERSION:${_.get(record,'appVersion', null)}`,
                                `EVENT_NAME:${eventName}`
                            ]);
                            self.L.log('postpaidSmsParsing :: publishCtEvents', 'Message published successfully in Kafka', ' on topic REMINDER_CT_EVENTS', JSON.stringify(clonedData));
                        }
                        return next(error);
                    }, [200, 800]);
                }
            ], (err) => {
                if(err)
                    self.L.log('postpaidSmsParsing :: publishCtEvents', 'Error while publishing message in Kafka: ', err)
                return cb(err)
            })
        }, (error, res) => {
            if (error) {
                self.L.error("SmsParsingLoanEmi :: publishCtEvents ", "Error occured", error);
            }
            return done(error);
        });
    }
    
    getOriginOfPayloadCurrentlyBeingProcessed(record) {
        let self = this;
        if(self.smsParsingBillsDwhRealtime) {
            return "SMS_PARSING_DWH_REALTIME"
        }
        return _.get(record,'isRuSmsParsing', false)==true? 'SMS_PARSING_REALTIME':'SMS_PARSING_DWH';
    }

    getBillsOfSameRN(params) {
        let self = this;
        return new Promise((resolve, reject) => {
            self.bills.getBillsOfSameRech((err, dbRecord) => {
                if(err) {
                    self.L.error(`SmsParsingLoanEmi :: findRechargeNumberMatch :: Error on getting records from db with error: ${err}`);
                    self.sendMetrics(params, 'ERROR', 'ERROR_GETTING_RECORDS_FOR_SAME_RN', 'findRechargeNumberMatch');
                    reject(err)
                }
                resolve(dbRecord);
            }, params.tableName, {
                rechargeNumber: params.rechargeNumber,
                service: params.service,
                operator: params.operator
            })
        })
    }
    
    async suspendOperations() {
        const self = this;
        self.L.log(`SmsParsingLoanEmi :: suspendOperations kafka consumer shutdown initiated`);
    
        try {
            // Clear config refresh interval
            if (this.configRefreshInterval) {
                clearInterval(this.configRefreshInterval);
                self.L.log('SmsParsingLoanEmi :: suspendOperations', 'Config refresh interval cleared');
            }

            // Close kafka consumer
            await new Promise((resolve, reject) => {
                self.kafkaSMSParsingLoanConsumer.close((error, res) => {
                    if (error) {
                        self.L.error(`SmsParsingLoanEmi :: stopConsumer :: Error Stopping Consumer Err : ${error}`);
                        reject(error);
                    } else {
                        self.L.info(`SmsParsingLoanEmi :: stopConsumer :: Consumer Stopped! Response : ${res}`);
                        resolve();
                    }
                });
            });
    
            self.L.log(`SmsParsingLoanEmi :: suspendOperations kafka consumer shutdown successful`);
            return Promise.resolve();
    
        } catch (err) {
            self.L.error(`SmsParsingLoanEmi :: suspendOperations error in shutting kafka consumer`, err);
            return Promise.reject(err);
        }
    }
}

export default SmsParsingLoanEmi;