"use strict";
import _, { reject } from 'lodash'
import utility from '../lib'
import RecentsLayerLib from '../lib/recentsLayer'
import VALIDATOR from 'validator'
import ASYNC from 'async'
import OS from 'os'
import MOMENT from 'moment'
import BILLS from '../models/bills'
import MODELS from '../models'
import digitalUtility from 'digital-in-util'
import CATALOGVERTICALRECHARGE from '../models/catalogVerticalRecharge';
import SmsParsingSyncCCBillLibrary from '../lib/smsParsingSyncCCBills';
import PG from '../lib/pg'
import DigitalCatalog from '../lib/digitalReminderConfig'
import RemindableUsersLibrary from '../lib/remindableUser'
import uuidv1 from 'uuidv1'
import recentBillLibrary from '../lib/recentBills'
import BILLSUBSCRIBER from './billSubscriber'
import nonPaytmBills from "../models/nonPaytmBills";
import STATSD from 'paytm-statsd';

import Q from 'q'
import { nextTick } from 'process';

class ccIngestionNonRu {
    constructor(options) {
        this.L = options.L;
        this.infraUtils = options.INFRAUTILS;
        this.config = options.config;
        this.bills = new BILLS(options);
        this.billsModel = new MODELS.Bills(options)
        this.nonPaytmBillsModel  = new nonPaytmBills(options)
        this.commonLib = new utility.commonLib(options);
        this.recentsLayerLib = new RecentsLayerLib(options); // TODO
        this.billSubscriber = new BILLSUBSCRIBER(options);
        this.bills_operator_table = _.get(this.config, 'OPERATOR_TABLE_REGISTRY', {});
        this.recentBillLibrary = new recentBillLibrary(options);
        this.infraUtils = _.get(options, 'INFRAUTILS', null);
        this.cc_bills_operators = this.recentBillLibrary._initRecentBillSpecificData(this.bills_operator_table, this.ccOperators);
        this.paymentGatewayUtils = new PG(options);
        this.smsParsingSyncCCBillLib = new SmsParsingSyncCCBillLibrary(options);
        this.digitalCatalogLib = DigitalCatalog;
        this.remindableUsersLibrary = new RemindableUsersLibrary(options);
        this.paytmBillsTable = 'bills_creditcard';
        this.nonPaytmBillsTable = 'bills_non_paytm';
        this.refereshIntervalForCategoryData = 15 * 60 * 1000; // DCAT getCategoryProductDetail API refresh interval 
        this.reminderUtils = new digitalUtility.ReminderUtils();
        this.catalogVerticalRecharge = new CATALOGVERTICALRECHARGE(options);
        this.activePidLib = options.activePidLib;
        this.greyScaleEnv = _.get(options, 'greyScaleEnv', false);
        this.commonLib = new utility.commonLib(options);
        this.kafkaBatchSize = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'SMS_PARSING_CC_BILLS', 'BATCHSIZE'], 2) : 500;
        this.kafkaBatchDelay = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'SMS_PARSING_CC_BILLS', 'DELAY'], 5 * 60 * 1000) : 0;
        this.bbpsBanks = ["sbi", "nkmb", "aubl", "canara", "fdeb", "bob", "inds"];
    }

    start() {
        let self=this;
        self.smsParsingSyncCCBillLib.refreshDCATCacheData(self.refereshIntervalForCategoryData);
        ASYNC.waterfall([
            next=>{
                self._configureAndStartConsumer((error) => {
                    if (error) {
                        self.L.error("ccIngestionNonRu consumer", "Failed to initialize ccIngestionNonRu consumer service");
                        process.exit(1);
                    }
                });
                next();
            },
            next => {

                self.nonPaytmKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.CC_INGESTION_NONRU.HOSTS
                });
                this.nonPaytmKafkaPublisher.initProducer('high', function (error) {
                    self.L.critical('nonPaytmKafkaPublisher:: error in initialising Producer :: ', error);
                    self.L.log("RECENTBILLS :: NON_PAYTM_RECORDS KAFKA PRODUCER STARTED....");
                    self._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION_NONRU", 'STATUS:ERROR', 'TYPE:KAFKA_PRODUCER', 'TOPIC:NON_PAYTM']);
                });
            }
        ],function (err) {
            process.exit(0);
        })
    }

    _configureAndStartConsumer() {
        let
            self = this;

        self.L.log('_configureAndStartConsumer', `Going to initialize Kakfa Consumer for topic ${_.get(self.kafkaConsumerConfig, 'TOPIC', null)}`);

        // Initialize consumer for the topic --> CC_INGESTION_NONRU
        let topic = _.get(self.config.KAFKA, 'SERVICES.CC_INGESTION_NONRU.TOPIC');
        let kafkaHost = _.get(self.config.KAFKA, 'TOPICS.CC_INGESTION_NONRU.HOSTS');

        self.kafkaccIngestionNonRuConsumer = new self.infraUtils.kafka.consumer({
            "kafkaHost": kafkaHost,
            "groupId": "ccIngestionNonRuConsumer",
            "topics": topic,
            "id": 'ccIngestionNonRuConsumer_' + OS.hostname(),
            "fromOffset": "earliest",
            "autoCommit": false,
            "batchSize": self.kafkaBatchSize
        });

        self.kafkaccIngestionNonRuConsumer.initConsumer(self.execSteps.bind(self), (error) => {
            if (error) {
                self.L.critical("_configureAndStartConsumer", "error while configuring consumer", error);
            } else {
                self.L.log("_configureAndStartConsumer", `consumer of topic : ${topic} Configured`);
            }
            return;
        });
    }

    execSteps(records) {
        let self = this,
            chunkSize = 2,   // ?
            currentPointer = 0, lastMessage;
      
        let startTime = new Date().getTime();
        if (records && _.isArray(records)) {
            lastMessage = records[records.length - 1];
            self.kafkaccIngestionNonRuConsumer._pauseConsumer();
            self._sendMetricsToDD(records.length, [
                'STATUS:CC_DETAILS_CONSUMED',
                'TOPIC:' + _.get(self.config.KAFKA, 'SERVICES.CC_INGESTION_NONRU.TOPIC'),
                'REQUEST_TYPE:ccIngestionNonRu_CONSUMER'
            ])
        } else {
            self.L.critical('execSteps', `No valid kafka records found. Received records-`, records);
            return;
        }

        self.L.log('execSteps:: ', `Processing ${records.length} ccIngestionNonRu Data !!`);

        ASYNC.whilst(
            () => {
                return currentPointer < records.length;
            },
            (callback) => {
                let nextChunk = records.slice(currentPointer, (currentPointer + chunkSize));
                currentPointer += chunkSize;
                self.processBatch(nextChunk, () => {
                    setTimeout(() => {
                        callback();
                    }, 1);  // ?
                });
            },
            (err) => {
                try{
                    self.kafkaccIngestionNonRuConsumer.commitOffset(lastMessage, (error) => {
                        if (error) {
                            self.L.error('execSteps::', 'Error while commit for-', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                        }
                        else {
                            self.L.log('execSteps::', 'Commit success for offset :', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                        }
    
                        // Resume consumer now
                        let endTime = new Date().getTime();
                        let executionTime = (endTime - startTime) / 1000;      //in seconds
                        executionTime = Math.round(executionTime);
                        self.L.log('execSteps::', 'per batchSize record Execution time :', executionTime, 'seconds', 'recordLength :',records.length);
                        self._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_STATUS", 'STATUS:SUCCESS', "SOURCE:CC_INGESTION_NONRU", "TIME_TAKEN:" + executionTime]);

                        self.kafkaccIngestionNonRuConsumer._resumeConsumer();
                    });
                } catch (err){
                    self.L.error('execSteps::', 'Error while commit for-', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                    self.kafkaccIngestionNonRuConsumer._resumeConsumer();
                }
            }
        );
    }

    processBatch(records, done) {
        let self = this;
        ASYNC.map(
            records,
            (record, next) => {
                self.processData(() => {
                    next();
                }, record);
            },
            err => {
                done();
            }
        )
    }

    processData(done, data) {
        let self = this,
            record, 
            debugKey = data;

        ASYNC.waterfall([
            (callback) => {
                self.validateAndConvertKafkaPayloadToRecord(function (error, result) {
                    if (error) {
                        return callback(`Unable to validate data, error - ${error}`);
                    } else {
                        record = result;
                        return callback();
                    }
                }, data);
            },
            (callback) => {
                self.processRecord((error) => {
                    if (error) {
                        return callback(error);
                    } else {
                        return callback(null);
                    }
                }, record);
            }], (error) => {
                if (error) {
                    self.L.error('ccIngestionNonRu', 'debugKey', debugKey, 'error', error);
                }
                return done();
            }
        );
    }

    validateAndConvertKafkaPayloadToRecord(callback , kafkaPayload){
        let self = this;
        self.L.log('1. validateDataToProcessForNotification :: convert payload to record and validate');

        let record = self.validateKafkaPayload(kafkaPayload);

        if (record == null) {
            // Push a Metric to DataDog
            return callback('unable to get valid data from kafka payload', record);
        }

        return callback (null,record);
    }

    validateKafkaPayload(kafkaPayload) {
        let
            self = this,
            kafkaPayloadData;
        let record = {};

        try {
            const [customerId, rechargeNumber, operator, productId, service, paytype, bankCode , source] = kafkaPayload.value.match(/"([^"]*)"/g).map(value => value.replace(/"/g, ''));
            record.customerId = VALIDATOR.toInt(customerId);
            record.rechargeNumber = rechargeNumber;
            record.operator = operator;
            record.productId = productId;
            record.service = service;
            record.paytype = paytype;
            record.bankCode = bankCode;
            record.source = source;
        } catch (error) {
            if (error) {
                self.L.critical('validateKafkaPayload', `Invalid Kafka record received`, kafkaPayload);
                return null;
            }
        }

        let productId = _.get(record,'productId',null);

        if (!productId) {
            self.L.error("Product Id missing", _.toString(data));
            self._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION_NONRU", "STATUS:PARAMS_MISSING", "TYPE:PRODUCT_ID_MISSING"]);
            return null;
        }

        let operator = _.get(self.config, ["CVR_DATA", productId, "operator"], "");
        let bankAttributes, bankCode;
        try {
            bankAttributes = JSON.parse(_.get(self.config, ["CVR_DATA", productId, "attributes"], "{}"));
            bankCode = _.toLower(_.get(bankAttributes, ["bank_code"], ""));

            record.nonPaytmoperator = bankCode;
        }
        catch (error) {
            this.L.error('convertKafkaPayloadRecord', `Error occured while parsing CVR_DATA:${data}`, error)
            self._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION_NONRU", 'STATUS:ERROR', 'TYPE:PID_INVALID_PARSE', `PRODUCT_ID : ${productId}`]);
        }

        record.source = _.toUpper(record.source)


        if (!record.customerId) {
            self.L.error("Customer ID missing", record);
            self._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION_NONRU", "STATUS:record_MISSING", `SERVICE:${record.service}`, `OPERATOR:${record.operator}`, "TYPE:customerId_NOT_FOUND_FROM_CSV"]);
            return null;
        }
        if (!record.rechargeNumber) {
            self.L.error("Recharge number missing", record);
            self._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION_NONRU", "STATUS:record_MISSING", `SERVICE:${record.service}`, `OPERATOR:${record.operator}`, "TYPE:RECHARGE_NUMBER_NOT_FOUND_FROM_CSV"]);
            return null;
        }
        if (!record.operator) {
            self.L.error("Operator missing", record, _.get(data, "operator", null));
            self._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION_NONRU", "STATUS:record_MISSING", `SERVICE:${record.service}`, `OPERATOR:${record.operator}`, "TYPE:OPERATOR_NOT_FOUND_FROM_CSV"]);
            return null;
        }
        if (!record.service) {
            self.L.error("Service name missing", record);
            self._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION_NONRU", "STATUS:record_MISSING", `SERVICE:${record.service}`, `OPERATOR:${record.operator}`, "TYPE:SERVICE_NOT_FOUND_FROM_CSV"]);
            return null;
        }
        if (!record.paytype) {
            self.L.error("Paytype missing", record);
            self._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION_NONRU", "STATUS:record_MISSING", `SERVICE:${record.service}`, `OPERATOR:${record.operator}`, "TYPE:PAYTYPE_NOT_FOUND_FROM_CSV"]);
            return null;
        }
        if (!record.source) {
            self.L.error("Source missing", record);
            self._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION_NONRU", "STATUS:record_MISSING", `SERVICE:${record.service}`, `OPERATOR:${record.operator}`, "TYPE:SOURCE_NOT_FOUND_FROM_CSV"]);
            return null;
        }
        if (!record.bankCode) {
            self.L.error("BankName missing", record);
            self._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION_NONRU", "STATUS:record_MISSING", `SERVICE:${record.service}`, `OPERATOR:${record.operator}`, "TYPE:BANK_NAME_NOT_FOUND_FROM_CSV"]);
            return null;
        }

        if(!(_.toLower(bankCode) == _.toLower(record.bankCode))){
            self.L.error("BankCode Mismatch", record, bankCode);
            self._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION_NONRU", "STATUS:PARAMS_INVALID", `SERVICE:${record.service}`, "TYPE:INVALID_BANKCODE"]);
            return null;
        }

        if (!(_.toLower(record.operator) == _.toLower(operator))) {
            self.L.error("Operator Mismatch", record, operator);
            self._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION_NONRU", "STATUS:PARAMS_INVALID", `SERVICE:${record.service}`, "TYPE:INVALID_OPERATOR"]);
            return null;
        }

        if (!self.validateRechargeNumber(record ,record.rechargeNumber)) {
            self.L.error("Invalid Recharge number", record);
            self._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION_NONRU", "STATUS:PARAMS_INVALID", `SERVICE:${record.service}`, `OPERATOR:${record.operator}`, "TYPE:INVALID_RECHARGE_NUMBER"]);
            return null;
        };

        if (!self.validateCustomerID(record.customerId)) {
            self.L.error("Invalid Customer ID", record.customerId);
            self._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION_NONRU", "STATUS:PARAMS_INVALID", `SERVICE:${record.service}`, `OPERATOR:${record.operator}`, "TYPE:INVALID_CUSTOMER_ID"]);
            return null;
        }

        return record;
    }

    async processRecord(done , record){

        let self =this;

        ASYNC.waterfall([
            next => {
                let customerId = _.get(record, 'customerId');
                return self.billsModel.getBillByCustomer(next, this.paytmBillsTable, customerId) ;
            },
            async(rows, next) => {
                try {
                    self.L.log('processRecord', `Found ${rows.length} transaction history for the customerId:${_.get(record,'customerId',null)}`);

                    console.log("-------rows--------", rows);
            
                    if (rows && _.isArray(rows) && rows.length > 0) {

                        // let last4DigitsMatchingRecords = [];
                        // for (let index = 0; index < rows.length; index++) {
            
                        //     let recordBankName = rows[index] && rows[index].bank_name;
                        //     recordBankName = _.toLower(recordBankName);
                        //     let mappedBankName = _.get(self.config, ['DYNAMIC_CONFIG', 'BANKNAME_CONFIG', recordBankName, 'BANKNAME_MAPPING'], '');
                        //     let rechargeNumber = rows[index].recharge_number.replace(/\s+/g, ''); // removing white space from RN. 1234 XXXX XX12 12 <-- to handle scenarioes like this
                        //     if (rechargeNumber && rechargeNumber.substr(rechargeNumber.length - record.lastCC.length) == record.lastCC  && _.toLower(mappedBankName) == _.toLower(record.bankName)) {
                        //         last4DigitsMatchingRecords.push(rows[index]);
                        //     }
                        // }
            
                        // if (last4DigitsMatchingRecords.length === 0) {
                        //     return next();
                        // } else if (last4DigitsMatchingRecords.length >= 1) {
                        //     return done();
                        // }

                        let dbRecord = false;

                        for (let row of rows) {
                            let last4_MCN = row.recharge_number.replace(/\s+/g, '').substr(-4);  // removing white space from RN. 1234 XXXX XX12 12 <-- to handle scenarioes like this
                            let record_lastmcn = record.rechargeNumber.replace(/\s+/g, '').substr(-4);

                            console.log("-----last4_MCN-----", last4_MCN, "---record_lastmcn---", record_lastmcn);
                            if (last4_MCN == record_lastmcn) {
                                let bankAttributes,bankCode;
                                try{
                                    bankAttributes = JSON.parse(_.get(self.config, ["CVR_DATA", row.product_id, "attributes"], "{}"));
                                    bankCode = _.toLower(_.get(bankAttributes, ["bank_code"], ""));
                                }catch(err){
                                    console.log("-------errr---------", err);
                                }
                                if (record.bankCode == bankCode) {
                                    dbRecord = true;
                                }
                            }
                        }

                        if (dbRecord) {
                            return done();
                        } else {
                            return next();
                        }

                    }else {
                        return next();
                    }
                } catch (error) {
                    return next(error);
                }
            },
            async next => {
                try {
                    if(this.bbpsBanks.includes(_.toLower(record.nonPaytmoperator))){
                        await self.nonPaytmBillsModel.insertBillFetchTable(self.formatDataForCt(record));
                    }
            
                    self.prepareNonPaytmKafkaResponse(record, (error) => {
                        if (error) {
                            self.L.error('processRecord', 'Failed to publish data to kafka', error);
                            return done(error); // Pass the error to done
                        }
                        return done();
                    });
                } catch (error) {
                    return done(error); // Pass the error to done
                }
            }
        ], function (error) {
            if (error) {
                self.L.error(`_prepareData`, `Failed with error ${error} for data ${record}`);
                self._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION_NONRU", 'STATUS:ERROR','SOURCE:MAIN_FLOW_EXECUTION']);
            }
            return done();
        });
    }

    validateCustomerID(customer_id){
        let self =this;
        // Regular expression to match only numeric characters
        let numericRegex =/^[0-9]+$/;

        // Check if the trimmed input string matches the numeric regex
        return numericRegex.test(customer_id);
    }

    validateRechargeNumber(record ,rechargeNumber){
        let self=this;
        let trimmedString = rechargeNumber.replace(/\s+/g, '');

        // Check if last 4 characters are numbers and all other characters are 'X'
        const lastFourDigits = trimmedString.slice(-4);
        const allXExceptLastFour = trimmedString.slice(0, -4).replace(/X/g, '');

        if(lastFourDigits.match(/^\d{4}$/) && allXExceptLastFour.length === 0) {
            record.lastCC = lastFourDigits;
        }

        return lastFourDigits.match(/^\d{4}$/) && allXExceptLastFour.length === 0;
    }

    formatDataForCt(billsData){
        let self = this;
        let currentDate  = MOMENT().format('YYYY-MM-DD HH:mm:ss');
        let response = {
            "source" : "reminderNonRuBillFetch",
            "customerId": _.get(billsData, 'customerId',null),
            "rechargeNumber": _.get(billsData, 'rechargeNumber', null),
            "service": _.get(billsData, 'service', null),
            "operator": _.get(billsData, 'nonPaytmoperator', null),
            "paytype": _.get(billsData, 'paytype', null),
            "productId": _.get(billsData, 'productId', null),
            "amount" : _.get(billsData, 'amount', null),
            "billDate": _.get(billsData, 'billDate',null),
            "billFetchDate": _.get(billsData, 'billFetchDate', null),
            "nextBillFetchDate": _.get(billsData, 'nextBillFetchDate',MOMENT().add(30,'days').format('YYYY-MM-DD HH:mm:ss')),
            "dueDate": _.get(billsData, 'billDueDate',null),
            "customerEmail": _.get(billsData, 'customerEmail',null),
            "circle":_.get(billsData, 'circle', null),
            "customerMobile": _.get(billsData, 'customerMobile',null),
            "customerOtherInfo": _.get(billsData, 'customerOtherInfo',null),
            "status": _.get(billsData, 'status', 6),
            "nextBillFetchStartDate": _.get(billsData, 'nextBillFetchStartDate',MOMENT().add(30, 'days').format('YYYY-MM-DD HH:mm:ss')),
            "publishedDate": _.get(billsData, 'publishedDate', currentDate),
            "retryCount":_.get(billsData, 'retryCount', 0),
            "extra":_.get(billsData,'extra',{}),
            "dbEvent" : "upsert",
            "bankName" : _.get(billsData,'nonPaytmoperator',null),
            "user_data": JSON.stringify(_.get(billsData, 'userData', {}))
        }
        response.customerOtherInfo = "{\"customerId\":null,\"subscriberNumber\":null,\"subscriberName\":null,\"subscriberEmailId\":null,\"subscriberDOB\":null,\"subscriberAltNumber\":null,\"subscriberAddress\":null,\"subscriberGender\":null,\"subscriberCity\":null,\"minReloadAmount\":null,\"currentBillAmount\":null,\"billDueDate\":null,\"currentMinBillAmount\":null}";
        response.extra = "{\"created_source\":\"CSV\"}";
        return response;
    }

    prepareNonPaytmKafkaResponse(billsData, cb){
        let self = this;
        let ctData = self.formatDataForCt(billsData);
        let recharge_number = _.get(ctData, 'rechargeNumber', null),
            operator = _.get(ctData, 'operator', null),
            productId = _.get(ctData, 'productId', null),
            service = _.get(ctData, 'service', null),
            customerId = _.get(ctData, 'customerId', null);
        
        if (!service || !operator || !recharge_number || !productId || !customerId) {
            self.L.critical('prepareKafkaResponse :: invalid inputs ', service, operator, recharge_number, productId,customerId);
            return cb();
        }
        // Need to change the topic in which data is to be published.
        self.nonPaytmKafkaPublisher.publishData([{
            topic: _.get(self.config.KAFKA, 'SERVICES.NON_PAYTM_RECORDS_DWH.TOPIC', ''),
            messages: JSON.stringify(ctData)
        }], (error) => {
            if (error) {
                self._sendMetricsToDD(1, [
                    "REQUEST_TYPE:CC_INGESTION_NONRU", 
                    `SERVICE:${_.get(ctData, 'service', null)}`, 
                    'STATUS:ERROR', 
                    "TYPE:NON_PAYTM_EVENTS",
                    'ORIGIN:CC_INGESTION_NONRU'
                ]);
                self.L.critical('CC_INGESTION_NONRU :: nonPaytmKafkaPublisher', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(ctData), error);
                return cb('Error while publishing message in Kafka');
            } else {
                self._sendMetricsToDD(1, [
                    "REQUEST_TYPE:CC_INGESTION_NONRU", 
                    `SERVICE:${_.get(ctData, 'service', null)}`, 
                    'STATUS:PUBLISHED', 
                    "TYPE:NON_PAYTM_EVENTS",
                    "OPERATOR:" + ctData.operator,
                    'ORIGIN:CC_INGESTION_NONRU'
                ]);
                self.L.log('PUBLISHER NON-RU :: nonPaytmKafkaPublisher', 'Message published successfully in Kafka', ' on topic NON_PAYTM_RECORDS_CSV', JSON.stringify(ctData));
                return cb(null);
            }
        })
    }

    _sendMetricsToDD (value, tags,typeOfMetric='increment') {

        var self = this,
           metrics = [];
     
        metrics = [
           {
              name: 'CcIngestion.Metrics',
              value: value,
              tags: tags
           }
        ];
     
        self.L.verbose('self._sendMetricsToDD: metricType,tags,value ::',typeOfMetric,tags,value);
     
        try {
           STATSD.sendMonitorMetrics(metrics, { metricType: typeOfMetric }, function (err) {
              if (err) {
                self.L.error('Error while sending metric data to DataDog..', err);
              }
              else {
                self.L.verbose('Metric data sent Successfully to DataDog...');
              }
           });
        }
        catch (exception) {
           self.L.error('Exception occurred while sending metric data to DataDog..', exception);
        }
    };
}

export default ccIngestionNonRu;