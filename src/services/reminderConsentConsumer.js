import _, { set } from 'lodash';
import OS from 'os';
import ASY<PERSON> from 'async';
import utility from '../lib';
import CATALOGVERTICALRECHARGE from '../models/catalogVerticalRecharge';
import MOMENT from 'moment';
import BILLS from '../models/bills'
import KafkaConsumer from '../lib/KafkaConsumer';
import KafkaConsumerChecks from '../lib/kafkaConsumerChecks';
import Logger from '../lib/logger';

class ReminderConsentConsumer{
    constructor(options){
        this.L = options.L;
        this.config = options.config;
        this.greyScaleEnv = options.greyScaleEnv;
        this.bills = new BILLS(options);
        this.catalogVerticalRecharge = new CATALOGVERTICALRECHARGE(options);
        this.retryDelay = {
            1: 10*1000,
            2: 20*1000,
            3: 30*1000
        };
        this.kafkaConsumerChecks = new KafkaConsumerChecks(options);
        this.kafkaBatchSize = this.greyScaleEnv ? 2 : 100;
        this.logger = new Logger(options);
    }

    start() {
        let self = this;
        self.L.log('ReminderConsentConsumer:: Starting service');
        self.configureKafka(function(error){
            if (error) {
                self.L.critical('ReminderConsentConsumer :: start', 'unable to configure kafka', error);
                process.exit(0);
            } else {
                self.L.log('ReminderConsentConsumer', 'Kafka configured successfully!');
            }
        })
        setInterval(() => {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:REMINDER_CONSENT_CONSUMER", 'TYPE:HEARTBEAT', 'STATUS:SUCCESS']);
        }, 60 * 1000)
    }

    configureKafka(done) {
        let self = this;
        self.L.log('configureKafka', 'Going to initialize kafka consumer');
        
        self.kafkaConsentConsumer = new KafkaConsumer({
            "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.REMINDER_KAFKA_CLUSTER.HOSTS'),
            "groupId": "reminder-consent-consumer",
            "topics": _.get(self.config.KAFKA, 'SERVICES.REMINDER_CONSENT_CONSUMER.CONSENT_TOPIC'),
            "id": 'reminderConsentConsumer_' + OS.hostname() + '_' + process.pid,
            "batchSize": self.kafkaBatchSize, 
            connectTimeout: 60000,
            maxBytes: 500000,
            sessionTimeout: 5*60*1000,
            maxProcessTimeout: 10*60*1000
        });
        self.kafkaConsentConsumer.initConsumer(self._processKafkaData.bind(self), (error) => {
            if(error){
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:REMINDER_CONSENT_CONSUMER", 'STATUS:ERROR', 'TYPE:KAFKA_CONSUMER', 'TOPIC:REMINDER_CONSENT_TOPICS']);
            }
            if (!error)
                self.L.log("configureKafka", "consumer of topic : REMINDER_CONSENT_TOPICS Configured");
            return done(error);
        });
    }

    _processKafkaData(records, resolveOffset, topic, partition, cb) {
        let self = this,
        chunkSize = 1,
        consentData,
        recordsToProcess = [], lastMessage;

        if (records && _.isArray(records)) {
            lastMessage = records[records.length - 1];
            self.L.info('reminderConsentConsumer received number of messages from kafka ' + records.length);
        } else {
            self.L.critical('execSteps', `No valid kafka records found. Received records-`, records);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:REMINDER_CONSENT_CONSUMER", 'STATUS:ERROR', 'TYPE:INVALID_RECORDS']);
            return cb();
        }
        records.forEach(function (data){
            if(data?.value){
                try{
                    consentData = JSON.parse(data.value);
                    recordsToProcess.push(consentData);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:REMINDER_CONSENT_CONSUMER", 'STATUS:TRAFFIC_INGESTED_FOR_CONSENT', `TOPIC:REMINDER_BILL_CONSENT`]);
                } catch (error) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:REMINDER_CONSENT_CONSUMER", 'STATUS:ERROR', 'TYPE:INVALID_JSON_PAYLOAD', `TOPIC:REMINDER_BILL_CONSENT`]);
                    self.L.error("_processKafkaData", "Failed to parse consent data topic,partition,offset,timestamp ::", data.topic, data.partition, data.offset, data.timestamp + ", " + error);
                }

            }else{
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:REMINDER_CONSENT_CONSUMER", 'STATUS:INVALID_PAYLOAD', `TOPIC:REMINDER_BILL_CONSENT`]);
                self.L.error("_processKafkaData", "Unable to get valid data from kafka topic,partition,offset,timestamp ::", data.topic, data.partition, data.offset, data.timestamp);
            }
        });

        self.kafkaConsumerChecks.findOffsetDuplicates("ReminderConsentConsumer", records, topic, partition);
        const latencyStart = new Date().getTime();
        ASYNC.eachLimit(recordsToProcess, chunkSize, self._processMessage.bind(self), async (err, res) => {
            self.L.info('reminderConsentConsumer offset commit');
            await resolveOffset(lastMessage.offset);
            utility._sendLatencyToDD(latencyStart, {
                "REQUEST_TYPE": "REMINDER_CONSENT_CONSUMER",
                "RECORDS_PROCESSED": records.length,
                "TOPIC": "REMINDER_BILL_CONSENT"
            });
            setTimeout(function (){
                return cb();
            }, 1000);
        });
    }
        
    async _processMessage(payload, done) {
        const self = this;
        let record = {
            customerId: _.get(payload, 'customerId', 0),
            lastFourDigits: _.get(payload, 'lastFourDigits', ''),
            mobileNumber : _.get(payload, 'mobileNumber', ''),
            bankCode : _.get(payload, 'bankCode', ''),
            tableName : "bills_creditcard",
            consentValidTill: _.get(payload, 'consentValidTill', null)
        }
        if(!record.consentValidTill) {
            self.L.critical('consentValidTill for record is null or undefined: ' + JSON.stringify(record));
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:REMINDER_CONSENT_CONSUMER", 'STATUS:INVALID_CONSENT_VALID_TILL', `TOPIC:REMINDER_BILL_CONSENT`]);
            done();
        }else{
            await self.processRecord(record);
            done();
        }
    }

    async processRecord(record, maxRetry = 3) {
        const self = this;
        let failed = false,
            noRecordFoundInDb = false;
        try {
            const dbRecord = await self.getRecordsFromDbByCustId(record);

            if (dbRecord) {
                const nbfd = _.get(dbRecord, 'next_bill_fetch_date', null);
                let nbfd_new = null;
                if (MOMENT(nbfd) < MOMENT()) {
                    nbfd_new = MOMENT().add(1, 'days').format('YYYY-MM-DD HH:mm:ss');
                }
                let param = {
                    id: dbRecord.id,
                    nextBillFetchDate: nbfd_new,
                    consentValidTill: MOMENT(record.consentValidTill).format('YYYY-MM-DD HH:mm:ss')
                };

                let consentValidTillUpdateFlag = _.get(dbRecord, 'consent_valid_till', null) !== null ? MOMENT.utc(record.consentValidTill, 'YYYY-MM-DD HH:mm:ss').isAfter(MOMENT(dbRecord.consent_valid_till)) : true;

                self.L.log('consetValidTillUpdateFlag: ' + consentValidTillUpdateFlag, 'record consentValidTill: ' + _.get(record, 'consentValidTill',null), 'dbRecord consent_valid_till: ' + _.get(dbRecord, 'consent_valid_till', null));

                if(!consentValidTillUpdateFlag){
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:REMINDER_CONSENT_CONSUMER", 'STATUS:CONSENT_VALID_TILL_FLAG_FALSE', `TOPIC:REMINDER_BILL_CONSENT`]);
                }

                if(consentValidTillUpdateFlag){
                    await self.bills.updateConsentById(record.tableName, param);
                }
            } else {
                self.logger.critical("No record found in db for", record, "financial services");
                noRecordFoundInDb = true;
            }
        } catch (error) {
            self.logger.critical(`Error _error ${error} processing record:`, record, "financial services");
            failed = true;
        }

        if(failed && maxRetry > 0 ) {
            maxRetry -= 1;
            self.L.info('failed, retries left ' + maxRetry);

            return new Promise((resolve, reject) => {
                setTimeout(async () => {
                    await self.processRecord(record, maxRetry);
                    return resolve();
                }, 1000);
            });
        }else if(failed && maxRetry == 0){
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:REMINDER_CONSENT_CONSUMER", 'STATUS:FAILED_AFTER_RETRY', `TOPIC:REMINDER_BILL_CONSENT`]);
        }else if(!failed && !noRecordFoundInDb){
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:REMINDER_CONSENT_CONSUMER", 'STATUS:SUCCESS', `TOPIC:REMINDER_BILL_CONSENT`]);
        }else if(noRecordFoundInDb){
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:REMINDER_CONSENT_CONSUMER", 'STATUS:RECORD_NOT_FOUND', `TOPIC:REMINDER_BILL_CONSENT`]);
        }
    }

    async getRecordsFromDbByCustId(record) {
        let self = this;

        return new Promise((resolve, reject) => {
            self.bills.getBillByCustomer(async (err, data) => {
                if (err) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:REMINDER_CONSENT_GETRECORDS", 'STATUS:ERROR', "OPERATOR:" + record.operator]);
                    return reject(err);
                }
                if (!data || !_.isArray(data) || data.length < 1) return resolve(null);

                let dbRecord = false;
                for (let row of data) {
                    if (row){
                        const last4Digs = row.recharge_number.replace(/\s+/g, '').substr(-4); // removing white space from RN. 1234 XXXX XX12 12 <-- to handle scenarioes like this
                        if (last4Digs == record.lastFourDigits){
                            let attributes = JSON.parse(_.get(self.config, ['CVR_DATA', row.product_id, 'attributes'], '{}'));
                            let bankName = _.toLower(_.get(attributes, ['bank_code'], ''));
                            //self.L.log("customerId", record.customerId,"last4Digs from record ", _.get(record, 'lastFourDigits',null), "last4Digs from db ", last4Digs, "record.bankCode ", _.get(record, 'bankCode', null), "bankName ", bankName);
                            if(record.bankCode == bankName){
                                dbRecord = row;
                                break;
                            }
                        }
                    }
                }
                return resolve(dbRecord);
            }, record.tableName, record.customerId);
        });
    }
}

export default ReminderConsentConsumer;