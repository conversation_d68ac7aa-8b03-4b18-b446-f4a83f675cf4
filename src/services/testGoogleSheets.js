const { google } = require('googleapis');
const path = require('path');

async function testGoogleSheets() {
    try {
        // Initialize auth
        const auth = new google.auth.GoogleAuth({
            keyFile: path.join(__dirname, '../config/googleSheetCreds.json'),
            scopes: ['https://www.googleapis.com/auth/spreadsheets']
        });

        const sheets = google.sheets({ version: 'v4' });
        const spreadsheetId = '1Jnz4uEETur9F-J4FEMVH4VsfzEc-Lquq9dt96Aq6kJ8'; // Replace with your ID

        // Test 1: Create a new sheet
        const sheetTitle = 'Test_' + new Date().toISOString().split('T')[0];
        await sheets.spreadsheets.batchUpdate({
            auth: await auth.getClient(),
            spreadsheetId,
            requestBody: {
                requests: [{
                    addSheet: {
                        properties: {
                            title: sheetTitle
                        }
                    }
                }]
            }
        });
        console.log('✅ Created new sheet:', sheetTitle);

        // Test 2: Write some test data
        const testData = [
            ['Timestamp', 'Alert Name', 'Priority'],
            [new Date().toISOString(), 'Test Alert', 'P1'],
            [new Date().toISOString(), 'Another Test', 'P2']
        ];

        await sheets.spreadsheets.values.update({
            auth: await auth.getClient(),
            spreadsheetId,
            range: `${sheetTitle}!A1:C3`,
            valueInputOption: 'RAW',
            requestBody: {
                values: testData
            }
        });
        console.log('✅ Written test data to sheet');

        // Test 3: Read the data back
        const response = await sheets.spreadsheets.values.get({
            auth: await auth.getClient(),
            spreadsheetId,
            range: `${sheetTitle}!A1:C3`
        });
        console.log('✅ Read data from sheet:', response.data.values);

        console.log('🎉 All tests passed! Google Sheets integration is working.');
    } catch (error) {
        console.error('❌ Error testing Google Sheets integration:', error.message);
        if (error.message.includes('credentials')) {
            console.error('Check if your credentials file is correct and in the right location');
        }
        if (error.message.includes('permission')) {
            console.error('Check if the service account email has access to the spreadsheet');
        }
    }
}

// Run the test
testGoogleSheets();