import Q from 'q'
import RQ from 'rqueue'
import <PERSON><PERSON><PERSON><PERSON><PERSON> from 'validator'
import MOMENT from 'moment'
import _ from 'lodash'
import ASYNC from 'async'
import REQUEST from 'request'
import BILLS from '../models/bills'
import utility from '../lib'
import BILLS_SUBSCRIBER from './billSubscriber'
import PUBLISHER_BILL_FETCH_PATTERN from '../lib/publisherBillFetchPattern'
import CUSTOMER_BUCKETS_HANDLER from '../lib/customerBucketsHandler'


let L = null;
const MAX_QUEUE_COUNT = 10,
    MAX_RETRY_COUNT = 3;

// let recordsPublished = 0;
/*
   This service will push those records to corresponding gateway queues whose next_bill_fetch_date is just passed
   Refer the publisherPseudoCode.txt file in root directory to understand the flow
 */
class Publisher {

    constructor(options) {
        L = options.L;
        this.L = options.L;
        this.config = options.config;
        this.dbInstance = options.dbInstance;
        this.activePidLib = options.activePidLib;
        this.bills = new BILLS(options);
        this.dbBatchSize = 2;
        this.dbBatchIndex = 0;
        this.servicePeriod = 10 * 60; //time in seconds
        this.amILive = true
        this.tableName = options.tableName
        this.operator = options.operator
        this.whenRMQPublisherAvailable = this._configureRMQPublisher()  //OMG!! Its a promise
        this.secondaryCycle = 1 //1 hour
        this.publishedInSession = {};
        this.publishedInBatch = {}
        this.latencyProvider = options.latencyProvider
        this.recordsPublished = 0
        this.billSubscriber = new BILLS_SUBSCRIBER(options);
        this.validationTimeOut = _.get(this.config, ["COMMON", "VALIDATIONTIMEOUT"], 180000);
        this.initialized = false;
        this.batchId = options.batchId;
        this.publisherBillFetchPattern  = null;
        this.currentTps = 5;
        this.fetchedRecords = []
        this.prepaidPublisherInstance = _.get(options, 'prepaidPublisherInstance', false);
        this.prepaidOperatorsAllowed = _.get(options.config, ['DYNAMIC_CONFIG', 'PREPAID_BILL_FETCH_CONFIG', 'PREPAID_ENABLED_OPERATORS', 'OPERATORS'], null);
        this.includedOperatorList = _.get(this.config, ['DYNAMIC_CONFIG', 'OPERATOR_MONTHLY_PAYMENT', 'COMMON', 'INCLUDE_OPERATOR'], 'rent payment');
        this.includedOperator = this.includedOperatorList.split(',').map((e) => e.trim());

        this.ottOperatorList =  _.get(this.config, ['DYNAMIC_CONFIG', 'OPERATOR_OTT', 'COMMON', 'INCLUDE_OPERATOR'], 'disneyhotstar');
        this.ottOperators = this.ottOperatorList.split(',').map((e) => e.trim());

        L.info('Publisher::', this.tableName, this.operator, 'initialising Publisher...', this.ottOperators);
        this.customerBucketsHandler = new CUSTOMER_BUCKETS_HANDLER(options);
        this.priorityResetTime =5; // _.get(this.config, ['DYNAMIC_CONFIG', 'USER_SCORE_INGESTOR', 'TIME_IN_MINUTES', 'PRIORITY_RESET_TIME'], 50);
        this.firstquery = true;
        this.firstQueryBatchSize = _.get(this.config, ['DYNAMIC_CONFIG', 'USER_SCORE_INGESTOR', 'PUBLISHER_QUERY_SIZE' , 'FIRST_QUERY_BATCH_SIZE'], 1);
        this.commonLib = new utility.commonLib(options);

        setInterval(() => {
            this.initializeVariable();
        }, _.get(this.config, 'COMMON.INTERVAL_FOR_REINITIALIZE', 15) * 60 * 1000);


    }

    initializeVariable(){
        this.includedOperatorList = _.get(this.config, ['DYNAMIC_CONFIG', 'OPERATOR_MONTHLY_PAYMENT', 'COMMON', 'INCLUDE_OPERATOR'], 'rent payment');
        this.includedOperator = this.includedOperatorList.split(',').map((e) => e.trim());
        this.priorityResetTime =5;// _.get(this.config, ['DYNAMIC_CONFIG', 'USER_SCORE_INGESTOR', 'TIME_IN_MINUTES', 'PRIORITY_RESET_TIME'], 50);
        this.firstQueryBatchSize = _.get(this.config, ['DYNAMIC_CONFIG', 'USER_SCORE_INGESTOR', 'PUBLISHER_QUERY_SIZE' , 'FIRST_QUERY_BATCH_SIZE'], 1);
    }

    /*
      Function which will start the publisher for a table
    */
    start() {
        let self = this;
        self.amILive = true;
        L.info('Publisher::', self.operator, 'starting the service loop', self.tableName);
        function startMainLoop() {
            self.publishedInSession = {} //intialise a fresh session object
            if (self.amILive) {
                self._execSteps(function () {
                    L.log('Publisher::', self.operator, 'session finished successfully, Msg Count Published to gateway service: ', Object.keys(self.publishedInSession).length);
                    self.calculateTheBillFetchTPS(() => {
                        self.currentTps = 5; // resetting tps
                        self.dbBatchIndex = 0;
                        setTimeout(function () {
                            startMainLoop();
                        }, self.servicePeriod * 1000);  //converting period in milli seconds
                    });
                    
                });
            }
            else {
                L.info('Publisher::', self.operator, 'Stopping the publisher instance')
            }
        }
        if (!self.initialized) {
            let sourceObj = {
                'SOURCE' : 'PUBLISHER',
                'OPERATOR' : self.operator
            };
            //Start the process only when publisher is available
            // this.whenRMQPublisherAvailable.then(() => {
                self.billSubscriber._configureKafkaPublisher((error) => {
                    if (error) {
                        L.critical('Publisher :: start', 'Error while configuring Kafka Publisher.. for', self.operator, 'with error', error);
                    }
                    else {
                        L.log('Publisher :: start', 'Kafka Publisher configured', self.operator, 'with error', error);
                        self.initialized = true;
                        startMainLoop();
                    }
                }, sourceObj);
            // });
        } else {
            L.info('publisher:: start ', 'Restarting publisher for operator ', self.operator);
            startMainLoop();
        }
    }



    /*
       function which signals the publisher process to stop
     */
    stop() {
        let self = this;
        self.amILive = false;
    }

    /*
       Configuring the RMQ publisher
    */
    _configureRMQPublisher() {
        let self = this,
            deferred = Q.defer(),
            ex = {
                NAME: _.get(self.config, 'RABBITMQ_CONFIG.PUBLISHER_EXCHANGE_NAME', 'ex_recharge_to_gw'),
                TYPE: _.get(self.config, 'RABBITMQ_CONFIG.PUBLISHER_EXCHANGE_TYPE', 'topic'),
                OPTIONS: {
                    durable: true,
                    internal: false,
                    autoDelete: false
                }
            },
            rabbitConfig = _.get(self.config, 'RABBITMQ_CONFIG.RABBITMQ', null);

        self.reminderPublisher = new RQ({ RABBITMQ: rabbitConfig }).getSimplePublisher(ex);
        self.reminderPublisher.start({}, function () {
            L.log('_configureRMQPublisher::', self.operator, 'publisher configured');
            deferred.resolve();
        });

        return deferred.promise;
    };

    /*
       This function will process the records in 2 steps:
         1. Process all dead records
         2. Process all fresh records
    */
    _execSteps(done) {
        let self = this;
        L.log('_execSteps::', self.operator, 'performing steps for publishing...')

        ASYNC.waterfall([
            /**
            //Step 1: Try processing Dead records (refer definition of DEAD_RECORD from documentation)
            next => {
                L.log('_execSteps::', self.operator, 'processing dead records...')
                let params = {
                    status: _.get(self.config, 'COMMON.bills_status.PUBLISHED', 1),
                    nextBillFetchDateTo: MOMENT().subtract(1, 'days').format('YYYY-MM-DD HH:mm:ss'),
                    nextBillFetchDateFrom: MOMENT().subtract(32, 'days').format('YYYY-MM-DD HH:mm:ss')
                },
                    totalDeadRecordsCount = 0; //Just for logging purpose
                self._processDeadRecords(params, function __keepFetchingUntilDone(err, recordsCountInLastBatch) {
                    if (err) {
                        L.error('_execSteps', 'Error processing dead records')
                        //No matter what happened, don't stop at error and proceed further
                        next()
                    }
                    else if (recordsCountInLastBatch > 0) { //here we are assuming that there could be more records
                        L.log('_execSteps._processDeadRecords::', self.operator, 'recordsCountInLastBatch', recordsCountInLastBatch)
                        totalDeadRecordsCount += recordsCountInLastBatch
                        self._processDeadRecords(params, __keepFetchingUntilDone)
                    }
                    else { //No records left, so proceeding to next step
                        L.log('_execSteps._processDeadRecords::', self.operator, 'total dead records processed', totalDeadRecordsCount);
                        if (totalDeadRecordsCount) {
                            utility._sendMetricsToDD(totalDeadRecordsCount, ['REQUEST_TYPE:PUBLISHED', 'STATUS:DEAD_RECORDS', 'TABLE:' + self.tableName, `batch:${self.batchId}`]);
                        }
                        next()
                    }
                });
            },
             */
            //Step 2: Process fresh records (refer definition of FRESH_RECORD from documentation)
            next => {
                L.log('_execSteps::', self.operator, 'processing fresh records...')
                // In case of change in params, Please also update in models/aggregator.js query which is generating report using same params
                let params = {
                    statuses: [
                        _.get(self.config, 'COMMON.bills_status.DISABLED', 7),
                        _.get(self.config, 'COMMON.bills_status.NOT_IN_USE', 13)
                    ],
                    operator: self.operator
                },
                    totalFreshRecordsCount = 0; //Just for logging purpose
                self.recordsPublished = 0;
                self.customerBucketsHandler.startPriorityResetTimer(self);
                self._processFreshRecords(params, function __keepFetchingUntilDone(err, recordsCountInLastBatch) {
                    if (err) {
                        L.error('_execSteps', 'Error processing fresh records', 'for NBFD', params.nextBillFetchDate)
                        //No matter what happened, don't stop at error and proceed further
                        next()
                    }
                    // check if amIlive is false then stop publishing
                    else if (!self.amILive) {
                        L.log('_execSteps', `Operator not alive now: ${self.operator}`);
                        return next();
                    }
                    else if (self.priorityBuckets){
                        self.handleCaseWhenPriorityBucketsAreSet(params, recordsCountInLastBatch, totalFreshRecordsCount, __keepFetchingUntilDone, next);
                    } else {
                        self.handleCaseWhenPriorityBucketsAreNotSet(params, recordsCountInLastBatch, totalFreshRecordsCount, __keepFetchingUntilDone, next);
                    }
                });
            }
        ],
            err => {
                //Lets invoke the main callback to finish the cycle
                done()
            }
        );
    }

    handleCaseWhenPriorityBucketsAreSet(params, recordsCountInLastBatch, totalFreshRecordsCount, __keepFetchingUntilDone, next){
        let self = this;
        let comparingBatchSize = self.firstquery ? self.firstQueryBatchSize : self.dbBatchSize;
        self.firstquery = false;
        if (recordsCountInLastBatch >= comparingBatchSize && (!self.priorityResetTimerCompleted || (self.currentPriorityBucket==0))) {
            L.log('_execSteps._processFreshRecords::', self.operator, 'recordsCountInLastBatch', recordsCountInLastBatch, 'for NBFD', params.nextBillFetchDate);
            totalFreshRecordsCount += recordsCountInLastBatch
            _.set(params, 'customerBucket', self.customerBucketsHandler.getCurrentPriorityBucket(self));
            self._processFreshRecords(params, __keepFetchingUntilDone)
        }
        else if (self.priorityResetTimerCompleted && self.currentPriorityBucket!=0) {
            self.customerBucketsHandler.resetPriorityBucket(self);
            L.log('_execSteps._processFreshRecords::', self.operator, 'total fresh records processed', totalFreshRecordsCount, 'for NBFD', params.nextBillFetchDate);
            totalFreshRecordsCount += recordsCountInLastBatch
            _.set(params, 'customerBucket', self.customerBucketsHandler.getCurrentPriorityBucket(self));
            self._processFreshRecords(params, __keepFetchingUntilDone);
        }
        else if (recordsCountInLastBatch < comparingBatchSize && self.customerBucketsHandler.getCurrentPriorityBucket(self)!=self.customerBucketsHandler.getLastPriorityBucket(self)) {
            totalFreshRecordsCount += recordsCountInLastBatch
            L.log('_execSteps._processFreshRecords::', self.operator, 'total fresh records processed', totalFreshRecordsCount, 'for NBFD', params.nextBillFetchDate);
            self.customerBucketsHandler.shiftPriorityBucket(self);
            _.set(params, 'customerBucket', self.customerBucketsHandler.getCurrentPriorityBucket(self));
            self._processFreshRecords(params, __keepFetchingUntilDone);
        }
        else { //No records left, so proceeding to next step
            totalFreshRecordsCount += recordsCountInLastBatch
            L.log('_execSteps._processFreshRecords::', self.operator, 'total fresh records processed', totalFreshRecordsCount, 'for NBFD', params.nextBillFetchDate);
            if (totalFreshRecordsCount) {
                utility._sendMetricsToDD(totalFreshRecordsCount, ['REQUEST_TYPE:PUBLISHED', 'STATUS:FRESH_RECORDS', 'TABLE:' + self.tableName, `batch:${self.batchId}`]);
            }
            next()
        }
    };

    handleCaseWhenPriorityBucketsAreNotSet(params, recordsCountInLastBatch, totalFreshRecordsCount, __keepFetchingUntilDone, next){
        let self = this;
        let comparingBatchSize = self.firstquery ? self.firstQueryBatchSize : self.dbBatchSize;
        self.firstquery = false;
        if (recordsCountInLastBatch >= comparingBatchSize) {
            L.log('_execSteps._processFreshRecords::', self.operator, 'recordsCountInLastBatch', recordsCountInLastBatch, 'for NBFD', params.nextBillFetchDate);
            totalFreshRecordsCount += recordsCountInLastBatch
            self._processFreshRecords(params, __keepFetchingUntilDone)
        }
        else { //No records left, so proceeding to next step
            totalFreshRecordsCount += recordsCountInLastBatch
            L.log('_execSteps._processFreshRecords::', self.operator, 'total fresh records processed', totalFreshRecordsCount, 'for NBFD', params.nextBillFetchDate);
            if (totalFreshRecordsCount) {
                utility._sendMetricsToDD(totalFreshRecordsCount, ['REQUEST_TYPE:PUBLISHED', 'STATUS:FRESH_RECORDS', 'TABLE:' + self.tableName, `batch:${self.batchId}`]);
            }
            next()
        }
    }

    /*
       Function to process dead records
    */
   /** 
    _processDeadRecords(params, done) {
        let self = this

        ASYNC.waterfall([
            //fetch dead records from Database
            next => {
                self.bills.fetchDeadRecords(self.tableName, 1000, params, next);
            },

            //update records in DB so that they can be picked again later for publishing
            (records, next) => {
                self._retryDeadRecords(records, () => {
                    next(null, records.length)
                })
            }
        ],
            (err, count) => {
                if (err) {
                    return done(err)
                }
                done(null, count)
            }
        );
    }

    _retryDeadRecords(records, done) {
        let self = this;
        ASYNC.map(
            records,
            (record, next) => {
                self._promoteToNextTrialPhase(record, self._getNthPrimaryCycle(1), () => {
                    next()
                });
            },
            (err) => {
                if (err) {
                    L.error('_retryDeadRecords', 'error occurred while retrying dead record.');
                }
                done()
            }
        );
    }
*/
    /*
       Function to process fresh records
    */
    _processFreshRecords(params, done) {
        let self = this
        self.publishedInBatch = {}
        ASYNC.waterfall([
            //fetch fresh records from Database
            next => {
                /**
                 * refreshing nextBillFetchDateFrom and nextBillFetchDateTo in every function call
                 * as it runs in loop, if records are always >=batchsize, then nextBillFetchDateTo will not change in whole day.
                 */
                _.set(params, 'nextBillFetchDateFrom', MOMENT().subtract(32, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss'));
                _.set(params, 'nextBillFetchDateTo', MOMENT().format('YYYY-MM-DD HH:mm:ss'));
                let batchSize = self.firstquery ? self.firstQueryBatchSize : self.dbBatchSize;
                self.bills.fetchFreshRecords(self.tableName, batchSize, self.dbBatchIndex * self.dbBatchSize, params, next);
            },
            //Publish and update the records
            (records, next) => {
                self.customerBucketsHandler.setPriorityBucketsByServicePaytype(records,self);
                L.log('_processFreshRecords', `Processing ${records.length} fresh records for ${self.operator}`);
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:PUBLISHED', 'STATUS:PROCESSING_RECORDS', `OPERATOR:${self.operator}`, `RECORDS_PUBLISHED_COUNT:${self.recordsPublished}` , `RECORDS_COUNT:${records.length}`]);
                self.startProcessingRecords(function () {
                    return next(null, records.length);
                }, records);

            }
        ],
            (err, count) => {
                if (err) {
                    return done(err)
                }
                done(null, count)
            }
        );

    }

    startProcessingRecords(callback, records) {
        let self = this,
            processRecordStrategy = _.get(self.config, ['PUBLISHER_CONFIG', 'BILL_FETCH_PATTERN', self.operator], 'FIXED_WINDOW');
        // processRecordStrategy = <"FIXED_WINDOW"/"SLIDING_WINDOW">
        L.log('startProcessingRecords::', `bill fetch flow strategy is ${processRecordStrategy}`);

        records = self.removeDuplicateRechargeNumbers(records);

        switch(processRecordStrategy) {
            case 'FIXED_WINDOW': {
                self._processRecordsInBatch(function () {
                    self.dbBatchIndex = 0;
                    return callback();
                }, records, 0);
            }
            break;
            case 'SLIDING_WINDOW': {
                self._processRecordsInBatchSlidingWindow(function () {
                    self.dbBatchIndex = 0;
                    return callback();
                }, records);
            }
            break;
            case 'TPS_WINDOW': {
                self._processRecordsInBatchInTpsWindow((promiseWaiting) => {
                  if (!promiseWaiting) {
                    return callback();
                  }
                  return this.calculateTheBillFetchTPS(callback);
                }, records);
            }
            break;
            default: {
                self._processRecordsInBatch(function () {
                    return callback();
                }, records, 0);
            }
            break;
        }
    }

    removeDuplicateRechargeNumbers(records) {
        let self = this;
        let filteredRecords = {};
        L.log('removeDuplicateRechargeNumbers::', `records length received for removal of duplicate recharge_numbers is ${records.length}`);
        for(let index=0; index < records.length; index++) {
            if(_.get(records[index],'recharge_number',null) == null || _.get(records[index],'recharge_number',null) == "") {
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:FILTER_PUBLISHER_RECORDS', 'ERROR:NULL_RECHARGE_NUMBER','TABLE:' + self.tableName,'batch:' + self.batchId]);
                L.error('removeDuplicateRechargeNumbers::', `record with null recharge_number found having id: ${_.get(records[index],'id')} in table ${self.tableName}`);
                continue;
            }
            let active_pid = self.activePidLib.getActivePID(_.get(records[index],'product_id'));
            if(active_pid != null){
                filteredRecords[`${_.get(records[index],'recharge_number')}_${active_pid}`] = records[index];
            } else {
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:FILTER_PUBLISHER_RECORDS', 'ERROR:NULL_ACTIVE_PID','TABLE:' + self.tableName,'batch:' + self.batchId, 'PRODUCT_ID:'+`${records[index]['product_id']}`]);
                L.log('removeDuplicateRechargeNumbers::', `${_.get(records[index],'recharge_number')}_${active_pid} :: no pid found for recharge_number ${records[index]['recharge_number']} and id: ${records[index]['id']}`);        
            }
        }
        filteredRecords = Object.values(filteredRecords);
        L.log('removeDuplicateRechargeNumbers::', `records length after removing duplicate recharge_numbers is ${filteredRecords.length}`);
        
        return filteredRecords;
    }

    async _processRecordsInBatchInTpsWindow(done, records) {
        let tpsWindowTime = _.get(this.config, ['PUBLISHER_CONFIG', 'TPS_WINDOW_TIME', this.operator], 15);
        let tpsTotal = this.currentTps * tpsWindowTime * 60;

        this.fetchedRecords.push(...records);

        if ((records && records.length < this.dbBatchSize) || this.fetchedRecords.length >= tpsTotal) {
            return done(true);
        }
        this.dbBatchIndex++;
        return done();
    }

    async calculateTheBillFetchTPS(cb) {
        if (this.fetchedRecords.length <= 0) {
            return cb();
        }

        let timeInterval = 1000 / this.currentTps, promiseTpsArr = [], index = 0;

        this.fetchedRecords.forEach(async (record) => {
            index++;
            // await new Promise((resolve, reject) => setTimeout(resolve, timeInterval));
            promiseTpsArr.push(
                new Promise((resolve, reject) =>
                    setTimeout(() => {
                        this._processRecords(record, (err, billData) => {
                            if (!err && billData && billData.commonStatus
                                && (billData.commonStatus == _.get(this.config, 'COMMON.bills_status.BILL_FETCHED', 4)
                                    || billData.commonStatus == _.get(this.config, 'COMMON.bills_status.BILL_NOT_FOUND', 6)
                                    || billData.commonStatus == _.get(this.config, 'COMMON.bills_status.PAID_ON_OTHER_PLATFORM', 14))) {
                                resolve(true);
                            } else {
                                resolve(false);
                            }
                        }, false)
                    }, timeInterval * index)
                )
            );
        });

        this.fetchedRecords = []; 
        this.dbBatchIndex = 0;

        const tpsArrResults = await Promise.all(promiseTpsArr);

        let successCount = 0, totalCount = 0;

        tpsArrResults.forEach((success) => {
            if (success) {
                successCount++;
            }
            totalCount++;
        });

        let successPercent = (successCount / totalCount) * 100;
        L.log(
            "_processRecordsInBatchInTpsWindow",
            `Current TPS: ${this.currentTps
            }, Success Percentage: ${successPercent.toFixed(2)}%, Total Count: ${totalCount}`
        );

        if (successPercent <= 5) {
            this.currentTps = 5;
        } else if (successPercent > 5 && successPercent <= 10) {
            this.currentTps = 20;
        } else if (successPercent > 10 && successPercent <= 15) {
            this.currentTps = 50;
        } else {
            this.currentTps = 150;
        }

        return cb();
    }

    _processRecordsInBatch(done, records, index) {
        let
            self = this;

        if (index >= records.length) {
            L.log('_processRecordsInBatch', `Records published in this iteration for ${this.tableName} ::: ${this.recordsPublished} out of ${records.length}`);
            this.recordsPublished = 0;
            setTimeout(function () {
                done();
            }, 1 * 1000)
            return;
        }

        let parallelBillFetch = _.get(self.config, ['DYNAMIC_CONFIG', 'OPERATOR_CONFIG', self.operator, 'PARALLEL_BILL_FETCH_HITS'],
            _.get(self.config, ['COMMON', 'PARALLEL_BILL_FETCH_HITS', self.tableName],
                _.get(self.config, ['COMMON', 'DEFAULT_BILL_FETCH_HITS'], 30)));
        ASYNC.each(records.slice(index, index + parallelBillFetch), self._processRecords.bind(self), function (err) {
            if (err) {
                L.error('_processRecordsInBatch', `error encountered while publishing records in batch ${err}`);
            }
            self._processRecordsInBatch(done, records, index + parallelBillFetch);
        });

    }

    /**
     * 
     * @param {*} done 
     * @param {*} records 
     */
    _processRecordsInBatchSlidingWindow(done, records){
        let self = this,
            delayAfterBatchCompletion = 1 * 1000 , // delay after max. 1000 records processing → 1 sec
            promiseArr = [],
            parallelBillFetch = _.get(self.config, ['DYNAMIC_CONFIG', 'OPERATOR_CONFIG', self.operator, 'PARALLEL_BILL_FETCH_HITS'],
                _.get(self.config, ['COMMON', 'PARALLEL_BILL_FETCH_HITS', self.tableName],
                    _.get(self.config, ['COMMON', 'DEFAULT_BILL_FETCH_HITS'], 30)));

        self.publisherBillFetchPattern  = new PUBLISHER_BILL_FETCH_PATTERN();
        self.publisherBillFetchPattern.reInitializeBillFetchVars(records);

        for(let promiseNumber = 1; promiseNumber <= Math.min(parallelBillFetch, records.length); promiseNumber++) {
            promiseArr.push(new Promise((resolves, reject)=>{
                self._processRecordSubsequently(promiseNumber , (error, data)=>{
                    if(error) {
                        resolves();
                    } else {
                        resolves();
                    }
                })
            })); 
        }

        Promise.all(promiseArr)
        .then((resolvesData)=>{
            L.log('_processRecordsInBatchSlidingWindow', `Records published in this iteration for ${this.tableName} ::: ${this.recordsPublished} out of ${records.length}`);
            this.recordsPublished = 0;

            setTimeout(function () {
                done();
            }, delayAfterBatchCompletion)
        }).catch((errorData)=>{
            L.log('_processRecordsInBatchSlidingWindow', `Records published in this iteration for ${this.tableName} ::: ${this.recordsPublished} out of ${records.length}`);
            this.recordsPublished = 0;

            setTimeout(function () {
                done();
            }, delayAfterBatchCompletion)
        });
    }

    _processRecordSubsequently(promiseNumber, done){
        let self = this,
            recordNumber = self.publisherBillFetchPattern.getToBeProcessedRecordNumber();

        if(recordNumber >= self.publisherBillFetchPattern.records.length) {
            return done();
        }
        let currentRecord = self.publisherBillFetchPattern.getCurrentRecordAndUpdateToBeProcessedRecordNumber();
        self._processRecords(currentRecord, (err)=>{
            if (err) {
                L.error('_processRecordsInBatch', `error encountered while publishing records in batch ${err}`);
            }
            self._processRecordSubsequently(promiseNumber, done);
        })
    }

    /*
       Going recursively for this function instead with Async lib's method, because we need to have a provision of halting the process for just a small moment, in cases when the queue is full
    */
    _processRecords(currentRecord, done, waitForNextPush = true) {

        // Replacing product_id with active PID
        let
            self = this,
            activePid = self.activePidLib.getActivePID(currentRecord.product_id);
            
        utility._sendMetricsToDD(1, ['REQUEST_TYPE:PUBLISHER', 'STATUS:PROCESSING_RECORD', `BUCKET:${_.get(currentRecord, 'customer_bucket', 'noBucket')}`,`batch:${self.batchId}`, `operator:${self.operator}`]);
        L.verbose('_processRecords', `Found active Pid ${activePid} against PID ${currentRecord.product_id}`);
        currentRecord.old_product_id = currentRecord.product_id; // Keeping track of original PID
        currentRecord.product_id = activePid;                // Replacing active PID
        currentRecord.traceKey = `Id:${_.get(currentRecord, 'id', '')}_Op:${_.get(currentRecord, 'operator', '')}_RN:${_.get(currentRecord, 'recharge_number', '')}`;

        let
            newStatus = _.get(self.config, 'COMMON.bills_status.PUBLISHED', 1),
            prepaidProductId = _.get(self.config, 'COMMON.PREPAID_PRODUCT_ID', []),
            retryCount = currentRecord.retry_count + 1,
            nextBillFetchDate = currentRecord.next_bill_fetch_date,
            reason = currentRecord.reason,
            fromRuleEngine = prepaidProductId.indexOf(currentRecord.product_id) > -1 ? null : _.get(self.config, 'RULEENGINE_CONFIG.' + currentRecord.operator, null),
            fromCurrentRecord = _.get(currentRecord, 'gateway', null),
            fromOperatorGatewayRegistry = _.get(self.config, ['OPERATOR_GATEWAY_REGISTRY', currentRecord.product_id], null) || _.get(self.config, ['OPERATOR_GATEWAY_REGISTRY', currentRecord.operator], fromCurrentRecord),
            gateway = (fromOperatorGatewayRegistry ? fromOperatorGatewayRegistry : fromCurrentRecord),
            queueName = 'q_gw_' + gateway,
            message = {
                customerInfo: {
                    customer_id: _.get(currentRecord, 'customer_id', ''),
                    customer_mobile: _.get(currentRecord, 'customer_mobile', '')
                },
                userData: {
                    recharge_number: _.get(currentRecord, 'recharge_number', ''),
                    amount: 10,
                },
                productInfo: {
                    operator: _.get(currentRecord, 'operator', ''),
                    circle: _.get(currentRecord, 'circle', ''),
                    service: _.get(currentRecord, 'service', ''),
                    paytype: _.get(currentRecord, 'paytype', ''),
                    validationtimeout: self.validationTimeOut
                },
                currentGw: gateway,
                logs: '',
                catalogProductID: _.get(currentRecord, 'product_id', ''),
                oldCatalogProductID: _.get(currentRecord, 'old_product_id', ''),
                customerDataResponse: {},
                etc: {},
                billFetchTime: MOMENT().format('YYYY-MM-DD HH:mm:ss'),
                lastDueDate: _.get(currentRecord, 'due_date', null),
                lastBillDate: _.get(currentRecord, 'bill_date', null),
                lastAmount: _.get(currentRecord, 'amount', null),
                lastBillFetchDate: _.get(currentRecord, 'bill_fetch_date', null),
                extra: _.get(currentRecord, 'extra', {}),
                source: 'digital-reminder',
                status: _.get(currentRecord, 'status', 0),
                serviceId: _.get(currentRecord, 'service_id', 0),
                prepaidPublisherInstance: _.get(self, 'prepaidPublisherInstance', false),
                tableName: self.tableName
            }

        //If the gateway is not found then processing the next record.
        L.log('processRecords', `For operator:${currentRecord.operator} Gateway From rule engine:: ${fromRuleEngine}, fromCurrentRecord:: ${fromCurrentRecord}, fromOperatorGatewayRegistry :: ${fromOperatorGatewayRegistry} and FINAL VALUE:: ${gateway}`);

        L.log('_processRecords', `Processing record for gateway ${gateway} and recharge_number ${message.userData.recharge_number} and catalogProductID ${message.catalogProductID} oldProductId ${message.oldCatalogProductID}`);

        // for additional user_info
        let userData = _.get(currentRecord, 'user_data', null);
        if (userData) {
            try {
                userData = JSON.parse(userData);
                message.userData = _.extend({}, userData, message.userData);
            } catch (err) {
                L.error("services::publisher::processRecord", "Error in JSON parsing" + err);
            }
        }

        // fetching amount from CVR
        let minAmount = _.get(self.config, ['CVR_DATA', _.get(currentRecord, 'product_id'), 'min_amount'], 10);
        message.userData.amount = minAmount > 10 ? minAmount : 10;


        /*if(self.publishedInSession[`${message.userData.recharge_number}:${message.catalogProductID}`]) {
            L.log('_processRecords', `Already published in session ${self.tableName}, ${message.userData.recharge_number}, ${message.catalogProductID}`);
            return self._processRecords(records, index+1, done);
        }*/

        if (!self.commonLib.isCreditCardOperator(currentRecord.service) && self.publishedInBatch[`${self.operator}:${currentRecord.recharge_number}`]) {
            L.log('publisher :: _ProcessRecords:', `Already processed Record of recharge number: ${currentRecord.recharge_number} and operator ${self.operator}`);
            return done()
        }
        let prefetchStrategy = _.get(self.config, ['DYNAMIC_CONFIG', 'OPERATOR_CONFIG', self.operator, 'PREFETCH_STRATEGY'],null);
        let prefetchPercent = _.get(self.config, ['DYNAMIC_CONFIG', 'OPERATOR_CONFIG', self.operator, 'PREFETCH_PERCENT'],null);
        let nextRetryFreq = self.billSubscriber.getNextRetryFreq(currentRecord.operator, message.catalogProductID, message.oldCatalogProductID , 1 );
        /*New Code for publishing mechanism */
        utility.pauseIndefinitely({
            L: L,
            rmqPublisher: self.reminderPublisher,
            maxQueueCount: MAX_QUEUE_COUNT,
            gateway: gateway,
            queueName: queueName,
            config: self.config,
            latencyProvider: self.latencyProvider,
            prefetchStrategy: prefetchStrategy,
            prefetchPercent: prefetchPercent,
            productId : currentRecord.product_id,
            operator  : currentRecord.operator,
            traceKey : currentRecord.traceKey
        }, (err, waitingTimeForNextPush) => {

            ASYNC.waterfall([
                next => {
                    if (err) {
                        L.error('publisher :: _processRecords :: pauseIndefinitely', `Error occured while checking latency or getting Q details for recharge_number ${message.userData.recharge_number} and catalogProductID ${message.catalogProductID}`, err);
                        newStatus = _.get(self.config, 'COMMON.bills_status.ERROR_WHILE_PUBLISHING', 12);
                        next(err);
                    } else {
                        let recordNeedsToBePublished = self.checkIfRecordHasToBePublished(currentRecord),
                            productStatus = _.get(self.config, ['CVR_DATA', _.get(currentRecord, 'product_id'), 'status'], null);
                        self.markingUnusedToOtherCustomerId(currentRecord,this.tableName, function(err,data){
                            if (!recordNeedsToBePublished) {
                                newStatus = _.get(self.config, 'COMMON.bills_status.NOT_IN_USE', 13);
                                reason = "EXPIRED_USER";
                                next('record not in use');
                            }
                            else if (!productStatus) {
                                newStatus = _.get(self.config, 'COMMON.bills_status.DISABLED', 7);                            
                                L.log(`publisher :: _ProcessRecords :: PID Is marked inactive ::recharge_number_${message.userData.recharge_number}_catalogProductID_${message.catalogProductID}_operator_${message.productInfo.operator}_productStatus_${productStatus}`);                                  
                                utility._sendMetricsToDD(1, ['REQUEST_TYPE:PUBLISHED', 'STATUS:PID_INACTIVE','OPERATOR:' + _.get(currentRecord, 'operator'),'batch:' + self.batchId,]);
                                next('PID Is marked inactive');                                
                            }
                            else {
                                next();
                            }
                        });
                    }
                },
                // Marking status = PUBLISHED before hitting validation API, This updation was required since in some cases subscriber logic do not update status which can mmis guide with previous status (4/2) even after record was processed.
                next => {
                    let billStatus,
                    statusMap=_.get(self.config, 'COMMON.bills_status', null);
                    for(const property in statusMap) {
                        if(statusMap[property]==newStatus){
                            billStatus=property;
                        }
                    }
                    self.bills.updateBillByPublisher(err => {
                        if (err) {
                            L.error('publisher :: _processRecords ', 'error occurred while updating record before publishing for ', err);
                        }
                        next();
                    }, self.tableName, {
                        status: newStatus,
                        retryCount: retryCount,
                        amount: currentRecord.amount,
                        billDueDate: currentRecord.due_date,
                        reason: reason,
                        rechargeNumber: currentRecord.recharge_number,
                        productId: currentRecord.product_id,
                        operator: currentRecord.operator,
                        service: currentRecord.service,
                        customerId: currentRecord.customer_id,
                        nextBillFetchDate: this.billSubscriber.eligibleForReschedule(billStatus, currentRecord.operator)?  this.billSubscriber.eligibleForReschedule(billStatus, currentRecord.operator).format('YYYY-MM-DD HH:mm:ss') : MOMENT().add(nextRetryFreq, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                        publishedDate: MOMENT().format('YYYY-MM-DD HH:mm:ss'),
                        gateway: gateway,
                        customerId: currentRecord.customer_id,
                        id: currentRecord.id
                    })
                },
                next => {
                    L.log('_processRecords', `Publishing data to FFR for gateway ${gateway} and recharge_number ${message.userData.recharge_number} and catalogProductID ${message.catalogProductID}`);
                    self.recordsPublished++;
                    //Lets cache this, to prevent ourselves from sending same bill record (rechargeNumber:ProductId combinatiion) again to gw Service
                    self.publishedInSession[`${message.userData.recharge_number}:${message.catalogProductID}`] = true
                    // Hit FFR Validation API
                    let source = "publisher";
                    if (self.includedOperator.includes(currentRecord.operator) || (self.prepaidPublisherInstance && self.prepaidOperatorsAllowed && self.prepaidOperatorsAllowed.includes(currentRecord.operator))) {
                        self.skipFfrValidation(next, currentRecord, message, source);
                    }
                    else{
                        self.hitFfrValidationApi(next, currentRecord, message,source);
                    }
                },

                (message, next) => {
                    /* Temp fix to avoid validation block on FFR*/
                    if (_.get(message, 'validationGwResponse.errorMessageCode', '') == 1000) {
                        waitingTimeForNextPush = 10 * 60 * 1000; // 10 minutes
                        L.log('_processRecords', `Detected Validation block pausing for ${waitingTimeForNextPush} mSec for Operator : ${currentRecord.operator} recharge_number : ${currentRecord.recharge_number} ,customer id : ${currentRecord.customer_id}`);
                    }
                    self.billSubscriber._dumpInSQL(message, currentRecord, next);
                }
            ],
                (err, message) => {
                    // updating status = RETRY or MAX_RETRY_REACHED in case of API failure
                    if (err == 'VALIDATION_API_REQUEST_FAILURE') {
                        /**
                         *  Update => STATUS  and RETRY_COUNT  and  NEXT_BILL_FETCH_DATE
                         * if FFR request fails one time it will inc. retry_count by 1 and nextBillFetchDate set to now() + 1 day
                         * On consecutive 3 fails nextBillFetchDate set to now() + 3 days
                         */
                        self._promoteToNextTrialPhase(currentRecord, nextBillFetchDate, () => {
                            L.info('_processRecords', 'setting NBFD:', nextBillFetchDate);
                        });
                    }
                    else if (err) {
                        let billStatus,statusMap=_.get(self.config, 'COMMON.bills_status', null);
                        for(const property in statusMap) {
                            if(statusMap[property]==newStatus){
                                billStatus=property;
                            }
                        }
                        L.error('_processRecords', 'error encountered', err);

                        self.bills.updateBillByPublisher(err => {
                            if (err) {
                                L.error('_processRecords.updateBill', `error occurred while updating record for recharge_number ${message.userData.recharge_number} and catalogProductID ${message.catalogProductID}`, err);
                            }
                        }, self.tableName, {
                            status: newStatus,
                            retryCount: retryCount,
                            amount: currentRecord.amount,
                            billDueDate: currentRecord.due_date,
                            reason: reason,
                            rechargeNumber: currentRecord.recharge_number,
                            productId: currentRecord.product_id,
                            operator: currentRecord.operator,
                            service: currentRecord.service,
                            customerId: currentRecord.customer_id,
                            nextBillFetchDate:  this.billSubscriber.eligibleForReschedule(billStatus, currentRecord.operator)?  this.billSubscriber.eligibleForReschedule(billStatus, currentRecord.operator).format('YYYY-MM-DD HH:mm:ss') : MOMENT().add(nextRetryFreq, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                            publishedDate: MOMENT().format('YYYY-MM-DD HH:mm:ss'),
                            gateway: gateway,
                            customerId: currentRecord.customer_id,
                            id: currentRecord.id
                        })
                    }

                    else if (!err) {
                        if (!self.commonLib.isCreditCardOperator(currentRecord.service)) {
                            self.publishedInBatch[`${self.operator}:${currentRecord.recharge_number}`] = true;
                        }
                    }

                    L.log('_processRecords::', 'checking is_automatic for reset, rech=' + currentRecord.recharge_number + ", existing is_automatic=" + _.get(currentRecord, 'is_automatic', null) + ", channel_id=" + _.get(message, 'channel_id', null));
                    if ((_.get(message, 'channel_id', null) !== "digital-reminder-realtime-subs 1" && _.get(message, 'channel_id', null) !== "SUBS 1" && _.get(message, ['customerInfo', 'channel_id'], null) !== "SUBS 1")
                        && (_.get(currentRecord, 'is_automatic', null) == 3 || _.get(currentRecord, 'is_automatic', null) == 4) 
                        && (message.commonStatus == _.get(this.config, 'COMMON.bills_status.BILL_FETCHED', 4))) {
                        L.log('_processRecords::', 'Resetting is_automatic back to 1, ' + currentRecord.recharge_number);
                        self.bills.resetIsAutomatic(function () { }, self.tableName, currentRecord.recharge_number, [3, 4]);
                    }

                    utility._sendMetricsToDD(waitingTimeForNextPush, [
                        'REQUEST_TYPE:' + 'PUBLISHER_BATCH_DELAY_BY_GW_LATENCY',
                        'OPERATOR:' + _.get(currentRecord, 'operator'),
                        'GATEWAY:' + gateway
                    ]);
                    let MAX_WAIT_TIME_FOR_NEXT_PUSH = _.get(self.config, ['PUBLISHER_CONFIG' ,'MAX_WAIT_TIME_FOR_NEXT_PUSH','THRESHOLD_TIME'], 0);
                    MAX_WAIT_TIME_FOR_NEXT_PUSH *= self.getMillisecondsFromTimeUnit(_.get(self.config, ['PUBLISHER_CONFIG' ,'MAX_WAIT_TIME_FOR_NEXT_PUSH','THRESHOLD_TIME_UNIT'], 'hours'));

                    L.info('_processRecords', 'waitingTimeForNextPush,', waitingTimeForNextPush);
                    if (MAX_WAIT_TIME_FOR_NEXT_PUSH && waitingTimeForNextPush > MAX_WAIT_TIME_FOR_NEXT_PUSH) {
                        waitingTimeForNextPush = MAX_WAIT_TIME_FOR_NEXT_PUSH;    
                        L.info('_processRecords', 'maxWaitingTimeForNextPush', waitingTimeForNextPush);
                    }

                    if(!waitForNextPush) {
                        return done(null, message);
                    }
                    //Since, we now know at what rate gateway is processing requests, therefore, we are holding our hoses
                    setTimeout(() => {
                        done(null, message);
                    }, waitingTimeForNextPush)

                }
            );
        })
    }

    getMillisecondsFromTimeUnit(timeUnit) {
        switch (timeUnit) {
            case 'hours':
                timeUnit = 60 * 60 * 1000; // convert hours into milliseconds
                break;
            case 'minutes':
                timeUnit = 60 * 1000; // convert minutes into milliseconds
                break;
            case 'seconds':
                timeUnit = 1000; // convert seconds into milliseconds
                break;
            default:
                timeUnit = 60 * 60 * 1000; // convert hours into milliseconds
                break;
        }
        return timeUnit;
    }

    skipFfrValidation(done, currentRecord, message,source = null ) {
        let self = this;
        let next_bill_fetch_date,billDueDate,
            timeDelayNbfd = _.get(this.config, ['DYNAMIC_CONFIG', 'OPERATOR_MONTHLY_PAYMENT', 'COMMON', 'TIME_DELAY_NBFD'], 5),
            aboveThresholdAddTimeDuration = _.get(this.config, ['DYNAMIC_CONFIG', 'OPERATOR_MONTHLY_PAYMENT', 'COMMON', 'ABOVE_TIME_DURATION'], 1),
            aboveThresholdAddTimeUnit = _.get(this.config, ['DYNAMIC_CONFIG', 'OPERATOR_MONTHLY_PAYMENT', 'COMMON', 'ABOVE_TIME_UNIT'], 'months');
        if(MOMENT().diff(MOMENT(_.get(currentRecord, 'payment_date', null)), 'days') > 15 && MOMENT().diff(MOMENT(_.get(currentRecord, 'due_date', null))) > 0){
            billDueDate = String(MOMENT(_.get(currentRecord, 'due_date', null)).add(aboveThresholdAddTimeDuration, aboveThresholdAddTimeUnit).format('YYYY-MM-DD HH:mm:ss'));
            next_bill_fetch_date = String(MOMENT(_.get(currentRecord, 'next_bill_fetch_date', null)).add(aboveThresholdAddTimeDuration, aboveThresholdAddTimeUnit).format('YYYY-MM-DD HH:mm:ss'));
        }
        else if(MOMENT().diff(MOMENT(_.get(currentRecord, 'payment_date', null)), 'days') <= 15){
            billDueDate = String(MOMENT(_.get(currentRecord, 'due_date', null)).format('YYYY-MM-DD HH:mm:ss'));
            next_bill_fetch_date = String(MOMENT('2037-05-21 00:00:00').format('YYYY-MM-DD HH:mm:ss')); //'2040-05-21 00:00:00';
        }
        else{
            timeDelayNbfd = _.get(this.config, ['DYNAMIC_CONFIG', 'OPERATOR_MONTHLY_PAYMENT', 'COMMON', 'TIME_DELAY_NBFD'], 5);
        
            let aboveThresholdAddTimeDuration = _.get(this.config, ['DYNAMIC_CONFIG', 'OPERATOR_MONTHLY_PAYMENT', 'COMMON', 'ABOVE_TIME_DURATION'], 1),
                aboveThresholdAddTimeUnit = _.get(this.config, ['DYNAMIC_CONFIG', 'OPERATOR_MONTHLY_PAYMENT', 'COMMON', 'ABOVE_TIME_UNIT'], 'months');
            if(MOMENT().diff(MOMENT(_.get(currentRecord, 'payment_date', null)), 'days') > 15 && MOMENT().diff(MOMENT(_.get(currentRecord, 'due_date', null))) > 0){
                billDueDate = String(MOMENT(_.get(currentRecord, 'due_date', null)).add(aboveThresholdAddTimeDuration, aboveThresholdAddTimeUnit).format('YYYY-MM-DD HH:mm:ss'));
                next_bill_fetch_date = String(MOMENT(_.get(currentRecord, 'next_bill_fetch_date', null)).add(aboveThresholdAddTimeDuration, aboveThresholdAddTimeUnit).format('YYYY-MM-DD HH:mm:ss'));
            }
            else if(MOMENT().diff(MOMENT(_.get(currentRecord, 'payment_date', null)), 'days') <= 15){
                billDueDate = String(MOMENT(_.get(currentRecord, 'due_date', null)).format('YYYY-MM-DD HH:mm:ss'));
                next_bill_fetch_date = String(MOMENT(_.get(currentRecord, 'bill_date', null)).add(aboveThresholdAddTimeDuration, aboveThresholdAddTimeUnit).format('YYYY-MM-DD HH:mm:ss'));
                next_bill_fetch_date = String(MOMENT(next_bill_fetch_date).add(timeDelayNbfd, 'days').format('YYYY-MM-DD HH:mm:ss'));
            }
            else{
                billDueDate = String(_.get(currentRecord, 'due_date', null));
                next_bill_fetch_date = String(_.get(currentRecord, 'next_bill_fetch_date', null));
            }
        }

        if (self.prepaidPublisherInstance && self.prepaidOperatorsAllowed && self.prepaidOperatorsAllowed.includes(currentRecord.operator)) {
            billDueDate = String(MOMENT().format('YYYY-MM-DD'));
            next_bill_fetch_date = String(MOMENT().add(_.get(self.config, ['DYNAMIC_CONFIG', 'PREPAID_OPERATORS', 'COMMON', 'NEXT_BILL_FETCH_DATE'], 15), 'days').format('YYYY-MM-DD'));
        }

        let customerDataResponse={
            "customerId":String(message.customerInfo.customer_id),
            "subscriberNumber": null,
            "subscriberName": null,
            "subscriberEmailId": null,
            "subscriberDOB": null,
            "subscriberAltNumber": null,
            "subscriberAddress": null,
            "subscriberGender": null,
            "subscriberCity": null,
            "minReloadAmount": null,
            "currentBillAmount": String(_.get(currentRecord, 'amount', null)),
            "billDueDate": billDueDate,
            "billDate": String(MOMENT().format('YYYY-MM-DD HH:mm:ss')),
            "nextBillFetchDate" : next_bill_fetch_date,
            "complianceRespCd": "",
            "responseCode": "",
            "complianceReason": "",
            "refId": "",
            "msgId": "",
            "customerName": String(_.get(currentRecord.customerOtherInfo, 'customerName', null)),
            "billNumber": "",
            "billPeriod": "",
            "additionalFees": {}
        }

        let validationGwResponse={
            "connectionError": null,
                      "deducedStatus": true,
                      "noBill": null,
                      "errorMessageCode": null,
                      "frontendErrorMessage": null
        }
        _.set(message, 'customerDataResponse', customerDataResponse);
        _.set(message, 'validationGwResponse', validationGwResponse);

        _.set(message, 'source', 'ffrSkip');

        return done(null, message);
    }

    hitFfrValidationApi(done, currentRecord, message,source = null ) {
        let
            self = this,
            validationGwResponse,

            apiOpts = {
                "uri": _.get(self.config, ['FFR', 'VALIDATION_URL'], null)+ '&rech_num='+_.get(message, 'userData.recharge_number', ''),
                "method": "POST",
                "timeout": 60000,
                'json': {
                    'cart_items': [
                        {
                            price: _.get(message, 'userData.amount', 10),
                            product_id: _.get(message, 'catalogProductID', null),
                            quantity: 1,
                            fulfillment_req: _.get(message, 'userData', {})
                        }
                    ],
                    'channel_id': _.get(message, 'channel_id', "digital-reminder"),
                }
            };

            // apiOpts = {
            //      "uri":'http://inmockjava.nonprod.onus.paytmdgt.io/v1/lic/validation?rech_num='+ _.get(message, 'userData.recharge_number', ''),
            //     "method": "GET",
            //     "timeout": 50000,
            // };

        if (self.commonLib.isCreditCardOperator(currentRecord.service)) {
            _.set(apiOpts, 'json.customer_id', _.get(currentRecord, 'customer_id', null));
        }
        L.verbose("hitFfrValidationApi", `apiOpts for recharge_number ${message.userData.recharge_number} and catalogProductID ${message.catalogProductID}`, JSON.stringify(apiOpts));
        var latencyStart = new Date().getTime();        
        REQUEST(apiOpts, (error, response, body) => {
            // publish status to datadog
            utility._sendLatencyToDD(latencyStart, {
                'REQUEST_TYPE': 'FFR_VALIDATION',
                'URL': '/v1/recharge/validate',
                'OPERATOR': _.get(currentRecord, 'operator'),
                'batch': self.batchId,
                'source' : source
            });

            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:' + 'FFR_VALIDATION',
                'STATCODE:' + _.get(response, 'statusCode', '5XX'),
                'OPERATOR:' + _.get(currentRecord, 'operator'),
                'batch:' + self.batchId,
                'source:' + source
            ]);
            L.log('hitFfrValidationApi', `FFR_VALIDATION_API_latency::operator:${_.get(currentRecord, 'operator')}_batchId:${self.batchId}_recharge_number:${message.userData.recharge_number}_latency:${new Date().getTime() - latencyStart}milliseconds`);

            if (body && typeof body === 'string') {
                try {
                    body = JSON.parse(body);
                } catch (e) {
                    this.L.error("hitFfrValidationApi", `Error parsing data received for recharge_number ${message.userData.recharge_number} and catalogProductID ${message.catalogProductID}`, e);
                    return done('VALIDATION_API_REQUEST_FAILURE', message);
                }
            }
            if (error || _.get(response, 'statusCode', null) != '200') {
                let errorStamp = error || "Invalid Statcode " + _.get(response, 'statusCode', null);

                this.L.error("hitFfrValidationApi", `error encountered for recharge_number ${message.userData.recharge_number} and catalogProductID ${message.catalogProductID}`, errorStamp);
                return done('VALIDATION_API_REQUEST_FAILURE', message);
            } else {
                this.L.verbose("hitFfrValidationApi", `Received response for recharge_number ${message.userData.recharge_number} and catalogProductID ${message.catalogProductID}`, JSON.stringify(body));

                validationGwResponse = _.get(body, 'cart_items.0.validationGwResponse', null);
                if(_.get(validationGwResponse,'allowBillFetch',null) == "NO"){
                    this.L.verbose("hitFfrValidationApi :: allowBillFetch is NO",message);
                }
                else{
                    _.set(message, 'customerDataResponse', _.get(body, 'cart_items.0.customerDataResponse', null));
                    _.set(message, 'validationGwResponse', _.get(body, 'cart_items.0.validationGwResponse', null));

                    // If frontendErrorMessage is blank, then will pick error message from body.error in case of error response
                    if (!_.get(message, 'validationGwResponse.frontendErrorMessage', null)) {
                        _.set(message, 'validationGwResponse.frontendErrorMessage', _.get(body, 'error', null))
                    }
                    _.set(message, 'source', 'ffr');
                }
            }
            if(_.get(validationGwResponse,'allowBillFetch',null) == "NO"){
                let waitTime = _.get(validationGwResponse,'billFetchWaitTime',0);
                this.L.log("hitFfrValidationApi :: Waiting for next push");
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:HIT_FFR_RESPONSE', 'STATUS:ALLOW_BILL_FETCH_NO','OPERATOR:' + _.get(currentRecord, 'operator'),'PRODUCT_ID:'+ _.get(message, 'catalogProductID', null),'WAIT_TIME:' + waitTime]);
                setTimeout(() => {
                    self.hitFfrValidationApi(done, currentRecord, message, source)
                }, waitTime)
            }
            else{
                return done(null, message);
            }
        });
    }

    checkIfRecordHasToBePublished(currentRecord) {
        let self = this,
           // noOfDaysToStopPublishing = _.get(self.config, ['OPERATOR_NOT_IN_USE_CONFIG', _.get(currentRecord, 'operator', null)], 90),
            noOfDaysToStopPublishing = 90,
            
            paymentDate = _.get(currentRecord, 'payment_date', null),
            createdDate = _.get(currentRecord, 'created_at', null),
            customerBucket = _.get(currentRecord, 'customer_bucket', null);

        if (_.get(currentRecord, 'is_automatic', 0)!== 0 || customerBucket)
            return true;
        if (!paymentDate && MOMENT().diff(MOMENT(createdDate), 'days') > noOfDaysToStopPublishing) {
            L.log('publisher::_processRecords', 'paymentDate is null or created at date reached threshhold', _.get(currentRecord, 'id'));
            return false;
        }
        if (paymentDate && MOMENT().diff(MOMENT(paymentDate), 'days') > noOfDaysToStopPublishing) {
            L.log('publisher::_processRecords', 'Threshold reached for publishing the data for id ', _.get(currentRecord, 'id'));
            return false;
        }
        return true;
        
    }

    /*
        This function basically perform the following for a passed record:
            if($record.retry_count == MAX_RETRY_COUNT) { //Special Case, when retry_count is reached to max value
              update the $record in DB and set
                  STATUS = 'pending' and
                  RETRY_COUNT = 0 and
                  NEXT_BILL_FETCH_DATE = N-th PRIMARY_CYCLE
            }
            else {
              update the $record in DB and set
                  STATUS = 'retry' and
                  RETRY_COUNT += 1 and
                  NEXT_BILL_FETCH_DATE = $cycle
            }
    */
    _promoteToNextTrialPhase(record, cycle, done) {
        let self = this,
            params = {
                status: record.status,
                retryCount: record.retry_count,
                amount: record.amount,
                billDueDate: record.due_date,
                reason: record.reason,
                rechargeNumber: record.recharge_number,
                productId: record.product_id,
                publishedDate: MOMENT().format('YYYY-MM-DD HH:mm:ss'),
                gateway: record.gateway,
                customerId: record.customer_id,
                operator: record.operator,
                service: record.service,
                id: record.id
            }
        if (record.retry_count >= MAX_RETRY_COUNT - 1) { //Special Case, when retry_count is reached to max value
            let operatorsNthPrimaryCycle = _.get(self.config, ['COMMON', 'operatorNthPrimaryCycle', record.operator], 3)
            params.nextBillFetchDate = self._getNthPrimaryCycle(operatorsNthPrimaryCycle); // now() + 3 days
            params.status = _.get(self.config, 'COMMON.bills_status.PENDING', 0)
            params.retryCount = 0
            params.reason = 'MAX_RETRY_COUNT reached for this record'
        }
        else {
            params.nextBillFetchDate = cycle // when dead record then cycle = now() + 1 day 
            params.status = _.get(self.config, 'COMMON.bills_status.RETRY', 2)
            params.retryCount += 1
            params.reason = ''
        }

        self.bills.updateBillByPublisher(err => {
            if (err) {
                L.error('_promoteToNextTrialPhase', 'error occurred while updating record: err', err);
            }
            done()
        }, self.tableName, params);
    }

    /*
       This function returns the formatted date time ('YYYY-MM-DD HH:mm:ss') by adding some configurable number of hours to the current time
    */
    _getSecondaryCycle() {
        return MOMENT().add(this.secondaryCycle, 'hours').format('YYYY-MM-DD HH:mm:ss')
    }

    /*
       This function returns the n-th primary cycle, in our case we are considering PRIMARY_CYCLE as the 00:00 hrs
       So, it will return that datetime, on which n-th cycle is falling, And by default it returns first primary cycle
       in the format ('YYYY-MM-DD HH:mm:ss')

       -- Optimising query performance by setting time value as 00
    */
    _getNthPrimaryCycle(n = 1) { //default value of n will be 1
        let todaysPrimaryCycleTime = MOMENT().startOf('day'),
            currentTime = MOMENT(),
            nthCycleFromNow = null;

        if (currentTime.diff(todaysPrimaryCycleTime) < -1) {
            nthCycleFromNow = MOMENT().add(n - 1, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss')
        }
        else {
            nthCycleFromNow = MOMENT().add(n, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss')
        }
        return nthCycleFromNow
    }

    markingUnusedToOtherCustomerId(currentRecord,table,cb){
        let self=this;
        let rechargeNumber = currentRecord.recharge_number,
            productId = currentRecord.product_id,
            operator = currentRecord.operator,
            service = currentRecord.service,
            customerId = currentRecord.customer_id;
            
        ASYNC.waterfall([
            next => {
                let read_query = `SELECT * FROM ${table} WHERE customer_id != ${customerId} AND recharge_number = "${rechargeNumber}" AND operator = "${operator}" AND service = "${service}" AND status<>13`;
                self.L.log(`markingUnusedToOtherCustomerId::select_query service:${read_query}`);
                self.dbInstance.exec(function (error, data) {
                    if (error || !(data)) {
                        L.critical('markingUnusedToOtherCustomerId', 'error in fetching data from db ->  ', error);
                        return cb();
                    }
                    else {
                        return next(null, data)
                    }
                }, 'DIGITAL_REMINDER_SLAVE', read_query);
            },
            (results,next) => {
                results = JSON.parse(JSON.stringify(results));
                ASYNC.forEach(results, async (result) => {
                    let toBePublished = self.checkIfRecordHasToBePublished(result);
                    if(!toBePublished){
                        let query = `UPDATE ${table} SET status = 13, reason = 'EXPIRED_USER' where customer_id = ${result.customer_id} AND recharge_number = "${result.recharge_number}" AND service = "${result.service}"`;
                        self.L.log(`markingUnusedToOtherCustomerId::update_query service:${query}`);
                        self.dbInstance.exec(function (error, data) {
                            if (error || !(data)) {
                                self.L.log("markingUnusedToOtherCustomerId::error",error)
                            }
                            else {
                                self.L.log("markingUnusedToOtherCustomerId::updated")
                            }
                        }, 'DIGITAL_REMINDER_MASTER', query);
                    }
                })
                return cb();
            },
        ],  (err) => {
                if(err) {
                    let errMsg = 'Error in processRecord :: ' + err;
                    L.error(errMsg);
                } 
                return cb();
        });
    }
}


export default Publisher
