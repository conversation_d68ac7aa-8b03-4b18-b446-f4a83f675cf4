import VALIDATOR from 'validator';
import MOMENT from 'moment';
import REQUEST from 'request';
import ASYNC from 'async'
import _ from 'lodash';
import utility from '../../lib';
import OS from 'os';
import processCreditCardStrategy from './creditCardStrategy';
import prepaidSmsparsing from './prepaidSmsParsing';
import postpaidSmsParsing from './postpaidSmsParsing';
import postpaidSmsParsingBillPaid from './postpaidSmsParsingBillPaid';
import PostpaidSmsParsingCAIdentify from './postpaidSmsParsingCAIdentify';
import SmsParsingSyncCCBillLibrary from '../../lib/smsParsingSyncCCBills';
import DigitalCatalog from '../../lib/digitalReminderConfig'
import INFRAUTILS from 'infra-utils'
import { promisify } from 'util'
import Q from 'q'
import KafkaConsumerChecks from '../../lib/kafkaConsumerChecks';
import KafkaConsumer from '../../lib/KafkaConsumer';

class ElectricitySMSParsingBillPayment {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.activePidLib = options.activePidLib;
        this.infraUtils = options.INFRAUTILS;
        this.processCreditCardStrategy = new processCreditCardStrategy(options, this);
        this.electricityDwhRealTimeSmsParsing = _.get(options,'electricityDwhRealTimeSmsParsing',false);
        if(this.electricityDwhRealTimeSmsParsing) _.set(options,'smsParsingBillsDwhRealtime',true);
        this.prepaidSmsparsing = new prepaidSmsparsing(options, this);
        this.postpaidSmsParsing = new postpaidSmsParsing(options, this);
        this.postpaidSmsParsingBillPaid = new postpaidSmsParsingBillPaid(options,this);
        this.postpaidSmsParsingCAIdentify = new PostpaidSmsParsingCAIdentify(options,this);
        this.smsParsingSyncCCBillLib = new SmsParsingSyncCCBillLibrary(options);
        this.digitalCatalogLib = DigitalCatalog;
        this.refereshIntervalForCategoryData = 15 * 60 * 1000; // DCAT getCategoryProductDetail API refresh interval 
        this.greyScaleEnv = _.get(options, 'greyScaleEnv', false);
        this.kafkaBatchSize = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'SMS_PARSING_CC_BILLS', 'BATCHSIZE'], 2) : 500;
        this.kafkaBatchDelay = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'SMS_PARSING_CC_BILLS', 'DELAY'], 5 * 60 * 1000) : 0;

        let { MONGO_DB, MYSQL, KAFKA } = this.config.VIL_SYNC_DB;
        this.mongoDbInstance = new INFRAUTILS.mongo(this.config.MONGO.HIDDEN_SLAVE);
        this.mongoCollection = 'users';
        this.retryCountForMongo = _.get(this.config, ['DYNAMIC_CONFIG', 'VIL_SYNC_DB_PUBLISHER', 'MONGO_DB', 'RETRY_COUNT'], MONGO_DB.RETRY_COUNT);
        this.commonLib = new utility.commonLib(options);
        this.mongoDbTps = _.get(this.config, ['DYNAMIC_CONFIG', 'VIL_SYNC_DB_PUBLISHER', 'MONGO_DB', 'TPS'], MONGO_DB.TPS);
        this.timeThresholdForMongo = parseInt(1000 / this.mongoDbTps);  //this.mongoDbTps = 3 or 4;
        this.lastMongoFetchTime = this.getTimeInMs() - this.timeThresholdForMongo;
        this.mongoDbFetchRecordsFailureRetryInterval = _.get(this.config, ['DYNAMIC_CONFIG', 'VIL_SYNC_DB_PUBLISHER', 'MONGO_DB', 'FAILURE_RETRY_INTERVAL'], MONGO_DB.FAILURE_RETRY_INTERVAL);
        this.RUreadsKafkaTime =  new Date().getTime();       // Time at which RU reads from the KAFKA
        this.kafkaConsumerChecks = new KafkaConsumerChecks(options);
    }

    async start() {
        let self = this;
        self.smsParsingSyncCCBillLib.refreshDCATCacheData(self.refereshIntervalForCategoryData);
        self.L.log('start', 'Going to configure Kakfa..');
        self.configureKafka(function (error) {
            if (error) {
                self.L.critical('SMS_PARSING_ELECTRICITY_BILL_PAYMENT :: start', 'unable to configure kafka', error);
                process.exit(0);
            }
            else {
                self.L.log('SMS_PARSING_ELECTRICITY_BILL_PAYMENT :: start', 'Kafka Configured successfully !!');
            }
        });
        this.L.log("start", "Mongo DB connectivity!!")

        // await promisify(this.mongoDbInstance.connect.bind(this.mongoDbInstance))();

        this.L.log("Mongo DB Connected!!")
    }

    configureKafka(done) {
        let self = this;
        ASYNC.waterfall([
            next => {
                /**
                 * Kafka Publisher to publish bill fetch to Automatic
                 */
                self.kafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": self.config.KAFKA.TOPICS.AUTOMATIC_SYNC.HOSTS
                });
                self.kafkaPublisher.initProducer('high', function (error) {
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_ELECTRICITY_BILL_PAYMENT", 'STATUS:ERROR','TYPE:AUTOMATIC_SYNC_PRODUCER','SOURCE:MAIN_FLOW']);
                    }
                    return next(error);
                });
            },
            next => {
                /**
                 * Kafka publisher to publish events to CT publisher pipeline 
                 */
                self.ctKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.CT_EVENTS_PUBLISHER.HOSTS
                });
                this.ctKafkaPublisher.initProducer('high', function (error) {
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:ERROR','TYPE:CT_EVENTS_PUBLISHER','SOURCE:MAIN_FLOW']);
                    }
                    return next(error)
                });
            },
            next => {
                /**
                 * Kafka publisher to update events to non paytm bills pipeline 
                 */
                self.nonPaytmKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.NON_PAYTM_RECORDS.HOSTS
                });
                this.nonPaytmKafkaPublisher.initProducer('high', function (error) {
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:ERROR','TYPE:NON_PAYTM_RECORDS_PUBLISHER','SOURCE:MAIN_FLOW']);
                    }
                    return next(error)
                });
            },
            next => {
                /**
                 * Kafka publisher to update reminder bill fetch
                 */
                self.billFetchKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.REMINDER_BILLFETCH_PIPELINE.HOSTS
                });
                this.billFetchKafkaPublisher.initProducer('high', function (error) {
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_DWH", 'STATUS:ERROR', 'TYPE:PRODUCER', 'TOPIC:REMINDER_BILL_FETCH', 'SOURCE:MAIN_FLOW']);
                    }
                    return next(error)
                });
            },
            next => {
                /**
                 * Kafka publisher to update reminder bill fetch
                 */
                self.billFetchRealTimeKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.REMINDER_BILLFETCH_PIPELINE_REALTIME.HOSTS
                });
                this.billFetchRealTimeKafkaPublisher.initProducer('high', function (error) {
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_DWH_REALTIME", 'STATUS:ERROR', 'TYPE:PRODUCER', 'TOPIC:REMINDER_BILL_FETCH_REALTIME', 'SOURCE:MAIN_FLOW']);
                    }
                    return next(error)
                });
            },
            next => {
                /**
                 * Kafka publisher to publish events to Paytm First Team (events will be same as CT)
                 */
                 self.paytmFirstKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.PAYTM_FIRST_CC_EVENTS_PUBLISHER.HOSTS
                });
                this.paytmFirstKafkaPublisher.initProducer('high', function (error) {
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:ERROR','TYPE:PAYTM_FIRST_CC_EVENTS_PUBLISHER','SOURCE:MAIN_FLOW']);
                    }
                    return next(error)
                });
            },
            next => {
                self.L.log('configureKafka', 'Going to initialize Kakfa Consumer for topic : FAILED_SMS_PARSING_REGEX_BACKUP');
                /**
                 * Kafka publisher to publish events of Failed SMS Parsing by REGEX
                 */
                self.failedSMSParsingPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.FAILED_SMS_PARSING_PUBLISHER.HOSTS
                });
                this.failedSMSParsingPublisher.initProducer('high', function (error) {
                    self.L.log('configureKafka', 'For topic : FAILED_SMS_PARSING_REGEX_BACKUP', error);
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:ERROR','TYPE:FAILED_SMS_PARSING_PUBLISHER','SOURCE:MAIN_FLOW']);
                    }
                    return next(error)
                });
            },
            next => {
                /**
                 * Kafka publisher to publish events of UMPS_SUBCRIPTION
                 */
                self.upmsPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.UPMS_PUBLISHER.HOSTS
                });
                this.upmsPublisher.initProducer('high', function (error) {
                    if (error) {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:ERROR', 'TYPE:UPMS_PUBLISHER', 'SOURCE:MAIN_FLOW']);
                    }
                    return next(error)
                });
            },
            next => {
                self.L.log('configureKafka', 'Going to initialize Kakfa Consumer for topic : SMS_PARSING_BILL_PAYMENT');

                let kafkaConsumerObj;
                
                if(self.electricityDwhRealTimeSmsParsing) {
                    kafkaConsumerObj = {
                        "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.ELECTRICITY_SMS_PARSING_BILL_PAYMENT_DWH_REALTIME.HOSTS'),
                        "groupId": "smsParsingBillPayment-electricity-realtime-consumer",
                        "topics": _.get(self.config.KAFKA, 'SERVICES.ELECTRICITY_SMS_PARSING_BILL_PAYMENT_DWH_REALTIME.TOPIC'),
                        "id": `smsParsingBillPayment-consumer_${OS.hostname()}_${process.pid}`,
                        sessionTimeout:_.get(self.config,'DYNAMIC_CONFIG.KAFKA_CONFIG.SESSION_TIMEOUT',2*60*1000),
                        maxProcessTimeout:_.get(self.config,'DYNAMIC_CONFIG.KAFKA_CONFIG.MAX_PROCESS_TIMEOUT',3*60*1000)
                    };
                } else {
                    kafkaConsumerObj = {
                        "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.ELECTRICITY_SMS_PARSING_BILL_PAYMENT.HOSTS'),
                        "groupId": "smsParsingBillPayment-electricity-consumer",
                        "topics": _.get(self.config.KAFKA, 'SERVICES.ELECTRICITY_SMS_PARSING_BILL_PAYMENT.TOPIC'),
                        "id": `smsParsingBillPayment-consumer_${OS.hostname()}_${process.pid}`,
                        sessionTimeout:_.get(self.config,'DYNAMIC_CONFIG.KAFKA_CONFIG.SESSION_TIMEOUT',2*60*1000),
                        maxProcessTimeout:_.get(self.config,'DYNAMIC_CONFIG.KAFKA_CONFIG.MAX_PROCESS_TIMEOUT',3*60*1000)
                    };
                }
                
                self.kafkasmsParsingBillPaymentConsumer = new KafkaConsumer(kafkaConsumerObj);
                self.kafkasmsParsingBillPaymentConsumer.initConsumer(self.execSteps.bind(self), (error) => {
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:ELECTRICITY_SMS_PARSING_BILL_PAYMENT", 'STATUS:ERROR','TYPE:ELECTRICITY_SMS_PARSING_BILL_PAYMENT_CONSUMER','SOURCE:MAIN_FLOW']);
                    }
                    if (!error) {
                        self.L.log("configureKafka", `consumer of topic : ELECTRICITY_SMS_PARSING_BILL_PAYMENT Configured`);
                    }
                    return next(error);
                });
            },

        ], function (error) {
            if (error) {
                self.L.critical('configureKafka', 'Could not initialize Kafka', error);
                return done(error)
            }
            return done(null);
        });
    }

    execSteps(records, resolveOffset , topic , partition , cb) {
        let self = this,
            chunkSize = 50,
            currentPointer = 0, lastMessage;

        self.RUreadsKafkaTime = new Date().getTime();

        let startTime = new Date().getTime();
        if (records && _.isArray(records)) {
            lastMessage = records[records.length - 1];
        } else {
            self.L.critical('ELECTRICITY_SMS_PARSING_BILL_PAYMENT:execSteps', `No valid kafka records found. Received records-`, records);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:ELECTRICITY_SMS_PARSING_BILL_PAYMENT", 'STATUS:INVALID_PAYLOAD', 'SOURCE:MAIN_FLOW']);
            return cb();
        }


        self.L.log('ELECTRICITY_SMS_PARSING_BILL_PAYMENT:execSteps:: ', `Processing ${records.length} Electricity SMS Parsing Bill Payment data !!`);
        utility._sendMetricsToDD(records.length, ['REQUEST_TYPE:ELECTRICITY_SMS_PARSING_BILL_PAYMENT', 'STATUS:TRAFFIC', 'SOURCE:MAIN_FLOW']);

        ASYNC.whilst(
            () => {
                return currentPointer < records.length;
            },
            (callback) => {
                let nextChunk = records.slice(currentPointer, (currentPointer + chunkSize));
                currentPointer += chunkSize;
                self.processBatch(nextChunk, () => {
                    setTimeout(() => {
                        callback();
                    }, 2);
                });
            },
            async (err) => {
                self.kafkaConsumerChecks.findOffsetDuplicates("ElectricitySMSParsingBillPayment", records, topic , partition);

                await resolveOffset(lastMessage.offset)
                self.L.log('execSteps::', 'Commit success for offset :', _.get(lastMessage, 'offset') + ', topic :' + topic + ', partition :' + partition + ', timestamp :' + _.get(lastMessage, 'timestamp'));

                    // Resume consumer now


                    let endTime = new Date().getTime();
                    let executionTime = (endTime - startTime) / 1000;
                    executionTime = Math.round(executionTime);
                    self.L.log('execSteps::', 'per chunkSize record Execution time: ', executionTime, 'seconds');
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_STATUS", 'STATUS:SUCCESS', "SOURCE:ELECTRICITY_SMS_PARSING_BILL_PAYMENT_CONSUMER", "TIME_TAKEN:" + executionTime]);

                    setTimeout(function () {
                        return cb();
                    }, self.kafkaBatchDelay);
        }) 
    }

    processBatch(records, done) {
        let self = this,
            currentPointer = 0;
        ASYNC.whilst(
            () => {
                return currentPointer < records.length;
            },
            (callback) => {
                let record = records[currentPointer];
                currentPointer = currentPointer + 1;
                self.processData(record, () => {
                    setTimeout(() => {
                        callback();
                    }, 1);
                });
            },
            (err) => {
                if (err) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:ELECTRICITY_SMS_PARSING_BILL_PAYMENT", 'STATUS:INVALID_PAYLOAD', 'SOURCE:MAIN_FLOW']);
                    self.L.error("Invalid records found! :", JSON.stringify(records));
                }
                return done()
            }
        );
    }
    processData(record, done) {
        let self = this;
        self.L.info("Record received: ",record);
        let published_time = Number(_.get(record, 'timestamp', null));
        try {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:ELECTRICITY_SMS_PARSING_BILL_PAYMENT", 'STATUS:TRAFFIC', `PARTITION:${_.get(record, 'partition', null)}`, `TOPIC:${_.get(record, 'topic', null)}`]);
            record = JSON.parse(_.get(record, 'value', null));
            if (!record.data) {
                self.L.critical('ELECTRICITY_SMS_PARSING_BILL_PAYMENT:processData', `Invalid Kafka record received. data key is missing`, record);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:ELECTRICITY_SMS_PARSING_BILL_PAYMENT", 'STATUS:INVALID_PAYLOAD', 'SOURCE:MAIN_FLOW']);
                return done();
            }
        }
        catch (error) {
            if (error) {
                self.L.critical('ELECTRICITY_SMS_PARSING_BILL_PAYMENT:processData', `Invalid Kafka record received`, record);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:ELECTRICITY_SMS_PARSING_BILL_PAYMENT", 'STATUS:INVALID_JSON_PAYLOAD', 'SOURCE:MAIN_FLOW']);
            }
            return done();
        }


        let kafkaTopic = _.get(record, 'kafka_topic', null);
        if (!_.isArray(kafkaTopic) || !_.isArray(_.get(record, 'data', null))) {
            self.L.critical('ELECTRICITY_SMS_PARSING_BILL_PAYMENT:processData', `Invalid Kafka record received`, JSON.stringify(record));
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:INVALID_JSON_PAYLOAD', 'SOURCE:MAIN_FLOW']);
            return done();
        }
        else if (kafkaTopic[0] == 'dwh-ingest-SMS_PARSING_CC_BILLS_PAYMENT') {
            return ASYNC.map(
                record.data,
                (smsData, next) => {
                    _.set(smsData, 'published_time', published_time);
                    self.processCreditCardStrategy.executeStrategy(() => {
                        return next();
                    }, smsData, self);
                },
                err => {
                    if (err) {
                        self.L.critical('SMS_PARSING_BILL_PAYMENT:processData', `Invalid Kafka record received`, err);
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:INVALID_JSON_PAYLOAD', 'SOURCE:MAIN_FLOW']);
                    }
                    return done();
                }
            )
        }
        else if (kafkaTopic[0] == "SMS_PARSER_ELECTRICITY" || kafkaTopic[0] == "REALTIME_SMS_PARSER_ELECTRICITY") {
            let record_data = _.get(record, 'data', null);

            if(record_data.length < 1){
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:ELECTRICITY_SMS_PARSING_BILL_PAYMENT", 'STATUS:EMPTY_DATA', 'SOURCE:MAIN_FLOW']);
                self.L.log(`ElectricitySMSParsingBillPayment :: Empty sms Data found`);
                return done();
            }

            let level_2_category = _.get(record_data[0], 'level_2_category', null);

            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:ELECTRICITY_INDEX', 
                `SERVICE:ELECTRICITY`, 
                'STATUS:TRAFFIC', 
                'TYPE:LEVEL_2_CATEGORY',
                'LEVEL_2_CATEGORY:' + level_2_category, 
                `ORIGIN:${self.electricityDwhRealTimeSmsParsing == true ? "SMS_PARSING_DWH_REALTIME" : "SMS_PARSING_DWH"}`
            ]);

            if(level_2_category != 1 && level_2_category != 2 && level_2_category != 3){
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:ELECTRICITY_SMS_PARSING_BILL_PAYMENT", 'STATUS:LEVEL_2_CATEGORY_NOT_1', 'SOURCE:MAIN_FLOW']);
                self.L.log(`ElectricitySMSParsingBillPayment :: level_2_category is not 1, 2 and 3`);
                return done();
            }
            
            self.L.log(`ElectricitysmsParsingBillPayment :: executing postpaidSmsParsing flow as per data level_2_category : ${level_2_category}`);
            if(level_2_category == 1){
                return ASYNC.map(
                    record.data,
                    (smsData, next) => {
                        _.set(smsData, 'published_time', published_time);
                        smsData = self.formatData(smsData);
                        self.postpaidSmsParsing.executeStrategy(() => {
                            return next();
                        }, smsData,self);
                    },
                    err => {
                        if (err) {
                            self.L.critical('SMS_PARSING_POSTPAID:processData', `Invalid Kafka record received`, err);
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_POSTPAID", 'STATUS:INVALID_JSON_PAYLOAD', 'SOURCE:MAIN_FLOW']);
                        }
                        return done();
                    }
                )
            }
            else if(level_2_category == 2){
                return ASYNC.map(
                    record.data,
                    (smsData,next) => {
                        _.set(smsData, 'published_time', published_time);
                        self.postpaidSmsParsingBillPaid.executeStrategy(() => {
                            return next();
                        }, smsData,self);
                    },
                    err => {
                        if(err){
                            self.L.critical('SMS_PARSING_POSTPAID_BILL_PAID:processData', `Invalid Kafka record received`, err);
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_POSTPAID_BILL_PAID", 'STATUS:INVALID_JSON_PAYLOAD', 'SOURCE:MAIN_FLOW']);
                        }
                        return done();
                    }
                )
            }
            else if(level_2_category == 3){
                return ASYNC.map(
                    record.data,
                    (smsData, next) => {
                        _.set(smsData, 'published_time', published_time);
                        smsData = self.formatData(smsData);
                        self.postpaidSmsParsingCAIdentify.executeStrategy(() => {
                            return next();
                        }, smsData,self);
                    },
                    err => {
                        if (err) {
                            self.L.critical('SMS_PARSING_POSTPAID:processData', `Invalid Kafka record received`, err);
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_POSTPAID", 'STATUS:INVALID_JSON_PAYLOAD', 'SOURCE:MAIN_FLOW']);
                        }
                        return done();
                    }
                )
            }
            else{
                return done();
            }
        }
        else if (kafkaTopic[0] == "SMS_PARSING_TELECOM") {
            let record_data = _.get(record, 'data', null);

            if(record_data.length < 1){
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:EMPTY_DATA', 'SOURCE:MAIN_FLOW']);
                self.L.log(`smsParsingBillPayment :: Empty sms Data found`);
                return done();
            }

            let level_2_category = record_data[0].level_2_category;

            if(level_2_category == 5){
                self.L.log(`smsParsingBillPayment :: executing postpaidSmsParsing flow as per data level_2_category : ${level_2_category}`);
                return ASYNC.map(
                    record.data,
                    (smsData, next) => {
                        _.set(smsData, 'published_time', published_time);
                        self.postpaidSmsParsing.executeStrategy(() => {
                            return next();
                        }, smsData,self);
                    },
                    err => {
                        if (err) {
                            self.L.critical('SMS_PARSING_POSTPAID:processData', `Invalid Kafka record received`, err);
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_POSTPAID", 'STATUS:INVALID_JSON_PAYLOAD', 'SOURCE:MAIN_FLOW']);
                        }
                        return done();
                    }
                )
            }
            else{
            //return self.prepaidSmsparsing.executeStrategy(record.data[0], self);
                self.L.log(`smsParsingBillPayment :: executing prepaidSmsParsing flow as per data level_2_category : ${level_2_category}`);
                return ASYNC.map(
                    record.data,
                    (smsData, next) => {
                        _.set(smsData, 'published_time', published_time);
                        self.prepaidSmsparsing.executeStrategy(() => {
                            return next();
                        }, smsData, self);
                    },
                    err => {
                        if (err) {
                            self.L.critical('SMS_PARSING_PREPAID:processData', `Invalid Kafka record received`, err);
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_PREPAID", 'STATUS:INVALID_JSON_PAYLOAD', 'SOURCE:MAIN_FLOW']);
                        }
                        return done();
                    }
                )
            }
        }

        else {
            return ASYNC.map(
                record.data,
                (smsData, next) => {
                    self.defaultStrategy(() => {
                        return next();
                    }, smsData);
                },
                err => {
                    self.L.error("Invalid records found! :", JSON.stringify(record));
                    return done();
                }
            )
        }

    }

    defaultStrategy(done, record) {
        this.L.log('SMS_PARSING_BILL_PAYMENT:defaultStrategy:: ', "Not applying any logic for record ", JSON.stringify(record));
        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:NO_STRATEGY', 'SOURCE:MAIN_FLOW']);
        return done();
    }


    /**
    * run mongoDb query on recents after specified time, 
    *  retry if query faied after specified time
    */
    async mongoThrottleWapper(queryObj, retryCount = 0) {
        try {
            let sleep = (this.getTimeInMs() - this.lastMongoFetchTime);

            if (sleep < this.timeThresholdForMongo) {
                await this.commonLib.calledAfterTime(this.timeThresholdForMongo - sleep);
            }

            let data = await this.runMongoQuery(queryObj);

            this.L.verbose("mongoThrottleWapper: data from mongo", data);

            this.lastMongoFetchTime = this.getTimeInMs();

            return data;
        } catch (error) {
            this.L.error(`mongoThrottleWapper:: error in fetchRecords from table: ${this.mongoCollection}`, error);
            this.lastMongoFetchTime = this.getTimeInMs();

            /**
             * you can apply checks on error message, what ever here we are fetching data, so there should be no issue on fetching
             * if there is possible some other issue 
             */

            if (retryCount < this.retryCountForMongo) {
                await this.mongoThrottleWapper(queryObj, retryCount + 1);
            } else {
                this.L.critical(`mongoThrottleWapper:: error in fetchRecords from table: ${this.mongoCollection}`, error);
                await this.commonLib.calledAfterTime(this.mongoDbFetchRecordsFailureRetryInterval);
                await this.mongoThrottleWapper(queryObj, retryCount + 1);
            }
        }
    }

    runMongoQuery(queryObj) {
        return new Promise((resolve, reject) => {

            this.L.verbose("runMongoQuery: ", queryObj);

            this.mongoDbInstance.fetchDataFromCollection((err, results) => {
                if (err) {
                    let stats = { type: "MONGO_QUERY_FAILED", STATE: "ERROR", count: 1 }
                    this.publishStats(stats);
                    reject(err);
                } else {
                    resolve(results);
                }
            }, this.mongoCollection, queryObj);
        });
    }

    getTimeInMs(date) {
        return date ? new Date(date).getTime() : new Date().getTime();
    }

    suspendOperations() {

        var self = this,
            deferred = Q.defer();
        self.L.log(`smsParsingBillPayment::suspendOperations kafka consumer shutdown initiated`);

        Q()
            .then(function () {
                self.kafkasmsParsingBillPaymentConsumer.close(function (error, res) {
                    if (error) {
                        self.L.error(`smsParsingBillPayment::stopConsumer :: Error Stopping Consumer Err : ${error}`);
                        return error;
                    }
                    self.L.info(`smsParsingBillPayment::stopConsumer :: Consumer Stopped!  Response : ${res}`);
                })
            })
            .then(function () {
                self.L.log(`smsParsingBillPayment::suspendOperations kafka consumer shutdown successful`);
                deferred.resolve();
            })
            .catch(function (err) {
                self.L.error(`smsParsingBillPayment::suspendOperations error in shutting kafka consumer`, err);
                deferred.reject(err);
            });
        return deferred.promise;
    }

    formatRechargeNumber(formattedRecord) {
        let operator = _.toLower(_.get(formattedRecord,'operator',null));
        let rechargeNumber = _.get(formattedRecord,'rechargeNumber',null);
        if(operator == "assam power distribution company ltd. (apdcl)" && rechargeNumber && rechargeNumber.length === 11){
            formattedRecord.rechargeNumber = '0' + rechargeNumber;
        }
        if(operator == "tneb" && rechargeNumber && rechargeNumber.length >= 9 && rechargeNumber.length <= 12) {
            formattedRecord.rechargeNumber = '0' + rechargeNumber;
        }
        return formattedRecord;
    }

    formatData(smsData) {
        let self = this;
        let electricityDetails = _.get(smsData, 'electricity_details', null);

        let formattedRecord = {
            "cId": _.get(smsData, 'cId', null),
            "smsDateTime": _.get(smsData, 'smsDateTime', null),
            "timestamp": _.get(smsData, 'timestamp', null),
            "deviceDateTime": _.get(smsData, 'deviceDateTime', null),
            "collector_timestamp": _.get(smsData, 'collector_timestamp', null),
            "uploadTime": _.get(smsData, 'uploadTime', null),
            "smsOperator": _.get(smsData, 'smsOperator', null),
            "smsReceiver": _.get(smsData, 'smsReceiver', null),
            "smsSenderID": _.get(smsData, 'smsSenderID', null),
            "smsUUID": _.get(smsData, 'smsUUID', null),
            "category": _.get(smsData, 'predicted_category', null),
            "rechargeNumber": _.get(electricityDetails, 'primary_ca_no', null),
            "operator": _.get(electricityDetails, 'operator', null),
            "amount": _.get(electricityDetails, 'bill_due_amount', null),
            "dueDate": _.get(electricityDetails, 'bill_due_date', null),
            "rechargeNumber2": _.get(electricityDetails, 'secondary_ca_no', null),
            "level_2_category": _.get(smsData, 'level_2_category', null),
            "circle": _.get(smsData, 'circle', null),
            "isPrepaid": String(_.get(electricityDetails, 'is_prepaid', '0') || '0'),
            "published_time": _.get(smsData, 'published_time', null),
        };

        formattedRecord = self.formatRechargeNumber(formattedRecord);

            if(self.electricityDwhRealTimeSmsParsing) {
                formattedRecord.isDwhSmsParsingRealtime = true;
            }else {
                formattedRecord.isDwhSmsParsing = true;
            }
        if(typeof _.get(formattedRecord,"smsDateTime") == "string" && _.get(formattedRecord,"smsDateTime") != null && self.electricityDwhRealTimeSmsParsing == true) {
            _.set(formattedRecord,"smsDateTime", parseInt(_.get(formattedRecord,"smsDateTime")));
        }
            
        utility._sendMetricsToDD(1, [
            'REQUEST_TYPE:ELECTRICITY_INDEX', 
            `SERVICE:ELECTRICITY`, 
            'STATUS:TRAFFIC', 
            'TYPE:OPERATOR',
            'LEVEL_2_CATEGORY:' + formattedRecord.level_2_category, 
            `OPERATOR:${_.get(formattedRecord,'operator',"NO_OPERATOR")}`,
            `ORIGIN:${self.electricityDwhRealTimeSmsParsing == true ? "SMS_PARSING_DWH_REALTIME" : "SMS_PARSING_DWH"}`
        ]);
        return formattedRecord;
    }
}

export default ElectricitySMSParsingBillPayment;
