// this consumer will accept incoming records from DWH and will send directly to CT
import _ from 'lodash'
import OS from 'os';
import VA<PERSON><PERSON><PERSON><PERSON> from 'validator';
import digitalUtility from 'digital-in-util'
import Q from 'q'
import utility from '../../lib';
import KafkaConsumer from '../../lib/KafkaConsumer';
import ASYNC from 'async'
import SmsParsingSyncCCBillLibrary from '../../lib/smsParsingSyncCCBills';
import DigitalCatalog from '../../lib/digitalReminderConfig'
import KafkaConsumerChecks from '../../lib/kafkaConsumerChecks';
import genericSmsParsing from './genericSmsParsing';
import genericSmsParsingBillPaid from './genericSmsParsingBillPaid';


class GeneralSmsParser {
    constructor(options) {

        this.reminderUtils = new digitalUtility.ReminderUtils();
        this.config = options.config;
        this.L = options.L;
        this.infraUtils = options.INFRAUTILS;
        this.config = options.config;
        this.activePidLib = options.activePidLib;
        this.infraUtils = options.INFRAUTILS;
        this.categoryId = options.categoryId;
        this.isEnable = _.get(this.config, ['DYNAMIC_CONFIG', 'GENERIC_SMS_PARSING_CONFIG', 'SAVE_TO_RU', 'IS_ENABLE'], 0);
        this.whitelistCustId = _.get(this.config, ['DYNAMIC_CONFIG', 'GENERIC_SMS_PARSING_CONFIG', 'SAVE_TO_RU', 'WHITELIST_CUST_ID'], []);
        this.whitelistCategory = _.get(this.config, ['DYNAMIC_CONFIG', 'GENERIC_SMS_PARSING_CONFIG', 'SAVE_TO_RU', 'WHITELIST_CATEGORY'], []);
        this.genericDwhRealTimeSmsParsing = _.get(options, 'genericDwhRealTimeSmsParsing', false);
        if (this.genericDwhRealTimeSmsParsing) _.set(options, 'smsParsingBillsDwhRealtime', true);
        this.genericSmsParsing = new genericSmsParsing(options, this);
        this.genericSmsParsingBillPaid = new genericSmsParsingBillPaid(options, this);
        this.smsParsingSyncCCBillLib = new SmsParsingSyncCCBillLibrary(options);
        this.digitalCatalogLib = DigitalCatalog;
        this.refereshIntervalForCategoryData = 15 * 60 * 1000; // DCAT getCategoryProductDetail API refresh interval 
        this.greyScaleEnv = _.get(options, 'greyScaleEnv', false);
        this.kafkaBatchSize = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'SMS_PARSING_CC_BILLS', 'BATCHSIZE'], 2) : 500;
        this.kafkaBatchDelay = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'SMS_PARSING_CC_BILLS', 'DELAY'], 5 * 60 * 1000) : 0;
        this.RUreadsKafkaTime = new Date().getTime();       // Time at which RU reads from the KAFKA
        this.kafkaConsumerChecks = new KafkaConsumerChecks(options);
        setInterval(() => {
            this.initializeVariable();
        }, _.get(this.config, 'COMMON.INTERVAL_FOR_REINITIALIZE', 15) * 60 * 1000);


    }

    initializeVariable() {
        let self = this;
        self.isEnable = _.get(this.config, ['DYNAMIC_CONFIG', 'GENERIC_SMS_PARSING_CONFIG', 'SAVE_TO_RU', 'IS_ENABLE'], 0);
        self.whitelistCustId = _.get(this.config, ['DYNAMIC_CONFIG', 'GENERIC_SMS_PARSING_CONFIG', 'SAVE_TO_RU', 'WHITELIST_CUST_ID'], []);
        self.whitelistCategory = _.get(this.config, ['DYNAMIC_CONFIG', 'GENERIC_SMS_PARSING_CONFIG', 'SAVE_TO_RU', 'WHITELIST_CATEGORY'], []);
    }

    async start() {

        let self = this;
        let
            isEnable = self.isEnable;
        try {
            await self.startCTProducer();

            if (isEnable && isEnable == 1) {
                self.smsParsingSyncCCBillLib.refreshDCATCacheData(self.refereshIntervalForCategoryData);
                self.L.log('start', 'Going to configure Kakfa..');
                self.configureKafka(function (error) {
                    if (error) {
                        self.L.critical('SMS_PARSING_GENERIC_BILL_PAYMENT :: start', 'unable to configure kafka', error);
                        process.exit(0);
                    }
                    else {
                        self.L.log('SMS_PARSING_GENERIC_BILL_PAYMENT :: start', 'Kafka Configured successfully !!');
                    }
                });
                return;
            }
            else {

                await self.initializeDWHConsumer();
                self.L.info("GeneralSmsParser :: start :: ", " service started successfully.");
            }
        } catch (error) {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:GENERAL_DWH_SMS_PARSER", 'STATUS:SERVICE_START_ERROR', 'ERROR:ERROR_WHILE_STARTING_SERVICE']);
            self.L.error("GeneralSmsParser :: start :: ", " error :: ", error);
        }


    }

    configureKafka(done) {
        let self = this;
        let configurations = null;

        if (!self.genericDwhRealTimeSmsParsing) {
            configurations = _.get(self.config, ['DYNAMIC_CONFIG', 'GENERIC_SMS_PARSING_CONFIG'], null);

        }
        else {
            configurations = _.get(self.config, ['DYNAMIC_CONFIG', 'GENERIC_SMS_PARSING_REALTIME_CONFIG'], null)
        }

        if (!configurations) {
            self.L.error('processNotificationsFromKafka: No configurtion found for the kafka topic')
            return done();
        }
        ASYNC.waterfall([
            next => {
                /**
                 * Kafka Publisher to publish bill fetch to Automatic
                 */


                self.kafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": self.config.KAFKA.TOPICS.AUTOMATIC_SYNC.HOSTS
                });
                self.kafkaPublisher.initProducer('high', function (error) {
                    if (error) {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_GENERIC_BILL_PAYMENT", 'STATUS:ERROR', 'TYPE:AUTOMATIC_SYNC_PRODUCER', 'SOURCE:MAIN_FLOW']);
                    }
                    return next(error);
                });



            },
            next => {
                /**
                 * Kafka publisher to publish events to CT publisher pipeline 
                 */

                self.ctKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.CT_EVENTS_PUBLISHER.HOSTS
                });
                this.ctKafkaPublisher.initProducer('high', function (error) {
                    if (error) {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_GENERIC_BILL_PAYMENT", 'STATUS:ERROR', 'TYPE:CT_EVENTS_PUBLISHER', 'SOURCE:MAIN_FLOW']);
                    }
                    return next(error)
                });


            },
            next => {
                /**
                 * Kafka publisher to update events to non paytm bills pipeline 
                 */

                self.nonPaytmKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.NON_PAYTM_RECORDS.HOSTS
                });
                this.nonPaytmKafkaPublisher.initProducer('high', function (error) {
                    if (error) {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_GENERIC_BILL_PAYMENT", 'STATUS:ERROR', 'TYPE:NON_PAYTM_RECORDS_PUBLISHER', 'SOURCE:MAIN_FLOW']);
                    }
                    return next(error)
                });

            },
            next => {
                /**
                 * Kafka publisher to update reminder bill fetch
                 */

                self.billFetchKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.REMINDER_BILLFETCH_PIPELINE.HOSTS
                });
                this.billFetchKafkaPublisher.initProducer('high', function (error) {
                    if (error) {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_GENERIC_BILL_PAYMENT_DWH", 'STATUS:ERROR', 'TYPE:PRODUCER', 'TOPIC:REMINDER_BILL_FETCH', 'SOURCE:MAIN_FLOW']);
                    }
                    return next(error)
                });


            },
            next => {
                /**
                 * Kafka publisher to update reminder bill fetch
                 */

                self.billFetchRealTimeKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.REMINDER_BILLFETCH_PIPELINE_REALTIME.HOSTS
                });
                this.billFetchRealTimeKafkaPublisher.initProducer('high', function (error) {
                    if (error) {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_GENERIC_BILL_PAYMENT_DWH_REALTIME", 'STATUS:ERROR', 'TYPE:PRODUCER', 'TOPIC:REMINDER_BILL_FETCH_REALTIME', 'SOURCE:MAIN_FLOW']);
                    }
                    return next(error)
                });


            },
            next => {
                self.L.log('configureKafka', 'Going to initialize Kakfa Consumer for generic topics');

                let kafkaConsumerObj;

                if (self.genericDwhRealTimeSmsParsing) {
                    kafkaConsumerObj = {
                        "kafkaHost": self.categoryId ? _.get(self.config, ['DYNAMIC_CONFIG', 'GENERIC_SMS_PARSING_REALTIME_CONFIG', self.categoryId, 'KAFKA_HOSTS'], 0) : _.get(self.config.KAFKA, 'TOPICS.SMS_PARSING_BILL_PAYMENT.HOSTS'),
                        "groupId": `generalSmsParser`,
                        "topics": self.categoryId ? _.get(self.config, ['DYNAMIC_CONFIG', 'GENERIC_SMS_PARSING_CONFIG', self.categoryId, 'KAFKA_TOPIC'], 0) : _.get(self.config, ['DYNAMIC_CONFIG', 'GENERAL_SMS_PARSER', 'COMMON', 'kafkaTopics'], ["SMS_PARSER_PIPED_GAS", "SMS_PARSING_INSURANCE"]),
                        "id": "general-sms-parser-consumer-" + OS.hostname(),
                        sessionTimeout: _.get(self.config, 'DYNAMIC_CONFIG.KAFKA_CONFIG.SESSION_TIMEOUT', 2 * 60 * 1000),
                        maxProcessTimeout: _.get(self.config, 'DYNAMIC_CONFIG.KAFKA_CONFIG.MAX_PROCESS_TIMEOUT', 3 * 60 * 1000)
                    };
                } else {
                    kafkaConsumerObj = {
                        "kafkaHost": self.categoryId ? _.get(self.config, ['DYNAMIC_CONFIG', 'GENERIC_SMS_PARSING_REALTIME_CONFIG', self.categoryId, 'KAFKA_HOSTS'], 0) : _.get(self.config.KAFKA, 'TOPICS.SMS_PARSING_BILL_PAYMENT.HOSTS'),
                        "groupId": `generalSmsParser`,
                        "topics": self.categoryId ? _.get(self.config, ['DYNAMIC_CONFIG', 'GENERIC_SMS_PARSING_CONFIG', self.categoryId, 'KAFKA_TOPIC'], 0) : _.get(self.config, ['DYNAMIC_CONFIG', 'GENERAL_SMS_PARSER', 'COMMON', 'kafkaTopics'], ["SMS_PARSER_PIPED_GAS", "SMS_PARSING_INSURANCE"]),
                        "id": "general-sms-parser-consumer-" + OS.hostname(),
                        sessionTimeout: _.get(self.config, 'DYNAMIC_CONFIG.KAFKA_CONFIG.SESSION_TIMEOUT', 2 * 60 * 1000),
                        maxProcessTimeout: _.get(self.config, 'DYNAMIC_CONFIG.KAFKA_CONFIG.MAX_PROCESS_TIMEOUT', 3 * 60 * 1000)
                    };
                }

                self.kafkasmsParsingBillPaymentConsumer = new KafkaConsumer(kafkaConsumerObj);
                self.kafkasmsParsingBillPaymentConsumer.initConsumer(self._processKafkaData.bind(self), (error) => {
                    if (error) {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:GENERIC_SMS_PARSING_BILL_PAYMENT", 'STATUS:ERROR', 'TYPE:GENERIC_SMS_PARSING_BILL_PAYMENT_CONSUMER', 'SOURCE:MAIN_FLOW']);
                    }
                    if (!error) {
                        self.L.log("configureKafka", `consumer of topic : GENERIC_SMS_PARSING_BILL_PAYMENT Configured`);
                    }
                    return next(error);
                });
            },

        ], function (error) {
            if (error) {
                self.L.critical('configureKafka', 'Could not initialize Kafka', error);
                return done(error)
            }
            return done(null);
        });
    }



    startCTProducer() {
        let self = this;

        return new Promise((resolve, reject) => {
            self.ctKafkaPublisher = new self.infraUtils.kafka.producer({
                "kafkaHost": self.config.KAFKA.TOPICS.CT_EVENTS_PUBLISHER.HOSTS
            });
            self.ctKafkaPublisher.initProducer('high', function (error) {
                if (error) {
                    self.L.critical('error in initialising ctKafkaPublisher Producer :: ', error);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:GENERAL_DWH_SMS_PARSER", 'STATUS:SERVICE_START_ERROR', 'ERROR:COULD_START_CT_PRODUCER']);
                    return reject(error);
                }
                self.L.log("GENERAL_DWH_SMS_PARSER :: ctKafkaPublisher KAFKA PRODUCER STARTED....");
                return resolve();
            })
        });
    }

    initializeDWHConsumer() {

        let self = this;
        self.L.log('GeneralSmsParser ::initializeDWHConsumer ');

        return new Promise((resolve, reject) => {
            try {
                self.consumer = new KafkaConsumer({
                    "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.SMS_PARSING_BILL_PAYMENT.HOSTS'),
                    "groupId": 'generalSmsParser',
                    "topics": self.categoryId ? _.get(self.config, ['DYNAMIC_CONFIG', 'GENERIC_SMS_PARSING_CONFIG', self.categoryId, 'KAFKA_TOPIC'], 0) : _.get(self.config, ['DYNAMIC_CONFIG', 'GENERAL_SMS_PARSER', 'COMMON', 'kafkaTopics'], ["SMS_PARSER_PIPED_GAS", "SMS_PARSING_INSURANCE"]),
                    "id": "general-sms-parser-consumer-" + OS.hostname(),
                    "maxBytes": _.get(self.config, 'DYNAMIC_CONFIG.KAFKA_CONFIG.GENERAL_SMS_PARSER.BATCHSIZE', 1000000),
                    sessionTimeout: _.get(self.config, 'DYNAMIC_CONFIG.KAFKA_CONFIG.SESSION_TIMEOUT', 2 * 60 * 1000),
                    maxProcessTimeout: _.get(self.config, 'DYNAMIC_CONFIG.KAFKA_CONFIG.MAX_PROCESS_TIMEOUT.GENERAL_SMS_PARSER_TIMEOUT', 30 * 60 * 1000)
                });
                self.consumer.initConsumer(self._processKafkaData.bind(self), (error) => {
                    if (error) {
                        self.L.critical("GeneralSmsParser : consumer Configured cannot start.", error);
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:GENERAL_DWH_SMS_PARSER", 'STATUS:SERVICE_START_ERROR', 'ERROR:COULD_START_KAFKA_CONSUMER']);
                        reject(error);
                    }
                    else if (!error) {
                        self.L.log("GeneralSmsParser : consumer Configured");
                        resolve();
                    }
                });
            } catch (error) {
                return reject(error);
            }
        })
    }

    async _processKafkaData(records, resolveOffset, topic, partition, cb) {

        let self = this;
        let lastMessage;
        if (records && _.isArray(records)) {
            lastMessage = records[records.length - 1];
            self.L.log('GeneralSmsParser ::_processKafkaData received data from kafka ', records.length);
        } else {
            self.L.critical('GeneralSmsParser ::_processKafkaData error while reading kafka');
            return cb();
        }
        let chunk = _.get(self.config, ['DYNAMIC_CONFIG', 'GENERAL_SMS_PARSER', 'COMMON', 'CHUNKSIZE'], 1000),
            timeDelayAfterEachBatch = _.get(self.config, ['DYNAMIC_CONFIG', 'GENERAL_SMS_PARSER', 'COMMON', 'TIME_DELAY_AFTER_EACH_BATCH_PROCESSING'], 1000),
            recordsToProcess = [];
        records.forEach(row => {
            if (row && row.value) {
                try {
                    let rechargeData = JSON.parse(row.value);
                    recordsToProcess.push(rechargeData);
                } catch (error) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:GENERAL_DWH_SMS_PARSER", 'STATUS:INVALID_JSON_PAYLOAD']);
                    self.L.error("GeneralSmsParser ::_processKafkaData", "Failed to parse recents data topic,partition,offset,timestamp ::", row.topic, row.partition, row.offset, row.timestamp + ", " + error);
                }
            } else {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:GENERAL_DWH_SMS_PARSER", 'STATUS:INVALID_PAYLOAD']);
                self.L.error("GeneralSmsParser ::_processKafkaData", "Unable to get valid data from kafka topic,partition,offset,timestamp ::", row.topic, row.partition, row.offset, row.timestamp);
            }
        });
        self.L.log('GeneralSmsParser ::_processKafkaData', `Processing ${recordsToProcess.length} out of ${records.length} !!`);
        let data = _.chunk(recordsToProcess, chunk);
        for (let dataIndex = 0; dataIndex < data.length; dataIndex++) {
            let startTime = new Date().getTime();
            let batch = data[dataIndex];
            let promises = [];
            for (let batchIndex in batch) {
                promises.push(self.processRecord(batch[batchIndex], topic));
            }
            let promiseResults = await Promise.all(promises);
            let endTime = new Date().getTime();
            let batchLength = batch.length > 0 ? batch.length : 1;
            let executionTime = (endTime - startTime) / batchLength;      //in seconds
            executionTime = Math.round(executionTime);
            self.L.log('execSteps::', 'per chunkSize record Execution time :', executionTime, 'seconds, batchLength: ', batch.length);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_STATUS", 'STATUS:SUCCESS', "SOURCE:GENERAL_SMS_PARSER", "TIME_TAKEN:" + executionTime, `RECORDS_PROCESSED:${batch.length}`, `PARTITION:${partition}`, `TOPIC:${topic}`]);
            await self.delay(timeDelayAfterEachBatch);
        }
        await resolveOffset(lastMessage.offset);
        self.L.log('_processKafkaData::', 'Commit success for offset :', _.get(lastMessage, 'offset') + ', topic :' + topic + ', partition :' + partition + ', timestamp :' + _.get(lastMessage, 'timestamp'));
        cb();
        /*setTimeout(() => {
            // Resume consumer now
            return cb();
        }, 1000);*/


    }

    processData(record, kafkaTopic, done) {
        let self = this;

        //self.L.info("Record received: ", record);

        let record_data = record
        if (!record) {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:GENERIC_SMS_PARSING_BILL_PAYMENT", 'STATUS:EMPTY_DATA', 'SOURCE:MAIN_FLOW']);
            self.L.log(`GeneralSMSParsingBillPayment :: Empty sms Data found`);
            return done();
        }

        let level_2_category = _.get(record_data, 'level_2_category', null);


        if (level_2_category != 1 && level_2_category != 2 && level_2_category != 3) {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:GENERIC_SMS_PARSING_BILL_PAYMENT", 'STATUS:LEVEL_2_CATEGORY_NOT_1', 'SOURCE:MAIN_FLOW']);
            self.L.log(`ElectricitySMSParsingBillPayment :: level_2_category is not 1, 2 and 3`);
            return done();
        }

        self.L.log(`GenericSmsParsingBillPayment :: executing postpaidSmsParsing flow as per data level_2_category : ${level_2_category}`);
        if (level_2_category == 1) {
            try {
                let smsData = self.formatData(record_data, kafkaTopic);
                self.genericSmsParsing.executeStrategy(
                    () => {
                        self.L.log('GENERIC_SMS_PARSING_BILL_PAYMENT:processData', `Strategy execution completed successfully.`);
                        return done();
                    },
                    smsData,
                    kafkaTopic,
                    self
                );
            } catch (err) {
                self.L.critical('GENERIC_SMS_PARSING_BILL_PAYMENT:processData', `Error processing Kafka record`, err);
                utility._sendMetricsToDD(1, [
                    "REQUEST_TYPE:GENERIC_SMS_PARSING_BILL_PAID",
                    "STATUS:INVALID_JSON_PAYLOAD",
                    "SOURCE:MAIN_FLOW"
                ]);
                return done();
            }

        }
        else if (level_2_category == 2) {
            try {
                let smsData = self.formatData(record_data, kafkaTopic);
                self.genericSmsParsingBillPaid.executeStrategy(
                    () => {
                        self.L.log('SMS_PARSING_POSTPAID_BILL_PAID:processData', `Strategy execution completed successfully.`);
                        return done();
                    },
                    smsData,
                    kafkaTopic,
                    self
                );
            } catch (err) {
                self.L.critical('SMS_PARSING_POSTPAID_BILL_PAID:processData', `Error processing Kafka record`, err);
                utility._sendMetricsToDD(1, [
                    "REQUEST_TYPE:GENERIC_SMS_PARSING_BILL_PAID",
                    "STATUS:INVALID_JSON_PAYLOAD",
                    "SOURCE:MAIN_FLOW"
                ]);
                return done();
            }


        }
        else {
            return done();
        }

    }
    async processRecord(record, kafkaTopic) {
        record = record['data'][0];
        let self = this;
        let smsPayload = _.get(record, _.get(self.config, ['DYNAMIC_CONFIG', 'GENERAL_SMS_PARSER', 'COMMON', 'KAFKA_TOPIC-PAYLOAD_KEY-MAPPING', kafkaTopic]), {});
        self.L.log('processRecord :: processing', JSON.stringify(kafkaTopic));
        self.L.log('processRecord :: processing', JSON.stringify(record));
        self.L.log('processRecord :: smsPayload', JSON.stringify(smsPayload));
        let tempRecord = {};
        let eventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENTS', 'generalSmsParser'], 'generalSmsParser');

        tempRecord.rechargeNumber = _.get(smsPayload, 'recharge_number', null);
        tempRecord.customer_id = (typeof record.cId === 'number') ? record.cId : (typeof record.cId === 'string' && VALIDATOR.isNumeric(record.cId)) ? VALIDATOR.toInt(record.cId) : null;
        tempRecord.dueDate = utility.getFilteredDate(_.get(smsPayload, 'due_date')).value;
        tempRecord.amount = _.get(smsPayload, 'due_amount', null) ? utility.getFilteredAmount(_.get(smsPayload, 'due_amount', null)) : null;
        tempRecord.bill_date = utility.getFilteredDate(_.get(smsPayload, 'bill_generation_date')).value;
        tempRecord.paymentDate = utility.getFilteredDate(_.get(smsPayload, 'payment_date')).value;
        tempRecord.operator = _.get(smsPayload, 'operator', null);
        tempRecord.service = _.get(record, 'predicted_category', null);
        let dbDebugKey = `rech:${tempRecord.rechargeNumber}::cust:${tempRecord.customer_id}::op:${tempRecord.operator}`;
        let finalRecord = self.reminderUtils.createCTPipelinePayload(tempRecord, eventName, dbDebugKey);
        utility._sendMetricsToDD(1, ["REQUEST_TYPE:GENERAL_DWH_SMS_PARSER", 'STATUS:PROCESSING_PAYLOAD', `KAFKA_TOPIC:${kafkaTopic}`]);
        await self.publishToCT(finalRecord, _.get(record, 'appVersion', null), eventName);

        if ((self.isEnable && self.isEnable === 1) && (self.whitelistCustId.includes(tempRecord.customer_id) || self.whitelistCustId.includes('all')) && (self.whitelistCategory.includes(tempRecord.service) || self.whitelistCategory.includes('all'))) {
            try {

                let result = await new Promise((resolve, reject) => {
                    self.processData(record, kafkaTopic, (error, result) => {
                        if (error) {
                            self.L.error('processRecord :: Error processing data', error);
                            reject(error);
                        } else {
                            self.L.log('processRecord :: Data processed successfully', JSON.stringify(record));
                            resolve(record);
                        }
                    });
                });


            } catch (error) {
                self.L.error('processRecord :: processData encountered an error', error);

            }
        }




    }
    publishToCT(record, appVersion, eventName) {
        let self = this;
        return new Promise((resolve, reject) => {
            self.ctKafkaPublisher.publishData([{
                topic: _.get(self.config.KAFKA, 'SERVICES.CT_EVENTS_PUBLISHER.TOPIC', ''),
                messages: JSON.stringify(record)
            }], (error) => {
                if (error) {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:GENERAL_SMS_PARSING",
                        `SERVICE:${_.get(record, 'category', null)}`,
                        'STATUS:ERROR',
                        "TYPE:KAFKA_PUBLISH",
                        "TOPIC:CT_EVENTS",
                        "OPERATOR:" + `${_.get(record, 'operator')}`,
                        `ORIGIN:GENERAL_SMS_PARSING`,
                        `APP_VERSION:${appVersion}`,
                        `EVENT_NAME:${eventName}`
                    ]);
                    self.L.critical('generalSmsParsing :: publishCtEvents', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(record), error);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:GENERAL_DWH_SMS_PARSER", `ERROR:ERROR_WHILE_PUBLISHING_TO_CT`]);
                } else {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:GENERAL_SMS_PARSING",
                        `SERVICE:${_.get(record, 'category', null)}`,
                        'STATUS:PUBLISHED',
                        "TYPE:KAFKA_PUBLISH",
                        "TOPIC:CT_EVENTS",
                        "OPERATOR:" + `${_.get(record, 'operator')}`,
                        `ORIGIN:GENERAL_SMS_PARSING`,
                        `APP_VERSION:${appVersion}`,
                        `EVENT_NAME:${eventName}`
                    ]);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:GENERAL_DWH_SMS_PARSER", `STATUS:PUBLISHED_TO_CT`]);
                    self.L.log('generalSmsParsing :: publishCtEvents', 'Message published successfully in Kafka', ' on topic REMINDER_CT_EVENTS', JSON.stringify(record));
                }
                resolve();
            }, [200, 800]);
        });
    }

    defaultStrategy(done, record) {
        this.L.log('SMS_PARSING_BILL_PAYMENT:defaultStrategy:: ', "Not applying any logic for record ", JSON.stringify(record));
        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:NO_STRATEGY', 'SOURCE:MAIN_FLOW']);
        return done();
    }



    formatData(smsData, kafkaTopic) {
        let self = this;
        let config = null;
        if (!self.genericDwhRealTimeSmsParsing) {
            config = _.get(self.config, ['DYNAMIC_CONFIG', 'GENERIC_SMS_PARSING_CONFIG', kafkaTopic], null);
            if (!config) {
                config = _.get(self.config, ['DYNAMIC_CONFIG', 'GENERIC_SMS_PARSING_CONFIG', _.get(smsData, 'predicted_category', null)], null);
            }
        }
        else {
            config = _.get(self.config, ['DYNAMIC_CONFIG', 'GENERIC_SMS_PARSING_REALTIME_CONFIG', kafkaTopic], null)
            if (!config) {
                config = _.get(self.config, ['DYNAMIC_CONFIG', 'GENERIC_SMS_PARSING_REALTIME_CONFIG', _.get(smsData, 'predicted_category', null)], null);
            }
        }


        if (!config) {
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:SMS_PARSING',
                `SERVICE:UNKNOWN`,
                'STATUS:ERROR',
                'TYPE:UNKNOWN_CATEGORY'
            ]);
            return null;
        }


        const categoryDetails = _.get(smsData, config.detailsPath, {});

        let formattedRecord = {
            "cId": _.get(smsData, 'cId', null),
            "smsDateTime": _.get(smsData, 'smsDateTime', null),
            "timestamp": _.get(smsData, 'timestamp', null),
            "deviceDateTime": _.get(smsData, 'deviceDateTime', null),
            "collector_timestamp": _.get(smsData, 'collector_timestamp', null),
            "uploadTime": _.get(smsData, 'uploadTime', null),
            "smsOperator": _.get(smsData, 'smsOperator', null),
            "smsReceiver": _.get(smsData, 'smsReceiver', null),
            "smsSenderID": _.get(smsData, 'smsSenderID', null),
            "smsUUID": _.get(smsData, 'smsUUID', null),
            "category": _.get(smsData, 'predicted_category', null),
            "rechargeNumber": _.get(categoryDetails, config.fieldMappings.rechargeNumber, null),//make it configurable
            "rechargeNumber2": _.get(categoryDetails, config.fieldMappings.rechargeNumber2, null),
            "operator": _.get(categoryDetails, config.fieldMappings.operator, null),
            "amount": _.get(categoryDetails, config.fieldMappings.amount, null),
            "dueDate": _.get(categoryDetails, config.fieldMappings.dueDate, null),
            "level_2_category": _.get(smsData, 'level_2_category', null),
            "circle": _.get(smsData, 'circle', null)
        };
        self.L.log('generalSmsParsing :: formatData', 'formatted data', JSON.stringify(formattedRecord));

        if (self.genericDwhRealTimeSmsParsing) {
            formattedRecord.isDwhSmsParsingRealtime = true;
        } else {
            formattedRecord.isDwhSmsParsing = true;
        }
        if (typeof _.get(formattedRecord, "smsDateTime") === "string" && _.get(formattedRecord, "smsDateTime") != null && self[`${category.toLowerCase()}DwhRealTimeSmsParsing`]) {
            _.set(formattedRecord, "smsDateTime", parseInt(_.get(formattedRecord, "smsDateTime")));
        }
        let smsReceiver = _.get(smsData, 'smsReceiver', null);
        if (smsReceiver && smsReceiver.startsWith('+91')) {
            smsReceiver = smsReceiver.slice(3);
            _.set(formattedRecord, "smsReceiver", smsReceiver);
        }


        utility._sendMetricsToDD(1, [
            `REQUEST_TYPE:GENERIC_SMS_PARING`,
            `SERVICE:${formattedRecord.category}`,
            'STATUS:TRAFFIC',
            'TYPE:OPERATOR',
            `LEVEL_2_CATEGORY:${formattedRecord.level_2_category}`,
            `OPERATOR:${_.get(formattedRecord, 'operator', "NO_OPERATOR")}`,
            `ORIGIN:${self.genericDwhRealTimeSmsParsing ? "SMS_PARSING_DWH_REALTIME" : "SMS_PARSING_DWH"}`
        ]);

        return formattedRecord;
    }


    suspendOperations() {
        let self = this,
            deferred = Q.defer();
        self.L.log(`generalSmsParsingConsumer::suspendOperations kafka consumer shutdown initiated`);

        Q()
            .then(function () {
                self.consumer.close(function (error, res) {
                    if (error) {
                        self.L.error(`generalSmsParsingConsumer::stopConsumer :: Error Stopping Consumer Err : ${error}`);
                        return error;
                    }
                    self.L.info(`generalSmsParsingConsumer::stopConsumer :: Consumer Stopped!  Response : ${res}`);
                })
            })
            .then(function () {
                self.L.log(`generalSmsParsingConsumer::suspendOperations kafka consumer shutdown successful`);
                deferred.resolve();
            })
            .catch(function (err) {
                self.L.error(`generalSmsParsingConsumer::suspendOperations error in shutting kafka consumer`, err);
                deferred.reject(err);
            });
        return deferred.promise;
    }
    delay(time) {
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                resolve();
            }, time);
        })
    }
}
export default GeneralSmsParser;
