import _ from 'lodash';
import utility from '../../lib';

class DynamicSmsParsingRegexExecutor {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.context = {};
        this.context["removeFromFront"] = this.removeFromFront;
        this.context["appendAtFront"] = this.appendAtFront;
    }

    checkValidityOfRechargeNumberByRegex(record){
        let self = this,
            productAttributes = JSON.parse(_.get(this.config, ['CVR_DATA', record.productId, 'attributes'] , '{}')),
            productRxStr = _.get(productAttributes, ['input_field-regex-1'], null),
            rechargeNum = _.get(record, 'rechargeNumber', ''),
            operator = _.get(record, 'operator', '').toUpperCase().replace(/ /g, "_"),
            category = _.get(record, 'category', '').toUpperCase(),
            paytype = _.get(record, 'paytype', '').toUpperCase(),
            isRegexValidationEnabled = _.get(self.config, ['DYNAMIC_CONFIG', 'SMS_PARSING_RN_REGEX_VALIDATION', `${category}_${paytype}`, 'ENABLED'], 0);       
        
        self.L.log("DynamicSmsParsingRegexExecutor record and product attributes: ", JSON.stringify(record), JSON.stringify(productAttributes));
        self.L.log("DynamicSmsParsingRegexExecutor dynamic config: ", JSON.stringify(_.get(this.config, ['DYNAMIC_CONFIG', 'SMS_PARSING_RNO_CORRECTION_CONFIG', `${operator}_${category}`, 'REGEX_ACTIONS'], [])));

        category = _.isEmpty(category) ? _.get(record, 'service', '').toUpperCase() : category;
        if (category == 'MOBILE') {
            productRxStr = _.get(self.config, ['DYNAMIC_CONFIG', 'SMS_PARSING_RN_REGEX_VALIDATION', `${category}`, 'CATEGORY_REGEX'], null);
        }

        if (productRxStr && isRegexValidationEnabled) {
            let pRx = new RegExp(productRxStr);
            if(pRx.test(rechargeNum)){
                return true;
            } else if (_.get(this.config, ['DYNAMIC_CONFIG', 'SMS_PARSING_RNO_CORRECTION_CONFIG', `${operator}_${category}`, 'REGEX_ACTIONS'], [])) {
                let pRx = new RegExp(productRxStr);
                if(!pRx.test(rechargeNum)){
                    let regexActions = _.get(this.config, ['DYNAMIC_CONFIG', 'SMS_PARSING_RNO_CORRECTION_CONFIG', `${operator}_${category}`, 'REGEX_ACTIONS'], []);
                    for(let regexAction of regexActions){
                        let coorectionRx = new RegExp(_.get(regexAction, 'regEx', ''));
                        if(coorectionRx.test(rechargeNum)){
                            rechargeNum = self.makeCorrectionOnString(_.get(regexAction, 'actions', []), rechargeNum);
                            _.set(record, 'rechargeNumber', rechargeNum);
                            if(pRx.test(rechargeNum)){
                                self.L.log(`DynamicSmsParsingRegexExecutor :: Recharge number of operator ${_.get(record, 'operator', '').toUpperCase()} corrected to ${_.get(record, 'rechargeNumber', '')}`);
                                utility._sendMetricsToDD(1, [
                                    'REQUEST_TYPE:SMS_PARSING_POSTPAID', 
                                    `SERVICE:${_.get(record, 'category', '')}`, 
                                    'STATUS:SUCCESS',
                                    'TYPE:RECHARGE_NUMBER_CORRECTION_SUCCESS', 
                                    'OPERATOR:' + _.get(record, 'operator', ''),
                                    `APP_VERSION:${_.get(record,'appVersion', null)}`
                                ]);
                                return true;
                            } else {
                                return false;
                            }
                        }
                    };
                    return false;
                }
            } else {
                return false;
            }
        } else {
            return true;
        }
    }

    makeCorrectionOnString(actions, inputStr){
        let result = inputStr;
        actions.forEach((actionObj) => {
            result = this.context[actionObj["action"]].apply(this.context, [actionObj, result]);
        });
        return result;
    }

    removeFromFront(actionObj, inputStr) {
        return inputStr.slice(actionObj["count"]);
    }

    appendAtFront(actionObj, inputStr) {
        return actionObj["value"].concat(inputStr);
    }
}

export default DynamicSmsParsingRegexExecutor;