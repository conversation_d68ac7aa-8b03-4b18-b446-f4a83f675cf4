"use strict";

import OS from 'os'
import _ from 'lodash'
import CATALOGVERTICALRECHARGE from '../models/catalogVerticalRecharge'
import MODELS from '../models'
import VALIDATOR from 'validator'
import utility from '../lib'
import ASYNC from 'async'
import MOMENT from 'moment'
import digitalUtility from 'digital-in-util'
import Q from 'q'
import BILLS from '../models/bills' 
import BillFetchAnalytics from '../lib/billFetchAnalytics.js'
import KafkaConsumerChecks from '../lib/kafkaConsumerChecks';

/**
 * Recent Bills service is used to insert recent bill data received from kafka
 * recharge topic and insert in mysql, mongo DB
 */
class PaytmPostpaid {
    /**
     * @param { object } options Contains configuration and dependencies
     * @param { object } options.L Paytm Logger (lgr) object
     * @param { object } options.config Local config object
     * @param { object } options.INFRAUTILS Contains util libraries like kafka
     */

    constructor(options) {
        let self = this;
        self.L = options.L;
        self.config = options.config;
        self.infraUtils = options.INFRAUTILS;
        self.dbInstance = options.dbInstance;
        self.billsModel = new MODELS.Bills(options)
        this.bills = new BILLS(options);
        self.consentData = {};
        self.catalogVerticalRecharge = new CATALOGVERTICALRECHARGE(options);
        self.activePidLib = options.activePidLib;
        self.bills_operator_table = _.get(self.config, 'OPERATOR_TABLE_REGISTRY', {});
        self.recordCount = true;
        self.cvrReloadInterval = _.get(self.config, 'COMMON.CVR_RELOAD_INTERVAL', 86400000);
        self.commonLib = new utility.commonLib(options);
        self.reminderUtils = new digitalUtility.ReminderUtils();
        self.catalogVerticalRecharge = new CATALOGVERTICALRECHARGE(options);
        self.greyScaleEnv = options.greyScaleEnv;
        self.kafkaBatchSize = self.greyScaleEnv ? _.get(self.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'RECENTS', 'BATCHSIZE'], 2) : 500;
        self.kafkaResumeTimeout = self.greyScaleEnv ? _.get(self.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'RECENTS', 'BATCH_DELAY'], 5*60*1000) : 500;    
        this.billFetchAnalytics = new BillFetchAnalytics(options);
        self.kafkaConsumerChecks = new KafkaConsumerChecks(options);

        setInterval(() => {
            this.initializeVariable();
        }, _.get(this.config, 'COMMON.INTERVAL_FOR_REINITIALIZE', 15) * 60 * 1000);
    }

    /**
     * This method will intialize the dynamic operator config specific variables after certain interval
     */
    initializeVariable() {
        let self = this;
        self.L.log("initializeVariable :: recentBills", "Re-initializing variable after interval");
        self.bills_operator_table = _.get(this.config, 'OPERATOR_TABLE_REGISTRY', {});
    }

    /*
     * Starting point for service, intializing consumer for paytm postpaid service
     */
    start() {
        let self = this;
        self.automatic_sync_publisher = new self.infraUtils.kafka.producer({
            "kafkaHost": self.config.KAFKA.TOPICS.AUTOMATIC_SYNC.HOSTS
        });
        self.automatic_sync_publisher.initProducer('high', function (error) {
            if (error)
                self.L.critical('error in initialising automatic sync Producer :: ', error);
            self.L.log("PAYTM_POSTPAID :: AUTOMATIC_SYNC KAFKA PRODUCER STARTED....");
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:PAYTM_POSTPAID", 'STATUS:ERROR', 'TYPE:KAFKA_PRODUCER', 'TOPIC:AUTOMATIC_SYNC']);
        });
        self.ctKafkaPublisher = new self.infraUtils.kafka.producer({
            "kafkaHost": self.config.KAFKA.TOPICS.CT_EVENTS_PUBLISHER.HOSTS
        });
        self.ctKafkaPublisher.initProducer('high', function (error) {
            if (error)
                self.L.critical('error in initialising ctKafkaPublisher Producer :: ', error);
            self.L.log("PAYTM_POSTPAID :: ctKafkaPublisher KAFKA PRODUCER STARTED....");
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:PAYTM_POSTPAID", 'STATUS:ERROR', 'TYPE:KAFKA_PRODUCER', 'TOPIC:CT_KAFKA']);
        });
        self.billFetchKafka = new self.infraUtils.kafka.producer({
            "kafkaHost": self.config.KAFKA.TOPICS.REMINDER_BILLFETCH_PIPELINE.HOSTS
        });
        self.billFetchKafka.initProducer('high', function (error) {
            if (error)
                self.L.critical('error in initialising billFetchKafka Producer :: ', error);
            self.L.log("PAYTM_POSTPAID :: billFetchKafka KAFKA PRODUCER STARTED....");
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:PAYTM_POSTPAID", 'STATUS:ERROR', 'TYPE:KAFKA_PRODUCER', 'TOPIC:REMINDER_BILLFETCH']);
        });
        self.billFetchKafkaRealtime = new self.infraUtils.kafka.producer({
            "kafkaHost": self.config.KAFKA.TOPICS.REMINDER_BILLFETCH_PIPELINE_REALTIME.HOSTS
        });
        self.billFetchKafkaRealtime.initProducer('high', function (error) {
            if (error)
                self.L.critical('error in initialising billFetchKafka Producer :: ', error);
            self.L.log("PAYTM_POSTPAID :: billFetchKafka realtime KAFKA PRODUCER STARTED....");
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:PAYTM_POSTPAID", 'STATUS:ERROR', 'TYPE:KAFKA_PRODUCER', 'TOPIC:REMINDER_BILLFETCH_REALTIME']);
        });
        self._initializePaytmPostpaidConsumer((error) => {
            if (error) {
                self.L.error("RecentBills", "Failed to initialize recent Bills service");
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:PAYTM_POSTPAID", 'STATUS:ERROR', 'TYPE:KAFKA_CONSUMER', 'TOPIC:PAYTM_POSTPAIDS']);
            }
        });        
    }

    setConsentData(callback) {
        let self = this;

        let whereQuery = null;
        if (Object.keys(self.consentData).length) {
            whereQuery = `updated_at > "${MOMENT().subtract(2, 'day').subtract(1, 'hour').format("YYYY-MM-DD HH:mm:ss")}"`;
        }

        self.catalogVerticalRecharge.getCvrData(function (error, data) {
            try {
                if (error || !data) callback(error);
                else {
                    data.forEach(function (row) {
                        if (row && row.attributes) {
                            try {
                                let remindable = JSON.parse(row.attributes).remindable;
                                self.consentData[row.product_id] = remindable == _.get(self.config, 'COMMON.USER_CONSENT_REQUIRED', 2) ? true : false;
                            } catch(error){
                                self.L.critical('setConsentData',`Error while parsing attributes for product_id:${row && row.product_id},row: ${JSON.stringify(row)}`);
                            }
                        }
                    })
                    callback();
                }
            } catch (Exception) {
                callback(Exception);
            }
        }, whereQuery);
    }

    /**
     * @param {function} cb callback function
     * initializing bill consumer service
     */
    _initializePaytmPostpaidConsumer(cb) {
        let self = this;
        self.L.log("Service started on topics::" + _.get(self.config.KAFKA, 'SERVICES.PAYTM_POSTPAID.TOPIC'));
        try {
            self.consumer = new self.infraUtils.kafka.consumer({
                "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.PAYTM_POSTPAID.HOSTS'),
                "groupId": "reminder-paytmpostpaid-consumer",
                "topics": _.get(self.config.KAFKA, 'SERVICES.PAYTM_POSTPAID.TOPIC'),
                "id": 'reminder_paytmpostpaid' + OS.hostname(),
                "fromOffset": "earliest",
                "autoCommit": false,
                "batchSize": self.kafkaBatchSize
            });

            self.consumer.initConsumer(self._processBillsData.bind(self), (error) => {
                if (error){
                    self.L.critical("_initializePaytmPostpaidConsumer : PaytmPostpaid consumer Configured cannot start.", error);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:PAYTM_POSTPAID", 'STATUS:ERROR', 'TYPE:KAFKA_CONSUMER', 'TOPIC:PAYTM_POSTPAID_BILLS']);
                }
                else if (!error)
                    self.L.log("_initializePaytmPostpaidConsumer : PaytmPostpaid consumer Configured");
                return cb(error);
            });
        } catch (error) {
            return cb(error);
        }
    }

    /**
     * @param {object} data contains recharge data value
     */

    _processBillsData(records) {
        let self = this,
            billsData = null,
            chunkSize = 30,
            lastMessage,
            recordsToProcess = [];

        let startTime = new Date().getTime();

        if (records && _.isArray(records)) {
            lastMessage = records[records.length - 1];
            self.consumer._pauseConsumer();
        } else {
            self.L.critical('_processBillsData', `No valid kafka records found. Received records-`, records);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:PAYTM_POSTPAID", 'STATUS:ERROR', 'TYPE:INVALID_RECORDS']);
            return;
        }

        self.kafkaConsumerChecks.findOffsetDuplicates("PaytmPostpaid", records);

        if(_.get(self.config, ['DYNAMIC_CONFIG', 'PAYTM_POSTPAID_CONFIG', 'CONSUME_MESSAGE_FLAG', 'FLAG'], 1)==0){
            self.L.log('_processBillsData', `Directly committing records without processing record count: `, records.length);
            self.consumer.commitOffset(lastMessage, (error) => {
                if (error) {
                    self.L.error('_processBillsData::', 'Error while commit for-', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:PAYTM_POSTPAID", 'STATUS:ERROR', 'TYPE:OFFSET_COMMIT',`PARTITION:${lastMessage.partition}`, `TOPIC:${lastMessage.topic}`]);
                }
                else {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:PAYTM_POSTPAID", 'STATUS:COMMIT_OFFSET', `PARTITION:${lastMessage.partition}`, `TOPIC:${lastMessage.topic}`]);
                    self.L.log('_processBillsData::', 'Commit success for offset :', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                }
                recordsToProcess = [];

                let endTime = new Date().getTime();
                let executionTime = (endTime - startTime) / 1000;      //in seconds
                executionTime = Math.round(executionTime);
                self.L.log('execSteps::', 'per batchSize record Execution time :', executionTime, 'seconds', 'recordLength :',records.length);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_STATUS", 'STATUS:SUCCESS', "SOURCE:PAYTM_POSTPAIDS", "TIME_TAKEN:" + executionTime]);
              
                setTimeout(() => {
                    // Resume consumer now
                    self.consumer._resumeConsumer();
                }, self.kafkaResumeTimeout);
            });
        }else{
            records.forEach(function (data) {
                if (data && data.value) {
                    try {
                        //Adding temp check to print first record
                        if (self.recordCount) {
                            self.L.log("_processBillsData : First Record:: " + JSON.stringify(data));
                            self.recordCount = false;
                        }
                        billsData = JSON.parse(data.value);
                        recordsToProcess.push(billsData);
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:PAYTM_POSTPAID", 'STATUS:TRAFFIC', `PARTITION:${data.partition}`, `TOPIC:${data.topic}`]);
                    } catch (error) {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:PAYTM_POSTPAID", 'STATUS:ERROR', 'TYPE:INVALID_PAYLOAD']);
                        self.L.error("_processBillsData", "Failed to parse recharges data topic,partition,offset,timestamp ::", data.topic, data.partition, data.offset, data.timestamp + ", " + error);
                    }
                } else {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:PAYTM_POSTPAID", 'STATUS:INVALID_PAYLOAD']);
                    self.L.error("_processBillsData", "Unable to get valid data from kafka topic,partition,offset,timestamp ::", data.topic, data.partition, data.offset, data.timestamp);
                }
            });
    
            self.L.log('_processBillsData:: ', `Processing ${recordsToProcess.length} out of ${records.length} Recents data !!`);
            ASYNC.eachLimit(recordsToProcess, chunkSize, self._prepareDataToInsert.bind(self), function () {
                self.consumer.commitOffset(lastMessage, (error) => {
                    if (error) {
                        self.L.error('_processBillsData::', 'Error while commit for-', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:PAYTM_POSTPAID", 'STATUS:ERROR', 'TYPE:OFFSET_COMMIT',`PARTITION:${lastMessage.partition}`, `TOPIC:${lastMessage.topic}`]);
                    }
                    else {
                        self.L.log('_processBillsData::', 'Commit success for offset :', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                    }
                    recordsToProcess = [];
    
                    let endTime = new Date().getTime();
                    let executionTime = (endTime - startTime) / 1000;      //in seconds
                    executionTime = Math.round(executionTime);
                    self.L.log('execSteps::', 'per batchSize record Execution time :', executionTime, 'seconds', 'recordLength :',records.length);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_STATUS", 'STATUS:SUCCESS', "SOURCE:PAYTM_POSTPAIDS", "TIME_TAKEN:" + executionTime]);
                  
                    setTimeout(() => {
                        // Resume consumer now
                        self.consumer._resumeConsumer();
                    }, self.kafkaResumeTimeout);
    
                });
            });
        }
    }

    /**
     *
     * @param {object} billsData contains recharge data value
     */
    pushToKafkaForAutomaticSync(done, row, tableName) {
        let self = this;
        let dbData = _.get(row, 'updatedDBRecord[0]', {})
        if (_.get(dbData, 'is_automatic',0) == 0 || _.get(dbData, 'is_automatic',0) == 5 || _.get(dbData, 'is_automatic',0) == 8) // push data having only is_automatic == 1,2,3,4
            return done();
        let updatedData = self.commonLib.mapBillsTableColumns(dbData);
        if(_.get(row, ['processedRecord','billGen'], false)){
            _.set(updatedData, 'billGen',true);
        }
        let payLoad = [{
            topic: _.get(self.config.KAFKA, 'SERVICES.AUTOMATIC_SYNC.AUTOMATIC_SYNC_TOPIC', ''),
            messages: JSON.stringify(updatedData),
            key: _.get(updatedData, 'rechargeNumber', '')
        }]
        self.automatic_sync_publisher.publishData(payLoad, function (error) {
            if (error) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:PAYTM_POSTPAID", 'STATUS:ERROR', `TYPE:AUTOMATIC_SYNC`]);
                self.L.critical('Error while publishing message in Kafka - MSG:- ' + JSON.stringify(payLoad), error);
            } else {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:PAYTM_POSTPAID", 'STATUS:PUBLISHED', `TYPE:AUTOMATIC_SYNC`]);
                self.L.log('Message published successfully in Kafka', ' on topic AUTOMATIC_SYNC', JSON.stringify(payLoad));
            }
            return done(null);
        }, [200, 800]);
    }

    decideTopicToPublishBillGen(){

        let
            self = this,
            slotFrom = _.get(self.config, ['DYNAMIC_CONFIG', 'PAYTM_POSTPAID_CONFIG', 'REALTIME_NOTIFY_PERIOD', `START_TIME`], null),
            slotTo = _.get(self.config, ['DYNAMIC_CONFIG', 'PAYTM_POSTPAID_CONFIG', 'REALTIME_NOTIFY_PERIOD', `END_TIME`], null);

        if (!(slotFrom && slotTo)) return true;

        let
            slotFromArr = slotFrom.split(':'),
            slotToArr = slotTo.split(':');

        if (!(slotFromArr.length > 0 && slotToArr.length > 0)) return true;

        slotFrom = MOMENT().set({ hour: _.get(slotFromArr, '0', '00'), minute: _.get(slotFromArr, '1', '00'), second: _.get(slotFromArr, '2', '00') });
        slotTo = MOMENT().set({ hour: _.get(slotToArr, '0', '00'), minute: _.get(slotToArr, '1', '00'), second: _.get(slotToArr, '2', '00') });

        if (!(MOMENT().isBetween(slotFrom, slotTo))) {
            return false;
        } else {
            return true;
        }
    }

    publishInBillFetchKafka(done,processedRecord){
        let self = this;
        let dbData = _.get(processedRecord, 'updatedDBRecord[0]', {});
        let currentRecord = _.get(processedRecord, 'billsData', {});
        let dbDebugKey = `rech:${dbData.recharge_number}::cust:${dbData.customer_id}::op:${dbData.operator}`;
        if(dbData.notification_status == 0){
            self.L.error(`stop publishing data on REMINDER_BILL_FETCH_REALTIME via the Kafka pipeline for notification status : ${dbData.notification_status} debugKey::`, dbDebugKey);
            return done();                         
        }

        if(dbData.is_automatic != 0 && dbData.is_automatic != 5){
            self.L.error(`stop publishing data on REMINDER_BILL_FETCH_REALTIME via the Kafka pipeline for automatic status : ${dbData.is_automatic} debugKey::`, dbDebugKey);
            return done();                         
        }

        if(_.get(processedRecord, ['processedRecord', 'billGen'], false)==false){
            self.L.error(`stop publishing data on REMINDER_BILL_FETCH_REALTIME via the Kafka pipeline if event is not BILL_GEN debugKey::`, dbDebugKey);
            return done();                         
        }

        if(_.get(self.config, ['DYNAMIC_CONFIG', 'PAYTM_POSTPAID_CONFIG', 'BILL_GEN_NOTIFICATIONS', 'PAUSE_NOTIFICATIONS'], 0)){
            self.L.log(`stop publishing data on REMINDER_BILL_FETCH_REALTIME via the Kafka pipeline if notifications are disabled::`, dbDebugKey);
            return done();
        }


        let payload = {
            source: "paytmPostpaidBillFetchRealtime",
            notificationType: "DUEDATE",
            data: {
                customerId: dbData.customer_id,
                rechargeNumber: dbData.recharge_number,
                productId: currentRecord.productId,
                operator: dbData.operator,
                amount: dbData.amount,
                bill_fetch_date: MOMENT(),
                paytype: "postpaid",
                service: dbData.service,
                circle: dbData.circle,
                customerMobile:  dbData.customer_mobile,
                customerEmail: dbData.customer_email,
                status: self.config.COMMON.bills_status.BILL_FETCHED,
                userData: null,
                billDate: dbData.bill_date,
                notification_status: dbData.notification_status,
                dueDate: dbData.due_date,
                customerOtherInfo: dbData.customer_other_info,
                extra: dbData.extra,
            }
        }
        _.set(payload, ['data', 'billFetchReminder_onBoardTime'],new Date().getTime());

        let toBeNotifiedRealtime = self.decideTopicToPublishBillGen();
        if(!toBeNotifiedRealtime){
            _.set(payload, 'source', "paytmPostpaidBillFetch");
        }
        self.billFetchKafkaRealtime.publishData([{
            topic: _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE_REALTIME.REMINDER_BILL_FETCH_REALTIME', ''),
            messages: JSON.stringify(payload)
        }], (error) => {
            if (error) {
                utility._sendMetricsToDD(1, [
                    "REQUEST_TYPE: PAYTM_POSTPAID", 
                    'STATUS:ERROR',
                    'TYPE:KAFKA_PUBLISH',
                    'TOPIC:REMINDER_BILL_FETCH_REALTIME', 
                    "OPERATOR:" + _.get(dbData,'operator',null),
                ]);
                self.L.critical('publishInKafka :: REMINDER_BILL_FETCH_REALTIME', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(payload), error);
            } else {
                utility._sendMetricsToDD(1, [
                    "REQUEST_TYPE:PAYTM_POSTPAID", 
                    'STATUS:PUBLISHED',
                    'TYPE:KAFKA_PUBLISH',
                    'TOPIC:REMINDER_BILL_FETCH_REALTIME', 
                    "OPERATOR:" + _.get(dbData,'operator',null),
                ]);
                self.L.log('prepareKafkaResponse :: REMINDER_BILL_FETCH_REALTIME', 'Message published successfully in Kafka', ' on topic REMINDER_BILL_FETCH_REALTIME', JSON.stringify(payload));
            }
            return done(null);
        }, [200, 800]);
    }

    /**
     *
     * @param {object} billsData contains recharge data value
     */
     publishCtEvents(done, record) {

        let self = this;
        let dbData = _.get(record, 'updatedDBRecord[0]',{});
        let currentRecord = _.get(record, 'processedRecord',{});
        let eventName = 'billPaymentPartial';
        if(_.get(currentRecord, 'billGen', false)){
            eventName = 'reminderBillGen'
        }else if(!_.get(currentRecord, 'dueAmount', null)){
            eventName = 'billPaymentFull'
        }
        dbData.productId = _.get(currentRecord,'productId',null);
        let debugKey = _.get(record,'debugKey',null);
        if(self.commonLib.isCTEventBlocked(eventName)){
            self.L.info(`Blocking CT event ${eventName}`)
            return done()
        }

        if(_.get(dbData, 'notification_status', 1)==0){
            self.L.log('publishCtEvents :: Not publishing on CT as notification_status : ', `${_.get(dbData, 'notification_status', 1)} for debugKey :: ${debugKey}`);
            return done();
        }

        ASYNC.waterfall([
            next => {
                self.commonLib.getRetailerData((error) => {
                    if(error) {
                        return next(error)
                    } else {
                        return next(null)
                    }
                }, dbData.customer_id, dbData);
            },
            next => {
                self.commonLib.getCvrData((error) => {
                    if(error) {
                        return next(error)
                    } else {
                        return next(null)
                    }
                }, dbData.product_id, dbData);
            },
            next => {                  
                let mappedData = self.reminderUtils.createCTPipelinePayload(dbData, eventName, debugKey);
                let clonedData = _.cloneDeep(mappedData); // needed for correct async logging
                self.ctKafkaPublisher.publishData([{
                    topic: _.get(self.config.KAFKA, 'SERVICES.CT_EVENTS_PUBLISHER.TOPIC', ''),
                    messages: JSON.stringify(mappedData)
                }], (error) => {
                    if (error) {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:PAYTM_POSTPAID", 'STATUS:ERROR', "TYPE:CT_EVENTS", "OPERATOR:" + dbData.operator]);
                        self.L.error('publishInKafka :: publishCtEvents', 'Error while publishing message topic REMINDER_CT_EVENTS - MSG:- ' + JSON.stringify(clonedData), error);
                    } else {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:PAYTM_POSTPAID", 'STATUS:PUBLISHED', "TYPE:CT_EVENTS", "OPERATOR:" + dbData.operator]);
                        self.L.log('prepareKafkaResponse :: publishCtEvents', 'Message published successfully in Kafka', ' on topic REMINDER_CT_EVENTS', JSON.stringify(clonedData));
                    }
                    return next(error);
                }, [200, 800]);
            }
        ], (err) => {
            if(err){
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:PAYTM_POSTPAID", 'STATUS:ERROR', "TYPE:publishCtEvents"]);
                self.L.error('publishInKafka :: publishCtEvents', 'Error while publishing message topic REMINDER_CT_EVENTS - MSG:- ' , err);
            }
            return done(null)
        })
    }

     parseAmount(amountStr) { 
         if(amountStr==0 && typeof(amountStr)!='string') return amountStr;
        if (!amountStr) return null;
        if (typeof amountStr === 'number') return amountStr;
        if (typeof amountStr === 'string' && VALIDATOR.isNumeric(amountStr)) return VALIDATOR.toFloat(amountStr);
        // case of "Rs.x.y" i.e. "Rs.101.54"
        let foundMatch = amountStr.match(new RegExp(/Rs[\s.]*([-+]?\d*(?:\.\d*)?)/));
        let parsedAmount = (foundMatch && foundMatch[1]) ? VALIDATOR.toFloat(foundMatch[1]) : null;
        if(parsedAmount) return parsedAmount;
        //case of "x.y" i.e. "101.54"
        let foundMatch2 = amountStr.match(new RegExp(/([-+]?\d*(?:\.\d*)?)/));
        let parsedAmount2 = (foundMatch2 && foundMatch2[1]) ? VALIDATOR.toFloat(foundMatch2[1]) : null;
        return parsedAmount2;
    }

    validateAndProcessRecord(billsData){
        let self=this,
            dateFormat = "YYYY-MM-DD HH:mm:ss";
        try{
        let processedRecord ={
            customerId : (typeof _.get(billsData,'customer_id',null) === 'number') ? _.get(billsData,'customer_id',null) : (typeof _.get(billsData,'customer_id',null) === 'string' && VALIDATOR.isNumeric(_.get(billsData,'customer_id',null))) ? VALIDATOR.toInt(_.get(billsData,'customer_id',null)) : null,
            rechargeNumber : _.get(billsData, 'account_number', null),
            currentBillAmount : self.parseAmount(_.get(billsData, 'current_month_bill_amount', null)),
            dueAmount :  self.parseAmount(_.get(billsData, 'due_amount', null)),
            unBilledAmount :   self.parseAmount(_.get(billsData, 'unbilled_spends', null)),
            dueDate :  _.get(billsData, 'due_date', null) ? MOMENT(_.get(billsData, 'due_date', null)).startOf('day').format(dateFormat) : null,
            billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
            nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
            billFetchDate : MOMENT().format(dateFormat),
            productId : _.get(billsData, 'catalog_repayment_pid', null),
            productType :  _.get(billsData, 'product_type', null),
            custMobile : _.get(billsData, 'mobile_number', null),
            eventType :  _.get(billsData, 'event_type', null),
            billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
            operator : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'catalog_repayment_pid', null), 'operator'] , null)),
            service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'catalog_repayment_pid', null), 'service'] , null)),
            paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'catalog_repayment_pid', null), 'paytype'] , null)),
        },
        mandatoryParams= ['customerId', 'rechargeNumber', 'productId', 'operator', 'service'];
        if(_.get(processedRecord,'billGen',false)){
            mandatoryParams.push('dueDate');
            try {
                if (_.get(processedRecord, 'dueDate', null) != null) {
                    var dueDateFromPayload = MOMENT(_.get(processedRecord, 'dueDate', null)).startOf('day').format(dateFormat);

                    if ((MOMENT(dueDateFromPayload).isSame(MOMENT().startOf('day')) || MOMENT(dueDateFromPayload).isBefore(MOMENT().startOf('day')))) {
                        _.set(processedRecord, 'dueDate', MOMENT().add(3, 'days').startOf('day').format(dateFormat));
                        self.L.log(`validateAndProcessRecord::Due date is in past or today, setting it to end of month for ${JSON.stringify(billsData)}`);
                    }
                }
            }
            catch (error) {
                self.L.error(`validateAndProcessRecord::Error received during setting up change of duedate for billgen service ${error} for dueDate: ${_.get(processedRecord, 'dueDate', null)}, bills Data: ${JSON.stringify(billsData)}`);
            }
        }
        let invalidParams = [];
        mandatoryParams.forEach(function (key) {
            if (!processedRecord[key]) invalidParams.push(key);
        });
        if(!billsData.hasOwnProperty("due_amount") || _.get(processedRecord, 'dueAmount', null)==null || _.get(processedRecord, 'dueAmount', null) < 0){
            invalidParams.push("due_amount");
        }
        if (invalidParams.length > 0) return [`Mandatory Params ${invalidParams} is Missing / Invalid`, processedRecord];
        else return [null, processedRecord];
    }
    catch(error){
        self.L.error(`validateAndProcessRecord::Error received during validation of record ${error} for ${JSON.stringify(billsData)}` );
        }
    }

    getBillsData(record,processedRecord) {
        let self = this;
        let dbRecord = _.get(record, 'transactionHistory[0]', {});
        let extraDetails = {};
        let customerOtherInfo = {};
        extraDetails.billSource = `paytmPostpaid-${MOMENT().format('YYYY-MM-DD HH:mm:ss')}`;
        extraDetails.lastSuccessBFD = _.get(dbRecord, 'bill_fetch_date', null);
        extraDetails.billFetchDate = MOMENT().format('YYYY-MM-DD HH:mm:ss'),
        extraDetails.lastDueDt = _.get(dbRecord, 'due_date', null),
        extraDetails.lastBillDt = _.get(dbRecord, 'bill_date', null);
        extraDetails.lastAmount = _.get(dbRecord, 'amount', null);
        extraDetails.customer_type = _.get(processedRecord, 'customer_type', null);
        extraDetails.source_subtype_2 = 'FULL_BILL'
        
        extraDetails.updated_data_source = "paytmPostpaid";

        if(_.get(dbRecord, 'extra', null)){
            try{
                let dbExtra = JSON.parse(_.get(dbRecord, 'extra', null));
                if( dbExtra &&_.get(dbExtra, 'blockedBy', null)){
                    extraDetails.blockedBy = _.get(dbExtra, 'blockedBy', null);
                }
                if(dbExtra && _.get(dbExtra, 'errorCounters', null)){ 
                    extraDetails.errorCounters = {};
                }
            }catch(error)
            {
                self.L.error("getBillsData", "Error in JSON parsing" + error);
            }
        }

        if(_.get(dbRecord, 'customerOtherInfo', null)){
            try{
                customerOtherInfo = JSON.parse(_.get(dbRecord, 'customerOtherInfo', '{}'));
            }catch(error)
            {
                self.L.error("getBillsData", "Error in JSON parsing customerOtherInfo" + error);
            }
        }
        
        if(_.get(processedRecord,'dueAmount',null) == _.get(dbRecord, 'amount', null) && MOMENT(_.get(processedRecord,'dueDate',null)).isSame(MOMENT(_.get(dbRecord, 'due_date', null)).startOf('day')) && customerOtherInfo != null && _.get(customerOtherInfo, 'billGen', null) == true){
            self.L.error("getBillsData :: ", "recharge_number : " + _.get(processedRecord,'rechargeNumber','') + " : Same bill updated with billGen true");
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:PAYTM_POSTPAID", 'STATUS:ERROR', 'TYPE:same bill updated with billGen true']);
            return null;
        }

        let billsData = {
            user_data: _.get(processedRecord,'user_data',null),
            nextBillFetchDate: _.get(processedRecord,'nextBillFetchDate',null),
            billFetchDate: _.get(extraDetails,'billFetchDate',null),
            amount: _.get(processedRecord,'dueAmount',null),
            dueDate: _.get(processedRecord,'dueDate'),
            rechargeNumber: _.get(processedRecord,'rechargeNumber',null),
            billDate: _.get(processedRecord,'billDate',null),
            productId: _.get(processedRecord,'productId',null),
            status: 4,
            customerId: _.get(processedRecord,'customerId',null),
            customerMobile: _.get(processedRecord, 'custMobile', null),
            operator: _.get(processedRecord,'operator',null),
            circle: _.get(processedRecord,'circle',null),
            service: _.get(processedRecord,'service',null),
            gateway: _.get(processedRecord,'gateway',null),
            retryCount: 0,
            reason: null,
            paytype: _.get(processedRecord, 'paytype', null),
            customerEmail: _.get(processedRecord, 'custEmail', null),
            service_id: _.get(processedRecord, 'serviceId', 0),
            is_automatic: _.get(dbRecord, 'is_automatic', 0),
            notificationStatus : (_.get(dbRecord, 'notification_status',1)==0)? 0:1,
            is_automatic_diffCustId : _.get(processedRecord, 'is_automatic_diffCustId', 0),
            extra: JSON.stringify(extraDetails, function (key, value) {
                if (key && typeof (value) === 'string')
                    return value.replace(/[?']/g, "");
                else
                    return value;
            }),
            customerOtherInfo: JSON.stringify(processedRecord, function (key, value) {
                if (key && typeof (value) === 'string')
                    return value.replace(/[?']/g, "");
                else
                    return value;
            })
        };

        return billsData;
    }


    publishInKafka(done, record,tableName) {
        let self = this;
        ASYNC.parallel([
            function (cb) {
                self.pushToKafkaForAutomaticSync(function(err){
                    cb(err);
                },record,tableName)
            },
            function (cb) {
                self.publishInBillFetchKafka(function(err){
                    cb(err)
                },record)
            },
            function (cb) {
                self.publishCtEvents(function(err){
                    cb(err)
                },record)
            },
        ], function(error) {
            console.log("reched here")
            if (error) {
                self.L.error('Error occurred during parallel tasks:', error);
            }
            return done(error);
        });
    }

    getUpdatedRecordFromDB(done,row,tableName){
        let self = this,
            query = `select * from ${tableName} where operator=${JSON.stringify(row.operator)} and service=${JSON.stringify(row.service)} and recharge_number=${JSON.stringify(row.rechargeNumber)} and customer_id=${row.customerId}`,
            queryParams = [
                tableName
            ];
            let latencyStart = new Date();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'getUpdatedRecordFromDB'});
            if (err || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getUpdatedRecordFromDB`]);
                self.L.critical('getUpdatedRecordFromDB ::', 'error occurred while getting data from DB for data : ', JSON.stringify(row), err);
                return done('error occurred while getting data from DB');
            } else if (!data.length) {
                if (!self.consentData[row.productId]) {
                    self.L.critical('pushToKafkaAutomaticSync :: data not found in table for : ', JSON.stringify(row));
                }
                return done('No data found');
            } else {
                return done(null, data);
            }
        },'DIGITAL_REMINDER_MASTER', query, queryParams);
    }



    /**
     *
     * @param {object} billsData contains recharge data value
     */
    _prepareDataToInsert(billsData, done) {
        let self = this,
        billsRecord={};
        self.L.log(`Record ${JSON.stringify(billsData)} recieved having debug key: customer_id:${_.get(billsData,'customer_id',null)}_recharge_number:${_.get(billsData,'account_number',null)}_amount:${_.get(billsData,'spent_in_bill',null)}_product_id:${_.get(billsData,'product_id',null)}_eventType:${_.get(billsData,'event_type',null)}`);
        let [error,processedRecord] = self.validateAndProcessRecord(billsData);
        
        if (error) {
            self.L.error('_prepareDataToInsert', `failed with error ${error} for ${JSON.stringify(billsData)}`);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:PAYTM_POSTPAID", 'STATUS:VALIDATION_FAILED', `REASON:${error}`]);
            return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(billsData, self.getBillStatus(billsData), "RU"), error,done,false);
        }
        _.set(billsRecord,'processedRecord',processedRecord);
        // let tableName = _.get(self.config, ['OPERATOR_TABLE_REGISTRY', processedRecord.productId], null) || _.get(self.config, ['OPERATOR_TABLE_REGISTRY', processedRecord.operator], null),
        let tableName='bills_paytmpostpaid',
            debugKey = `customerId:${_.get(processedRecord, "customerId")}_rechargeNumber:${_.get(processedRecord, "rechargeNumber")}_operator:${_.get(processedRecord, "operator")}_product_id:${_.get(processedRecord, "productId")}`;
            if (!tableName) {
                let err = `table not found`;
                
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:PAYTM_POSTPAID", 'STATUS:ERROR', `TYPE:TABLE_NOT_FOUND`, `OPERATOR:${_.get(processedRecord,'operator',null)}`]);
                self.L.error('_prepareDataToInsert', `table not found for ${debugKey}`);
                return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(billsData, self.getBillStatus(billsData), "RU"), err,done,false);
            }
            self.L.log('Processing data', debugKey);
            _.set(billsRecord, 'debugKey', debugKey);

        ASYNC.waterfall([
            next => {
                return self.billsModel.getBillsOfSameRech(next, tableName, _.get(billsRecord,'processedRecord',null));
            },
            (rows, next) => {
                self.L.log('_prepareDataToInsert', `Found ${rows.length} transaction history for same RN for:${debugKey}`)
                _.set(billsRecord, 'transactionHistory', rows)
                return next();  
            },
            next => {
                    let billsData = self.getBillsData(billsRecord,_.get(billsRecord,'processedRecord',null));
                    if(!billsData){
                        let err = `bills data could not formed`;
                        return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(_.get(billsRecord,'processedRecord',null), self.getBillStatus(_.get(billsRecord,'processedRecord',null)), "RU"), err,next);
                        //return next('bills data could not formed');
                    }else{
                        billsRecord.billsData = billsData;
                        self.bills.createBillForPaytmPostpaid((error) => {
                            if (error) {
                                utility._sendMetricsToDD(1, ["REQUEST_TYPE:PAYTM_POSTPAID", 'STATUS:ERROR', 'TYPE:CREATE_BILL']);
                                self.L.error('_prepareDataToInsert', 'Error in response', JSON.stringify(billsRecord));
                                return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(_.get(billsRecord,'processedRecord',null), self.getBillStatus(_.get(billsRecord,'processedRecord',null)), "RU"), error,next);
                                //return next(error);
                            }
                            else {
                                utility._sendMetricsToDD(1, ["REQUEST_TYPE:PAYTM_POSTPAID", 'STATUS:CREATED']);
                                return next();
                            }
                        }, tableName, billsData);
                    }
            },
            next => {
                self.getUpdatedRecordFromDB((err,result)=>{
                    if(err){
                        self.L.error(`SMS_PARSING_POSTPAID: ERROR in publishInKafka for : ${processedRecord.debugKey} with error: ${err}`);
                        return next(err);
                    }else {
                        _.set(billsRecord, 'updatedDBRecord', result);
                        return next(null);
                    }
                },billsRecord.billsData, tableName);
            },
            next => {
                self.publishInKafka((err)=>{
                    if(err){
                        self.L.error(`SMS_PARSING_POSTPAID: ERROR in publishInKafka for : ${processedRecord.debugKey} with error: ${err}`);
                        return next(err);
                    }else return next(null);
                },billsRecord, tableName);
            },
        ], function (error) {
            if (error) {
                self.L.error(`_prepareDataToInsert`, `Failed with error ${error} for data ${debugKey}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:PAYTM_POSTPAID", 'STATUS:ERROR','SOURCE:MAIN_FLOW_EXECUTION']);
            }
            return done();
        });
    }

    createRecordForAnalytics(record, source_subtype_2, user_type) {
        console.log("record is --->",record);
        let recordForAnalytics = {};
        var self=this;
        recordForAnalytics.source = "paytmPostpaid";
        recordForAnalytics.source_subtype_2 = source_subtype_2;
        recordForAnalytics.user_type = user_type;
        recordForAnalytics.customer_id = _.get(record,'customerId',null) !==null?_.get(record,'customerId',null):(typeof _.get(record,'customer_id',null) === 'number') ? _.get(record,'customer_id',null) : (typeof _.get(record,'customer_id',null) === 'string' && VALIDATOR.isNumeric(_.get(record,'customer_id',null))) ? VALIDATOR.toInt(_.get(record,'customer_id',null)) : null;
        recordForAnalytics.service = _.get(record,'service',null) !==null?_.get(record,'service',null) :_.toLower(_.get(self.config, ['CVR_DATA', _.get(record, 'catalog_repayment_pid', null), 'service'] , null));
        recordForAnalytics.recharge_number = _.get(record, 'rechargeNumber', null) !==null?_.get(record, 'rechargeNumber', null) :_.get(record, 'account_number', null);
        recordForAnalytics.operator = _.get(record,'operator',null) !==null?_.get(record,'operator',null):_.toLower(_.get(self.config, ['CVR_DATA', _.get(record, 'catalog_repayment_pid', null), 'operator'] , null));
        recordForAnalytics.due_amount = self.parseAmount(_.get(record, 'due_amount', null));
        recordForAnalytics.additional_info = null;
        recordForAnalytics.sms_id = _.get(record, 'smsId', null);
        recordForAnalytics.paytype = _.get(record,'paytype',null) !==null?_.get(record,'paytype',null):_.toLower(_.get(self.config, ['CVR_DATA', _.get(record, 'catalog_repayment_pid', null), 'paytype'] , null));
        recordForAnalytics.updated_at = _.get(record, 'updatedAt', null);
        recordForAnalytics.sender_id = _.get(record, 'smsSenderID', null);
        recordForAnalytics.sms_date_time = _.get(record, 'smsDateTime', null);
        recordForAnalytics.dwh_class_id = _.get(record, 'dwhClassId', null);
        recordForAnalytics.due_date = _.get(record, 'dueDate', null);
        recordForAnalytics.bill_date = _.get(record, 'billDate', null);
        recordForAnalytics.bill_fetch_date = _.get(record, 'billFetchDate',  MOMENT().format('YYYY-MM-DD HH:mm:ss'));
        return recordForAnalytics;
    }

    getBillStatus(record){
        let billstatus=_.toLower(_.get(record,'event_type',null))=="bill_gen"?'FULL_BILL':null;
        return billstatus;
     }
 



    suspendOperations(){
        var self = this,
        deferred = Q.defer();
        self.L.log(`recentBills::suspendOperations kafka consumer shutdown initiated`);

        Q()
        .then(function(){
            self.consumer.close(function(error, res){
                if(error){
                    self.L.error(`recentBills::stopConsumer :: Error Stopping Consumer Err : ${error}`);
                    return error;
                }
                self.L.info(`recentBills::stopConsumer :: Consumer Stopped!  Response : ${res}`);
            })
        })
        .then(function(){
            self.L.log(`recentBills::suspendOperations kafka consumer shutdown successful`);
            deferred.resolve();
        })
        .catch(function(err){
            self.L.error(`recentBills::suspendOperations error in shutting kafka consumer`, err);
            deferred.reject(err);
        });
        return deferred.promise;
    }

}
export default PaytmPostpaid;
