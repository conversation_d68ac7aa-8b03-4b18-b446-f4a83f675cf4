import _, { isArray } from 'lodash'
import utility from '../lib'
import RecentsLayerLib from '../lib/recentsLayer'
import VALIDATOR from 'validator'
import ASYNC from 'async'
import OS from 'os'
import MOMENT from 'moment'
import BILLS from '../models/bills'
import digitalUtility from 'digital-in-util'
import CATALOGVERTICALRECHARGE from '../models/catalogVerticalRecharge';
import PG from '../lib/pg'
import SmsParsingSyncCCBillLibrary from '../lib/smsParsingSyncCCBills'
import DigitalCatalog from '../lib/digitalReminderConfig'
import RemindableUsersLibrary from '../lib/remindableUser'
import SmsParsingLagDashboard from '../lib/smsParsingLagDashboard'
import uuidv1 from 'uuidv1'
import recentBillLibrary from '../lib/recentBills'
import BILLSUBSCRIBER from './billSubscriber'
import BillFetchAnalytics from '../lib/billFetchAnalytics'
import BillsLib from '../lib/bills'
import Logger from '../lib/logger'
import EncryptionDecryptioinHelper from '../lib/EncryptionDecryptioinHelper'
import InternalCustIdNonRUFlowTagger from '../lib/InternalCustIdNonRUFlowTagger'
import Q from 'q'

class SmsParsingCCBills {
    constructor(options) {
        this.L = options.L;
        this.infraUtils = options.INFRAUTILS;
        this.config = options.config;
        this.bills = new BILLS(options);
        this.commonLib = new utility.commonLib(options);
        this.recentsLayerLib = new RecentsLayerLib(options); // TODO
        this.billSubscriber = new BILLSUBSCRIBER(options);
        this.bills_operator_table = _.get(this.config, 'OPERATOR_TABLE_REGISTRY', {});
        this.ccOperators = _.get(this.config, ['RECENT_BILL_CONFIG', 'OPERATORS'], {});
        this.recentBillLibrary = new recentBillLibrary(options);
        this.billsLib = new BillsLib(options);
        this.cc_bills_operators = this.recentBillLibrary._initRecentBillSpecificData(this.bills_operator_table, this.ccOperators);
        this.paymentGatewayUtils = new PG(options);
        this.smsParsingSyncCCBillLib = new SmsParsingSyncCCBillLibrary(options);
        this.digitalCatalogLib = DigitalCatalog;
        this.remindableUsersLibrary = new RemindableUsersLibrary(options);
        this.tableName = 'bills_creditcard';
        this.nonPaytmCCtableName = _.get(this.config, 'COMMON.NON_PAYTM_CC.tableName', 'bills_nonpaytm_creditcard');
        this.reminderUtils = new digitalUtility.ReminderUtils();
        this.catalogVerticalRecharge = new CATALOGVERTICALRECHARGE(options);
        this.activePidLib = options.activePidLib;
        this.refereshIntervalForCategoryData = 15 * 60 * 1000; // DCAT getCategoryProductDetail API refresh interval 
        this.greyScaleEnv = _.get(options, 'greyScaleEnv', false);
        this.commonLib = new utility.commonLib(options);
        this.kafkaBatchSize = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'SMS_PARSING_CC_BILLS', 'BATCHSIZE'], 2) : 500;
        this.kafkaBatchDelay = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'SMS_PARSING_CC_BILLS', 'DELAY'], 5 * 60 * 1000) : 0;
        this.smsParsingLagDashboard = new SmsParsingLagDashboard(options);
        this.timestamps = {};
        this.smsParsingCCBillsRealtime = _.get(options, 'smsParsingCCBillsRealtime', false);
        this.smsParsingCCBillsDwhRealtime = _.get(options, 'smsParsingCCBillsDwhRealtime', false);
        this.RUreadsKafkaTime =  new Date().getTime(); 
        this.billFetchAnalytics = new BillFetchAnalytics(options);
        this.service = 'financial services';
        this.logger = new Logger(options);
        this.encryptionDecryptionHelper = new EncryptionDecryptioinHelper(options);
        this.internalCustIdNonRUFlowTagger = new InternalCustIdNonRUFlowTagger(options);
    }

    start() {
        let self = this;
        self.smsParsingSyncCCBillLib.refreshDCATCacheData(self.refereshIntervalForCategoryData);
        self.L.log('start', 'Going to configure Kakfa..');
        self.configureKafka(function (error) {
            if (error) {
                self.L.critical('SmsParsingCCBills :: start', 'unable to configure kafka', error);
                process.exit(0);
            }
            else {
                self.L.log('SmsParsingCCBills :: start', 'Kafka Configured successfully !!');
            }
        });
    }

    configureKafka(done) {
        /**
         * maintain this sequence
         * 1) Initialize all publisher
         * 2) Initialize all consumers
         */
        let self = this;
        ASYNC.waterfall([
            next => {
                self.L.log('configureKafka', 'Going to initialize Kakfa Publisher for REMINDER_BILL_FETCH topic');
                self.kafkaBillFetchPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": self.config.KAFKA.TOPICS.REMINDER_BILLFETCH_PIPELINE.HOSTS
                });
                self.kafkaBillFetchPublisher.initProducer('high', function (error) {
                    return next(error);
                });
            },
            next => {
                self.L.log('configureKafka', 'Going to initialize Kakfa Publisher for REMINDER_BILL_FETCH_REALTIME topic');
                self.kafkaBillFetchRealtimePublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": self.config.KAFKA.TOPICS.REMINDER_BILLFETCH_PIPELINE_REALTIME.HOSTS
                });
                self.kafkaBillFetchRealtimePublisher.initProducer('high', function (error) {
                    return next(error);
                });
            },
            next => {
                /**
                 * Kafka publisher to publish events to CT publisher pipeline 
                 */
                self.ctKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.CT_EVENTS_PUBLISHER.HOSTS
                });
                this.ctKafkaPublisher.initProducer('high', function (error) {
                    return next(error)
                });
            },
            next => {
                /**
                 * Kafka publisher to publish events to non paytm bills pipeline 
                 */
                 self.nonPaytmKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.NON_PAYTM_RECORDS.HOSTS
                });
                this.nonPaytmKafkaPublisher.initProducer('high', function (error) {
                    return next(error)
                });
            },
            next => {
                /**
                 * Kafka publisher to publish events to Paytm First Team (events will be same as CT)
                 */
                 self.paytmFirstKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.PAYTM_FIRST_CC_EVENTS_PUBLISHER.HOSTS
                });
                this.paytmFirstKafkaPublisher.initProducer('high', function (error) {
                    return next(error)
                });
            },
            next => {
                if(self.smsParsingCCBillsRealtime){
                    self.L.log('configureKafka', 'Going to initialize Kakfa Consumer for topic : ru_sms_reminder');
                    // Initialize consumer of topic REMINDER_BILL_FETCH
                    let kafkaConsumerObj = {
                        "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.SMS_PARSING_CC_RU_REALTIME.HOSTS'),
                        "groupId": "smsParsingCCBills-consumer",
                        "topics": _.get(self.config.KAFKA, 'SERVICES.SMS_PARSING_CC_RU_REALTIME.SMS_PARSING_CC_RU_REALTIME_TOPIC'),
                        "id": `smsParsingCCBills-consumer_${OS.hostname()}_${process.pid}`,
                        "fromOffset": "earliest",
                        "autoCommit": false,
                        "batchSize": self.kafkaBatchSize
                    };
                    self.kafkaSMSParsingConsumer = new self.infraUtils.kafka.consumer(kafkaConsumerObj);
                    self.kafkaSMSParsingConsumer.initConsumer(self.execSteps.bind(self), (error) => {
                        if (!error) {
                            self.L.log("configureKafka", "consumer of topic : ru_sms_reminder Configured");   
                        }
                        return next(error);
                    });
                }else if(self.smsParsingCCBillsDwhRealtime){
                    self.L.log('configureKafka', 'Going to initialize Kakfa Consumer for topic : REALTIME_SMS_PARSER_CC_BILLS');
                    // Initialize consumer of topic REMINDER_BILL_FETCH
                    let kafkaConsumerObj = {
                        "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.SMS_PARSING_CC_DWH_REALTIME.HOSTS'),
                        "groupId": "smsParsingCCBills-consumer",
                        "topics": _.get(self.config.KAFKA, 'SERVICES.SMS_PARSING_CC_DWH_REALTIME.SMS_PARSING_CC_DWH_REALTIME_TOPIC'),
                        "id": `smsParsingCCBills-consumer_${OS.hostname()}_${process.pid}`,
                        "fromOffset": "latest",
                        "autoCommit": false,
                        "batchSize": self.kafkaBatchSize
                    };
                    self.kafkaSMSParsingConsumer = new self.infraUtils.kafka.consumer(kafkaConsumerObj);
                    self.kafkaSMSParsingConsumer.initConsumer(self.execSteps.bind(self), (error) => {
                        if (!error) {
                            self.L.log("configureKafka", "consumer of topic : REALTIME_SMS_PARSER_CC_BILLS Configured");   
                        }
                        return next(error);
                    });
                }else{
                    self.L.log('configureKafka', 'Going to initialize Kakfa Consumer for topic : dwh-ingest-SMS_PARSING_CC_BILLS');
                    // Initialize consumer of topic REMINDER_BILL_FETCH
                    let kafkaConsumerObj = {
                        "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.SMS_PARSING_CC.HOSTS'),
                        "groupId": "smsParsingCCBills-consumer",
                        "topics": _.get(self.config.KAFKA, 'SERVICES.SMS_PARSING_CC.SMS_PARSING_CC_TOPIC'),
                        "id": `smsParsingCCBills-consumer_${OS.hostname()}_${process.pid}`,
                        "fromOffset": "earliest",
                        "autoCommit": false,
                        "batchSize": self.kafkaBatchSize
                    };
                    self.kafkaSMSParsingConsumer = new self.infraUtils.kafka.consumer(kafkaConsumerObj);
                    self.kafkaSMSParsingConsumer.initConsumer(self.execSteps.bind(self), (error) => {
                        if (!error) {
                            self.L.log("configureKafka", "consumer of topic : dwh-ingest-SMS_PARSING_CC_BILLS Configured");   
                        }
                        return next(error);
                    });
                }
            }
        ], function (error) {
            if (error) {
                self.L.critical('configureKafka', 'Could not initialize Kafka', error);
                return done(error)
            }
            return done(null);
        });
    }

    execSteps(records) {
        let self = this,
            chunkSize = 50,
            startTime = new Date().getTime(),
            currentPointer = 0, lastMessage;
        self.RUreadsKafkaTime = new Date().getTime();

        if (records && _.isArray(records)) {
            lastMessage = records[records.length - 1];
            self.kafkaSMSParsingConsumer._pauseConsumer();
        } else {
            self.L.critical('execSteps', `No valid kafka records found. Received records-`, records);
            return;
        }

        self.L.log('execSteps:: ', `Processing ${records.length} SMS Parsing CC Bills data !!`);
        utility._sendMetricsToDD(records.length, ['REQUEST_TYPE:SMSPCC_BILLS_TRAFFIC']);

        ASYNC.whilst(
            () => {
                return currentPointer < records.length;
            },
            (callback) => {
                let nextChunk = records.slice(currentPointer, (currentPointer + chunkSize));
                currentPointer += chunkSize;
                self.processBatch(nextChunk, () => {
                    setTimeout(() => {
                        callback();
                    }, 2);
                });
            },
            (err) => {
                self.kafkaSMSParsingConsumer.commitOffset(lastMessage, (error) => {
                    if (error) {
                        self.L.error('execSteps::', 'Error while commit for-', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                    }
                    else {
                        self.L.log('execSteps::', 'Commit success for offset :', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                    }
                let endTime = new Date().getTime();
                let executionTime = (endTime - startTime) / 1000;      //in seconds
                executionTime = Math.round(executionTime);
                self.L.log('execSteps::', 'per chunkSize record Execution time :', executionTime, 'seconds ',records.length);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_STATUS", 'STATUS:SUCCESS', "SOURCE:SmsParsingCCBills", "TIME_TAKEN:" + executionTime]);

                    // Resume consumer now
                setTimeout(function () {
                        self.kafkaSMSParsingConsumer._resumeConsumer();
                    }, self.kafkaBatchDelay);
                });
            }
        );
    }
    
    processBatch(records, done) {
        let self = this,
         currentPointer = 0;
        ASYNC.whilst(
            () => {
                return currentPointer < records.length;
            },
            (callback) => {
                let record = records[currentPointer];
                currentPointer = currentPointer+1;
                self.processData(record, () => {
                    setTimeout(() => {
                        callback();
                    }, 1);
                });
            },
            (err) => {
                return done()
            }
        );
    }
    processData(record, done) {
        let self = this;
        let options = {};
        let published_time = Number(_.get(record, 'timestamp',null));
        try {
            options.smsParsingEntryTime = MOMENT().format('YYYY-MM-DD HH:mm:ss'),
            options.smsRcvdTime = MOMENT(_.get(record, "timestamp")).format('YYYY-MM-DD HH:mm:ss');
            options.topic = _.get(record, "topic");
            record = JSON.parse(_.get(record, 'value', null));
            if(!record.data){
                self.L.critical('processData', `Invalid Kafka record received. data key is missing`, record);
                return done();
            }
        } catch (error) {
            if (error) {
                self.L.critical('processData', `Invalid Kafka record received`, record);
                
            }
            return done();
        }
        ASYNC.map(
            record.data,
            (smsData, next) => {
                _.set(smsData, 'published_time', published_time);
                self.processRecords(() => {
                    return next();
                }, smsData, options);
            },
            err => {
                done();
            }
        )
    }

    initializeAnalyticsPayload(options) {
        let self = this;
        let dataSource;
        let source;
        if(_.get(options, "topic") === "ru_sms_reminder") {
            dataSource = "RTSP";
            source = "SMS_PARSING_REALTIME";
        } else if(_.get(options, "topic") === "REALTIME_SMS_PARSER_CC_BILLS") {
            dataSource = "DWH_RTSP";
            source = "SMS_PARSING_DWH_REALTIME"
        } else {
            dataSource = "DWH";
            source = "SMS_PARSING_DWH";
        }
        return {
            category: null,
            rtspId: null,
            refId: uuidv1(), 
            isUpsert: false,
            notificationCreationTime: null, 
            notificationPublishTime: null,
            smsParsingEntryTime: _.get(options, "smsParsingEntryTime"),
            smsRcvdTime: _.get(options, "smsRcvdTime"),
            customerId: null,
            dataSource: dataSource,
            cassandraAnalyticsPayload: {
                source: source,
                source_subtype_2: null,
                user_type: null,
                customer_id: null,
                service: "financial services",
                recharge_number: null,
                paytype: "credit card",
                operator: null,
                due_amount: null,
                due_date: null,
                bill_date: null,
                bill_fetch_date:null,
                sender_id: null,
                updated_at: null,
                sms_date_time: null,
                sms_id: null
            }
        }
    }

    getOriginOfPayloadCurrentlyBeingProcessed(record) {
        let self = this;
        if(self.smsParsingCCBillsDwhRealtime) {
            return "SMS_PARSING_CC_DWH_REALTIME"
        }
        return _.get(record,'isRuSmsParsing', false)==true? 'SMS_PARSING_CC_REALTIME':'SMS_PARSING_CC';
    }

    processRecords(done, record, options) {
        let self = this;
        self.timestamps = {};
        let analyticsPayload = self.initializeAnalyticsPayload(options);
        utility._sendMetricsToDD(1, [
            'REQUEST_TYPE:SMSPCC_BILLS', 
            'STATUS:TRAFFIC', 
            `APP_VERSION:${_.get(record,'appVersion', '')}`,
            `APP_COUNT:${_.get(record,'appCount', null)}`,
            `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`]);
        try {
            ASYNC.waterfall([
                next => {
                self.validateAndProcessRecord(record, function(error, resp){
                    if (error) {
                        self.logger.error(`processRecords Invalid record received with error ${error}`, record, self.service);
                        utility._sendMetricsToDD(1, [
                            'REQUEST_TYPE:INVALID_RECORD_SMSPCC', 
                            'STATUS:ERROR', 
                            'TYPE:'+ error, 
                            `APP_VERSION:${_.get(record,'appVersion', '')}`,
                            `APP_COUNT:${_.get(record,'appCount', null)}`,
                            `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`]);
                            
                        return next(error);
                    }
                    else {
                        utility._sendMetricsToDD(1, [
                            'REQUEST_TYPE:INVALID_RECORD_SMSPCC', 
                            'STATUS:SUCCESS', 
                            'TYPE:VALIDATION_SUCCESS', 
                            `APP_VERSION:${_.get(record,'appVersion', '')}`,
                            `APP_COUNT:${_.get(record,'appCount', null)}`,
                            `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`]);
                        self.logger.log(`Processing record for with debugKey ${resp.debugKey}`, record, self.service);
                        return next(null,resp);
                    }
                }, analyticsPayload);
            },
            (processedRecord,next) => {
                    self.getActionforCCBills(function (error, action, dbRecordResp , message) {
                        if(error) { 
                            self.L.error('processRecords::getActionforCCBills', `Unable to get valid SingleMatchingCardByCustomer for ${processedRecord.debugKey} with reason:${error}`);
                            return next(error);
                        } else {
                            if(analyticsPayload.cassandraAnalyticsPayload){
                                if(_.get(analyticsPayload, ["cassandraAnalyticsPayload", "operator"]) == null){
                                    analyticsPayload.cassandraAnalyticsPayload.operator = _.get(dbRecordResp, "operator", null);
                                }
                            }
                            if(dbRecordResp) {
                                processedRecord.debugKey = `${processedRecord.debugKey}_Id:${dbRecordResp.id}_MCN:${self.encryptionDecryptionHelper.encryptData(dbRecordResp.recharge_number)}_operator:${dbRecordResp.operator}`;
                            }
                            self.L.log('processRecords::getActionforCCBills', `action:${action}_debugkey:${processedRecord.debugKey}${message ? '_msg:'+message : ''}`);
                            return next(null, action, dbRecordResp,processedRecord)
                        }
                    }, processedRecord, analyticsPayload)
                },
                (action, dbRecordResp,processedRecord, next) => {
                    if(action == 'update' || action == 'update_skipNotify') {
                        if(analyticsPayload.cassandraAnalyticsPayload){
                            analyticsPayload.cassandraAnalyticsPayload.user_type = "RU";
                        }
                        processedRecord.action = action;

                        if(_.get(processedRecord,'isPartial',false) === true){
                            let [skipPartialRecord,error] = self.shouldPartialRecordsBeSkipped(dbRecordResp, processedRecord);
                            if(error){
                                return next(error);
                            }
                        }
                        self.updateCCBillAndSendNotification(function (error) {
                            if(error) {
                                return next(error);
                            } else {
                                return next(null, dbRecordResp, action,processedRecord);
                            }
                        }, processedRecord, dbRecordResp, action, analyticsPayload);

                    } else if (action == 'create') {
                        self.createCCBillAndSendNotification(function (error, dataToBeInsertedInDB = {}) {
                            if(error) {
                                return next(error);
                            } else {
                                if(_.get(dataToBeInsertedInDB, "dbEvent") === "upsert"){
                                    analyticsPayload.isUpsert = true;
                                }
                                return next(null, dataToBeInsertedInDB, action,processedRecord);
                            }
                        }, processedRecord, analyticsPayload);
                    } else {
                        utility._sendMetricsToDD(1, [
                            'REQUEST_TYPE:SMSPCC_BILLS', 
                            'STATUS:ERROR',
                            'TYPE:NO_ACTION',
                            `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                            `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                            `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`]);
                        self.logger.error(`processRecords No action found for record:: with debugKey::${processedRecord.debugKey}`, record, "financial services");    
                        return next('No action found for record');
                    }   
                },
                (dbRecordResp, action,processedRecord, next) => {
                    let updateInDB, newBillCycle;
                    [updateInDB, newBillCycle] = self.shouldUpdateCCBillInSystem(processedRecord, dbRecordResp);
                    if(_.get(processedRecord,'isPartial',false) === true){
                        updateInDB = true;
                    }

                    if(action == 'update'){
                        dbRecordResp = self.getDbRecordToUpdate(processedRecord, dbRecordResp, newBillCycle);
                    }
                    if(action == 'create' || updateInDB){
                        self.publishCtAndPFCCEvents((error) => {
                            if(error){
                                return next(error);
                            } else {
                                return next(null, processedRecord)
                            }
                        }, dbRecordResp, processedRecord, record);
                    } else {
                        return next(null, processedRecord);
                    }
                },
                (processedRecord, next) =>{
                    let source='SMS_PARSING_CC'
                    if(_.get(processedRecord, 'isRuSmsParsing', false)==true){
                        source='SMS_PARSING_CC_REALTIME'
                    } else if(self.smsParsingCCBillsDwhRealtime) {
                        source = "SMS_PARSING_CC_DWH_RT"
                    }
                    self.smsParsingLagDashboard.publishDelaysMetrics((err)=>{
                        return next(err);
                    },source,self.timestamps, processedRecord.bankName, processedRecord);
                }
            ], function (error) {
                if(error) {
                    self.L.error('processRecords',`Exception occured Error Msg:: ${error} for debugKey:: lastCC:${self.encryptionDecryptionHelper.encryptData(_.get(record, 'lastCC', null))},customerId:${_.get(record, 'cId', null)}`);
                } else {
                    self.L.log(`processRecords`,`Record processed having debug key :: lastCC:${self.encryptionDecryptionHelper.encryptData(_.get(record, 'lastCC', null))},customerId:${_.get(record, 'cId', null)}`);
                }
                if (!_.get(analyticsPayload, "isUpsert")) {
                    return self.validateAndInsertAnalyticsRecordInDB(error, analyticsPayload)
                        .then(() => {
                            if(error){
                                return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(analyticsPayload.cassandraAnalyticsPayload, error);
                            }
                            else{
                                return Promise.resolve();
                            }
                        })
                        .then(() => {
                            self.L.log(`processRecords : Successfully Processed Records for Analytics`);
                            return done(error);
                        })
                        .catch((err) => {
                            self.L.log(`processRecords : Failed to Process Records for Analytics `, err);
                            return done(err);
                        })
                }
                else{
                    if(error){
                        return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(analyticsPayload.cassandraAnalyticsPayload, error)
                        .then(() => {
                            return done(error);
                        })
                    }
                    return done(error);
                }
            });
        } catch(err) {
            self.logger.error(`processRecords Exception occured for record: ${err}`, record, "financial services");
            return done(err);
        }
    }

    validateAndInsertAnalyticsRecordInDB(error, payload) {
        let self = this;
        return new Promise((resolve, reject) => {
            return self.commonLib.validateAndCreatePayload(error, payload)
                .then((params) => {
                    return self.bills.insertAnalyticsRecordInDB(params, "sms_parsing_analytics")
                        .then((data) => {
                            self.L.log(`validateAndInsertAnalyticsRecordInDB: Successfully inserted records for Analytics`);
                            return resolve();
                        })
                        .catch((err) => {
                            self.L.error(`validateAndInsertAnalyticsRecordInDB: Failed to insert record for Analytics: `, err);
                            return reject(err);
                        })
                })
                .catch((err) => {
                    self.L.error(`validateAndInsertAnalyticsRecordInDB: Failed at Analytics: `, err);
                    return reject(err);
                })

        })
    }

    /**
     * 
     * @param {*} done 
     * @param {*} processedRecord 
     * @param {*} dbRecord 
     * @returns 
     */
    updateCCBillAndSendNotification(done, processedRecord, dbRecord, action, analyticsPayload = {}) {
        let self = this,
            updateInDB = false, newBillCycle = false;
      
        try {
            ASYNC.waterfall([
                (next) => {
                    [updateInDB, newBillCycle] = self.shouldUpdateCCBillInSystem(processedRecord, dbRecord);
                    if(_.get(processedRecord,'isPartial',false) === true){
                        updateInDB = true;
                    }
                    self.L.log('processRecords-checkUniquenessInRecents', `updateInDB:${updateInDB},newBillCycle:${newBillCycle} for ${processedRecord.debugKey}`); 
                    if (_.get(dbRecord,'is_automatic',0) === 0 && (updateInDB || action == 'update_skipNotify')) {
                        dbRecord = self.getDbRecordToUpdate(processedRecord, dbRecord, newBillCycle);
                        if(action == 'update_skipNotify'){
                            _.set(dbRecord, 'amount', 0);
                            _.set(dbRecord, 'status', _.get(self.config, 'COMMON.bills_status.SKIP_CC_BILL', 17));
                        }
                        return self.updateCCBillInSystem(next, processedRecord, dbRecord, newBillCycle);
                    } else {
                        if(!updateInDB){
                            return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(analyticsPayload.cassandraAnalyticsPayload, "Skipping updation because same bill cycle")
                            .then(() => {
                                return next(null);
                            })
                        }
                        else{
                            return next(null);
                        }
                    }
                },
                next => {
                    if (newBillCycle && action !== 'update_skipNotify') {
                        // let [err, record] = self.checkMandatoryParams(processedRecord, null, processedRecord, false, true);
                        // if(err) {
                        //     self.L.error(`updateCCBillAndSendNotification`, `Error: ${err}. Skipping sending notification for record having debugkey:${processedRecord.debugKey}`);    
                        //     return next(null);
                        // }
                        dbRecord.refId = _.get(analyticsPayload, "refId");
                        dbRecord.rtspId = _.get(analyticsPayload, "rtspId");
                        return self.sendNotification(next, dbRecord, processedRecord, analyticsPayload);
                    } else {
                        return next(null);
                    }
                }
            ], function (error) {
                if(error) {
                    return done(error);    
                } else {
                    return done(null);    
                }
            });
        } catch(err) {
            utility._sendMetricsToDD(1, [
                'STATUS:ERROR',
                'TYPE:UPDATE_CC_BILLAND_SEND_NOTIFICATION',
                `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                'REQUEST_TYPE:SMSPCC_BILLS']);
            self.L.critical(`updateCCBillAndSendNotification`,`Exception occured for record having debugkey:${processedRecord.debugKey}`,err)
            return done(err);
        }
    }
    /**
     * 
     * @param {*} done 
     * @param {*} processedRecord 
     */
    createCCBillAndSendNotification(done, processedRecord, analyticsPayload = {}) {
        let self = this,
            sagaSavedCCData = null,
            sagaSavedCCDataUniqueKey,
            dataToBeInsertedInDB = {},
            dataToPassCT;
    
        try {
            ASYNC.waterfall([
                next => {
                    self.paymentGatewayUtils.getCreditCardDataFromSaga(function(error, sagaSavedCardsData) {
                        if(error) {
                            utility._sendMetricsToDD(1, [
                                'STATUS:ERROR',
                                'TYPE:CC_DATA_FROM_SAGA',
                                `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                                `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                                'REQUEST_TYPE:SMSPCC_BILLS']);
                            return next(error);
                        } else {
                            utility._sendMetricsToDD(1, [
                                'STATUS:SUCCESS',
                                'TYPE:CC_DATA_FROM_SAGA',
                                `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                                `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                                'REQUEST_TYPE:SMSPCC_BILLS']);
                            return next(null, sagaSavedCardsData);
                        }
                    }, processedRecord.customerId);
                },
                (sagaSavedCardsData, next) => {
                    self.smsParsingSyncCCBillLib.getProcessedSagaSavedCardsData(payload => {
                        let {status, type, data} = payload;
                        utility._sendMetricsToDD(1, [
                            'REQUEST_TYPE:PG_SAVED_CARDS_BY_USER_ID_API', 
                            `STATUS:${status}`, 
                            `TYPE:${type}`,
                            `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                            `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                            `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`]);
                        if(status === 'ERROR' && !data) {
                            self.L.error(`sagaSavedCardsData`, `Invalid record received ${self.encryptionDecryptionHelper.encryptData(JSON.stringify(sagaSavedCardsData))}`, `with error type ${type} ${data ? 'Error Msg:' + data : '' } having debugkey:${processedRecord.debugKey}`);    
                            return next(type);
                        } else if(data.nonPaytmCreditCard){
                            self.L.log(`sagaSavedCardsData`, `Found NON Paytm Card, details ${JSON.stringify(data)}`);
                            return next(null, data);
                        }
                        let [err, record] = self.checkMandatoryParams(processedRecord, null, processedRecord, false, true);
                        if(err) {
                            self.L.error(`sagaSavedCardsData`, `Error: ${err}. Skipping inserting in reminders for record having debugkey:${processedRecord.debugKey}`);    
                            return next(err);
                        }
                        self.L.log(`sagaSavedCardsData`, `Matched Card details`, `${this.encryptionDecryptionHelper.encryptData(JSON.stringify(data))}`, `Record processed`, `${this.encryptionDecryptionHelper.encryptData(JSON.stringify(sagaSavedCardsData))}`, `with Msg ${type} having debugkey:${processedRecord.debugKey}`);
                        return next(null, data);
                    }, processedRecord, sagaSavedCardsData);
                },
                (sagaSavedCCData, next) => {
                    if(sagaSavedCCData.nonPaytmCreditCard){
                        if(analyticsPayload.cassandraAnalyticsPayload)
                            analyticsPayload.cassandraAnalyticsPayload.user_type = "NON_RU";
                        let bankName = _.get(self.config, ['DYNAMIC_CONFIG','CC_BILLS_CONFIG','BANK_NAME_TO_BANK_CODE_MAPPING',_.toUpper(processedRecord.bankName)], null);
                        self.L.log(`sagaSavedCardsData`, `db config bankName: ${bankName} and original bankName: ${processedRecord.bankName}`);    
                        if(bankName == null) bankName = processedRecord.bankName;
                        sagaSavedCCDataUniqueKey = self.smsParsingSyncCCBillLib.getUniqueKeyForSavedCardsData({
                            isPaytmFirstCard: 0,
                            bankName: bankName,
                            cardScheme: _.get(self.config, 'COMMON.NON_PAYTM_CC.cardScheme', 'dummyNetwork')
                        })
                    } else {
                        if(analyticsPayload.cassandraAnalyticsPayload)
                            analyticsPayload.cassandraAnalyticsPayload.user_type = "RU";
                        sagaSavedCCDataUniqueKey = self.smsParsingSyncCCBillLib.getUniqueKeyForSavedCardsData({
                            isPaytmFirstCard : (self.smsParsingSyncCCBillLib.isPaytmFirstCCInSagaCCDetails(sagaSavedCCData) ? "1" : "0"),
                            bankName : sagaSavedCCData.product.bankName,
                            cardScheme : sagaSavedCCData.product.cardNetwork
                        })
                    }
                    self.L.log(`getFinancialServicesPID`, `sagaSavedCCDataUniqueKey : ${sagaSavedCCDataUniqueKey}`)

                    // caching global mappper with unique key and value as product id
                    self.smsParsingSyncCCBillLib.getFinancialServicesPID(function (error, productId) {
                        if(error) {
                            utility._sendMetricsToDD(1, [
                                'REQUEST_TYPE:SMSPCC_CREATE_BILLS' , 
                                `STATUS:ERROR` , 
                                `TYPE:PRODUCT_NOT_EXISTS_IN_DCAT` , 
                                `DEBUG_KEY_NAME:BANK_CARD_TYPE_UNIQ_KEY`,
                                `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                                `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}` ]);
                            self.L.error(`getFinancialServicesPID`, `Error Msg: ${error} for sagaSavedCCDataUniqueKey ${sagaSavedCCDataUniqueKey} having debugkey:${processedRecord.debugKey}`);    
                            return next(error);
                        } else {
                            if(analyticsPayload.cassandraAnalyticsPayload && _.get(analyticsPayload, ["cassandraAnalyticsPayload", "operator"]) == null)
                                analyticsPayload.cassandraAnalyticsPayload.operator = _.toLower(_.get(self.config, ['CVR_DATA', productId, 'operator'] , null));
                            self.L.log(`getFinancialServicesPID`, `productId: ${productId} for sagaSavedCCDataUniqueKey ${sagaSavedCCDataUniqueKey} having debugkey:${processedRecord.debugKey}`);    
                            return next(null, productId, sagaSavedCCData);    
                        }
                    }, sagaSavedCCDataUniqueKey);
                },
                (productId, sagaSavedCCData, next)=> {
                    /**
                     * safe check to ensure data exists for corresponding prosuct id 
                     */
                    if(!_.has(self.config, ['CVR_DATA', productId, 'operator'])) {
                        self.L.error('createCCBillAndSendNotification', `CVR data not exists for productId:${productId} having debugKey: ${processedRecord.debugKey}`);
                        utility._sendMetricsToDD(1, [
                            'REQUEST_TYPE:SMSPCC_CREATE_BILLS', 
                            `DEBUG_KEY_NAME:PRODUCT_ID` , 
                            `DEBUG_KEY_VALUE:${productId}` , 
                            `STATUS:ERROR` , 
                            `TYPE:CVR_DATA_NOT_EXISTS_FOR_PID`,
                            `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                            `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                            `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}` ]);
                        return next(`CVR data not exists for productId:${productId}`);
                    }

                    if(sagaSavedCCData.nonPaytmCreditCard){
                        dataToBeInsertedInDB = self.smsParsingSyncCCBillLib.formatDataForNonPaytmCards({
                            productId : productId,
                            sagaSavedCCData : sagaSavedCCData,
                            processedRecord : processedRecord,
                            origin: self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord),
                            RUreadsKafkaTime: self.RUreadsKafkaTime,
                            analyticsPayload : analyticsPayload
                        });
                        return next(null, sagaSavedCCData)
                    }

                    dataToBeInsertedInDB = self.smsParsingSyncCCBillLib.getFormattedData({
                        productId : productId,
                        sagaSavedCCData : sagaSavedCCData,
                        processedRecord : processedRecord,
                        analyticsPayload : analyticsPayload,
                        extras: {
                            next_bill_fetch_date : self.recentBillLibrary.decideNextBillFetchDate({...processedRecord , service:"financial services","productInfo_operator": (_.get(self.config, ['CVR_DATA', productId, 'operator'] , null))}, self.cc_bills_operators, self.billSubscriber)['nextBillFetchDate']
                        }
                    });

                    return next(null, sagaSavedCCData);
                },
                (sagaSavedCCData, next) => {
                    if(sagaSavedCCData && sagaSavedCCData.nonPaytmCreditCard){
                        return next(null, sagaSavedCCData)
                    }

                    if (!_.get(dataToBeInsertedInDB, 'customer_email', null) || !_.get(dataToBeInsertedInDB, 'customer_mobile', null)) {
                        self.remindableUsersLibrary._getUserDetails(function (error, status) {
                            if(!status || error) {
                                self.L.error(`getUserDetails`, `mobile & email can not be updated for customer id: ${dataToBeInsertedInDB.customer_id} debugkey:${processedRecord.debugKey}`);
                            }
                            return next(null, sagaSavedCCData);
                        }, dataToBeInsertedInDB);
                    } else {
                        return next(null, sagaSavedCCData);
                    }
                },
                (sagaSavedCCData, next)=>{
                    if(sagaSavedCCData && sagaSavedCCData.nonPaytmCreditCard){
                        return next(null, sagaSavedCCData)
                    }

                    self.createCCBillInSystem(function (error){
                        if(error) {
                            return next(error);
                        } else {
                            return next(null, sagaSavedCCData);
                        }
                    }, processedRecord , dataToBeInsertedInDB);
                },
                (sagaSavedCCData, next) => {
                    if(sagaSavedCCData && sagaSavedCCData.nonPaytmCreditCard){
                        return next(null, sagaSavedCCData)
                    }

                    return self.sendNotification(next, dataToBeInsertedInDB, processedRecord, analyticsPayload);
                },
                async (sagaSavedCCData, next) => {
                    let min_due_amount = _.get(processedRecord, 'currentMinBillAmount', null);
                    if(min_due_amount == null){
                        utility._sendMetricsToDD(1, [
                            'REQUEST_TYPE:SMSPCC_BILLS', 
                            'STATUS:SKIPPING_MIN_DUE_AMOUNT_NON_PAYTMCC_SENT',
                            'TYPE:TOTAL',
                            `BANK:${processedRecord.bankName}`,
                            `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                            `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                            `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`]);
                    }
                    dataToBeInsertedInDB.dbEvent = 'delete';
                    if(sagaSavedCCData.nonPaytmCreditCard){
                        dataToBeInsertedInDB.dbEvent = 'upsert'
                    }

                    //_.set(dataToBeInsertedInDB,'isOverdue',_.get(processedRecord,'isOverdue',''));

                    dataToPassCT = dataToBeInsertedInDB;

                    if(dataToBeInsertedInDB.dbEvent == 'delete'){
                        dataToBeInsertedInDB = self.smsParsingSyncCCBillLib.formatDataForDeletingNonPaytmCards(dataToBeInsertedInDB);
                    }
                    let nonRuDataToPublish = await self.internalCustIdNonRUFlowTagger.mapInternalCustIdToNonRUFlow(dataToBeInsertedInDB);
                    let loggerData = self.smsParsingSyncCCBillLib.formatDataForCustomLogger(dataToBeInsertedInDB); // had to make a separate payload because we are stringifying custotherinfo and extra which will cause problem in encryption
                    self.nonPaytmKafkaPublisher.publishData([{
                        topic: _.get(self.config.KAFKA, 'SERVICES.NON_PAYTM_RECORDS.TOPIC', ''),
                        messages: nonRuDataToPublish
                    }], (error) => {
                        if(error){
                            utility._sendMetricsToDD(1, [
                                "REQUEST_TYPE:SMS_PARSING", 
                                'STATUS:ERROR', 
                                "TYPE:NON_PAYTM_EVENTS",
                                `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                                `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`]);
                            self.logger.critical(`nonPaytmKafkaPublisher  Error while publishing message in Kafka ${error} - MSG:- `, loggerData, "financial services");   
                        } else {
                            utility._sendMetricsToDD(1, [
                                "REQUEST_TYPE:SMS_PARSING", 
                                'STATUS:PUBLISHED', 
                                "TYPE:NON_PAYTM_EVENTS", 
                                "OPERATOR:" + dataToBeInsertedInDB.operator,
                                `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                                `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`]);
                            self.logger.log("nonPaytmKafkaPublisher  Message published successfully in Kafka - MSG:- ", loggerData, "financial services");    
                        }
                    })
                    return next(null)
                }
            ], function (error) {
                if(error) {
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:SMSPCC_BILLS', 
                        'STATUS:ERROR',
                        'TYPE:CREATE_CC_BILL_SEND_NOTIFICATION',
                        `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                        `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`]);
                    self.L.error('nonPaytmKafkaPublisher',`Error occured for record having debugkey:${processedRecord.debugKey}`,error);
                    return done(error);    
                } else {
                    self.L.log('nonPaytmKafkaPublisher',`Record succesfully processed having debugkey:${processedRecord.debugKey}`);
                    return done(null, dataToPassCT);    
                }
            });
        } catch(err) {
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:SMSPCC_BILLS', 
                'STATUS:ERROR',
                'TYPE:CREATE_CC_BILL_SEND_NOTIFICATION_CATCH',
                `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`]);
            self.L.critical(`createCCBillAndSendNotification`,`Exception occured for record having debugkey:${processedRecord.debugKey}`,err)
            return done(err);
        }
    }

    /**
     * 
     * @param {*} done 
     * @param {*} processedRecord 
     */
    publishCtAndPFCCEvents(done, dbRecordResp,processedRecord, record) {
        let self = this;
        const operator = _.get(dbRecordResp, 'operator', '');
        const customerId = _.get(dbRecordResp, 'customer_id', '');
        const rechargeNumber = _.get(dbRecordResp, 'recharge_number', '');
        const referenceId = _.get(dbRecordResp, 'reference_id', '')
        const dbEvent = _.get(dbRecordResp, 'dbEvent', null)    //delete,upsert
        let eventName = _.get(self.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENTS', 'BILLGEN'], 'reminderBillGen')
        const dbDebugKey = `rech:${self.encryptionDecryptionHelper.encryptData(rechargeNumber)}::cust:${customerId}::op:${operator}::ref_id:${self.encryptionDecryptionHelper.encryptData(referenceId)}`;

        if(dbEvent=='upsert'){
            return done(null)
        }

        if(_.get(processedRecord,'isPartial',false) ===true  && (_.get(processedRecord,'action',null) === 'update' || _.get(processedRecord,'action',null) === 'update_skipNotify')) {
            eventName = 'partialbill_repeatuser';
        }

        let productId = _.get(dbRecordResp, 'product_id', '');
        productId = self.activePidLib.getActivePID(productId);
        _.set(dbRecordResp, 'product_id', productId);

        // if(_.get(processedRecord,'isOverdue',false)){
        //     _.set(dbRecordResp, 'isOverDue', _.get(processedRecord,'isOverdue',''));
        // }

        ASYNC.waterfall([
            next => {
                self.commonLib.getRetailerData((error) => {
                    if(error) {
                        return next(error)
                    } else {
                        return next(null)
                    }
                }, customerId, dbRecordResp);
            },
            next => {
                self.commonLib.getCvrData((error) => {
                    if(error) {
                        return next(error)
                    } else {
                        return next(null)
                    }
                }, productId, dbRecordResp);
            },
            next => {
                let mappedData = self.reminderUtils.createCTPipelinePayload(dbRecordResp, eventName, dbDebugKey);
                let clonedData = _.cloneDeep(mappedData); // needed for correct async logging
                let replicatedData = _.cloneDeep(mappedData);

                ASYNC.parallel([
                    function (cb) {
                        if (_.get(dbRecordResp, 'notification_status', 1)) {
                            if(_.get(record, "isBlocked", null) != null) {
                                if(_.get(record, "isBlocked", false) == true) _.set(replicatedData,"eventName","CCBlockedCardBillGen");
                                _.set(replicatedData, "isBlocked", _.get(record, "isBlocked"));
                                _.set(replicatedData, "paymentUnblocks", _.get(record, "paymentUnblocks"));
                            }
                            if(self.commonLib.isCTEventBlocked(replicatedData.eventName)){
                                self.L.info(`Blocking CT event ${replicatedData.eventName}`)
                                return cb()
                            }
                            self.ctKafkaPublisher.publishData([{
                                topic: _.get(self.config.KAFKA, 'SERVICES.CT_EVENTS_PUBLISHER.TOPIC', ''),
                                messages: JSON.stringify(replicatedData)
                            }], (error) => {
                                if (error) {
                                    utility._sendMetricsToDD(1, [
                                        "REQUEST_TYPE:SMS_PARSING", 
                                        'STATUS:ERROR', 
                                        "TYPE:CT_EVENTS", 
                                        `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                                        `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                                        "OPERATOR:" + operator]);
                                    self.logger.error(`publishInKafka :: publishCtEvents Error while publishing message in Kafka ${error} - MSG:- `, replicatedData, "financial services");    
                                } else {
                                    utility._sendMetricsToDD(1, [
                                        "REQUEST_TYPE:SMS_PARSING", 
                                        'STATUS:PUBLISHED', 
                                        "TYPE:CT_EVENTS", 
                                        `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                                        `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                                        "OPERATOR:" + operator,`EVENT_NAME:${replicatedData.eventName}`]);
                                    self.logger.log("publishInKafka :: publishCtEvents Message published successfully in Kafka - MSG:- ", replicatedData, "financial services");    
                                }
                                cb(error);
                            }, [200, 800]);
                            
                        } else {
                            self.L.error(`stop publishing data on CleverTap via the Kafka pipeline for notification status : ${_.get(dbRecordResp, 'notification_status', 0)} with debugKey::`, dbDebugKey);
                            cb();
                        }
                    },
                    function (cb) {
                        self.paytmFirstKafkaPublisher.publishData([{
                            topic: _.get(self.config.KAFKA, 'SERVICES.PAYTM_FIRST_CC_EVENTS_PUBLISHER.TOPIC', ''),
                            messages: JSON.stringify(mappedData)
                        }], (error) => {
                            if (error) {
                                utility._sendMetricsToDD(1, [
                                    "REQUEST_TYPE:SMS_PARSING", 
                                    'STATUS:ERROR', 
                                    "TYPE:PFCC_EVENTS", 
                                    `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                                    `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                                    "OPERATOR:" + operator]);
                                self.logger.error(`publishInKafka :: publishPFCCEvents Error while publishing message in Kafka - ${error} MSG:- `, clonedData, "financial services");    
                            } else {
                                utility._sendMetricsToDD(1, [
                                    "REQUEST_TYPE:SMS_PARSING", 
                                    'STATUS:PUBLISHED', 
                                    "TYPE:PFCC_EVENTS", 
                                    `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                                    `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                                    "OPERATOR:" + operator]);
                                self.logger.log("publishInKafka :: publishPFCCEvents Message published successfully in Kafka - MSG:- ", clonedData, "financial services");    
                            }
                            cb(error);
                        }, [200, 800]);
                    },
                ], function done(err) {
                    return next(err);
                });
            }
        ], error => {
            if(error) {
                self.L.error('publishCtAndPFCCEvents',`Exception occured Error Msg:: ${error} with debugKey::`, dbDebugKey);
            } else {
                self.L.log(`publishCtAndPFCCEvents`,`Record processed having debug key`, dbDebugKey);
            }
            return done(error);
        })
    }
    /**
     * Update bills_creditcard table and recents
     * @param {*} done 
     * @param {*} processedRecord 
     * @param {*} dbRecord 
     */
     createCCBillInSystem(done, processedRecord, dbRecord) {
        let self = this;
        self.bills.createCCBillForCustomerId(function (error) {
            if (error) {
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:SMSPCC_BILLS', 
                    'STATUS:ERROR',
                    'TYPE:DB_CREATE_CC_BILLS',
                    `BANK:${processedRecord.bankName}`,
                    `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                    `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`]);
                self.L.critical(`createCCBillInSystem::createCCBillForCustomerId`, `Error updating table for ${processedRecord.debugKey}_error:${error}`);
                return done(error);
            } else {
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:SMSPCC_BILLS', 
                    'STATUS:SUCCESS',
                    'TYPE:DB_CREATE_CC_BILLS',
                    `BANK:${processedRecord.bankName}`,
                    `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                    `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`]);
                let min_due_amount = _.get(processedRecord, 'currentMinBillAmount', null);
                if(min_due_amount == null){
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:SMSPCC_BILLS', 
                        'STATUS:SKIPPING_MIN_DUE_AMOUNT_CREATE_SUCCESS',
                        'TYPE:TOTAL',
                        `BANK:${processedRecord.bankName}`,
                        `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                        `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`]);
                }
                self.L.log(`createCCBillInSystem::createCCBillForCustomerId`, `Table Creation successful for ${processedRecord.debugKey}`);
                _.set(self.timestamps,'RUupdatesDbTime',new Date().getTime());
                return done(null);
            }
        }, self.tableName, dbRecord);
    }

    /**
     * if only one matching mcn(last 4 digits) is found
     *      returns single matching MCN as well as update action for given customer 
     * else if no matching mcn(last 4 digits) found 
     *      returns create action for given customer 
     * @param {*} done 
     * @param {*} record 
     */
    getActionforCCBills(done, processedRecord, analyticsPayload = {}) {
        let self = this;
        self.bills.getBillByCustomer(function (error, records) {
            if (error) {
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:SMSPCC_BILLS', 
                    'STATUS: ERROR',
                    'TYPE:ERROR_GETTING_RECORD_FROM_DB',
                    `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                    `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`]);
                self.L.critical(`checkOneCardForCustomer`, `failed with error for ${processedRecord.debugKey} and error:${error}`);
                return done(error);
            } else if (records && _.isArray(records) && records.length > 0) {
                let last4DigitsMatchingRecords = [];
                for (let index = 0; index < records.length; index++) {
                    
                    let recordBankName = records[index] && records[index].bank_name;
                    console.log("recordBankName print", recordBankName);
                    recordBankName = _.toLower(recordBankName);
                    let mappedBankName = _.get(self.config, ['DYNAMIC_CONFIG', 'BANKNAME_CONFIG', recordBankName, 'BANKNAME_MAPPING'], '');
                    let rechargeNumber = records[index].recharge_number.replace(/\s+/g, ''); // removing white space from RN. 1234 XXXX XX12 12 <-- to handle scenarioes like this
                    if (rechargeNumber && rechargeNumber.substr(rechargeNumber.length - processedRecord.lastCC.length) == processedRecord.lastCC  && mappedBankName == _.toLower(processedRecord.bankName)) {
                        if(records[index].par_id && records[index].par_id !== ''){
                            records[index].tokenisedCreditCard = true;
                        } else {
                            records[index].tokenisedCreditCard = false;
                        }
                        last4DigitsMatchingRecords.push(records[index]);
                    }
                }

                if (last4DigitsMatchingRecords.length === 0) {
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:SMSPCC_BILLS', 
                        'STATUS:NO_MATCHING_MCN',
                        `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                        `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`]);

                    return done(null, 'create' , null ,  "No records found with given last4Digits");
                } else if (last4DigitsMatchingRecords.length === 1) {
                    if(analyticsPayload.cassandraAnalyticsPayload){
                        analyticsPayload.cassandraAnalyticsPayload.user_type = "RU";
                    }

                    if(_.get(processedRecord,'isPartial', false) === false) {
                        let [err, record] = self.checkMandatoryParams(processedRecord, null, processedRecord, false, true);
                        if(err) {
                            return done(err);
                        }
                    }else{
                        let [skipPartialRecord,error] = self.shouldPartialRecordsBeSkipped(last4DigitsMatchingRecords[0], processedRecord);
                        if(error){
                            return done(error);
                        }
                    }

                    let [skipRecord,error] = self.shouldRecordBeSkipped(last4DigitsMatchingRecords[0], processedRecord);
                    if(error){
                        return done(error);
                    }
                    else if(skipRecord) {
                        utility._sendMetricsToDD(1, [
                            'REQUEST_TYPE:SMSPCC_BILLS', 
                            'STATUS:SKIPPING_MCN',
                            `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                            `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                            `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`]);
                        return done(null, 'update_skipNotify', last4DigitsMatchingRecords[0] , null );
                    } else {
                        utility._sendMetricsToDD(1, [
                            'REQUEST_TYPE:SMSPCC_BILLS', 
                            'STATUS:MATCHING_MCN',
                            `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                            `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                            `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`]);
                        return done(null, 'update', last4DigitsMatchingRecords[0] , null );
                    }
                }
                 //Adding this code-block to remove older format MCN and addition of newer format MCN
                else if(last4DigitsMatchingRecords.length > 1){
                    if(analyticsPayload.cassandraAnalyticsPayload){
                        analyticsPayload.cassandraAnalyticsPayload.user_type = "RU";
                    }
                    if(_.get(processedRecord,'isPartial',false) === false) {
                        let [err, record] = self.checkMandatoryParams(processedRecord, null, processedRecord, false, true);
                        if(err) {
                            return done(err);
                        }
                    }
                    self.getPriorityCard(function(error, result){
                        if(error) {
                            utility._sendMetricsToDD(1, [
                                'REQUEST_TYPE:SMSPCC_BILLS', 
                                'STATUS:ERROR',
                                `TYPE:FETCH_PRIORITY_CARD`,
                                `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                                `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`]);
                            return done(error);
                        }
                        else return done(null, 'update', result, null);
                    },last4DigitsMatchingRecords);
                } 
                else {
                    if(analyticsPayload.cassandraAnalyticsPayload){
                        analyticsPayload.cassandraAnalyticsPayload.user_type = "RU";
                    }
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:SMSPCC_BILLS', 
                        'STATUS:ERROR',
                        'TYPE:MULTIPLE_MATCHING_MCN',
                        `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                        `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`]);
                    return done("Multiple matching transactions !!");
                }
            } else {
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:SMSPCC_BILLS', 
                    'STATUS:NO_TXN_FOUND',
                    `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                    `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`]);
                return done(null, 'create' , null, 'Record not found');
            }
        }, self.tableName, processedRecord.customerId);
    }

    getPriorityCard(cb, last4DigitsMatchingRecords){
        let self = this;
        let olderCinCards = [], newerCinCards = [], tokenisedCards=[] , csvCards =[] ;
        for(let index=0; index < last4DigitsMatchingRecords.length;index++){
            let cin = _.get(last4DigitsMatchingRecords[index], 'reference_id', null);
            let par = _.get(last4DigitsMatchingRecords[index], 'par_id', null);
            if(par && par!=''){
                tokenisedCards.push(last4DigitsMatchingRecords[index]);
            }
            else if(cin && cin.toString().length > 4 && cin.slice(0,4) == "CSV_"){
                csvCards.push(last4DigitsMatchingRecords[index])
            }
            else if(cin && cin.toString().length > 40){
                olderCinCards.push(last4DigitsMatchingRecords[index]);
            }
            else if(cin){
                newerCinCards.push(last4DigitsMatchingRecords[index]);
            }
            
        }
        if(tokenisedCards.length >= 1){
            if(newerCinCards.length){
                for(var index = 0; index < newerCinCards.length; index++){
                    self.bills.deleteDuplicateCards(function (error) {
                        if (error) {
                            self.L.critical(`getActionforCCBills::getPriorityCard`, `Error deleting record for ${self.encryptionDecryptionHelper.encryptData(newerCinCards[0].recharge_number)}_${newerCinCards[0].customer_id}_error:${error}`);
                        } else {
                            self.L.log(`getActionforCCBills::getPriorityCard`, ` Deletion successful for ${self.encryptionDecryptionHelper.encryptData(newerCinCards[0].recharge_number)}_${newerCinCards[0].customer_id}`);
                        }
                    }, self.tableName, newerCinCards[index]);
                }
            }
            if(olderCinCards.length){
                for(var index = 0; index < olderCinCards.length; index++){
                    self.bills.deleteDuplicateCards(function (error) {
                        if (error) {
                            self.L.critical(`getActionforCCBills::getPriorityCard`, `Error deleting record for ${self.encryptionDecryptionHelper.encryptData(olderCinCards[0].recharge_number)}_${olderCinCards[0].customer_id}_error:${error}`);
                        } else {
                            self.L.log(`getActionforCCBills::getPriorityCard`, ` Deletion successful for ${self.encryptionDecryptionHelper.encryptData(olderCinCards[0].recharge_number)}_${olderCinCards[0].customer_id}`);
                        }
                    }, self.tableName, olderCinCards[index]);
                }
            }
            if(csvCards.length){
                for(var index = 0; index < csvCards.length; index++){
                    self.bills.deleteDuplicateCards(function (error) {
                        if (error) {
                            self.L.critical(`getActionforCCBills::getPriorityCard`, `Error deleting record for ${self.encryptionDecryptionHelper.encryptData(csvCards[0].recharge_number)}_${csvCards[0].customer_id}_error:${error}`);
                        } else {
                            self.L.log(`getActionforCCBills::getPriorityCard`, ` Deletion successful for ${self.encryptionDecryptionHelper.encryptData(csvCards[0].recharge_number)}_${csvCards[0].customer_id}`);
                        }
                    }, self.tableName, csvCards[index]);
                }
            }
            if(tokenisedCards.length==1) return cb(null,tokenisedCards[0]);
            else return self.smsParsingSyncCCBillLib.checkEncryptedCards(cb,tokenisedCards);
        }
        else if(newerCinCards.length >= 1){
            if(olderCinCards.length){
                for(var index = 0; index < olderCinCards.length; index++){
                    self.bills.deleteDuplicateCards(function (error) {
                        if (error) {
                            self.L.critical(`getActionforCCBills::getPriorityCard`, `Error deleting record for ${self.encryptionDecryptionHelper.encryptData(olderCinCards[0].recharge_number)}_${olderCinCards[0].customer_id}_error:${error}`);
                        } else {
                            self.L.log(`getActionforCCBills::getPriorityCard`, ` Deletion successful for ${self.encryptionDecryptionHelper.encryptData(olderCinCards[0].recharge_number)}_${olderCinCards[0].customer_id}`);
                        }
                    }, self.tableName, olderCinCards[index]);
                }
            }
            if(csvCards.length){
                for(var index = 0; index < csvCards.length; index++){
                    self.bills.deleteDuplicateCards(function (error) {
                        if (error) {
                            self.L.critical(`getActionforCCBills::getPriorityCard`, `Error deleting record for ${self.encryptionDecryptionHelper.encryptData(csvCards[0].recharge_number)}_${csvCards[0].customer_id}_error:${error}`);
                        } else {
                            self.L.log(`getActionforCCBills::getPriorityCard`, ` Deletion successful for ${self.encryptionDecryptionHelper.encryptData(csvCards[0].recharge_number)}_${csvCards[0].customer_id}`);
                        }
                    }, self.tableName, csvCards[index]);
                }
            }
            if(newerCinCards.length ==1) return cb(null,newerCinCards[0]);
            else return self.smsParsingSyncCCBillLib.checkEncryptedCards(cb,newerCinCards);
        } 
        else if(olderCinCards.length >= 1){
            if(csvCards.length){
                for(var index = 0; index < csvCards.length; index++){
                    self.bills.deleteDuplicateCards(function (error) {
                        if (error) {
                            self.L.critical(`getActionforCCBills::getPriorityCard`, `Error deleting record for ${self.encryptionDecryptionHelper.encryptData(csvCards[0].recharge_number)}_${csvCards[0].customer_id}_error:${error}`);
                        } else {
                            self.L.log(`getActionforCCBills::getPriorityCard`, ` Deletion successful for ${self.encryptionDecryptionHelper.encryptData(csvCards[0].recharge_number)}_${csvCards[0].customer_id}`);
                        }
                    }, self.tableName, csvCards[index]);
                }
            }
            if(olderCinCards.length==1) return cb(null, olderCinCards[0]);
            else return self.smsParsingSyncCCBillLib.checkEncryptedCards(cb,olderCinCards);
        }
        else{
            if(csvCards.length==1) return cb(null, csvCards[0]);
            else return self.smsParsingSyncCCBillLib.checkEncryptedCards(cb,csvCards);
        }
    }

    shouldPartialRecordsBeSkipped(dbRecord, processedRecord) {
        let self = this;
        
        try {
            let is_encrypted_card = _.get(dbRecord, 'is_encrypted', 0);
            const bufferDays = _.get(self.config, ['DYNAMIC_CONFIG', 'CC_BILLS_CONFIG', 'ACCEPT_PAST_DUE_DATE', 'DAYS'], 5);
    
            let smsAmount = (_.get(processedRecord, 'currentBillAmount',null) === null) ? null : Math.round(_.get(processedRecord, 'currentBillAmount', 0));
            let smsDueMinAmount = (_.get(processedRecord, 'currentMinBillAmount',null) === null) ? null : Math.round(_.get(processedRecord, 'currentMinBillAmount', 0));
            let paymentDate = _.get(dbRecord, 'payment_date', null);
            let now = MOMENT().utc();
            let dbBillDueDate = MOMENT(_.get(dbRecord, 'due_date', 0)).utc().format('YYYY-MM-DD HH:mm:ss');
            if(is_encrypted_card){
                if(_.get(dbRecord, 'due_date', null) && MOMENT(_.get(dbRecord, 'due_date', null)).isValid()){
                    dbBillDueDate = MOMENT.utc(_.get(dbRecord, 'due_date', 0)).format('YYYY-MM-DD HH:mm:ss');
                }else{
                    self.L.log("shouldPartialRecordsBeSkipped :: dbRecord is encrypted but due_date is not valid",JSON.stringify(dbRecord));
                    return [null, "shouldPartialRecordBeSkipped: dbRecord is encrypted but due_date is not valid, skipping the Payload"];
                }
            }
            let smsDueDate = (_.get(processedRecord, 'billDueDate', null) === null) ? null : MOMENT(_.get(processedRecord, 'billDueDate', 0));
    
            const customerOtherInfoObj = JSON.parse(_.get(dbRecord,'customerOtherInfo',null));
            const extra = JSON.parse(_.get(dbRecord,'extra',null));
    
            let dbSmsDateTime = _.get(customerOtherInfoObj, 'smsDateTime', null) || _.get(customerOtherInfoObj, 'sms_date_time', null) ;
            let smsDateTime = _.get(processedRecord, 'sms_date_time', null);

            let created_source = _.get(extra,'created_source',null);
    
            if (smsDueDate === null) {
                utility._sendMetricsToDD(1,[
                    'REQUEST_TYPE:SMSPCC_BILLS', 
                    'STATUS:ERROR',
                    'TYPE:SMS_DUE_DATE_NULL',
                    `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                    `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`]);
                return [null, "shouldPartialRecordBeSkipped: smsDueDate is null, skipping the Payload"];
            }
    
            if (smsAmount !== null && smsAmount <= 0) {
                utility._sendMetricsToDD(1,[
                    'REQUEST_TYPE:SMSPCC_BILLS', 
                    'STATUS:ERROR',
                    'TYPE:SMS_AMOUNT_NEGATIVE',
                    `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                    `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`]);
                return [null, `shouldPartialRecordBeSkipped: smsAmount is ${smsAmount}, skipping the Payload`];
            }
    
            if (smsDueDate.isSame(dbBillDueDate, 'day')) {
                utility._sendMetricsToDD(1,[
                    'REQUEST_TYPE:SMSPCC_BILLS', 
                    'STATUS:ERROR',
                    'TYPE:SMS_DUE_DATE_SAME_AS_DB_DUEDATE',
                    `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                    `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`]);
                return [null, "shouldPartialRecordBeSkipped: smsDueDate and dbBillDueDate are the same, skipping the Payload"];
            }
    
            if (smsDueDate.diff(MOMENT().startOf('day')) < 0) {
                if (dbSmsDateTime === null || smsDateTime === null || smsDateTime <= dbSmsDateTime || Math.abs(smsDueDate.diff(MOMENT().startOf('day'), 'days')) > bufferDays) {
                    utility._sendMetricsToDD(1,[
                        'REQUEST_TYPE:SMSPCC_BILLS', 
                        'STATUS:ERROR',
                        'TYPE:OLD_SMS_DATE_TIME'
                        `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                        `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`]);
                    return [null, `shouldPartialRecordBeSkipped: The dbSmsDateTime is ${dbSmsDateTime} , smsDateTime is ${smsDateTime} , smsDueDate is ${smsDueDate} , skipping the payload `];
                }
            }

            if(smsDueDate < MOMENT(dbBillDueDate)){
                self.L.log("shouldPartialRecordBeSkipped :: smsDueDate is less as compared to dbBillDueDate",JSON.stringify(processedRecord));
                utility._sendMetricsToDD(1,[
                    'REQUEST_TYPE:SMSPCC_BILLS', 
                    'STATUS:ERROR',
                    'TYPE:SMS_DUE_DATE_LESS_THAN_DB_DUEDATE',
                    `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                    `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`]);
                return [null,"PartialBill:: smsDueDate is less as compared to dbBillDueDate , skipping the payload "]
            }

            if(smsAmount === null && paymentDate === null  && (created_source === null || created_source !== 'validationSync')){
                utility._sendMetricsToDD(1,[
                    'REQUEST_TYPE:SMSPCC_BILLS', 
                    'STATUS:ERROR',
                    'TYPE:SMS_AMOUNT_NULL',
                    `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                    `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`]);
                return [null, `shouldPartialRecordBeSkipped: created_source is ${created_source} and paymentDate is ${paymentDate} and smsAmount is ${smsAmount} , skipping the payload `];
            }

            if(smsDueDate.isSame(MOMENT().startOf('day')) < 0 && paymentDate === null  && (created_source === null || created_source !== 'validationSync')){
                utility._sendMetricsToDD(1,[
                    'REQUEST_TYPE:SMSPCC_BILLS', 
                    'STATUS:ERROR',
                    'TYPE:SMS_DUE_DATE_IS_LESS',
                    `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                    `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`]);
                return [null, `shouldPartialRecordBeSkipped: created_source is ${created_source} and paymentDate is ${paymentDate} and smsDueDate is ${smsDueDate} , skipping the payload `];
            }

            return [false, null];

        } catch (error) {
            utility._sendMetricsToDD(1,[
                'REQUEST_TYPE:SMSPCC_BILLS', 
                'STATUS:EXCEPTION_OCCURED',
                'TYPE:COULD_NOT_REACH_SQL',
                `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`]);
            return [null, `shouldPartialRecordsBeSkipped: An error occurred : ${error}`];
        }
    }
    /**
     * skip record updation if the user has paid the cc bill in last x days
     * 
     * @param {ARRAY} dbRecord existing unique record of credit card 
     */
    shouldRecordBeSkipped(dbRecord, processedRecord){
        let self = this;
        try {
            const graceDays = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'SKIP_CC_NOTIFY', 'GRACE_DAYS'], 5)
            const graceAmount = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'SKIP_CC_NOTIFY', 'GRACE_AMOUNT'], 5)
            const extra = JSON.parse(_.get(dbRecord, 'extra', null));
            const billAmount = Math.round(extra.last_paid_amount);
            let smsAmount = (_.get(processedRecord, 'currentBillAmount', null) === null) ? null : Math.round(_.get(processedRecord, 'currentBillAmount', 0));
            let smsDueMinAmount = (_.get(processedRecord, 'currentMinBillAmount' , null) === null) ? null : Math.round(_.get(processedRecord, 'currentMinBillAmount', 0));
            let paymentDate = MOMENT(_.get(dbRecord, 'payment_date', 0)).utc();
            let now = MOMENT().utc();
            let dbBillDueDate = MOMENT(_.get(dbRecord,'due_date',0)).utc().format('YYYY-MM-DD HH:mm:ss');
            let smsDueDate=(_.get(processedRecord,'billDueDate' , null)===null)? null: MOMENT(_.get(processedRecord,'billDueDate',0));

            if(now.diff(paymentDate, 'days') < graceDays && smsAmount!=null && Math.abs(billAmount - smsAmount) < graceAmount){
                this.logger.log("shouldRecordBeSkipped :: Bill already paid in last x days ", processedRecord, dbRecord.service);
                
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:SMSPCC_BILLS', 
                'STATUS:CC_BILL_PAID',
                `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`]);

                return [true,null]
            }
            if(smsDueDate !== null && smsDueDate < MOMENT(dbBillDueDate)){
                this.logger.log("shouldRecordBeSkipped :: smsDueDate is less as compared to dbBillDueDate", processedRecord, dbRecord.service);
                utility._sendMetricsToDD(1,[
                    'REQUEST_TYPE:SMSPCC_BILLS', 
                    'STATUS:ERROR',
                    'TYPE:SMS_DUEDATE_LESS_THAN_DB_DUE_DATE',
                    `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                    `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`]);
                return [null,"smsDueDate is less as compared to dbBillDueDate"]
            }

            return [false,null]
        } catch (error) {
            this.logger.error("shouldRecordBeSkipped :: unable to get previous amount paid ", processedRecord, dbRecord.service);
            console.log('ShouldRecordBeSkipped ::The Error is ', error);
            return [false,null]
        }
    }

    checkUniquenessInRecents(done, params, processedRecord) {
        let self = this;
        return done(null, null); // removing dependency from mongo system

        // let recentUniqueRecord;
        // self.recentsLayerLib.getCCDetailsByMcnCustId(function (error, data) {
        //     if (error) {
        //         self.L.error('checkUniquenessInRecents', `Error updating recents for ${processedRecord.debugKey}_error:${error}`);
        //         utility._sendMetricsToDD(1,[
        //             'REQUEST_TYPE:SMSPCC_BILLS', 
        //             'STATUS : ERROR',
        //             `TYPE:FETCH_CC_DETAILS_FROM_RECENT`,
        //             `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
        //             `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
        //             `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`]);
        //         return done(error);
        //     } else {
        //         utility._sendMetricsToDD(1,[
        //             'REQUEST_TYPE:SMSPCC_BILLS', 
        //             'STATUS : SUCCESS',
        //             `TYPE:FETCH_CC_DETAILS_FROM_RECENT`,
        //             `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
        //             `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
        //             `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`]);
        //         let cinFromReminderDb =  _.get(params, 'par_id', null) ? null : _.get(params,'reference_id',null);
        //         let panUniqueReference = _.get(params, 'par_id', null);
        //         let lastFourMCN = _.get(params,'recharge_number').substr(-4);
        //         data = data.filter(row => row.recharge_number.substr(-4) == lastFourMCN); // removing other CC of customer
        //         for (let index = 0; index < data.length; index++) {
        //             //need to check PAR as well as reference ID
        //             if(panUniqueReference && panUniqueReference != data[index].panUniqueReference) return done(`different PAN:${data[index].panUniqueReference} exists`);
        //             if(panUniqueReference && panUniqueReference == data[index].panUniqueReference){
        //                 recentUniqueRecord =  data[index]
        //             }

        //             if(cinFromReminderDb && cinFromReminderDb != data[index].cin) return done(`different CIN:${data[index].cin} exists`);
        //             if(cinFromReminderDb && cinFromReminderDb == data[index].cin && !recentUniqueRecord){ // cant overwrte PAN record
        //                 recentUniqueRecord =  data[index]
        //             }
        //         }
        //         return done(null, recentUniqueRecord);
        //     }
        // }, params, "smsParsingCCBills");
    }

    /**
     * Checks if this bill should be updated in our system by checking old present data and incoming bills data
     * @param {*} processedRecord 
     * @param {*} dbRecord 
     * Response : [updateInDB, newBillCycle]
     */
    shouldUpdateCCBillInSystem(processedRecord, dbRecord) {
        if (!processedRecord || !dbRecord) return [false, false];
        let billDueDate = _.get(processedRecord, 'billDueDate', null),
            dbBillDueDate = _.get(dbRecord, 'due_date', null) ? MOMENT(_.get(dbRecord, 'due_date', null)).utc() : null;

        if (dbBillDueDate && billDueDate && billDueDate.diff(dbBillDueDate, 'days') == 0) { // Same Bill Cycle
            if(_.get(dbRecord, 'amount') === null){
                return [true, false];
            }

            if (_.get(dbRecord, 'amount', 0) > _.get(processedRecord, 'currentBillAmount')) { // Getting updated amount(less) -> we can update this amount but do not send notification
                return [true, false];
            } else {
                return [false, false];
            }
        } else {
            return [true, true];
        }
    }
    /**
     * Update bills_creditcard table and recents
     * @param {*} done 
     * @param {*} processedRecord 
     * @param {*} dbRecord 
     */
    updateCCBillInSystem(done, processedRecord, dbRecord, newBillCycle) {
        let self = this;
        self.bills.updateCCBillByCustomerId(function (error) {
            if (error) {
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:SMSPCC_BILLS',
                    'STATUS:ERROR',
                    'TYPE:UPDATE_DB',
                    `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                    `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`]);
                self.L.critical(`updateCCBillInSystem::updateCCBillByCustomerId`, `Error updating table for for ${processedRecord.debugKey}_error:${error}`);
            } else {
                let min_due_amount = _.get(processedRecord, 'currentMinBillAmount', null);
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:SMSPCC_BILLS',
                    'STATUS:SUCCESS',
                    'TYPE:UPDATE_DB',
                    `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                    `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`]);
                if(min_due_amount == null){
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:SMSPCC_BILLS', 
                        'STATUS:SKIPPING_MIN_DUE_AMOUNT_UPDATE_SUCCESS',
                        'TYPE:TOTAL',
                        `BANK:${processedRecord.bankName}`,
                        `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                        `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`]);
                }
                if(_.get(processedRecord,'isPartial',false) === true){
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:SMSPCC_BILLS', 
                        'STATUS:PARTIAL_BILL_UPDATE_SUCCESS',
                        'TYPE:TOTAL',
                        `BANK:${processedRecord.bankName}`,
                        `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                        `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`]);
                }
                self.L.log(`updateCCBillInSystem::updateCCBillByCustomerId`, `Table Updation successful for ${processedRecord.debugKey}`);
            }
            _.set(self.timestamps,'RUupdatesDbTime',new Date().getTime());
            return done(null);
        }, self.tableName, dbRecord);
    }
    
    /**
     * 
     * @param {*} done 
     * @param {*} processedRecord 
     * @param {*} dbRecord 
     */
    updateCCBillInRecentSystem(done, processedRecord, dbRecord, recentRecord) {
        /**
                 * After https://bitbucket.org/paytmteam/digital-reminder/pull-requests/1012/#chg-src/models/users.js PR merge we can directly call users.updateBillsInRecents method.
                 * Not doing as of now to reduce sanity scope
                 */
        let self = this;
        let queryParam = {
                recharge_number: _.get(dbRecord, 'recharge_number', null),
                operator: _.get(dbRecord, 'operator', null),
                paytype: _.get(dbRecord, 'paytype', null),
                service: _.get(dbRecord, 'service', null),
                reference_id: _.get(dbRecord, 'reference_id', null),
                customer_id: _.get(dbRecord, 'customer_id', null)
            }, fieldValue = {
                due_date: processedRecord.billDueDate.format('YYYY-MM-DD HH:mm:ss'),
                bill_date: processedRecord.billDate.format('YYYY-MM-DD'),
                amount: _.get(dbRecord, 'amount', null),
                min_due_amount: _.get(processedRecord, 'currentMinBillAmount', null),
                original_due_amount: _.get(dbRecord, 'amount', null),
                original_min_due_amount: _.get(processedRecord, 'currentMinBillAmount', null),
                label: _.get(dbRecord, 'amount', null) && _.get(dbRecord, 'due_date', null) ? `Bill Payment of Rs${_.get(dbRecord, 'amount', null)} due on ${processedRecord.billDueDate.format('DD MMM YYYY')}` : null
            };
        if(recentRecord && recentRecord.panUniqueReference && recentRecord.panUniqueReference !== '') {
            delete queryParam.reference_id;
            queryParam.panUniqueReference = recentRecord.panUniqueReference;
            queryParam.tin = recentRecord.tin;
        }
        self.recentsLayerLib.update(function (error) {
            _.set(self.timestamps,'RUupdateRecentTime',new Date().getTime());
            self.L.log('updateCCBillInSystem::recentsLayerLib.update', `update recents request completed for ${processedRecord.debugKey},error if any is:${error}`);
            return done(null);
        }, queryParam, "bills", [fieldValue], "smsParsingCCBills");

    }

    /**
     * Send Notification : Send PayLoad to Kafka Topic
     * @param {*} done 
     * @param {*} record 
     */
    sendNotification(done, record, processedRecord, analyticsPayload = {}) {
        let
            self = this,
            payload = {
                source: "smsParsingCCBillFetch",
                notificationType: "BILLGEN",
                data: self.commonLib.mapBillsTableColumns(record)
            };
            _.set(payload.data, 'rawLastCC', _.get(processedRecord, 'rawLastCC', null));
            if(_.get(processedRecord,'isRuSmsParsing', false)==true){
                _.set(payload, 'source', 'smsParsingCCBillFetchRealtime')
                utility.sendNotificationMetricsFromSource(payload)
                self.kafkaBillFetchRealtimePublisher.publishData([{
                    topic: _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE_REALTIME.REMINDER_BILL_FETCH_REALTIME', ''),
                    messages: JSON.stringify(payload)
                }], function (error) {
                    if (error) {
                        utility.sendNotificationMetricsFromSource(payload,"ERROR")
                        self.logger.error(`sendNotification :: kafkaBillFetchPublisher Error while publishing message in Kafka - ${error} MSG:- `, payload, _.get(record, 'service', null));
                    } else {
                        analyticsPayload.notificationPublishTime  = MOMENT().format('YYYY-MM-DD HH:mm:ss');
                        utility._sendMetricsToDD(1, [
                            'STATUS:PUBLISHED',
                            'TYPE:BILLGEN',
                            `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                            `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                            `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                            'REQUEST_TYPE:CC_SMSPARSING_BILLGEN_NOTIFICATION']);
                        self.logger.log("sendNotification :: kafkaBillFetchPublisher Message published successfully in Kafka", payload, _.get(record, 'service', null));    
                    }
                    return done(error, {});
                }, [200, 800]);
            }
            else if(self.smsParsingCCBillsDwhRealtime) {
                _.set(payload, 'source', 'smsParsingCCBillFetchDWHRealtime')
                utility.sendNotificationMetricsFromSource(payload)
                self.kafkaBillFetchRealtimePublisher.publishData([{
                    topic: _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE_REALTIME.REMINDER_BILL_FETCH_REALTIME', ''),
                    messages: JSON.stringify(payload)
                }], function (error) {
                    if (error) {
                        utility.sendNotificationMetricsFromSource(payload,"ERROR")
                        self.logger.error(`sendNotification :: kafkaBillFetchPublisher Error while publishing message in Kafka - ${error} MSG:- `, payload, _.get(record, 'service', null));
                    } else {
                        analyticsPayload.notificationPublishTime  = MOMENT().format('YYYY-MM-DD HH:mm:ss');
                        utility._sendMetricsToDD(1, [
                            'STATUS:PUBLISHED',
                            'TYPE:BILLGEN',
                            `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                            `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                            `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                            'REQUEST_TYPE:CC_SMSPARSING_BILLGEN_NOTIFICATION']);
                        self.logger.log("sendNotification :: kafkaBillFetchPublisher Message published successfully in Kafka", payload, _.get(record, 'service', null));    
                    }
                    return done(error, {});
                }, [200, 800]);
            }
            else{
                let toBeNotifiedRealtime = self.commonLib.decideTopicToPublishBillGen();
                if(!toBeNotifiedRealtime){
                    utility.sendNotificationMetricsFromSource(payload)
                    self.kafkaBillFetchPublisher.publishData([{
                        topic: _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE.REMINDER_BILL_FETCH', ''),
                        messages: JSON.stringify(payload)
                    }], function (error) {
                        if (error) {
                            utility.sendNotificationMetricsFromSource(payload,"ERROR")
                            self.logger.error(`sendNotification :: kafkaBillFetchPublisher Error while publishing message in Kafka - ${error} MSG:- `, payload, _.get(record, 'service', null));
                        } else {
                            utility._sendMetricsToDD(1, [
                                'STATUS:PUBLISHED',
                                'TYPE:BILLGEN',
                                `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                                `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                                'REQUEST_TYPE:CC_SMSPARSING_BILLGEN_NOTIFICATION']);
                            self.logger.log("sendNotification :: kafkaBillFetchPublisher Message published successfully in Kafka", payload, _.get(record, 'service', null));    
                        }
                        return done(error, {});
                    }, [200, 800]);
                }
                else{
                    payload.source = 'BillGenDwhCCSmsParsingRealTime';
                    utility.sendNotificationMetricsFromSource(payload)
                    self.kafkaBillFetchRealtimePublisher.publishData([{
                        topic: _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE_REALTIME.REMINDER_BILL_FETCH_REALTIME', ''),
                        messages: JSON.stringify(payload)
                    }], function (error) {
                        if (error) {
                            utility.sendNotificationMetricsFromSource(payload,"ERROR")
                            self.logger.error(`sendNotification :: kafkaBillFetchPublisherRealTime Error while publishing message in Kafka - ${error} MSG:- `, payload, _.get(record, 'service', null));
                        } else {
                            utility._sendMetricsToDD(1, [
                                'STATUS:PUBLISHED',
                                'TYPE:BILLGEN',
                                `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                                `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                                'REQUEST_TYPE:CC_SMSPARSING_BILLGEN_NOTIFICATION']);
                            self.logger.log("sendNotification :: kafkaBillFetchPublisherRealTime Message published successfully in Kafka", payload, _.get(record, 'service', null));    
                        }
                        return done(error, {});
                    }, [200, 800]);
                }
    }
}

    /**
     * 
     * @param {*} processedRecord 
     * @param {*} dbRecord 
     */
    getDbRecordToUpdate(processedRecord, dbRecord, newBillCycle) {
        let self = this;
        let updatedDbRecord = _.clone(dbRecord), dateFormat = 'YYYY-MM-DD HH:mm:ss';

        let currentBillAmount = _.get(processedRecord, 'currentBillAmount', 0);

        updatedDbRecord.amount = currentBillAmount < 0 ? 0 : currentBillAmount;
        updatedDbRecord.bill_date = processedRecord.billDate.format(dateFormat);
        updatedDbRecord.due_date =(_.get(processedRecord,'billDueDate')===null)? null : processedRecord.billDueDate.format(dateFormat);
        updatedDbRecord.next_bill_fetch_date = self.recentBillLibrary.decideNextBillFetchDate({...processedRecord , service:"financial services","productInfo_operator": updatedDbRecord['operator']}, self.cc_bills_operators, self.billSubscriber)['nextBillFetchDate'];
        updatedDbRecord.status = _.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4);
        if (newBillCycle) updatedDbRecord.bill_fetch_date = MOMENT().format(dateFormat);
        else updatedDbRecord.bill_fetch_date = MOMENT(dbRecord.bill_fetch_date).utc().format(dateFormat);
        updatedDbRecord.customerOtherInfo = _.clone(processedRecord);
        updatedDbRecord.customerOtherInfo.billDate = processedRecord.billDate.format(dateFormat);;
        updatedDbRecord.customerOtherInfo.billDueDate = (_.get(processedRecord,'billDueDate')===null)? null : processedRecord.billDueDate.format(dateFormat);
        updatedDbRecord.customerOtherInfo.currentBillAmount = currentBillAmount < 0 ? 0 : currentBillAmount;
        updatedDbRecord.customerOtherInfo.msgId = processedRecord.msgId;
        updatedDbRecord.customerOtherInfo.sms_date_time= _.get(processedRecord,'sms_date_time');
        updatedDbRecord.customerOtherInfo.sms_id= _.get(processedRecord,'sms_id');
        updatedDbRecord.customerOtherInfo.dwh_classId = _.get(processedRecord,'dwh_classId');
        updatedDbRecord.customerOtherInfo.sender_id = _.get(processedRecord,'sender_id');
        if(_.get(processedRecord, 'isRuSmsParsing', false)==true){
            updatedDbRecord.customerOtherInfo.isRuSmsParsing=true;
        } else if(self.smsParsingCCBillsDwhRealtime) {
            updatedDbRecord.customerOtherInfo.isDwhSmsParsingRealtime=true;
        }
        updatedDbRecord.customerOtherInfo = JSON.stringify(updatedDbRecord.customerOtherInfo);
        let extra = _.get(updatedDbRecord, 'extra', null);
        let tempExtra = extra ? JSON.parse(extra) : {};
        if(typeof tempExtra === 'string') {
            tempExtra ={};
            //self.L.critical('getDbRecordToUpdate',`Error in parsing extra ${extra}`);
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:EXTRA_PARSING_ERROR', `OPERATOR:${_.get(updatedDbRecord,'operator', '')}`, 'SOURCE:smsParsingCCBills', 'FIELD:extra']);
        }
        tempExtra.updated_source = 'sms';
        tempExtra.updated_data_source = 'SMS_PARSING_DWH';
        if(_.get(processedRecord, 'isRuSmsParsing', false)==true){
            tempExtra.isRuSmsParsing = true;
            tempExtra.updated_data_source = 'SMS_PARSING_REALTIME';
        } else if(self.smsParsingCCBillsDwhRealtime) {
            tempExtra.isDwhSmsParsingRealtime = true;
            tempExtra.updated_data_source = 'SMS_PARSING_DWH_REALTIME';
        }
        if(_.get(processedRecord, 'isPartial', false)==true){
            tempExtra.source_subtype_2='PARTIAL_BILL';
            updatedDbRecord.isPartial = true;
        }else{
            tempExtra.source_subtype_2='FULL_BILL';
        }
        updatedDbRecord.extra = JSON.stringify(tempExtra);

        try{
            updatedDbRecord = self.billsLib.updateRecordWithOffsetNbfd(updatedDbRecord, _.get(dbRecord, 'customer_bucket',null), _.get(dbRecord, 'published_date', null))
        }catch(e){
            self.L.error('getDbRecordToUpdate',`Error in updating record with offset nbfd ${e}`);
        }
        return updatedDbRecord;
    }

    /**
     * 
     * @param {*} record 
     */
    validateAndProcessRecord(record,cb, analyticsPayload = {}) {
        let self = this;
        
        if (!record) return ['Invalid record', record];
        try {
            analyticsPayload.customerId =  (typeof record.cId === 'number') ? record.cId : (typeof record.cId === 'string' && VALIDATOR.isNumeric(record.cId)) ? VALIDATOR.toInt(record.cId) : null;
            analyticsPayload.category = _.get(record, 'category', null);
            analyticsPayload.rtspId = _.get(record, 'rtspId', -1);
            if(analyticsPayload.cassandraAnalyticsPayload)
                analyticsPayload.cassandraAnalyticsPayload.customer_id = analyticsPayload.customerId;

            let kafkaPayloadSmsDateTime = null;
            if(self.smsParsingCCBillsDwhRealtime) {
                
                if (record.smsDateTime != null && (typeof record.smsDateTime == "string")) {
                    kafkaPayloadSmsDateTime = parseInt(record.smsDateTime);
                }
            }

            // set timestamps to checkk preformance delay on dashboard
            const   timestamp = new Date(record.timestamp).getTime(),
                    smsDateTime = new Date((kafkaPayloadSmsDateTime != null ? kafkaPayloadSmsDateTime :record.smsDateTime)).getTime(),
                    deviceDateTime = new Date(record.deviceDateTime).getTime(),
                    uploadTime = new Date(record.uploadTime).getTime(),
                    collector_timestamp = new Date(record.collector_timestamp).getTime(),
                    dwhKafkaPublishedTime = new Date(record.published_time).getTime();
            _.set(self.timestamps,'data_smsDateTime',smsDateTime);
            _.set(self.timestamps,'data_timestamp',timestamp);
            _.set(self.timestamps,'data_deviceDateTime',deviceDateTime);
            _.set(self.timestamps,'data_uploadTime',uploadTime);
            _.set(self.timestamps,'collector_timestamp',collector_timestamp);
            _.set(self.timestamps, 'RUreadsKafkaTime', self.RUreadsKafkaTime);
            _.set(self.timestamps, 'dwhKafkaPublishedTime', dwhKafkaPublishedTime);

            if (analyticsPayload.cassandraAnalyticsPayload) {
                if (MOMENT(smsDateTime).format('YYYY-MM-DD HH:mm:ss') == "Invalid date") {
                    analyticsPayload.cassandraAnalyticsPayload.sms_date_time = new MOMENT().format('YYYY-MM-DD HH:mm:ss');
                }
                else {
                    analyticsPayload.cassandraAnalyticsPayload.sms_date_time = MOMENT(smsDateTime).format('YYYY-MM-DD HH:mm:ss');
                }

                analyticsPayload.cassandraAnalyticsPayload.rawlastcc = _.get(record, 'lastCC', null);
                analyticsPayload.cassandraAnalyticsPayload.recharge_number = _.get(record, 'lastCC', null);
                analyticsPayload.cassandraAnalyticsPayload.raw_lastcc = _.get(record,'rawLastCC',null)
                analyticsPayload.cassandraAnalyticsPayload.due_amount = self.parseAmount(_.get(record, 'totalAmt', null));
                analyticsPayload.cassandraAnalyticsPayload.sender_id = _.get(record, 'smsSenderID', '');
                analyticsPayload.cassandraAnalyticsPayload.operator = _.get(record, 'bankName', null);
                analyticsPayload.cassandraAnalyticsPayload.sms_id = _.get(record, "msg_id", null);
                analyticsPayload.cassandraAnalyticsPayload.due_date = _.get(record, 'dueDate',null);
                analyticsPayload.cassandraAnalyticsPayload.bill_date = _.get(record, 'billDate',null);
                analyticsPayload.cassandraAnalyticsPayload.bill_fetch_date = MOMENT().format('YYYY-MM-DD HH:mm:ss');
                if (_.get(record, 'totalAmt', null) === null || _.get(record, 'dueDate', null) === null || _.get(record, 'dueAmt', null) === null) {
                    analyticsPayload.cassandraAnalyticsPayload.source_subtype_2 = "PARTIAL_BILL";
                }
                else {
                    analyticsPayload.cassandraAnalyticsPayload.source_subtype_2 = "FULL_BILL";
                }
            }

            ASYNC.waterfall([
                next => {
                    
                    let bankName = _.get(record, 'bankName',null);
                    let lastCC = _.get(record, 'lastCC', null);
                    if(!lastCC && bankName && _.get(self.config, ['DYNAMIC_CONFIG','CC_BILLS_CONFIG','NO_MCN_BANKS','BANK_LIST'], []).includes(bankName)){
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:SMSPCC_BILLS', 
                        'STATUS:NO_MCN_VALID_RECORDS',
                        'TYPE:TOTAL',
                        `BANK:${bankName}`,
                        `APP_VERSION:${_.get(record,'appVersion', '')}`,
                        `APP_COUNT:${_.get(record,'appCount', null)}`,
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`]);
                    self.getMcnByBank(record,bankName,function(error,result){
                        if(!error){
                            self.L.log(`validateAndProcessRecord`,`found ${result} as lastCC for record ${JSON.stringify(record)}`);
                            utility._sendMetricsToDD(1, [
                                'REQUEST_TYPE:SMSPCC_BILLS', 
                                'STATUS:NO_MCN_VALID_RECORDS',
                                'TYPE:MCN_ADDED',
                                `BANK:${bankName}`,
                                `APP_VERSION:${_.get(record,'appVersion', '')}`,
                                `APP_COUNT:${_.get(record,'appCount', null)}`,
                                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`]);
                            _.set(record, 'lastCC', result);
                            return next(null,record)
                        } else{
                            utility._sendMetricsToDD(1, [
                                'REQUEST_TYPE:SMSPCC_BILLS', 
                                'STATUS:ERROR',
                                'TYPE:LAST_CC_MISSING',
                                `BANK:${bankName}`,
                                `APP_VERSION:${_.get(record,'appVersion', '')}`,
                                `APP_COUNT:${_.get(record,'appCount', null)}`,
                                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`]);
                            return next(`Mandatory Params lastCC is Missing / Invalid`, record);
                        }
                    })
                } else{
                    return next(null,record)
                }
                },
                (record, next) => {
                    self.checkMandatoryParams(record,next,null,true,false, analyticsPayload);
                }
            ], function (error,processedRecord) {
                if(error) {
                    return cb(error);    
                } else {
                    return cb(null,processedRecord);    
                }
            });
        } catch(err) {
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:SMSPCC_BILLS', 
                'STATUS:ERROR',
                'TYPE:VALIDATE_FAILURE',
                `APP_VERSION:${_.get(record,'appVersion', '')}`,
                `APP_COUNT:${_.get(record,'appCount', null)}`,
                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`]);
            self.L.critical(`validateAndProcessRecord`,`Exception occured for record:${JSON.stringify(record)}`,err)
            return cb(err);
        }
    }

    checkMandatoryParams(record, cb, alreadyProcessedRecord, checkMandatoryParamsSet1 = true, checkMandatoryParamsSet2 = false, analyticsPayload = {}){
        let dateFormat = 'YYYY-MM-DD';
        let processedRecord;
        let mandatoryParams;
        let self = this;
        if(checkMandatoryParamsSet1){
            processedRecord = {
                customerId: (typeof record.cId === 'number') ? record.cId : (typeof record.cId === 'string' && VALIDATOR.isNumeric(record.cId)) ? VALIDATOR.toInt(record.cId) : null,
                lastCC: _.get(record, 'lastCC', ''),
                rawLastCC : _.get(record, 'lastCC', ''),
                currentBillAmount: self.parseAmount(_.get(record, 'totalAmt', null)),
                currentMinBillAmount: self.parseAmount(_.get(record, 'dueAmt', null)),
                billDate: record.billDate && MOMENT(record.billDate, dateFormat,true).isValid() ? MOMENT(record.billDate, dateFormat) : MOMENT().startOf('day'),
                billDueDate: record.dueDate && MOMENT(record.dueDate, dateFormat,true).isValid() ? MOMENT(record.dueDate, dateFormat) : null,
                sender_id: _.get(record, 'smsSenderID', ''),
                sms_id : _.get(record, 'sms_id', ''),
                dwh_classId: _.get(record, 'dwh_classId', ''),
                bankName: _.get(record,'bankName', ''),
                billConsumeTimestamp: MOMENT().format('YYYY-MM-DD HH:mm:ss'),
                msgId : _.get(record, 'msg_id', ''),
                appCount : _.get(record, 'appCount', null),
                appVersion : _.get(record, 'appVersion', ''),
                rtspId : _.get(record, 'rtspId', -1),
                category: _.get(record, 'category', null),
                refId: _.get(analyticsPayload, "refId", null),
                sms_date_time: new Date(record.smsDateTime).getTime(),
                isPartial :false,
                dwhKafkaPublishedTime: _.get(record, 'published_time', null),
            };
            
            if(processedRecord.currentMinBillAmount < 0) processedRecord.currentMinBillAmount = Math.abs(processedRecord.currentMinBillAmount);

            if(_.get(record, 'isRuSmsParsing', false)==true){
                _.set(processedRecord, 'isRuSmsParsing', true)
            } else if(self.smsParsingCCBillsDwhRealtime) {
                _.set(processedRecord, 'isDwhSmsParsingRealtime', true)
            }
            mandatoryParams = ['customerId', 'lastCC', 'billDate'];

            if(_.get(record, 'totalAmt', null) === null || _.get(record, 'dueDate', null) === null || _.get(record , 'dueAmt', null) === null){
                _.set(processedRecord, 'isPartial',true);

                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:SMSPCC_BILLS', 
                    'STATUS:PARTIAL_BILL',
                    'TYPE:TOTAL',
                    `BANK:${processedRecord.bankName}`,
                    `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                    `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`]);
            }

            if(_.get(processedRecord,'isPartial',false) === true &&  _.get(record,'totalAmt',null) !==null  && typeof(_.get(record, 'totalAmt')=== 'number') && !(_.get(record,'totalAmt'))) {
                _.set(processedRecord, 'currentBillAmount',0);
            }
        }
    
        if(checkMandatoryParamsSet2) {
            processedRecord = alreadyProcessedRecord;
            mandatoryParams = ['currentBillAmount', 'billDueDate'];
        }
    
        let invalidParams = [];
    
        mandatoryParams.forEach(function (key) {
            if (!processedRecord[key]) invalidParams.push(key);
        });
    
        // checking currentMinBillAmount exists and has value >= 0 since 0 is also valid amount
        if(checkMandatoryParamsSet2) {
            if(!_.get(self.config, ['DYNAMIC_CONFIG','CC_BILLS_CONFIG','NO_MINDUE_AMOUNT_BANKS','BANK_LIST'], []).includes(processedRecord.bankName)){
                if(typeof processedRecord['currentMinBillAmount'] != 'number' || processedRecord['currentMinBillAmount'] < 0) invalidParams.push('currentMinBillAmount');
            }
            else{
                self.L.log(`Skipping MINDUE_AMOUNT ${record}`);
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:SMSPCC_BILLS', 
                    'STATUS:SKIPPING_MIN_DUE_AMOUNT',
                    'TYPE:TOTAL',
                    `BANK:${processedRecord.bankName}`,
                    `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                    `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`]);
            }
        }
    
        if(checkMandatoryParamsSet1){    
            if( _.get(processedRecord, 'lastCC', null)=='null'){
                invalidParams.push('lastCC');
            }
    
            if(processedRecord.lastCC) {
                processedRecord.lastCC = processedRecord.lastCC + '';
                // Lets send lastCC for analytics purpose
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:SMSPCC_BILLS', 
                    `LASTCCLEN:${processedRecord.lastCC.length}`,
                    `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                    `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`]);
                if(processedRecord.lastCC.length < 2) invalidParams.push(`lastCC-length-${processedRecord.lastCC.length}`);// We  expecting at least last 2 digits
                else if (processedRecord.lastCC.length >= 5) { // greater then equal to 5 digits
                    processedRecord.lastCC = processedRecord.lastCC.slice(processedRecord.lastCC.length-4);
                }
            }
            processedRecord.debugKey = `smsSenderID:${processedRecord.sender_id}_custId:${processedRecord.customerId}_lastCC:${this.encryptionDecryptionHelper.encryptData(processedRecord.lastCC)}`;
        }
    
        if (invalidParams.length > 0){
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:SMSPCC_BILLS', 
                'STATUS: ERROR',
                `TYPE:MANDATORY_PARAMS_MISSING`,
                `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`]);
             return cb != null ? cb (`Mandatory Params ${invalidParams} is Missing / Invalid`, record) : [`Mandatory Params ${invalidParams} is Missing / Invalid`, record];
        }
    
        if (checkMandatoryParamsSet2 && processedRecord.billDueDate && processedRecord.billDueDate.diff(MOMENT().startOf('day')) < 0){
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:SMSPCC_BILLS', 
                'STATUS: ERROR',
                `TYPE:OLD_BILL_WITH_PAST_DUE_DATE`,
                `APP_VERSION:${_.get(processedRecord,'appVersion', '')}`,
                `APP_COUNT:${_.get(processedRecord,'appCount', null)}`,
                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`]);
            return cb != null ? cb(`Old Bill received with past due date`, record) : [`Old Bill received with past due date`, record];
        }
        
        return cb != null ? cb(null, processedRecord) : [null, processedRecord];
    }

    /**
     * 
     * @param {*} amountStr 
     */
    parseAmount(amountStr) { 
        if (!amountStr) return null;
        if (typeof amountStr === 'number') return amountStr;
        if (typeof amountStr === 'string' && VALIDATOR.isNumeric(amountStr)) return VALIDATOR.toFloat(amountStr);
        // case of "Rs.x.y" i.e. "Rs.101.54"
        let foundMatch = amountStr.match(new RegExp(/Rs[\s.]*([-+]?\d*(?:\.\d*)?)/));
        let parsedAmount = (foundMatch && foundMatch[1]) ? VALIDATOR.toFloat(foundMatch[1]) : null;
        if(parsedAmount || (typeof(parsedAmount) == "number" && parsedAmount >= 0)) return parsedAmount;
        //case of "x.y" i.e. "101.54"
        let foundMatch2 = amountStr.match(new RegExp(/([-+]?\d*(?:\.\d*)?)/));
        let parsedAmount2 = (foundMatch2 && foundMatch2[1]) ? VALIDATOR.toFloat(foundMatch2[1]) : null;
        return parsedAmount2;
    }

    getMcnByBank(record, bankName, cb){
        let self= this;
        let customerId = (typeof _.get(record, 'cId', null) === 'number') ? _.get(record, 'cId', null) : (typeof _.get(record, 'cId', null) === 'string' && VALIDATOR.isNumeric(_.get(record, 'cId', null))) ? VALIDATOR.toInt(_.get(record, 'cId', null)) : null;
        if(!customerId) return cb('CustomerId not found');
        self.L.log(`getMcnByBank`,`Going to fetch MCN from DB by bankName for customer_id: ${customerId}`)
        self.bills.getBillByCustomer(function (error, result) {
            if (error) {
                self.L.critical(`getMcnByBank`, `failed with error for ${JSON.stringify(record)} and error:${error}`);
                return cb(error);
            } else if (result && _.isArray(result) && result.length > 0) {
                let bankNameMatchingRecords = [];
                for (let index = 0; index < result.length; index++) {
                    let dbBankName = _.get(self.config, ['DYNAMIC_CONFIG','CC_BILLS_CONFIG','DWH_BANK_NAME_MAPPING',result[index].bank_name], null);

                    if (dbBankName && dbBankName == bankName) {
                        bankNameMatchingRecords.push(result[index]);
                    }
                }
                self.L.log(`getMcnByBank`,`Found ${bankNameMatchingRecords.length} matching bankName records for customer_id: ${customerId}`)
                if(bankNameMatchingRecords.length!==1){
                    if(bankNameMatchingRecords==0){
                        utility._sendMetricsToDD(1, [
                            'REQUEST_TYPE:SMSPCC_BILLS', 
                            'STATUS:NO_MCN_VALID_RECORDS',
                            'TYPE:NO_MATCHING_MCN',
                            `APP_VERSION:${_.get(record,'appVersion', '')}`,
                            `APP_COUNT:${_.get(record,'appCount', null)}`,
                            `BANK:${bankName}`,
                            `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`]);
                        return cb('No matching bankName record found');
                    }else{
                        utility._sendMetricsToDD(1, [
                            'REQUEST_TYPE:SMSPCC_BILLS', 
                            'STATUS:NO_MCN_VALID_RECORDS',
                            'TYPE:MULTIPLE_MATCHING_MCN',
                            `BANK:${bankName}`,
                            `APP_VERSION:${_.get(record,'appVersion', '')}`,
                            `APP_COUNT:${_.get(record,'appCount', null)}`,
                            `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`]);
                        return cb('Multiple matching bankName record found');
                    }
                }
            return cb(null,self.extractLastCC(bankNameMatchingRecords));
            }
            else{
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:SMSPCC_BILLS', 
                    'STATUS:NO_MCN_VALID_RECORDS',
                    'TYPE:NO_MATCHING_MCN',
                    `BANK:${bankName}`,
                    `APP_VERSION:${_.get(record,'appVersion', '')}`,
                    `APP_COUNT:${_.get(record,'appCount', null)}`,
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`]);
                return cb('No cards found for cust_id');
            }
        },this.tableName,customerId)
    }

    extractLastCC(record){  // how to handle?
        let revRechargeNumber = record[0].recharge_number;
        revRechargeNumber =  revRechargeNumber.replace(/ /g,'')
        revRechargeNumber = revRechargeNumber.split("").reverse()
        let lastCClen=4, i = 0, count = 0;

        while (i < revRechargeNumber.length) {
            if(count <= lastCClen) {
                if(!isNaN(revRechargeNumber[i])){
                    count = count+1
                }
            } else{
                if(revRechargeNumber[i] !=' ' && !isNaN(revRechargeNumber[i])){
                    revRechargeNumber[i] = 'X'
                }
            }
            i++
        }
        revRechargeNumber = revRechargeNumber.reverse()
        revRechargeNumber = revRechargeNumber.slice(-4);
        let lastDigits = revRechargeNumber.filter( r => Number.isInteger(parseInt(r)));
        return lastDigits.join("");
    }

     suspendOperations(){
        var self        = this,
        deferred = Q.defer();

        self.L.log(`SmsParsingCCBills::suspendOperations kafka consumer shutdown initiated`);
    
        Q()
        .then(function(){
            self.kafkaSMSParsingConsumer.close(function(error, res){
                if(error){
                    self.L.error(`SmsParsingCCBills::stopConsumer :: Error Stopping Consumer Err : ${error}`);
                    return error;
                }
                self.L.info(`SmsParsingCCBills::stopConsumer :: Consumer Stopped!  Response : ${res}`);
            })
        })
        .then(function(){
            self.L.log(`SmsParsingCCBills::suspendOperations kafka consumer shutdown successful`);
            deferred.resolve();
        })
        .catch(function(err){
            self.L.error(`SmsParsingCCBills::suspendOperations error in shutting kafka consumer`, err);
            deferred.reject(err);
        });
        return deferred.promise;
    }
}

export default SmsParsingCCBills;