import AWS from 'aws-sdk';
let s3 = new AWS.S3();
let bucketName = 'digital-reminder/slack-alerts';

export default class {
    constructor(options) {
        this.L = options.L;
    }

    getS3Path = (date = new Date()) => {
        let self = this;
        let year = date.getFullYear();
        let month = date.getMonth() + 1;
        let day = date.getDate();
        return `${year}/${month}/${day}/slack-alerts.json`;
    }

    handleAlertHomeEndpoint(req, res) {
        let self = this;
        self.L.info('Home endpoint hit');
        res.status(200).send('Server is running!');
    }

    async handleAlertSlackEvents(req, res) {
        let self = this;
        try {
            self.L.info('Slack events endpoint hit');
            self.L.info('Received data:', req.body);

            // Handle URL verification challenge
            if (req.body && req.body.type === 'url_verification') {
                const challenge = req.body.challenge;
                self.L.info('Challenge received:', challenge);
                return res.json({ challenge });
            }

            // Handle other events
            if (req.body.event) {
                const event = req.body.event;
                self.L.info('Event received:', event);
                // Process other events here

                if (event.attachments && event.attachments.length > 0) {
                    const attachment = event.attachments[0];
                    
                    // Parse alert details
                    const alertDetails = self.parseAlertDetails(attachment, event);
                    
                    // Store alert in S3
                    await self.storeAlertInS3(alertDetails);
                }
            }

            return res.json({ status: 'ok' });
        } catch (error) {
            self.L.error('Error occurred:', error);
            res.status(500).json({ error: error.message });
        }
    }

    parseAlertDetails(attachment, event) {
        let self = this;
        // Extract title components
        const titleParts = attachment.title.split('||').map(part => part.trim());
        const alertName = titleParts[0].replace('[FIRING:1]', '').trim();
        
        // Parse labels from text
        const labels = {};
        if (attachment.text) {
            const labelMatches = attachment.text.match(/\*Labels\*:(.*?)(?=\n\n|\*)/s);
            if (labelMatches) {
                labelMatches[1].split('|').forEach(label => {
                    const [key, value] = label.split(':').map(s => s.trim());
                    if (key && value) labels[key] = value;
                });
            }
        }
    
        return {
            title: alertName,
            severity: labels.severity || 'unknown',
            techTeam: labels.techteam || 'unknown',
            description: attachment.text,
            sourceUrl: attachment.title_link,
            timestamp: new Date(parseInt(event.event_ts.split('.')[0]) * 1000),
            alertId: `${alertName}-${labels.techteam}`.replace(/[^a-zA-Z0-9-]/g, '-')
        };
    }

    async storeAlertInS3(alertDetails) {
        let self = this;
        const date = new Date(alertDetails.timestamp);
        const s3Path = self.getS3Path(date);
        const fileName = `${alertDetails.timestamp.getTime()}-${alertDetails.alertId}.json`;
    
        try {
            await s3.putObject({
                Bucket: bucketName,
                Key: `${s3Path}/${fileName}`,
                Body: JSON.stringify(alertDetails),
                ContentType: 'application/json'
            }).promise();
    
            self.L.info(`Alert stored in S3: ${s3Path}/${fileName}`);
        } catch (error) {
            self.L.error('Error storing alert in S3:', error);
            throw error;
        }
    }

    handleAlertOAuthRedirect(req, res) {
        let self = this;
        try {
            self.L.info('OAuth redirect endpoint hit');
            const code = req.query.code;

            if (code) {
                self.L.info('Received OAuth code');
                // Here you would typically exchange the code for an access token
                return res.status(200).send('Successfully authenticated with Slack!');
            } else {
                self.L.error('No OAuth code received');
                return res.status(400).send('Authentication failed');
            }
        } catch (error) {
            self.L.error('OAuth error:', error);
            res.status(500).send('Internal Server Error');
        }
    }
}
