import MOMENT from 'moment'
import _ from 'lodash'
import utility from '../lib'
import EncryptorDecryptor from 'encrypt_decrypt';

class CassandraDb {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.client = options.cassandraDbClient;
        this.cryptr = new EncryptorDecryptor();
    }

    insertData(tableName, fields = [], params = [], tags = []) {
        if (fields.length === 0 || fields.length > params.length) {
            this.L.error('CassandraDb :: insertData ', 'Input parameters are wrong');
            return Promise.resolve('Input parameters are wrong');
        } else {
            let query = this.buildInsertQuery(tableName, fields);
            
            return new Promise((resolve, reject) => {
                var latencyStart = new Date().getTime();
                this.client.execute(query, params, { prepare: true })
                    .then(() => {
                        utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'CASSANDRA_INSERT', 'TYPE': 'insertData' });
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:CASSANDRA_INSERT", 'STATUS:SUCCESS', `SOURCE:${tags[0]}`]);
                        this.L.log('CassandraDb :: insertData ', `Data inserted successfully in cassandra for ${JSON.stringify(params)}`);
                        resolve();
                    })
                    .catch(error => {
                        utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'CASSANDRA_INSERT', 'TYPE': 'insertData' });
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:CASSANDRA_INSERT", 'STATUS:FAILED', `SOURCE:${tags[0]}`]);
                        this.L.error('CassandraDb :: insertData ', `Data insertion failed in cassandra for ${JSON.stringify(params)}, due to error:: ${error}`);
                        resolve();
                    })
            });
        }
    }

    selectData(tableName, whereClause, params = [], tags = []) {
        let query = this.buildSelectQuery(tableName, whereClause);
        return new Promise((resolve, reject) => {
            var latencyStart = new Date().getTime();
            this.client.execute(query, params, { prepare: true })
                .then(result => {
                    utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'CASSANDRA_SELECT', 'TYPE': 'selectData' });
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:CASSANDRA_SELECT", 'STATUS:SUCCESS', `SOURCE:${tags[0]}`]);
                    this.L.log('CassandraDb :: selectData ', `Data selected successfully from cassandra for ${JSON.stringify(params)}`);
                    resolve(result.rows);
                })
                .catch(error => {
                    utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'CASSANDRA_SELECT', 'TYPE': 'selectData' });
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:CASSANDRA_SELECT", 'STATUS:FAILED', `SOURCE:${tags[0]}`]);
                    this.L.error('CassandraDb :: selectData ', `Data selection/fetch failed from cassandra for ${JSON.stringify(params)}, due to error:: ${error}`);
                    resolve(null);
                })
        });
    }

    doesTableExists(tableName, tags = []) {
        let query = `SELECT table_name FROM system_schema.tables WHERE keyspace_name = 'reminder' AND table_name = ${tableName};`;

        return new Promise((resolve, reject) => {
            var latencyStart = new Date().getTime();
            this.client.execute(query, [], { prepare: true })
                .then(result => {
                    utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'CASSANDRA_TABLE_EXIST', 'TYPE': 'doesTableExists' });
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:CASSANDRA_TABLE_EXIST", 'STATUS:SUCCESS', `SOURCE:${tags[0]}`]);
                    this.L.log('CassandraDb :: doesTableExists ', `Table exists check successful from cassandra for ${tableName}`);
                    if (!result.rows || result.rows.length < 1) {
                        resolve(false);
                    } else {
                        resolve(true);
                    }
                })
                .catch(error => {
                    utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'CASSANDRA_TABLE_EXIST', 'TYPE': 'doesTableExists' });
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:CASSANDRA_TABLE_EXIST", 'STATUS:FAILED', `SOURCE:${tags[0]}`]);
                    this.L.error('CassandraDb :: doesTableExists ', `Table exists check failed from cassandra due to error:: ${error}`);
                    resolve(false);
                })
        });
    }

    createTableIfNotExist(tableName, fieldDetails = [], tags = []) {
        let query = `CREATE TABLE IF NOT EXISTS reminder.${tableName} (`;

        fieldDetails.forEach(field => {
            query += field + ',';
        });

        query = query.replace(/,\s*$/, "");

        query += `);`;

        return new Promise((resolve, reject) => {
            var latencyStart = new Date().getTime();
            this.client.execute(query, null, { prepare: true })
                .then(() => {
                    utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'CASSANDRA_TABLE_CREATE', 'TYPE': 'createTableIfNotExist' });
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:CASSANDRA_TABLE_CREATE", 'STATUS:SUCCESS', `SOURCE:${tags[0]}`]);
                    this.L.log('CassandraDb :: createTableIfNotExist ', `Table created successfully in cassandra for ${fieldDetails}`);
                    resolve();
                })
                .catch(error => {
                    utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'CASSANDRA_TABLE_CREATE', 'TYPE': 'createTableIfNotExist' });
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:CASSANDRA_TABLE_CREATE", 'STATUS:FAILED', `SOURCE:${tags[0]}`]);
                    this.L.error('CassandraDb :: createTableIfNotExist ', `Table creation failed in cassandra for ${fieldDetails}, due to error:: ${error}`);
                    resolve();
                })
        });
    }

    createIndexes(tableName, indexFields = [], tags = []) {
        let query = `CREATE INDEX IF NOT EXISTS ? ON reminder.${tableName} (`;

        indexFields.forEach(field => {
            let query1 = query + '?);';

            new Promise((resolve, reject) => {
                var latencyStart = new Date().getTime();
                this.client.execute(query1, ["idx_" + field, field], { prepare: true })
                    .then(() => {
                        utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'CASSANDRA_TABLE_INDEX', 'TYPE': 'createIndexes' });
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:CASSANDRA_TABLE_INDEX", 'STATUS:SUCCESS', `SOURCE:${tags[0]}`]);
                        this.L.log('CassandraDb :: createIndexes ', `Table index created successfully in cassandra for ${indexFields}`);
                        resolve();
                    })
                    .catch(error => {
                        utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'CASSANDRA_TABLE_INDEX', 'TYPE': 'createIndexes' });
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:CASSANDRA_TABLE_INDEX", 'STATUS:FAILED', `SOURCE:${tags[0]}`]);
                        this.L.error('CassandraDb :: createIndexes ', `Table index creation failed in cassandra for ${indexFields}, due to error:: ${error}`);
                        resolve();
                    })
            }).then(() => { }).catch((error) => { });
        });
    }

    buildInsertQuery(tableName, fields) {
        let query = `INSERT INTO ${tableName} (`;

        let tempParams = '';
        fields.forEach(val => {
            query += val + ',';
            tempParams += '?,';
        });

        query = query.replace(/,\s*$/, "");
        tempParams = tempParams.replace(/,\s*$/, "");

        query += ') VALUES (';

        return query += tempParams + ');';
    }

    buildSelectQuery(tableName, whereClauses) {
        let query = `SELECT * FROM ${tableName}`;

        if (whereClauses) {
            query += ' WHERE ' + whereClauses;
        }
        return query += ';';
    }

    buildUpdateQuery(tableName, fields, whereClauses) {
        let query = `UPDATE ${tableName} SET `;

        fields.forEach(val => {
            query += val + ' = ?,';
        });

        query = query.replace(/,\s*$/, "");

        if (whereClauses) {
            query += ' WHERE ' + whereClauses;
        }
        return query += ';';
    }
}

export default CassandraDb;