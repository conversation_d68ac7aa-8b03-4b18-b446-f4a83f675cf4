    import _ from 'lodash'
    import utility from '../lib'

class CatalogVerticalRecharge {
    constructor (options) {
        this.L = options.L;
        this.dbInstance = options.dbInstance;
    }

    getCvrData(callback, condition) {
        let self = this,
            query = 'SELECT * from catalog_vertical_recharge';
        if(condition){
            query += ' where '+condition;
        }
        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'getCvrData'});
            if (err) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getCvrData`]);
                self.L.error('getCvrData: ', 'error occurred while getting cvr data from the DB: ', query, err);
            }
            callback(err, data);
        }, 'FS_RECHARGE_SLAVE1', query, []);
    }

    /**
     * 
     * @param {*} callback of calling function
     * @param {*} condition on which we would fetch data
     * @param {*} columns string with comma seperated columns "id,product_id" - Default(*)
     */
    getSpecificDataFromCvr(callback, condition,columns) {
        let self = this,
            query = '';

        if(_.isEmpty(columns)) columns = "product_id, category_id, brand, paytype, service, operator, circle, operator_label, min_amount, attributes, thumbnail, status";
        
        query = `SELECT ${columns} from catalog_vertical_recharge`;
        
        if(condition){
            query += ' where '+condition;
        }
        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'getSpecificDataFromCvr'});
            if (err) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getSpecificDataFromCvr`]);
                self.L.error('getCvrData: ', 'error occurred while getting cvr data from the DB: ', query, err);
            }
            callback(err, data);
        }, 'FS_RECHARGE_SLAVE1', query, []);
        
    }

}

export default CatalogVerticalRecharge;
