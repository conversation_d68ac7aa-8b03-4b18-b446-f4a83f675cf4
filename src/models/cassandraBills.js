import MOMENT from 'moment'
import _, { reject } from 'lodash'
import <PERSON>Y<PERSON>, { compose } from 'async'
import utility from '../lib'
import EncryptorDecryptor from 'encrypt_decrypt';
import ENCDECPUTIL from '../lib/EncryptionDecryptioinHelper'
import cassandra from 'cassandra-driver';
import NOTIFICATION from './notification';
import CustomTableResolver from '../utils/customTableResolver';
import NonPaytmBills from './nonPaytmBills';
import EncryptionDecryptioinHelper from '../lib/EncryptionDecryptioinHelper';
import statsd from 'paytm-statsd';
import Logger from '../lib/logger';

import billsLib from '../lib/bills';
import LoanUtils from '../utils/loanUtil';

class cassandraBills {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.client = options.cassandraDbClient;
        this.notificationNewClusterClient = options.notificationNewClusterClient;
        this.notificationClient = options.notificationCassandraDbClient;
        this.cryptr = new EncryptorDecryptor();
        this.notification = new NOTIFICATION(options);
        this.retryLimit = 5;
        this.recentBillsCache = 'bills_recent_cache';
        this.nonPaytmBills = 'bills_non_paytm';
        this.encDecpUtil = new ENCDECPUTIL(options);
        this.nonRUTable = 'bills_nonru';
        this.notificationReject = 'ru_notification_rejects';
        this.blackListedRnsForAirtelPrepaid = 'blacklisted_rns_airtel_prepaid';
        this.notificationCache = 'notification_cache';
        this.billsAnalyticsData='bills_analytics_data';
        this.notificationTable = 'notifications';
        this.notificationLogsTable = 'notification_logs'; //type also present as primary key here
        this.paymentRemindlaterCache= "payment_remind_later_events";
        this.custIdRnMappingTable = 'cust_id_rn_mapping';
        this.serviceNotificationCapping = 'service_notification_capping';
        this.whatsappWhitelistedCustomers = 'whatsapp_whitelisted_customers';
        this.activePaytmUsers = 'active_paytm_users';
        this.activePaytmUsersNew = 'active_paytm_users_new';
        this.customerUpdatesTable = 'customer_updates'; // New table for customer updates
        this.notificationLogsTable = 'notification_logs'; //type also present as primary key here
        this.tableResolver = new CustomTableResolver(options);
        this.nonPaytmBillsModel = new NonPaytmBills(options);
        this.EncryptionDecryptioinHelper = new EncryptionDecryptioinHelper(options);
        this.whatsappNotificationType = 'whatsapp';
        this.commonLib = new utility.commonLib(options);
        this.logger = new Logger(options);
        this.whatsappNotificationType = 'whatsapp';
        this.billsLib = new billsLib(options);
        this.loanUtils = new LoanUtils(options);
        this.commonLib = new utility.commonLib(options);
    }

    queryClusterForPaymentCache(client, query, queryParams, clusterName) {

        const self = this;
        return client.execute(query, queryParams, { prepare: true })
            .then(result => {
                self.L.log(`queryClusterForPaymentCache (${clusterName})::payment history fetched ${result.rows.length} for ${JSON.stringify(queryParams)}`);
                return result.rows;
            })
            .catch(error => {
                self.L.error(`queryClusterForPaymentCache (${clusterName})::DB exception! for ${JSON.stringify(queryParams)} err: ${error}`);
                throw new Error(`queryClusterForPaymentCache (${clusterName})::DB exception! ${error.message}`);
            });

    }

    async getPaymentCache(params) {
        const self = this;
        const query = `SELECT * FROM ${self.recentBillsCache} WHERE paytype = ? AND service = ? AND operator = ?\
                        AND recharge_number = ?`;
        const queryParams = [params.paytype, params.service, params.operator, params.recharge_number];

        return new Promise((resolve, reject) => {
            if (queryParams.indexOf(null) > -1) {
                self.L.error(`getPaymentCache::missing param for ${JSON.stringify(params)}`);
                return reject(new Error('getPaymentCache:: missing param'));
            }
            self.queryClusterForPaymentCache(self.notificationNewClusterClient, query, queryParams, "new cluster")
                .then(result => {
                    if (result && result.length > 0) {
                        self.L.log(`getPaymentCache from new cluster ::payment history fetched ${result.length} for ${JSON.stringify(params)}`);
                        resolve(result);

                    }
                    else if (_.get(self.config, ['DYNAMIC_CONFIG', 'CASSANDRA_CLUSTER_CONFIG', 'NOTIFICATIONS', 'USE_OLD_CLUSTER_FOR_READ'], 0)) {
                        return self.queryClusterForPaymentCache(self.client, query, queryParams, "old cluster")
                    }
                    else {
                        return resolve([]);
                    }

                })
                .then(result => {
                    if (result) {
                        self.L.log(`getPaymentCache from old cluster::payment history fetched ${result.length} for ${JSON.stringify(params)}`);
                        resolve(result);
                    }
                    else {
                        return resolve([]);
                    }

                })
                .catch(error => {
                    self.L.error(`getPaymentCache  ::DB exception! for ${JSON.stringify(params)} err: ${error}`);
                    reject(new Error('getPaymentCache:: DB exception!'));
                })
        })
    }

    isWhitelistedForCCPaymentRemindLaterDateCacheTable(service, paytype, custId) {
        let self = this;
        const isWhitelistedCustIdForPaymentRemindLaterDateCacheTable = function(custId) {
            if (custId == null || custId == undefined || isNaN(Number(custId))) {
                return false;
            }
            // Get the allowed percentage from config or use a default value
            const allowedRolloutPercentage = _.get(
                self.config, 
                ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'ENCRYPTION', 'ALLOWED_ROLLOUT_PERCENTAGE_FOR_PAYMENT_REMIND_LATER_DATE_CACHE_TABLE'], 
                0
            );
            // Check if customer ID falls within the allowed percentage range
            return custId % 100 < allowedRolloutPercentage;
        };

        return self.EncryptionDecryptioinHelper.isWhitelistedForCC(service, paytype, custId) && isWhitelistedCustIdForPaymentRemindLaterDateCacheTable(custId);
    }

    /** ccbp encryption changes needed here. */
    async getPaymentRemindLaterDateCache(params) {
        const self = this;
        if(self.isWhitelistedForCCPaymentRemindLaterDateCacheTable(params.service, params.paytype, params.customer_id)) {
            let copyParams = _.cloneDeep(params);
            _.set(copyParams , 'recharge_number',self.EncryptionDecryptioinHelper.encryptData(params.recharge_number));
            self.L.log(`[cassandraBills.getPaymentRemindLaterDateCache] encrypted copyParams ${JSON.stringify(copyParams)}`);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:GET_PAYMENT_REMIND_LATER_DATE_CACHE", "STATUS:WHITELISTED_FOR_CC", `TYPE:DECRYPT`, `OPERATOR:${_.get(params,'operator',null)}`, `SERVICE:${_.get(params,'service',null)}`]);
            let result = await self.getPaymentRemindLaterDateCacheDefault(copyParams);
            if(result && result.length > 0){
                //set the encrypted fields to original decrypted value
                result = self.EncryptionDecryptioinHelper.parseDbResponse(result, params.customer_id);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:GET_PAYMENT_REMIND_LATER_DATE_CACHE", "STATUS:FOUND_ENCRYPTED", `TYPE:DECRYPT`, `OPERATOR:${_.get(params,'operator',null)}`, `SERVICE:${_.get(params,'service',null)}`]);
                self.L.log(`[cassandraBills.getPaymentRemindLaterDateCache] found encrypted returning decrypted for params ${JSON.stringify(copyParams)}`);
                return result;
            }
        }
        
        return await self.getPaymentRemindLaterDateCacheDefault(params);
    }

    async getPaymentRemindLaterDateCacheDefault(params) {
        const self = this;
        const oldQuery = `SELECT * FROM ${self.recentBillsCache} WHERE paytype = ? AND service = ? AND operator = ?\
                            AND recharge_number = ?`;
        const newQuery = `SELECT * FROM ${self.paymentRemindlaterCache} WHERE recharge_number = ? AND service = ? AND operator = ?`;
        const newQueryParams = [params.recharge_number, params.service, params.operator];
        const oldQueryParams = [params.paytype, params.service, params.operator, params.recharge_number];
        return new Promise((resolve, reject) => {
            if (newQueryParams.indexOf(null) > -1) {
                self.L.error(`getPaymentRemindLaterDateCache::missing param for ${JSON.stringify(newQueryParams)}`);
                return reject(new Error('getPaymentCache:: missing param'));
            }
            self.queryClusterForPaymentCache(self.notificationNewClusterClient, newQuery, newQueryParams, "paytment remind later cache")
                .then(result => {
                    if (result && result.length > 0) {
                        self.L.log(`getPaymentCache from payment remind later cache ::payment remind late date history fetched ${result.length} for ${JSON.stringify(newQueryParams)}`);
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_SERVICE", "STATUS:SUCCESS", 'TYPE:PAYMENT_REMIND_LATER_CACHE_FETCHED',`OPERATOR:${_.get(params,'operator',null)}`,`SERVICE:${_.get(params,'service',null)}`]);
                        resolve(result);

                    }
                    else if (_.get(self.config, ['DYNAMIC_CONFIG', 'CASSANDRA_CLUSTER_CONFIG', 'NOTIFICATIONS', 'USE_OLD_PAYMENT_CACHE'], 1)) {
                        return self.queryClusterForPaymentCache(self.notificationNewClusterClient, oldQuery, oldQueryParams, "payment cache")
                    }
                    else {
                        return resolve([]);
                    }

                })
                .then(result => {
                    if (result) {
                        self.L.log(`getPaymentCache from old payment cache ::payment history fetched ${result.length} for ${JSON.stringify(oldQueryParams)}`);
                        resolve(result);
                    }
                    else {
                        return resolve([]);
                    }

                })
                .catch(error => {
                    self.L.error(`getPaymentRemindLaterDateCache  ::DB exception! for ${JSON.stringify(params)} err: ${error}`);
                    reject(new Error('getPaymentRemindlaterDateCache:: DB exception!'));
                })
        })
                     
    }

    /** ccbp encryption changes DONE here, not being used in this repo though */
    async insertPaymentCache(params){
        const self = this;
        // let expiryTime = _.get(self.config, ['DYNAMIC_CONFIG', 'PAYMENT_CACHE_CONFIG', 'CACHE_EXPIRY', 'CACHE_EXPIRY_TIME'], 48)
        // let expiryTimeUnit = _.get(self.config, ['DYNAMIC_CONFIG','PAYMENT_CACHE_CONFIG', 'CACHE_EXPIRY', 'CACHE_EXPIRY_UNIT'], 'hours');

        // let TTL = 172800; //default of 2days in seconds;
        // if(expiryTimeUnit=='days'|| expiryTimeUnit=='hours'){
        //     TTL = expiryTimeUnit=='days' ? expiryTime*24*60*60 : expiryTime*60*60;
        // }
        
        let isCCEncryptionEnabled = self.EncryptionDecryptioinHelper.isWhitelistedForCC(params.service, params.paytype, params.customerId);
        let date = this.getDate();
        const insertQuery = `INSERT INTO ${self.recentBillsCache} (recharge_number, customer_id, operator , service, product_id, paytype,amount,circle, payment_date, created_at, updated_at, is_encrypted)                
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`; // USING TTL ${TTL}`;        
        const insertQueryParams = [
            isCCEncryptionEnabled ? self.EncryptionDecryptioinHelper.encryptData(params.recharge_number) : params.recharge_number,
            params.customer_id,
            _.toLower(params.operator),
            _.toLower(params.service),
            params.product_id,
            _.toLower(params.paytype),
            params.amount,
            _.toLower(params.circle),
            params.payment_date,
            date,
            date,
            isCCEncryptionEnabled ? 1 : 0
        ];
        const batchQuery = [
            {
                query: insertQuery,
                params: insertQueryParams
            }
        ]

        return new Promise((resolve, reject) => {
            var latencyStart = new Date().getTime();
            this.notificationNewClusterClient.batch(batchQuery, { prepare: true })
            .then(() => {
                utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'insertPaymentCache' });
                self.L.log(`insertPaymentCache::insertPaymentCache Data updated on cluster for ${JSON.stringify(params)}`);
                resolve(null)
            })
            .catch(error => {
                utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'insertPaymentCache' });
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:insertPaymentCache`]);
                self.L.error(`insertPaymentCache::insertPaymentCache Record not updated for ${JSON.stringify(params)}, error ${error}`)
                reject(`insertPaymentCache::insertPaymentCache Record not updated for ${JSON.stringify(params)}, error ${error}`);
            })
        })
       
    }

    /** ccbp encryption changes needed here : NO CHANGES needed as its not built for cc bp */
    async insertPaymentRemindLaterEvents(params){
        const self = this;
        let date = this.getDate();
        const now = new Date();
        const endOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
        const ttl = Math.round((endOfDay.getTime() - now.getTime()) / 1000);

        let isEncrypted = 0;
        let paramsForCCBP = self.EncryptionDecryptioinHelper.getNotificationParamsForCCBP(params);
        if(self.isWhitelistedForCCPaymentRemindLaterDateCacheTable(paramsForCCBP.service, paramsForCCBP.paytype, paramsForCCBP.customerId)) {
            self.L.log('insertPaymentRemindLaterEvents::', `record ${JSON.stringify(params)} is whitelisted for cc for payment remind later date cache table`);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:INSERT_PAYMENT_REMIND_LATER_EVENTS", "STATUS:WHITELISTED_FOR_CC", 'TYPE:ENCRYPT']);
            _.set(params, 'recharge_number', self.EncryptionDecryptioinHelper.encryptData(_.get(params, 'recharge_number', null)));
            isEncrypted = 1;
        }
        _.set(params, 'is_encrypted', isEncrypted);

        const insertQuery = `INSERT INTO payment_remind_later_events (recharge_number, service, operator, payment_date, is_encrypted)                
            VALUES (?, ?, ?, ?, ?) USING TTL ?`;        
        const insertQueryParams = [
            params.recharge_number,
            _.toLower(params.service),
            _.toLower(params.operator),
            params.payment_date,
            params.is_encrypted,
            ttl
        ];
        const batchQuery = [
            {
                query: insertQuery,
                params: insertQueryParams
            }
        ]
    
        return new Promise((resolve, reject) => {
            this.notificationNewClusterClient.batch(batchQuery, { prepare: true })
            .then(() => {
                self.logger.log(`insertPaymentRemindLaterEvents:: Data updated on cluster for`, params, _.get(params, 'service', null));
                resolve(null)
            })
            .catch(error => {
                self.logger.error(`insertPaymentRemindLaterEvents:: error ${error} Record not updated for`, params, _.get(params, 'service', null));
                reject(`insertPaymentRemindLaterEvents:: Record not updated for ${JSON.stringify(params)}, error ${error}`);
            })
        })
    }

    async getNotificationCache(cb, params, data){
        let self = this;
        let key = params.key;

        try{
            let resultFromNewNotificationCluster = await self.getNotificationCacheFromDB(key, data, 'NOTIFICATION')
            let rows = _.get(resultFromNewNotificationCluster, 'rows', []);
            if(rows.length>0){
                let value = JSON.parse(_.get(rows, '0.value', null));
                return cb(null, value);
            }

            if(_.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CACHE_CONFIG', 'USE_OLD_CLUSTER', 'ENABLED'], false)){
                
                let resultFromOldNotificationCluster = await self.getNotificationCacheFromDB(key, data, 'REMINDER');
                let rows = _.get(resultFromOldNotificationCluster, 'rows', []);
                if(rows.length>0){
                    let value = JSON.parse(_.get(rows, '0.value', null));
                    return cb(null, value);
                }else{
                    return cb(null, null);
                }
            }else{
                return cb(null, null);
            }
        }catch(e){
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_CACHE", 'STATUS:EXECUTION_ERROR', 'TYPE:GET']);
            self.L.error(`getNotificationCache::error in fetching cache for ${key} err: ${e}`);
            return cb(new Error('getNotificationCache:: DB exception!'+ e));
        }
    }

    async getNotificationCacheFromDB(key, data, cluster){
        const self = this;
        try {
            if(self.EncryptionDecryptioinHelper.isWhitelistedForCC(data.service, data.paytype, data.customerId)) {
                let encryptedKey = self.EncryptionDecryptioinHelper.encryptData(key);
                let result = await self.getNotificationCacheFromDBDefault(encryptedKey, cluster);
                if(result && result.rows.length > 0){
                    if(self.checkIfDbRecordIsEncrypted(result.rows[0])) {
                        result.rows[0].key = self.EncryptionDecryptioinHelper.decryptData(result.rows[0].key);
                    }
                } else {
                    self.L.log('encrypted record not found');
                }
                return result;
            } else {
                self.L.log('NON CC Record found');
                return await self.getNotificationCacheFromDBDefault(key, cluster);
            }
        } catch (e) {
            self.L.error(`getNotificationCacheFromDB::error in fetching cache for ${key} err: ${e}`);
            throw new Error('getNotificationCacheFromDB:: DB exception!' + e);
        }
    }

    async getNotificationCacheFromDBDefault(key, cluster) {
        let self = this;
        let query = `SELECT * FROM ${self.notificationCache} WHERE key=?`;
        let queryParams = [key];
        let client = cluster=='NOTIFICATION' ? self.notificationNewClusterClient : self.client;
        let consistencyLevel = cassandra.types.consistencies.localQuorum;
        return new Promise((resolve, reject) => {
            client.execute(query, queryParams, { prepare : true, consistency: consistencyLevel })
            .then(result =>{
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_CACHE", 'STATUS:SUCCESS', 'TYPE:GET', `CLUSTER:${cluster}`]);
                self.L.log(`getNotificationCache::notification cache fetched ${result.rows.length} for ${key}`);
                resolve(result);
            })
            .catch(error=>{
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_CACHE", 'STATUS:ERROR', 'TYPE:GET', `CLUSTER:${cluster}`]);
                self.L.error(`getNotificationCache::DB exception! for ${key} err: ${error}`);
                reject(new Error('getNotificationCache:: DB exception!'));
            })
        });
    }

    // getNotificationCache(cb, params){
    //     const self = this;
    //     let key = params.key;
    //     const query = `SELECT * FROM ${self.notificationCache} WHERE key=?`;
    //     const queryParams = [key];
    //     self.client.execute(query, queryParams, { prepare : true })
    //     .then(result =>{
    //         self.L.log(`getNotificationCache::notification cache fetched ${result.rows.length} for ${key}`);
    //         let value=null;
    //         try{
    //             value = JSON.parse(_.get(result, 'rows[0].value', null));
    //         }catch(e){
    //             self.L.error(`getNotificationCache::error in parsing cache fetched ${result.rows.length} for ${key} err: ${e}`);
    //             return cb(new Error('getNotificationCache:: DB exception!'));
    //         }
    //         return cb(null, value);
    //     })
    //     .catch(error=>{
    //         self.L.error(`getNotificationCache::DB exception! for ${JSON.stringify(params)} err:`, error);
    //         return cb(new Error('getNotificationCache:: DB exception!'));
    //     })
    // }

    updateNotificationCache(cb,params,data={}){
        const self = this;
        let key = params.key;
        let isEncrypted = 0;
        let encryptedKey = key;
        let value = params.value;
        let ttl = params.ttl;
        if(self.EncryptionDecryptioinHelper.isWhitelistedForCC(data.service, data.paytype, data.customerId)) {
            isEncrypted = 1;
            encryptedKey = self.EncryptionDecryptioinHelper.encryptData(key);
        }
        return self.updateNotificationCacheDefault(key, value, encryptedKey, isEncrypted, ttl, cb);
    }

    updateNotificationCacheDefault(key, value, encryptedKey, isEncrypted, ttl, cb) {
        const self = this;
        let date = this.getDate();
        self.L.log('inside updateNotificationCacheDefault', encryptedKey, key, value);
        const consistencyLevel = cassandra.types.consistencies.localQuorum;
        let insertQuery = `INSERT INTO ${self.notificationCache} (key, value, created_at, updated_at, is_encrypted)                
            VALUES (?, ?, ?, ?, ?)`;
        self.L.log('updateNotificationCache  ttl is ' + ttl);
        if (ttl) {
            insertQuery = `INSERT INTO ${self.notificationCache} (key, value, created_at, updated_at, is_encrypted) 
                VALUES (?, ?, ?, ?, ?) USING TTL ${ttl}`;
        } 
        const queryParams = [
            isEncrypted ? encryptedKey : key,
            JSON.stringify(value),
            date,
            date,
            Number(isEncrypted)
        ];
        self.notificationNewClusterClient.execute(insertQuery, queryParams, { prepare: true, consistency: consistencyLevel})
        .then(() => {
            self.L.log(`setNotificationCache::notification cache updated for ${encryptedKey}`);
            return cb(null)
        })
        .catch(error => {
            self.L.error(`setNotificationCache::notification cache not updated for ${encryptedKey}, error ${error}`)
            return cb(`setNotificationCache::notification cache not updated for ${encryptedKey}, error ${error}`);
        })
    }

    getSudoCassandraResp(applied = false, code = 4) {
        return {
            info: 
             { queriedHost: '127.0.0.1:9042',
               triedHosts: { '127.0.0.1:9042': null },
               speculativeExecutions: 0,
               achievedConsistency: 10,
               traceId: undefined,
               warnings: undefined,
               customPayload: undefined,
               isSchemaInAgreement: true },
            rows: [  { '[applied]': applied } ],
            rowLength: 1,
            columns: [ { name: '[applied]', type: {
                "code": 4,
                "type": null
            } } ],
            pageState: null,
            nextPage: undefined,
            nextPageAsync: undefined 
        }
    }

    async markAsPaidFromNonPaytm(data, cbcPublisher) {
        let self = this,
            service = _.get(self.config,['CVR_DATA',data.productID,'service'],'').toLowerCase(),
            paytype = _.get(self.config, ['CVR_DATA', data.productID, 'paytype'], '').toLowerCase();
            customer_id = Number(params.customerID);
        if(self.encDecpUtil.isWhitelistedForCC(service, paytype, customer_id)){
            
            let encryptedData = _.cloneDeep(data);
            if(_.get(encryptedData, 'rechargeNumber', null) != null) encryptedData.rechargeNumber = self.encDecpUtil.encryptData(encryptedData.rechargeNumber);

            // directly updating with encrypted params
            let updateQueryResponse = await self.markAsPaidFromNonPaytmDefault(encryptedData);
            let wasDbRecordUpdatedUsingEncryptedParams = _.get(updateQueryResponse, "rows[0]['[applied]']", false);
            if(wasDbRecordUpdatedUsingEncryptedParams == true) return updateQueryResponse;

            // fetching decrypted record from db
            let dbQueryParams = {
                    rechargeNumber: data.rechargeNumber,
                    customer_id: data.customerID,
                    operator: _.get(data,'operator').toLowerCase(),
                    service: service
                }

            let dbRecords = await self.getAsyncDataFromNonPaytm(dbQueryParams);

            if(dbRecords && dbRecords.length > 0) {

                _.set(dbRecords[0], 'due_amount', 0);
                _.set(dbRecords[0], 'amount', 0);

                // encrypting db record
                let rootLevelKeysToEncrypt = ['recharge_number','reference_id','customer_mobile','customer_email', 'enc_amount', 'enc_due_date'];
        
                let keysToEncryptForUserData = _.get(self.config, ['DYNAMIC_CONFIG', 'CCBP', 'ENCRYPTION_CONFIG', 'USER_DATA_KEYS'], ['recharge_number_2', 'recharge_number_3', 'recharge_number_4', 'recharge_number_5', 
                    'recharge_number_6']);
                                
                let keysToEncryptForCustomerOtherInfo =  _.get(self.config, ['DYNAMIC_CONFIG', 'CCBP', 'ENCRYPTION_CONFIG', 'CUSTOMER_OTHER_INFO_KEYS'], ['subscriberNumber', 'subscriberName', 'subscriberEmailId', 'subscriberDOB', 'subscriberAddress',
                'subscriberAltNumber', 'subscriberGender', 'subscriberCity', 'minReloadAmount', 'currentBillAmount', 
                'currentMinBillAmount', 'billDueDate', 'billDueAmount', 'lastCC', 'rawLastCC', 'debugKey', 'currentOutstandingAmount']);
    
                let keysToEncryptForCustomerExtra = _.get(self.config, ['DYNAMIC_CONFIG', 'CCBP', 'ENCRYPTION_CONFIG', 'EXTRA_KEYS'], ['subscriberNumber', 'subscriberName', 'subscriberEmailId', 'subscriberDOB', 'subscriberAddress',
                    'subscriberAltNumber', 'subscriberGender', 'subscriberCity', 'minReloadAmount', 'currentBillAmount', 
                    'currentMinBillAmount', 'billDueDate', 'billDueAmount', 'lastCC', 'rawLastCC', 'debugKey', 'currentOutstandingAmount']);
            
                
                let encryptedDbRecord = self.encDecpUtil.getEncryptedParamsFromGenericParams(dbRecords[0], rootLevelKeysToEncrypt, keysToEncryptForCustomerExtra, keysToEncryptForCustomerOtherInfo, keysToEncryptForUserData);

                _.set(encryptedDbRecord, 'status', 15);
                _.set(encryptedDbRecord, 'update_at', self.getDate());
                _.set(encryptedDbRecord, 'due_date', null);
                _.set(encryptedDbRecord, 'due_amount', null);
                _.set(encryptedDbRecord, 'amount', null);

                // inserting encrypted record in db and then deleting the old record
                let {insertQuery, insertQueryParams} = self.getInsertQueryAndParams(encryptedDbRecord, self.tableResolver.getNonRUTableName(customer_id), false);
                self.L.log('markAsPaidFromNonPaytm :: insertQuery :: ','query and params',insertQuery,insertQueryParams);
                let resp = await self.client.execute(insertQuery, insertQueryParams, { prepare : true });
                
                // self.L.log('markAsPaidFromNonPaytm :: deleteQuery :: ','query and params',deleteQuery,deleteQueryParams);
                // let {deleteQuery, deleteQueryParams} = self.getDeleteQueryAndParams(dbQueryParams, self.tableResolver.getNonRUTableName(customer_id));
                // await self.client.execute(deleteQuery, deleteQueryParams, { prepare : true });
                

                let finalDbQueryParams = {
                    rechargeNumber: data.rechargeNumber,
                    customer_id: data.customerID,
                    customerId: data.customerID,
                    operator: _.get(data,'operator').toLowerCase(),
                    service: service,
                    paytype: paytype
                }
                //insert encrypted record in bills_recent_records
                await self.nonPaytmBillsModel.insertRecordInBillsRecentRecordsPromise(finalDbQueryParams, true);

                //deleting records from bills_non_paytm and bills_recent_records
                await self.nonPaytmBillsModel.deleteUsingRecentRecordsPromise(finalDbQueryParams, cassandraCdcPublisher, true);
                //deleting old records from notification billGen and billDue tables
                // await self.nonPaytmBillsModel.deleteFromBillDueAndBillGenTable(null, finalDbQueryParams, dbRecords, true);

                return self.getSudoCassandraResp(true);
            } else {
                return self.getSudoCassandraResp(false);
            }
        }else {
            return self.markAsPaidFromNonPaytmDefault(data);
        }
    }

    markAsPaidFromNonPaytmDefault(data, is_encrypted = false) {
        var self = this;
        let params = _.clone(data),
        date = self.getDate();
        if(_.get(params,'service',null)=='mobile'){
            _.set(params,'rechargeNumber', self.cryptr.encrypt(_.get(params,'rechargeNumber',null)));
        } else if(_.get(params, 'service', '').toLowerCase() === 'loan') {
            self.loanUtils.updateRNtoDummy(params);
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:MARK_AS_PAID_FROM_NON_PAYTM_DEFAULT','STATUS:SUCCESS','TYPE:LOAN_RN_UPDATE', `OPERATOR:${params.operator}`]);
        }
        let query = `UPDATE ${self.tableResolver.getNonRUTableName(params.customerID)} SET ${is_encrypted == true ? "enc_amount": "due_amount"}=?, status=15, update_at = ? WHERE recharge_number = ? AND customer_id = ? AND operator = ?  AND service = ? IF EXISTS`,
            queryParams = [
                is_encrypted == true ? self.encDecpUtil.encryptData('0') : 0,
                date,
                params.rechargeNumber,
                params.customerID,
                params.operator.toLowerCase(),
                params.service.toLowerCase()
            ];
        self.L.log('deleteRecordFromNonPaytm','query and params',query,queryParams);
        return new Promise((resolve, reject) => {
            var latencyStart = new Date().getTime();
            self.client.execute(query, queryParams, { prepare : true })
                .then(data => {
                    utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'markAsPaidFromNonPaytm' });
                    resolve(data);
                })
                .catch(error => {
                    utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'markAsPaidFromNonPaytm' });
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:markAsPaidFromNonPaytm`]);
                    self.L.critical("deleteRecordFromNonPaytm", "Error occurred while deleting record ", error,params);
                    reject(error);
                })
        });
    }

    async markAsPaid(data, NonRURecord, NonPaytmRecord, cassandraCdcPublisher) {
        let self = this,
            service = _.get(self.config,['CVR_DATA',data.productID,'service'],'').toLowerCase(),
            paytype = _.get(self.config, ['CVR_DATA', data.productID, 'paytype'], '').toLowerCase(),
            customer_id = Number(data.customerID);
        if(self.encDecpUtil.isWhitelistedForCC(service, paytype, customer_id)) {
            let encryptedData = _.cloneDeep(data);
            if(_.get(encryptedData, 'rechargeNumber', null) != null) encryptedData.rechargeNumber = self.encDecpUtil.encryptData(encryptedData.rechargeNumber);

            // directly updating with encrypted params
            let updateQueryResponse = await self.markAsPaidDefault(encryptedData,NonRURecord,NonPaytmRecord, true);
            let wasDbRecordUpdatedUsingEncryptedParams = _.get(updateQueryResponse, "rows[0]['[applied]']", false);
            if(wasDbRecordUpdatedUsingEncryptedParams == true) return updateQueryResponse;


            // fetching decrypted record from db
            let dbQueryParams = {
                rechargeNumber: data.rechargeNumber,
                customer_id: data.customerID,
                operator: _.get(data,'operatorForSmsCC',null)? _.get(data,'operatorForSmsCC',null) : _.get(data,'operator',null).toLowerCase(),
                service: service
            }

            let dbRecords = await self.getAsyncDataFromNonPaytm(dbQueryParams);

            if(dbRecords && dbRecords.length > 0) {

                _.set(dbRecords[0], 'due_amount', 0);
                _.set(dbRecords[0], 'amount', 0);

                    // encrypting db record
                    let rootLevelKeysToEncrypt = ['recharge_number','reference_id','customer_mobile','customer_email', 'enc_amount', 'enc_due_date'];
            
                    let keysToEncryptForUserData = _.get(self.config, ['DYNAMIC_CONFIG', 'CCBP', 'ENCRYPTION_CONFIG', 'USER_DATA_KEYS'], ['recharge_number_2', 'recharge_number_3', 'recharge_number_4', 'recharge_number_5', 
                        'recharge_number_6']);
                                    
                    let keysToEncryptForCustomerOtherInfo =  _.get(self.config, ['DYNAMIC_CONFIG', 'CCBP', 'ENCRYPTION_CONFIG', 'CUSTOMER_OTHER_INFO_KEYS'], ['subscriberNumber', 'subscriberName', 'subscriberEmailId', 'subscriberDOB', 'subscriberAddress',
                    'subscriberAltNumber', 'subscriberGender', 'subscriberCity', 'minReloadAmount', 'currentBillAmount', 
                    'currentMinBillAmount', 'billDueDate', 'billDueAmount', 'lastCC', 'rawLastCC', 'debugKey', 'currentOutstandingAmount']);
    
                    let keysToEncryptForCustomerExtra = _.get(self.config, ['DYNAMIC_CONFIG', 'CCBP', 'ENCRYPTION_CONFIG', 'EXTRA_KEYS'], ['subscriberNumber', 'subscriberName', 'subscriberEmailId', 'subscriberDOB', 'subscriberAddress',
                        'subscriberAltNumber', 'subscriberGender', 'subscriberCity', 'minReloadAmount', 'currentBillAmount', 
                        'currentMinBillAmount', 'billDueDate', 'billDueAmount', 'lastCC', 'rawLastCC', 'debugKey', 'currentOutstandingAmount']);
                
                    
                    let encryptedDbRecord = self.encDecpUtil.getEncryptedParamsFromGenericParams(dbRecords[0], rootLevelKeysToEncrypt, keysToEncryptForCustomerExtra, keysToEncryptForCustomerOtherInfo, keysToEncryptForUserData);
                
                _.set(encryptedDbRecord, 'status', 15);
                if(NonRURecord) {
                    _.set(encryptedDbRecord, 'update_at', self.getDate());
                } else {
                    _.set(encryptedDbRecord, 'update_at', self.getDate());

                }
                _.set(encryptedDbRecord, 'due_date', null);
                _.set(encryptedDbRecord, 'due_amount', null);
                _.set(encryptedDbRecord, 'amount', null);

                // inserting encrypted record in db and then deleting the old record
                let {insertQuery, insertQueryParams} = self.getInsertQueryAndParams(encryptedDbRecord, self.tableResolver.getNonRUTableName(customer_id), false);
                self.L.log('markAsPaid :: insertQuery :: ','query and params',insertQuery,insertQueryParams);
                let resp = await self.client.execute(insertQuery, insertQueryParams, { prepare : true });
                
                // self.L.log('markAsPaidFromNonPaytm :: deleteQuery :: ','query and params',deleteQuery,deleteQueryParams);
                // let {deleteQuery, deleteQueryParams} = self.getDeleteQueryAndParams(dbQueryParams, self.tableResolver.getNonRUTableName(customer_id));
                // await self.client.execute(deleteQuery, deleteQueryParams, { prepare : true });
                
                let finalDbQueryParams = {
                    rechargeNumber: data.rechargeNumber,
                    customer_id: data.customerID,
                    customerId: data.customerID,
                    operator: _.get(data,'operatorForSmsCC',null)? _.get(data,'operatorForSmsCC',null) : _.get(data,'operator',null).toLowerCase(),
                    service: service,
                    paytype: paytype
                }

                //insert encrypted record in bills_recent_records
                await self.nonPaytmBillsModel.insertRecordInBillsRecentRecordsPromise(finalDbQueryParams, true);

                //deleting records from bills_non_paytm and bills_recent_records
                await self.nonPaytmBillsModel.deleteUsingRecentRecordsPromise(finalDbQueryParams, cassandraCdcPublisher, true);
                //deleting old records from notification billGen and billDue tables
                // await self.nonPaytmBillsModel.deleteFromBillDueAndBillGenTable(null, finalDbQueryParams, dbRecords, true);

                return self.getSudoCassandraResp(true);

            } else {
                return self.getSudoCassandraResp(false);
            }
        } else {
            return self.markAsPaidDefault(data,NonRURecord,NonPaytmRecord);
        }
    }


    getDeleteQueryAndParams(params, tableName) {
        let deleteQuery = `DELETE FROM ${tableName} WHERE recharge_number = ? AND customer_id = ? AND operator = ?  AND service = ?`;
        let deleteQueryParams = [
            params.rechargeNumber,
            Number(params.customer_id),
            _.get(params,'operatorForSmsCC',null)? _.get(params,'operatorForSmsCC',null) : _.get(params,'operator',null).toLowerCase(),
            params.service.toLowerCase()
        ];
        return {deleteQuery, deleteQueryParams};
    }

    getInsertQueryAndParams(params, tableName, setUpdatedAt = true) {
        let data = _.cloneDeep(params);
        delete data['id'];
        _.set(data, 'is_encrypted', 1);
        if(setUpdatedAt == true) _.set(data, 'updated_at', MOMENT().format('YYYY-MM-DD HH:mm:ss'));
        let keys = Object.keys(data);
        let values = Object.values(data);

        let insertQuery = `INSERT INTO ${tableName} (${keys.join(',')}) VALUES (${keys.map(() => '?').join(',')})`;
        return {insertQuery, insertQueryParams: values};
    }

    async markAsPaidDefault(data, NonRURecord, NonPaytmRecord, is_encrypted = false) {
        const self = this;
        const params = _.clone(data);
        const date = self.getDate();
        const table = self.tableResolver.getNonRUTableName(params.customerID);
        
        // Encrypt recharge number if service is mobile
        if (_.get(params, 'service', null) == 'mobile') {
            _.set(params, 'rechargeNumber', self.cryptr.encrypt(_.get(params, 'rechargeNumber', null)));
        }

        // Helper function to construct query and params
        const buildQueryAndParams = (rechargeNumber) => {
            const query = `UPDATE ${table} SET ${is_encrypted ? "enc_amount = ?" : 'due_amount = ?'}, status = 15, ${NonRURecord ? 'updated_at' : 'update_at'} = ? WHERE recharge_number = ? AND customer_id = ? AND operator = ? AND service = ? IF EXISTS`;
            const queryParams = [
                is_encrypted ? self.encDecpUtil.encryptData('0') : 0,
                date,
                rechargeNumber,
                Number(params.customerID),
                _.get(params, 'operatorForSmsCC', null) ? _.get(params, 'operatorForSmsCC', null) : _.get(params, 'operator', null).toLowerCase(),
                params.service.toLowerCase()
            ];
            return { query, queryParams };
        };

        // Build primary query and params
        const primaryQuery = buildQueryAndParams(params.rechargeNumber);
        self.L.log('markAsPaid', 'primary query and params', primaryQuery.query, primaryQuery.queryParams);

        // Check for alternate recharge number
        const [isOperatorPrefixEnabled, alternateRechargeNumber] = self.commonLib.getAlternateRechargeNumber(params.rechargeNumber, params.operator);

        // Execute queries
        try {
            if (isOperatorPrefixEnabled) {
                // Build alternate query and params
                const alternateQuery = buildQueryAndParams(alternateRechargeNumber);
                self.L.log('markAsPaid', 'alternate query and params', alternateQuery.query, alternateQuery.queryParams);

                // Execute both queries in parallel
                const [primaryResult, alternateResult] = await Promise.all([
                    self.client.execute(primaryQuery.query, primaryQuery.queryParams, { prepare: true }),
                    self.client.execute(alternateQuery.query, alternateQuery.queryParams, { prepare: true })
                ].map(p => p.catch(error => {
                    // Log error but don't fail the entire operation
                    self.L.critical("markAsPaid", "Error occurred while updating record", error, params);
                    return null;
                })));

                // Return primary result if it succeeded, otherwise return alternate result
                return primaryResult || alternateResult;
            } else {
                // Execute single query for primary recharge number
                return await self.client.execute(primaryQuery.query, primaryQuery.queryParams, { prepare: true });
            }
        } catch (error) {
            self.L.critical("markAsPaid", "Error occurred while updating record", error, params);
            throw error;
        }
    }

    updateNotificationStatusForNonRU(params, cb){
        const self = this;
        let billsQuery = `UPDATE ${self.nonRUTable} SET notification_status = ? , updated_at = ? WHERE`
        // recharge_number = ? AND customer_id = ? AND operator = ? AND service = ?  IF EXISTS`
        let billsQueryParams = [params.notificationStatus, date];
        //, params.rechargeNumber, params.customerId, params.operator.toLowerCase(), params.service.toLowerCase()];

        [billsQuery, billsQueryParams] = self.commonLib.formatQueryAndParams(billsQuery, billsQueryParams, params.rechargeNumber, params.operator);
        
        billsQuery += ' AND customer_id = ? AND operator = ? AND service = ? IF EXISTS';
        billsQueryParams.push(params.customerId);
        billsQueryParams.push(params.operator.toLowerCase());
        billsQueryParams.push(params.service.toLowerCase());

        self.L.log("updateNotificationStatusForNonRU: query and query params::", billsQuery, billsQueryParams);
        var latencyStart = new Date().getTime();
        self.client.execute(billsQuery, billsQueryParams, { prepare : true })
        .then(result =>{
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'updateNotificationStatusForNonRU' });
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:API_STATUS_CHANGE']);
            self.L.log(`updateNotificationStatusForNonRU::record updated successfully for ${JSON.stringify(params)}`);
            return cb(null, result)
        })
        .catch(error=>{
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'updateNotificationStatusForNonRU' });
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:updateNotificationStatusForNonRU`]);
            return cb('updateNotificationStatusForNonRU::updateNotificationStatusForNonRU DB exception!' + JSON.stringify(params) + error);
        })
    }

    getDataFromNonPaytm(cb, params){
        const self = this;
        const query = `SELECT customer_id, recharge_number, bill_fetch_date, bill_date, due_date, operator, service, due_amount, status FROM ${self.tableResolver.getNonRUTableName(params.customer_id)} WHERE recharge_number = ? limit 100`;
        const queryParams = [
            params.recharge_number
        ];
        var latencyStart = new Date().getTime();
        self.client.execute(query, queryParams, { prepare : true })
        .then(result =>{
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'getDataFromNonPaytm'});
            self.L.log(`getDataFromNonPaytm::customer fetched ${result.rows.length} for ${JSON.stringify(params)}`);
            return cb(null, result.rows);
        })
        .catch(error=>{
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'getDataFromNonPaytm' });
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getDataFromNonPaytm`]);
            self.L.error(`getDataFromNonPaytm::DB exception! for ${JSON.stringify(params)} err: ${error}`);
            return cb('getDataFromNonPaytm:: DB exception!');
        })
    }


    async getAsyncDataFromNonPaytm(params){
        const self = this;
        const query = `SELECT * FROM ${self.tableResolver.getNonRUTableName(params.customer_id)} WHERE recharge_number = ? AND customer_id = ? AND operator = ? AND service = ?`;
        const queryParams = [
            params.rechargeNumber, 
            params.customer_id, 
            _.toLower(_.get(params, 'operator', null)), 
            _.toLower(_.get(params, 'service', null))
        ];
    
        try {
            const result = await self.client.execute(query, queryParams, { prepare : true });
            self.L.log(`getAsyncDataFromNonPaytm::customer fetched ${result.rows.length} for ${JSON.stringify(queryParams)}`);
            return result.rows;
        } catch (error) {
            self.L.error(`getAsyncDataFromNonPaytm::DB exception! for ${JSON.stringify(queryParams)} err: ${error}`);
            throw new Error('getAsyncDataFromNonPaytm:: DB exception!');
        }
    }


    getRNFromBlacklist(cb, rechargeNumber){
        const self = this;
        const query = `SELECT * FROM ${self.blackListedRnsForAirtelPrepaid} WHERE recharge_number = ?`;
        const queryParams = [rechargeNumber];
        var latencyStart = new Date().getTime();
        self.client.execute(query, queryParams, { prepare : true })
        .then(result =>{
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'getRNFromBlacklist'});
            self.L.log(`getRNFromBlacklist::customer fetched ${result.rows.length} for recharge_number ${JSON.stringify(rechargeNumber)}`);
            return cb(null, result.rows);
        })
        .catch(error=>{
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'getRNFromBlacklist' });
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getRNFromBlacklist`]);
            self.L.error(`getRNFromBlacklist::DB exception! for ${JSON.stringify(rechargeNumber)} err: ${error}`);
            return cb('getRNFromBlacklist:: DB exception!');
        })
    }

    updateBlacklist(cb, rechargeNumber){
        const self = this;
        const query = `INSERT into ${self.blackListedRnsForAirtelPrepaid} (recharge_number, created_at, updated_at) VALUES (?, ?, ?) IF NOT EXISTS`;
        const queryParams = [rechargeNumber, self.getDate(), self.getDate()];
        var latencyStart = new Date().getTime();
        self.client.execute(query, queryParams, { prepare : true })
        .then(result =>{
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'updateBlacklist'});
            self.L.log(`updateBlackList::customer updated ${result.rows.length} for recharge_number ${JSON.stringify(rechargeNumber)}`);
            return cb(null, result.rows);
        })
        .catch(error=>{
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'updateBlacklist' });
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:updateBlacklist`]);
            self.L.error(`updateBlackList::DB exception! for ${JSON.stringify(rechargeNumber)} err: ${error}`);
            return cb('updateBlackList:: DB exception!');
        })
    }
    getDateMilliSec(){
        return MOMENT().format('YYYY-MM-DD HH:mm:ss.SSS');
    } 

    saveAnalyticsDataInCassandra(params) {
        const self = this;
        let date = this.getDate();
        const insertQuery = `INSERT INTO ${self.billsAnalyticsData} (source,source_subtype_2,user_type ,reject_reason,customer_id,service,operator,recharge_number, due_amount, sender_id,sms_date_time,dwh_class_id,created_at,additional_info,paytype,sms_id,updated_at)                
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?,?,?,?,?,?)`;
        const insertQueryParams = [

            params.source,
            params.source_subtype_2,
            params.user_type,
            params.reject_reason,
            params.customer_id,
            _.toLower(params.service),
            _.toLower(params.operator),
            params.recharge_number,
            params.due_amount,
            params.sender_id,
            params.sms_date_time,
            params.dwh_class_id,
            self.getDateMilliSec(),
            params.additional_info,
            params.paytype,
            params.sms_id,
            self.getDateMilliSec()


        ];
        const batchQuery = [
            {
                query: insertQuery,
                params: insertQueryParams
            }
        ]

        return new Promise((resolve, reject) => {
            var latencyStart = new Date().getTime();
            this.client.batch(batchQuery, { prepare: true })
                .then(() => {
                    utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'saveAnalyticsDataInCassandra'});
                    self.L.log(`saveAnalyticsDataInCassandra::saveAnalyticsDataInCassandra Data updated on cluster for ${JSON.stringify(params)}`);
                    resolve(null)
                })
                .catch(error => {
                    utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'saveAnalyticsDataInCassandra' });
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:saveAnalyticsDataInCassandra`]);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_ANALYTICS", 'STATUS:CASSANDRA_UPSERT_ERROR', 'TYPE:DB', 'TABLE:bills_analytics_data', 'SOURCE:MAIN_FLOW']);
                    self.L.error(`saveAnalyticsDataInCassandra::saveAnalyticsDataInCassandra Record not updated for ${JSON.stringify(params)}, error ${error}`)
                    reject(`saveAnalyticsDataInCassandra::saveAnalyticsDataInCassandra Record not updated for ${JSON.stringify(params)}, error ${error}`);
                })
        })
    }

    insertRejectedNotifications(cb, queryParams) {
        const self = this;
        const query = `INSERT into ${self.notificationReject} (recharge_number, customer_id, source_id, category_id, product_id, \
            recon_id, type, template_id, recipient, data, priority, reject_msg, status, notif_type, operator, amount, service, \
            raw_expiry_date, bill_source, promocode, msg_type, timepoint, template_name, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;
        var latencyStart = new Date().getTime();
        self.client.execute(query, queryParams, { prepare : true })
        .then(() => {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'insertRejectedNotifications'});
            self.L.log('insertRejectedNotifications::', `entry created in ${self.notificationReject} for record ${queryParams}`);
            cb();
        })
        .catch(error => {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'insertRejectedNotifications' });
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:insertRejectedNotifications`]);
            self.L.error('insertRejectedNotifications::', `error while creating entry in ${self.notificationReject} for record \
            ${queryParams}`, error);
            cb();
        })
    }

    createEntryInNotificationTable(action, kafkaPublish, dbInsertId, notifier, data , tableName, cb){
        let self = this;
        let encryptedNotifier = _.cloneDeep(notifier);
        let isEncrypted = 0;

        if(self.EncryptionDecryptioinHelper.isWhitelistedForCC( _.get(notifier, 'service', null), _.get(notifier, 'paytype', null), _.get(notifier, 'customer_id', null))) {
            _.set(encryptedNotifier, 'recharge_number', self.EncryptionDecryptioinHelper.encryptData(_.get(notifier, 'recharge_number', null)));
            _.set(encryptedNotifier, 'recipient', self.EncryptionDecryptioinHelper.encryptData(_.get(notifier, 'recipient', null)));
            isEncrypted = 1;
        }
        _.set(encryptedNotifier, 'is_encrypted', isEncrypted);
        return self.createEntryInNotificationTableDB(action, kafkaPublish, dbInsertId, encryptedNotifier, data , tableName, cb);
    }

    createEntryInNotificationTableDB(action, kafkaPublish, dbInsertId, notifier, data , tableName, cb) {
        let self = this;
        let insertQuery = `INSERT INTO ${tableName} (customer_id, service, recharge_number, app_created_at, recipient, user_type, paytype, source_id, category_id, product_id, type, template_id, priority, status, retry_count, retry_interval, max_retry_count, send_at, created_at, updated_at, is_encrypted, data)
        VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)`;
        let insertQueryParams = [
            _.get(notifier, 'customer_id', null),
            _.get(notifier, 'service', null),
            _.get(notifier, 'recharge_number', null),
            _.get(notifier, 'app_created_at', null),
            _.get(notifier, 'recipient', null),
            _.get(notifier, 'user_type', null),
            _.get(notifier, 'paytype', null),
            _.get(notifier, 'source_id', null),
            _.get(notifier, 'category_id', null),
            _.get(notifier, 'product_id', null),
            _.get(notifier, 'type', null),
            _.get(notifier, 'template_id', null),
            _.get(notifier, 'priority', null),
            _.get(notifier, 'status', null),
            _.get(notifier, 'retry_count', null),
            _.get(notifier, 'retry_interval', null),
            _.get(notifier, 'max_retry_count', null),
            _.get(notifier, 'send_at', null),
            _.get(notifier, 'app_created_at', null),
            _.get(notifier, 'app_created_at', null),
            _.get(notifier, 'is_encrypted', 0),
             data
        ]

        const consistencyLevel = cassandra.types.consistencies.localQuorum;
        self.notificationNewClusterClient.execute(insertQuery, insertQueryParams, { prepare : true, consistency: consistencyLevel})
        .then(() => {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_LOG", 'STATUS:SUCCESS', 'TYPE:INSERT', 'TABLE:notifications', 'SOURCE:MAIN_FLOW']);
            self.logger.log(`createEntryInNotificationTable:: entry created in ${tableName} for record`, notifier, _.get(notifier, 'service', ''));
            cb(null,action, kafkaPublish, dbInsertId);
        })
        .catch(error => {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_LOG", 'STATUS:ERROR', 'TYPE:INSERT', 'TABLE:notifications', 'SOURCE:MAIN_FLOW']);
            self.L.error('createEntryInNotificationTable::', `error while creating entry in ${tableName} for record \
            ${JSON.stringify(notifier)}`, error);
            cb(null,action, kafkaPublish, dbInsertId);
        })
    }

    checkIfNotifRecordIsEncrypted(record) {
        return _.get(record, 'is_encrypted', _.get(record, 'data.is_encrypted', 0)) === 1;
    }

    /** ccbp encryption changes needed here, TODO DISCUSS : only created notif status needs to be created, so no need to do it for decrypted RN */
    updateEntryInNotificationTable(cb, responseObj, row){
        let self=this;
        let params = self.EncryptionDecryptioinHelper.getNotificationParamsForCCBP(row);
        let isCCEncryptionEnabled = self.EncryptionDecryptioinHelper.isWhitelistedForCC(params.service, params.paytype, params.customerId);
        let recharge_number = _.get(row, 'recharge_number', null);
        if(isCCEncryptionEnabled && self.checkIfNotifRecordIsEncrypted(row)) {
            recharge_number = self.EncryptionDecryptioinHelper.encryptData(recharge_number);
        }

        let tableName = self.getTableNameForNotification(row);
        let updateQuery = `UPDATE ${tableName} SET status = ?,sent_at=?, updated_at = ?, job_id = ?, error_msg = ? WHERE customer_id = ? AND service = ?`;
        let updateQueryParams = [
            _.get(responseObj, 'status', null),
            _.get(responseObj, 'sent_at', null) ? MOMENT(_.get(responseObj, 'sent_at', null)).valueOf() : null,
            MOMENT().valueOf(),
            _.get(responseObj, 'job_id', null),
            _.get(responseObj, 'error_msg', null),
            _.get(row, 'customer_id', null),
            _.get(row, 'service', null)
            ]

        if(tableName == self.notificationLogsTable){
            updateQuery += ` AND type = ?`;
            updateQueryParams.push(_.get(row, 'type', null));
        }

        updateQuery += ` AND recharge_number = ? AND app_created_at = ?`;
        updateQueryParams.push(recharge_number, _.get(row, 'app_created_at', null));

        const consistencyLevel = cassandra.types.consistencies.localQuorum;
        self.notificationNewClusterClient.execute(updateQuery, updateQueryParams, { prepare : true, consistency: consistencyLevel})
        .then(result => {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_LOG", 'STATUS:SUCCESS', 'TYPE:UPDATE', 'TABLE:notifications', 'SOURCE:MAIN_FLOW']);
            self.L.log('updateEntryInNotificationTable::', `entry updated in ${self.notificationTable} for record ${JSON.stringify(row)}. Rows updated: ${result.rowLength}`);
            cb(null, responseObj);
        })
        .catch(error => {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_LOG", 'STATUS:ERROR', 'TYPE:UPDATE', 'TABLE:notifications', 'SOURCE:MAIN_FLOW']);
            self.L.error('updateEntryInNotificationTable::', `error while updating entry in ${self.notificationTable} for record \
            ${JSON.stringify(row)}`, error);
            cb(null, responseObj);
        })
    }

    createNotification(cb, record) {
        const self = this;
        let encryptedRecord = _.cloneDeep(record);
        let isEncrypted = 0;

        let params = self.EncryptionDecryptioinHelper.getNotificationParamsForCCBP(record);
        
        _.set(encryptedRecord, 'data', JSON.stringify(_.get(record, 'data', null)));

        if(self.EncryptionDecryptioinHelper.isWhitelistedForCC(params.service, params.paytype, params.customerId)) {
            _.set(encryptedRecord, 'recharge_number', self.EncryptionDecryptioinHelper.encryptData(_.get(record, 'recharge_number', null)));
            _.set(encryptedRecord, 'data', self.EncryptionDecryptioinHelper.encryptData(_.get(encryptedRecord, 'data', null)));
            _.set(encryptedRecord, 'recipient', self.EncryptionDecryptioinHelper.encryptData(_.get(record, 'recipient', null)));
            isEncrypted = 1;
        }
        _.set(encryptedRecord, 'is_encrypted', isEncrypted);
        
        return self.createNotificationDB(cb, encryptedRecord);
    }

    createNotificationDB(cb, record) {
        const self = this;
        let sendAtDay = MOMENT(_.get(record, 'send_at', null)).date();
        let tablePreffix = Math.floor(sendAtDay/7) + 1;
        let notificationTable = `notification${tablePreffix}`;
        let status = self.notification.getStatusForNotificationCreation(record);
        const query = `INSERT INTO ${notificationTable} (recharge_number, recipient, type, template_id, source_id, category_id, product_id, \
        send_at, created_at, data, max_retry_count, priority, retry_interval, status, updated_at, meta, retry_count, error_msg, job_id, is_encrypted) VALUES (?, ?, ?, ?, ?, ?, ?, ?, \
            ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;
    
        const queryParams = [
            String(_.get(record, 'recharge_number', null)), String(_.get(record, 'recipient', null)), _.get(record, 'type', null), _.get(record, 'template_id', null),
            _.get(record, 'source_id', null), _.get(record, 'category_id', null), _.get(record, 'product_id', null), _.get(record, 'send_at', null),
            _.get(record, 'created_at', null), _.get(record, 'data', null), _.get(record, 'max_retry_count', null), 
            _.get(record, 'priority', null), _.get(record, 'retry_interval', null), status, _.get(record, 'updated_at', null), 
            _.get(record, 'meta', null), _.get(record, 'retry_count', 0), _.get(record, 'error_msg', null), _.get(record, 'job_id', null), _.get(record, 'is_encrypted', 0)
        ]
        // debugKey = rechargeNumber_recipient_type_templateId_sourceId_categoryId_productId_sendAt (Primary Key)
        let debugKey = `${_.get(record, 'recharge_number', null)}_${_.get(record, 'recipient', null)}_${_.get(record, 'type', null)}_${_.get(record, 'template_id', null)}_${_.get(record, 'source_id', null)}_${_.get(record, 'category_id', null)}_${_.get(record, 'product_id', null)}_${_.get(record, 'send_at', null)}`;
        self.notificationNewClusterClient.execute(query, queryParams, { prepare: true })
        .then(() => {
            self.L.log('createNotification::', `entry created in cassandra ${notificationTable} table for debugKey ${debugKey}`);
            return cb(null, status);
        })
        .catch(error => {
            self.L.error('createNotification::', `error while creating entry in cassandra ${notificationTable} table for debugKey ${debugKey}`, error);
            return cb(error, status);
        })
    }

    getNotifications(cb, tableName, whereCondition, queryParams) {
        let self = this;
        const query = `SELECT * FROM ${tableName} WHERE ${whereCondition}`;
        self.notificationNewClusterClient.execute(query, queryParams, { prepare: true})
        .then((result) => {
            self.L.log('cassandraBills::getNotifications::', `notifications fetched ${result.rows.length} from ${tableName} table for queryParams ${queryParams}`);
            return cb(null, result);
        })
        .catch((err) => {
            self.L.error('cassandraBills::getNotifications::', `error while fetching notifications from ${tableName} for queryParams ${queryParams}`, err);
            cb(err);
        })
    }

    updateNotification(cb, fields, whereCondition, params, sendAt) {
        let self = this;
        let notificationTable = `notification${self.getNotificationTableSuffix(sendAt)}`;
        let query = `UPDATE ${notificationTable} SET `;
        fields.forEach(val => {
            query += val + ' = ?,';
        });
        query = query.replace(/,\s*$/, "");
        query += ' WHERE ' + whereCondition + ' IF EXISTS;';
        self.L.log('cassandraBills::updateNotification::', `executing query ${query} with params ${params}`);
        
        const queryParams = [...params];
        self.notificationNewClusterClient.execute(query, queryParams, { prepare: true})
        .then((result) => {
            self.L.log('cassandraBills::updateNotification::', `query executed, result returned is ${result}`);
            return cb(null, result);
        })
        .catch(error => {
            self.L.error('cassandraBills::updateNotification::', `error while executing update query`, error);
            return cb(error);
        })
    }

    deleteNotification(cb, whereCondition, params, sendAt) {
        let self = this;
        let notificationTable = `notification${self.getNotificationTableSuffix(sendAt)}`;
        let query = `DELETE FROM ${notificationTable} WHERE ${whereCondition} IF EXISTS`;

        self.notificationNewClusterClient.execute(query, params, { prepare: true})
        .then((result) => {
            cb(null, result);
        })
        .catch((error) => {
            self.L.error('cassandraBills::deleteNotification::', `error while executing delete query`, error);
            return cb(error);
        });
    }

    getJobIdNotificationMappingRecord(jobId, jobIdNotificationMappingTable) {
        let self = this;
        return new Promise((resolve, reject) => {
            let query = `SELECT * FROM ${jobIdNotificationMappingTable} WHERE job_id = ?`;
            let queryParams = [jobId];
            self.L.log('cassandraBills::getJobIdNotificationMappingRecord::', `executing query ${query}${jobId}`);
            self.client.execute(query, queryParams, { prepare: true})
            .then((result) => {
                self.L.log('cassandraBills::getJobIdNotificationMappingRecord::', `query returned ${JSON.stringify(result.rows)} for jobId ${jobId}`);
                resolve(result.rows);
            })
            .catch((err) => {
                self.L.error('cassandraBills::getJobIdNotificationMappingRecord::', `error occurred while fetching notification record against jobId ${jobId}`, err);
                resolve(null);
            })
        })
    }

    createJobIdNotificationMappingRecord(cb, jobId, record) {
        let self = this;
        let tableSuffix = self.getNotificationTableSuffix(_.get(record, 'send_at', null));
        let jobIdNotificationTable = `jobid_notification_mapping${tableSuffix}`;
        /** ccbp encryption changes needed here to encrypt recharge_number */
        let params = self.EncryptionDecryptioinHelper.getNotificationParamsForCCBP(record);
        let isCCEncryptionEnabled = self.EncryptionDecryptioinHelper.isWhitelistedForCC(params.service, params.paytype, params.customerId);
        let is_encrypted = 0;
        let recharge_number = _.get(record, 'recharge_number', null);
        let recipient = String(_.get(record, 'recipient', null));
        if(isCCEncryptionEnabled) {
            recharge_number = self.EncryptionDecryptioinHelper.encryptData(recharge_number);
            recipient = self.EncryptionDecryptioinHelper.encryptData(recipient);
            is_encrypted = 1;
        }

        let query = `INSERT INTO ${jobIdNotificationTable} (job_id, recharge_number, recipient, type, template_id, source_id, category_id, product_id, send_at, is_encrypted) VALUES (?,?,?,?,?,?,?,?,?,?)`;
        const queryParams = [
            jobId, String(recharge_number), recipient, _.get(record, 'type', null), _.get(record, 'template_id', null),
            _.get(record, 'source_id', null), _.get(record, 'category_id', null), _.get(record, 'product_id', null), _.get(record, 'send_at', null), is_encrypted
        ]
        
        let recordKey = `${_.get(record, 'recharge_number', null)}_${_.get(record, 'recipient', null)}_${_.get(record, 'type', null)}_${_.get(record, 'template_id', null)}_${_.get(record, 'source_id', null)}_${_.get(record, 'category_id', null)}_${_.get(record, 'product_id', null)}_${_.get(record, 'send_at', null)}`;
        if(isCCEncryptionEnabled) {
            recordKey = `${self.EncryptionDecryptioinHelper.encryptData(_.get(record, 'recharge_number', null))}_${self.EncryptionDecryptioinHelper.encryptData(_.get(record, 'recipient', null))}_${_.get(record, 'type', null)}_${_.get(record, 'template_id', null)}_${_.get(record, 'source_id', null)}_${_.get(record, 'category_id', null)}_${_.get(record, 'product_id', null)}_${_.get(record, 'send_at', null)}`;
        }
        self.client.execute(query, queryParams, { prepare: true })
        .then(() => {
            self.L.log('createJobIdNotificationMappingRecord::', `entry created in cassandra ${jobIdNotificationTable} table for record ${recordKey}`);
            return cb(null);
        })
        .catch(error => {
            self.L.error('createJobIdNotificationMappingRecord::', `error while creating entry in cassandra ${jobIdNotificationTable} table for record ${recordKey}`, error);
            return cb(error);
        })
    }

    getNotificationTableSuffix(sendAt) {
        let sendAtDay = MOMENT(sendAt).date();
        let tableSuffix = Math.floor(sendAtDay/7) + 1;
        return tableSuffix;
    }

    // createEntryInNotificationTable(action, kafkaPublish, dbInsertId, notifier, cb){
    //     let self = this;
    //     let insertQuery = `INSERT INTO ${self.notificationTable} (customer_id, service, recharge_number, app_created_at, recipient, user_type, paytype, source_id, category_id, product_id, type, template_id, priority, status, retry_count, retry_interval, max_retry_count, send_at, created_at, updated_at)
    //     VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)`;
    //     let insertQueryParams = [
    //         _.get(notifier, 'customer_id', null),
    //         _.get(notifier, 'service', null),
    //         _.get(notifier, 'recharge_number', null),
    //         _.get(notifier, 'app_created_at', null),
    //         _.get(notifier, 'recipient', null),
    //         _.get(notifier, 'user_type', null),
    //         _.get(notifier, 'paytype', null),
    //         _.get(notifier, 'source_id', null),
    //         _.get(notifier, 'category_id', null),
    //         _.get(notifier, 'product_id', null),
    //         _.get(notifier, 'type', null),
    //         _.get(notifier, 'template_id', null),
    //         _.get(notifier, 'priority', null),
    //         _.get(notifier, 'status', null),
    //         _.get(notifier, 'retry_count', null),
    //         _.get(notifier, 'retry_interval', null),
    //         _.get(notifier, 'max_retry_count', null),
    //         _.get(notifier, 'send_at', null),
    //         _.get(notifier, 'app_created_at', null),
    //         _.get(notifier, 'app_created_at', null)
    //     ]

    //     const consistencyLevel = cassandra.types.consistencies.localQuorum;
    //     self.notificationClient.execute(insertQuery, insertQueryParams, { prepare : true, consistency: consistencyLevel})
    //     .then(() => {
    //         utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_LOG", 'STATUS:SUCCESS', 'TYPE:INSERT', 'TABLE:notifications', 'SOURCE:MAIN_FLOW']);
    //         self.L.log('createEntryInNotificationTable::', `entry created in ${self.notificationTable} for record ${JSON.stringify(notifier)}`);
    //         cb(null,action, kafkaPublish, dbInsertId);
    //     })
    //     .catch(error => {
    //         utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_LOG", 'STATUS:ERROR', 'TYPE:INSERT', 'TABLE:notifications', 'SOURCE:MAIN_FLOW']);
    //         self.L.error('createEntryInNotificationTable::', `error while creating entry in ${self.notificationTable} for record \
    //         ${JSON.stringify(notifier)}`, error);
    //         cb(null,action, kafkaPublish, dbInsertId);
    //     })
    // }

    // updateEntryInNotificationTable(cb, responseObj, row){
    //     let self=this;
    //     let updateQuery = `UPDATE ${self.notificationTable} SET status = ?,sent_at=?, updated_at = ?, job_id = ?, error_msg = ? WHERE customer_id = ? AND service = ? AND recharge_number = ? AND app_created_at = ?`;
    //     let updateQueryParams = [
    //         _.get(responseObj, 'status', null),
    //         _.get(responseObj, 'sent_at', null) ? MOMENT(_.get(responseObj, 'sent_at', null)).valueOf() : null,
    //         MOMENT().valueOf(),
    //         _.get(responseObj, 'job_id', null),
    //         _.get(responseObj, 'error_msg', null),
    //         _.get(row, 'customer_id', null),
    //         _.get(row, 'service', null),
    //         _.get(row, 'recharge_number', null),
    //         _.get(row, 'app_created_at', null)
    //     ]
    //     const consistencyLevel = cassandra.types.consistencies.localQuorum;
    //     self.notificationClient.execute(updateQuery, updateQueryParams, { prepare : true, consistency: consistencyLevel})
    //     .then(() => {
    //         utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_LOG", 'STATUS:SUCCESS', 'TYPE:UPDATE', 'TABLE:notifications', 'SOURCE:MAIN_FLOW']);
    //         self.L.log('updateEntryInNotificationTable::', `entry updated in ${self.notificationTable} for record ${JSON.stringify(row)}`);
    //         cb(null, responseObj);
    //     })
    //     .catch(error => {
    //         utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_LOG", 'STATUS:ERROR', 'TYPE:UPDATE', 'TABLE:notifications', 'SOURCE:MAIN_FLOW']);
    //         self.L.error('updateEntryInNotificationTable::', `error while updating entry in ${self.notificationTable} for record \
    //         ${JSON.stringify(row)}`, error);
    //         cb(null, responseObj);
    //     })
    // }


    dumpIntoCustIdRnMappingTable(cb, record) {
        let self = this;
        let isCcEncrypted = _.get(record, 'is_encrypted', false);
        let product_id = record.product_id;
        let service = _.get(self.config, ['CVR_DATA', product_id, 'service'], '').toLowerCase();
        let paytype =  _.get(self.config, ['CVR_DATA', product_id, 'paytype'], "").toLowerCase();
        let customer_id = record.customer_id;
        let deleteNonEncryptedCcRecodsAndInsertNewEncryptedRecords = _.get(self.config, ['DYNAMIC_CONFIG', 'CCBP', 'ENCRYPTION_CONFIG', 'DELETE_NON_ENCRYPTED_AND_INSERT_ENCRYPTED_RECORDS_FOR_CCBP'], true);

        if(self.EncryptionDecryptioinHelper.isWhitelistedForCC(service, paytype, customer_id) && deleteNonEncryptedCcRecodsAndInsertNewEncryptedRecords == true && isCcEncrypted == false) {
            let encrypted_params = _.cloneDeep(record);
            encrypted_params.recharge_number = self.EncryptionDecryptioinHelper.encryptData(record.recharge_number);
            encrypted_params.is_encrypted = 1;

            self.updateCustIdRnMappingTable((err, result)=> {
                if(err) return cb(err);

                const updatedRecord = _.get(result.rows[0], '[applied]', false);

                if(updatedRecord == false) {
                    self.L.log('cassandraBills::dumpIntoCustIdRnMappingTable::', `record doesnot exists in ${self.custIdRnMappingTable} for ${record.debug_key}`);//recharge_number already logged in debug_key
                    self.dumpIntoCustIdRnMappingTableDefault((err) => {
                        if(err != null) return cb(err);

                        // deleting decrypted record from cust_id_rn_mapping table if exists
                        return self.deleteFromCustIdRnMappingTable(cb, record);

                    }, encrypted_params)
                } else {
                    return cb(null);
                }


            }, encrypted_params);
        } else {
            return self.dumpIntoCustIdRnMappingTableDefault(cb, record);
        }
    }

    dumpIntoCustIdRnMappingTableDefault(cb, record) {
        let self = this;
        let isCcEncrypted = _.get(record, 'is_encrypted', false);
        let query = `INSERT INTO ${self.custIdRnMappingTable} (customer_id, service, recharge_number, operator, paytype, product_id, created_at, updated_at, status, meta ${isCcEncrypted == true ? ', is_encrypted' : ''}) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?${isCcEncrypted == true ? ', ?' : ''})`;
        let params = [
            record.customer_id, record.service, record.recharge_number, record.operator,
            record.paytype, record.product_id, 
            MOMENT().format('YYYY-MM-DD HH:mm:ss'), MOMENT().format('YYYY-MM-DD HH:mm:ss'),
            record.status, record.meta
        ]

        if(isCcEncrypted == true) params.push(1);

        self.client.execute(query, params, { prepare: true })
        .then(() => {
            self.L.log('cassandraBills::dumpIntoCustIdRnMappingTable::', `entry created in ${self.custIdRnMappingTable} for ${record.debug_key}`);
            return cb(null);
        })
        .catch(error => {
            self.L.error('cassandraBills::dumpIntoCustIdRnMappingTable::', `error while creating entry in ${self.custIdRnMappingTable} for ${record.debug_key}`, error);
            return cb(error);
        })
    }

    deleteFromCustIdRnMappingTable(cb, record) {
        let self = this;
        let query = `DELETE FROM ${self.custIdRnMappingTable} WHERE customer_id = ? AND service = ? AND recharge_number = ? AND operator = ?`;
        let params = [
            record.customer_id, record.service, record.recharge_number, record.operator
        ]

        self.client.execute(query, params, { prepare: true })
        .then(() => {
            self.L.log('cassandraBills::deleteFromCustIdRnMappingTable::', `entry deleted from ${self.custIdRnMappingTable} for ${record.debug_key}`);//recharge_number already logged in debug_key
            cb(null);
        })
        .catch(error => {
            self.L.error('cassandraBills::deleteFromCustIdRnMappingTable::', `error while deleting entry from ${self.custIdRnMappingTable} for ${record.debug_key}`, error);
            cb(error);
        })
    }

    updateCustIdRnMappingTable(cb, record) {
        let self = this;
        let isCcEncrypted = _.get(record, 'is_encrypted', false);
        let query = `update ${self.custIdRnMappingTable} set paytype = ?, product_id = ?,  updated_at = ?,  status = ? , meta = ?, is_encrypted = ?  where customer_id = ? and service = ? and recharge_number = ? and  operator = ? IF EXISTS`;
        let params = [record.paytype, record.product_id, MOMENT().format('YYYY-MM-DD HH:mm:ss'), record.status, record.meta, isCcEncrypted == true ? 1 : 0, record.customer_id, record.service, record.recharge_number, record.operator];

        self.client.execute(query, params, { prepare: true })
        .then((result) => {
            self.L.log('cassandraBills::updateCustIdRnMappingTable::', `entry updated in ${self.custIdRnMappingTable} for ${record.debug_key}`);//recharge_number already logged in debug_key
            return cb(null, result);
        })
        .catch(error => {
            self.L.error('cassandraBills::updateCustIdRnMappingTable::', `error while creating entry in ${self.custIdRnMappingTable} for ${record.debug_key}`, error);
            return cb(error);
        })
    }

    getDate(){
        return MOMENT().format('YYYY-MM-DD HH:mm:ss');
    } 

    deleteAgentrecentBill(cb, record){
        let self = this;
        self.L.log(record);
        const selectQuery = `SELECT * FROM ${self.tableResolver.getNewBillsRecentTable(record.customerid)} WHERE customer_id = ?`;
        const deleteQuery = `DELETE FROM ${self.tableResolver.getNewBillsRecentTable(record.customerid)} WHERE customer_id = ? AND service = ? AND paytype = ? AND operator=?`;
        const updateQuery = `UPDATE ${self.tableResolver.getNewBillsRecentTable(record.customerid)} SET rn_count = 50 WHERE customer_id = ? AND service = ? AND paytype = ? AND operator = ?` ;
        self.L.log('deleteAgentrecentBill::', `record fetched from user agent ${record.customerid}`);
        const params = [record.customerid];
        self.client.execute(selectQuery, params, { prepare: true})
        .then((result) => {
            if(result && result.rows && result.rows.length > 0){
                self.L.log('deleteAgentrecentBill::', `record is eligible for delete or update for ${record.customerid} having records ${result.rows.length}`);
                ASYNC.eachSeries(result.rows, (row, cb) => {

                    let rechargeSet = _.get(row, 'recharge_number', '[]')
                    if (!Array.isArray(rechargeSet)) {
                        self.L.log('deleteAgentrecentBill::', `recharge_number is not a Set for customer_id ${row.customer_id}`);
                        return cb(null);
                    }
                    let query ;
                    if(rechargeSet.length >50){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:AGENT_RECENT_BILL_UPDATE", 'STATUS:SUCCESS', 'TYPE:DELETE']);
                        self.L.log('deleteAgentrecentBill::', `record to be deleted for customer_id ${record.customerid}`);
                        query = deleteQuery;
                    } else {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:AGENT_RECENT_BILL_UPDATE", 'STATUS:SUCCESS', 'TYPE:UPDATE']);
                        self.L.log('deleteAgentrecentBill::', `record to be updated for customer_id ${record.customerid}`);
                        query = updateQuery;
                    }
                    self.L.log('deleteAgentrecentBill::', `executing query ${query} with params ${row.customer_id}, ${row.service}, ${row.paytype}, ${row.operator}`);
                    let params = [row.customer_id, row.service, row.paytype, row.operator];
                    self.client.execute(query, params, { prepare: true})
                    .then(() => {
                        self.L.log('deleteAgentrecentBill::', `record deleted or updated for customer_id ${record.customerid}`);
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:AGENT_RECENT_BILL_UPDATE", 'STATUS:SUCCESS', 'TYPE:EXECUTED']);
                        return cb(null);
                    })
                    .catch((error) => {
                        self.L.error('deleteAgentrecentBill::', `error while deleting record for customer_id ${record.customerid}`, error);
                        return cb(error);
                    });
                },(err) => {
                    if(err) {
                    return cb(err);
                    }
                    return cb();
                });
            } else {
                return cb(null);
            }
        })
    }

    getCustomerScoreDetailsFromCustomerScoringTable(cust_id, service, paytype) {
        let self = this,
        query = 'select * from customer_score where customer_id = ? and service = ? and paytype = ?',
        params = [cust_id, service, paytype];

        return new Promise ((resolve, reject) => {
            self.client.execute(query, params, { prepare: true })
            .then(result => {
                self.L.log('getCustomerScoreDetailsFromCustomerScoringTable::', `result: ${JSON.stringify(result.rows.length)} for ${query} ${JSON.stringify(params)}`);
                return resolve(result.rows);
            })
            .catch(error => {
                self.L.error('getCustomerScoreDetailsFromCustomerScoringTable::', `DB exception! for ${JSON.stringify(params)} err: ${error}`);
                return reject(new Error(`getCustomerScoreDetailsFromCustomerScoringTable ::DB exception! ${error.message}`));
            });
        });
    }
    get_cust_id_rn_mapping_records(cust_id, category) {
        let query = "select * from cust_id_rn_mapping where customer_id = ? and service = ?",
            queryParams = [cust_id, category];
        const self = this;
        return new Promise ((resolve, reject) => {
            self.client.execute(query, queryParams, { prepare: true })
            .then(result => {
                self.L.log(`get_cust_id_rn_mapping_records :: data fetched ${result.rows.length} for ${JSON.stringify(queryParams)}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_SCORE_INGESTION_CONSUMER", 'STATUS:RECORD_FETCHED_FROM_CASSANDRA', "TYPE:DB_QUERY", "FUNCTION_NAME:get_cust_id_rn_mapping_records"]);
                return resolve(result.rows);
            })
            .catch(error => {
                self.L.error(`get_cust_id_rn_mapping_records ::DB exception! for ${JSON.stringify(queryParams)} err: ${error}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_SCORE_INGESTION_CONSUMER", 'STATUS:FAILED_FETCHING_RECORD_FROM_CASSANDRA', "TYPE:DB_QUERY", "FUNCTION_NAME:get_cust_id_rn_mapping_records"]);
                return reject(`get_cust_id_rn_mapping_records ::DB exception! ${error.message}`);
            });
        })
    }

    update_cust_id_rn_mapping_record(custId, service, recharge_number, operator, params, query2) {
        let query1 = "update cust_id_rn_mapping ", query3 ="where customer_id = ? and service = ? and recharge_number = ? and operator = ?";
            params.push(custId, service, recharge_number, operator);
        const self = this;
        return new Promise ((resolve, reject) => {
            self.client.execute(query1+query2+query3, params, { prepare: true })
            .then(result => {
                self.L.log(`update_cust_id_rn_mapping_record :: result: ${result} for ${JSON.stringify(params)}`);
                return resolve(result.rows);
            })
            .catch(error => {
                self.L.error(`update_cust_id_rn_mapping_record ::DB exception! for ${JSON.stringify(params)} err: ${error}`);
                return reject(new Error(`update_cust_id_rn_mapping_record ::DB exception! ${error.message}`));
            });
        })

    }

    addCustomerScoreDetailsInCustomerScoringTable(params, created_at = null) {
        let self = this,
        paramPlaceHolder,
        questionMarkPlaceHolder;
        if(created_at != null) {
            paramPlaceHolder = ", created_at";
            questionMarkPlaceHolder = ",?";
            params.push(created_at);
        } else {
            paramPlaceHolder = "";
            questionMarkPlaceHolder = "";
        }
        let query = `insert into customer_score (customer_id, service, paytype, operator, recharge_number, score, bucket, updated_at ${paramPlaceHolder}) values (?,?,?,?,?,?,?,?${questionMarkPlaceHolder})`;
        return new Promise ((resolve, reject) => {
            self.client.execute(query, params, { prepare: true })
            .then(result => {
                self.L.log('addCustomerScoreDetailsInCustomerScoringTable::', `inserted successfully for ${query} ${JSON.stringify(params)}`);
                return resolve(result.rows);
            })
            .catch(error => {
                self.L.error('addCustomerScoreDetailsInCustomerScoringTable::', `DB exception! for ${query} ${JSON.stringify(params)} err: ${error}`);
                return reject(new Error(`addCustomerScoreDetailsInCustomerScoringTable ::DB exception! ${error.message}`));
            });
        });
    }
    insertWhatsappCustomerWhitelistRecord(params) {
        let self = this
        let query = `
            INSERT INTO whatsapp_whitelisted_customers (
                customer_id, service, paytype, template_name, notification_type, day_value, operator, template_id, expiry_date, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?)
        `;
        
        return new Promise((resolve, reject) => {
            self.notificationNewClusterClient.execute(query, params, { prepare: true })
            .then(result => {
                self.L.log(`addWhatsappCustomerWhitelistRecordTable :: inserted successfully for ${query} ${JSON.stringify(params)}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:CRON_WHATSAPP_CUSTOMER_WHITELIST_INGESTOR", 'STATUS:RECORD_INSERTED_IN_CASSANDRA', "TYPE:DB_QUERY", "FUNCTION_NAME:insertWhatsappCustomerWhitelistRecord"]);
                return resolve(result.rows);
            })
            .catch(error => {
                self.L.error(`addWhatsappCustomerWhitelistRecordTable ::DB exception! for ${query} ${JSON.stringify(params)} err: ${error}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:CRON_WHATSAPP_CUSTOMER_WHITELIST_INGESTOR", 'STATUS:RECORD_INSERTION_FAILED', "TYPE:DB_QUERY", "FUNCTION_NAME:insertWhatsappCustomerWhitelistRecord"]);
                return reject(new Error(`addWhatsappCustomerWhitelistRecordTable ::DB exception! ${error.message}`));
            });
        });
    }
    getCustomerData(customer_id, service, paytype) {
        let self = this;
        let query = `
            SELECT * FROM whatsapp_whitelisted_customers
            WHERE customer_id = ? AND service = ? AND paytype = ?
        `;
        let params = [customer_id, service, paytype];
    
        return new Promise((resolve, reject) => {
            self.notificationNewClusterClient.execute(query, params, { prepare: true })
            .then(result => {
                self.L.log(`getCustomerData :: fetched successfully for ${query} ${JSON.stringify(params)}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:CRON_WHATSAPP_CUSTOMER_WHITELIST_INGESTOR", 'STATUS:RECORD_FETCHED_FROM_CASSANDRA', "TYPE:DB_QUERY", "FUNCTION_NAME:getCustomerData"]);
                return resolve(result.rows);
            })
            .catch(error => {
                self.L.error(`getCustomerData ::DB exception! for ${query} ${JSON.stringify(params)} err: ${error}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:CRON_WHATSAPP_CUSTOMER_WHITELIST_INGESTOR", 'STATUS:RECORD_FETCH_FAILED', "TYPE:DB_QUERY", "FUNCTION_NAME:getCustomerData"]);
                return reject(new Error(`getCustomerData ::DB exception! ${error.message}`));
            });
        });
    }
    updateWhatsappCustomerWhitelistExpiryDate(params) {
        let self = this;
        let query = `
            UPDATE notification.whatsapp_whitelisted_customers
            SET template_id = ?, expiry_date = ?, updated_at = ?
            WHERE customer_id = ? AND service = ? AND paytype = ? AND notification_type = ? AND day_value = ? AND template_name = ? AND operator = ?
        `;
        return new Promise((resolve, reject) => {
            self.notificationNewClusterClient.execute(query, params, { prepare: true })
            .then(result => {
                self.L.log(`updateWhatsappCustomerWhitelistExpiryDate :: updated successfully for ${query} ${JSON.stringify(params)}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:CRON_WHATSAPP_CUSTOMER_WHITELIST_INGESTOR", 'STATUS:RECORD_UPDATED_IN_CASSANDRA', "TYPE:DB_QUERY", "FUNCTION_NAME:updateWhatsappCustomerWhitelistExpiryDate"]);
                return resolve(result.rows);
            })
            .catch(error => {
                self.L.error(`updateWhatsappCustomerWhitelistExpiryDate ::DB exception! for ${query} ${JSON.stringify(params)} err: ${error}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:CRON_WHATSAPP_CUSTOMER_WHITELIST_INGESTOR", 'STATUS:RECORD_UPDATE_FAILED', "TYPE:DB_QUERY", "FUNCTION_NAME:updateWhatsappCustomerWhitelistExpiryDate"]);
                return reject(new Error(`updateWhatsappCustomerWhitelistExpiryDate ::DB exception! ${error.message}`));
            });
        });
    }
    deleteWhatsappCustomerWhitelistRecord(params) {
        let self = this;
        let query = `
            DELETE FROM whatsapp_whitelisted_customers
            WHERE customer_id = ? AND service = ? AND paytype = ? AND notification_type = ? AND day_value = ? AND template_name = ? AND operator = ?
        `;
        
        return new Promise((resolve, reject) => {
            self.notificationNewClusterClient.execute(query, params, { prepare: true })
            .then(result => {
                self.L.log(`deleteWhatsappCustomerWhitelistRecord :: deleted successfully for ${query} ${JSON.stringify(params)}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:CRON_WHATSAPP_CUSTOMER_WHITELIST_INGESTOR", 'STATUS:RECORD_DELETED_IN_CASSANDRA', "TYPE:DB_QUERY", "FUNCTION_NAME:deleteWhatsappCustomerWhitelistRecord"]);
                return resolve(result.rows);
            })
            .catch(error => {
                self.L.error(`deleteWhatsappCustomerWhitelistRecord ::DB exception! for ${query} ${JSON.stringify(params)} err: ${error}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:CRON_WHATSAPP_CUSTOMER_WHITELIST_INGESTOR", 'STATUS:RECORD_DELETE_FAILED', "TYPE:DB_QUERY", "FUNCTION_NAME:deleteWhatsappCustomerWhitelistRecord"]);
                return reject(new Error(`deleteWhatsappCustomerWhitelistRecord ::DB exception! ${error.message}`));
            });
        });
    }
    
    


    async fetchServiceNotificationCount(service, date, type) {
        let self = this;
        let query = `select * from ${self.serviceNotificationCapping} where service = ? and date = ? and type = ?`;
        let queryParams = [
            _.toLower(service),
            date,
            _.toUpper(type)
        ]

        self.L.log('fetchServiceNotificationCount::', `executing query ${query} with params ${queryParams}`);
        try {
            const result = await self.notificationNewClusterClient.execute(query, queryParams, { prepare : true });
            return result.rows.length == 0 ? 0 : Number(result.rows[0].notification_count);
        } catch(error) {
            self.L.error('fetchServiceNotificationCount::', `error occurred ${error}`);
            throw new Error('fetchServiceNotificationCount:: DB Exception!');
        }
    }

    async updateServiceNotificationCount(service, date, type, notificationCount) {
        let self = this;
        let query = `UPDATE ${self.serviceNotificationCapping} SET notification_count = notification_count + ? WHERE service = ? and date = ? and type = ?`;
        let queryParams = [
            Number(notificationCount), 
            _.toLower(service), 
            date,
            _.toUpper(type)
        ]

        self.L.log('updateServiceNotificationCount::', `executing query ${query} with params ${queryParams}`);
        try {
            const result = await self.notificationNewClusterClient.execute(query, queryParams, { prepare : true });
            return result.rows;
        } catch (err) {
            self.L.error('updateServiceNotificationCount::', `error occurred while updating service notification count, err ${err}`);
            throw new Error('updateServiceNotificationCount:: DB Exception!');
        }
    }

    async getWhatsappWhitelistedCustomerRecords(customerId, service, paytype, notificationType, dayValue) {
        let self = this;
        let query = `SELECT * FROM ${self.whatsappWhitelistedCustomers} WHERE customer_id = ? AND service = ? AND paytype = ? AND notification_type = ? AND day_value = ?`;
        let queryParams = [
            String(customerId),
            _.toLower(service),
            _.toLower(paytype),
            _.toUpper(notificationType),
            dayValue
        ]
        
        self.L.log('getWhatsappWhitelistedCustomerRecords::', `executing  query ${query} with params ${queryParams}`);
        try {
            const result = await self.notificationNewClusterClient.execute(query, queryParams, { prepare : true });
            return result.rows;
        } catch(error) {
            self.L.error('getWhatsappWhitelistedCustomerRecords::', `error occurred ${error}`);
            throw new Error('getWhatsappWhitelistedCustomerRecords:: DB Exception!');
        }
    }

    checkIfDbRecordIsEncrypted(record) {
        return _.get(record, 'is_encrypted', false);
    }

    getPaymentDatesFromActivePaytmUsers(customerId, mapped_service, callback) {
        let self = this;
        let query = `SELECT latest_payment_date, payment_date_list FROM active_paytm_users_new WHERE customer_id = ? AND service = ?`;
        let queryParams = [customerId, mapped_service];

        self.client.execute(query, queryParams, { prepare: true })
            .then(result => {
                callback(null, result);
            })
            .catch(error => {
                callback(error);
            });
    }
    /**
     * Inserts a record into the active_paytm_users table.
     *
     * @param {Object} params - The parameters for the query.
     * @returns {Promise<Array>} - A promise that resolves with the result rows or rejects with an error message.
     */
    writeInActivePaytmUsers(params) {
        let self = this;

        const query = `INSERT INTO ${self.activePaytmUsers} (customer_id, payment_date, service, source, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?);`;
        const queryParams = [params.customer_id, params.payment_date, params.service, params.source, MOMENT().format('YYYY-MM-DD HH:mm:ss'), MOMENT().format('YYYY-MM-DD HH:mm:ss')];
        self.L.log('writeInActivePaytmUsers::', `executing query ${query} with params ${queryParams}`);

        return new Promise ((resolve, reject) => {
            self.client.execute(query, queryParams, { prepare: true })
            .then(result => {
                return resolve(result.rows);
            })
            .catch(error => {
                return reject(`writeInActivePaytmUsers ::DB exception! ${error.message}`);
            });
        })
    }

    /**
     * Reads records from the active_paytm_users_new table by customer ID
     *
     * @param {string} customer_id - The customer ID.
     * @returns {Promise<Array>} - A promise that resolves with the result rows or rejects with an error message.
     */
    readActivePaytmUsersNewByCId(customer_id) {
        let self = this;

        let query = `SELECT * FROM ${self.activePaytmUsersNew} WHERE customer_id = ?`;
        let queryParams = [customer_id];
        self.L.log('readActivePaytmUsersNewByCId::', `executing query ${query} with params ${queryParams}`);

        return new Promise((resolve, reject) => {
            self.client.execute(query, queryParams, { prepare: true })
                .then(result => {
                    return resolve(result.rows);
                })
                .catch(error => {
                    return reject(`readActivePaytmUsersNewByCId ::DB exception! ${error.message}`);
                });
        });
    }

    /**
     * Inserts a record into the active_paytm_users_new table.
     *
     * @param {Array} queryParams - The parameters for the query.
     * @returns {Promise<Array>} - A promise that resolves with the result rows or rejects with an error message.
     */
    writeInActivePaytmUsersNew(queryParams) {
        let self = this;

        const query = `INSERT INTO ${self.activePaytmUsersNew} (customer_id, service, latest_payment_date, payment_date_list, created_source, updated_source, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?);`;
        self.L.log('writeInActivePaytmUsersNew::', `executing query ${query} with params ${queryParams}`);

        return new Promise ((resolve, reject) => {
            self.client.execute(query, queryParams, { prepare: true })
            .then(result => {
                return resolve(result.rows);
            })
            .catch(error => {
                return reject(`writeInActivePaytmUsersNew ::DB exception! ${error.message}`);
            });
        })
    }

    /**
     * Reads records from the cust_id_rn_mapping table by customer ID and services.
     *
     * @param {Array} queryParams - The parameters for the query.
     * @returns {Promise<Array>} - A promise that resolves with the result rows or rejects with an error message.
     */
    readCustIdRNMappingByCustIdServices(queryParams) {
        let self = this;

        const query = `SELECT customer_id, service, recharge_number, operator, paytype, product_id, status FROM ${self.custIdRnMappingTable} WHERE customer_id = ? and service in ? limit ?;`;

        return new Promise ((resolve, reject) => {
            self.client.execute(query, queryParams, { prepare: true })
            .then(result => {
                return resolve(result.rows);
            })
            .catch(error => {
                return reject(`readCustIdRNMappingByCustIdServices ::DB exception! ${error.message}`);
            });
        });
    }

    getPaymentDatesFromActivePaytmUsers(customerId, mapped_service, callback) {
        let self = this;
        let query = `SELECT latest_payment_date, payment_date_list FROM active_paytm_users_new WHERE customer_id = ? AND service = ?`;
        let queryParams = [customerId, mapped_service];

        self.client.execute(query, queryParams, { prepare: true })
            .then(result => {
                callback(null, result);
            })
            .catch(error => {
                callback(error);
            });
    }

    getNotificationsQuery(customer_id, service, last_ref_time, row, batchSize){
        let self = this;
        let tableName = self.getTableNameForNotification(row);
        let query = `SELECT * FROM ${tableName} WHERE customer_id = ? AND service = ?`;
        let queryParams = [customer_id, _.toLower(service)];

        if(tableName == self.notificationLogsTable){
            query += `AND type = ?`;
            queryParams.push(_.toUpper(row.type));
        }

        query += `AND app_created_at > ?`;
        queryParams.push(last_ref_time);

        query += `LIMIT ?`;
        queryParams.push(batchSize);
        return [query, queryParams];
    }

    getTableNameForNotification(row){
        let self = this;
        let notificationType = _.toLower(_.get(row, 'type', null));
        let tableName = notificationType == self.whatsappNotificationType ? self.notificationTable : self.notificationLogsTable; //This is temporary, later on we will be having single table.
        return tableName;
    }

    getNotificationsFromCassandraTable(parent,query,queryParams,row,checkType,time_interval,cb){
        let self=this;
        let consistencyLevel = cassandra.types.consistencies.localQuorum;
        self.L.verbose(`getNotificationsFromCassandraTable::select query ${query} for ${JSON.stringify(queryParams)}`);
        self.notificationNewClusterClient.execute(query, queryParams, { prepare: true, consistency: consistencyLevel})
        .then((result) => {
            self.L.verbose(`getNotificationsFromCassandraTable::select query success result: ${JSON.stringify(result)} for ${JSON.stringify(queryParams)}`);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_LOG", 'STATUS:SUCCESS', 'TYPE:GET', 'TABLE:notifications', 'SOURCE:MAIN_FLOW']);
            if(_.isArray(result.rows) && result.rows.length > 0){
                let filteredResult = parent.filterResult(result.rows,row,checkType, time_interval);
                self.L.verbose(`getNotificationsFromCassandraTable::filtered result ${JSON.stringify(filteredResult)}`)
                if(filteredResult.length > 0){
                    self.L.log(`getNotificationsFromCassandraTable::found ${filteredResult.length} notifications for ${JSON.stringify(queryParams)}`);
                    return cb('Found success notification in this month');
                }else {
                    self.L.log(`getNotificationsFromCassandraTable::fetched ${result.rows.length} notifications found, skipping checks for this payload as no duplicates found for ${JSON.stringify(queryParams)}`);
                    return cb(null);
                }
            }else {
                self.L.log(`getNotificationsFromCassandraTable::no notifications found for ${JSON.stringify(queryParams)}`);
                return cb(null);
            }
        })
        .catch(error => {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_LOG", 'STATUS:ERROR', 'TYPE:GET', 'TABLE:notifications', 'SOURCE:MAIN_FLOW']);
            self.L.error(`getNotificationsFromCassandraTable::select query failed for ${JSON.stringify(queryParams)}, error ${error}`)
            cb(null);
        })
    }

    /**
     * Check if combined notification key exists in cassandra table
     * @param {Object} params - Parameters containing customerid, rechargenumber, service, operator
     * @param {Function} callback - Callback function
     */
    checkCombinedNotificationKey(params, callback) {
        let self = this;
        let query = `SELECT * FROM combine_notification_key WHERE customerid = ? AND rechargenumber = ? AND service = ? AND operator = ?`;
        let queryParams = [
            String(params.customerid),
            params.rechargenumber,
            _.toLower(params.service),
            _.toLower(params.operator)
        ];
        
        self.L.log(`checkCombinedNotificationKey:: Checking combined notification key for ${JSON.stringify(queryParams)}`);
        
        self.notificationNewClusterClient.execute(query, queryParams, { prepare: true })
        .then((result) => {
            if (result && result.rows && result.rows.length > 0) {
                self.L.log(`checkCombinedNotificationKey:: Found combined notification key for ${JSON.stringify(queryParams)}`);
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:COMBINED_NOTIFICATION_KEY_CHECK',
                    'STATUS:FOUND',
                    'SOURCE:NORMAL_NOTIFICATION_VALIDATION'
                ]);
                return callback('Combined notification key exists for this recharge number', result.rows[0]);
            } else {
                self.L.log(`checkCombinedNotificationKey:: No combined notification key found for ${JSON.stringify(queryParams)}`);
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:COMBINED_NOTIFICATION_KEY_CHECK',
                    'STATUS:NOT_FOUND',
                    'SOURCE:NORMAL_NOTIFICATION_VALIDATION'
                ]);
                return callback(null, null);
            }
        })
        .catch(error => {
            self.L.error(`checkCombinedNotificationKey:: Error checking combined notification key for ${JSON.stringify(queryParams)}:`, error);
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:COMBINED_NOTIFICATION_KEY_CHECK',
                'STATUS:ERROR',
                'SOURCE:NORMAL_NOTIFICATION_VALIDATION'
            ]);
            // In case of error, proceed with normal flow (don't block notification)
            return callback(null, null);
        });
    }

    /**
     * Checks if a user is active in the last N days by querying the reco_logs table
     * 
     * @param {string|number} customerId - The customer ID to check
     * @param {number} days - Number of days to look back (default: 10)
     * @returns {Promise<boolean>} - A promise that resolves to true if the user is active, false otherwise
     */
    isUserActiveInLastDays(customerId, days = 10) {
        let self = this;
        
        // Calculate the date N days ago
        const cutoffTime = new Date(Date.now() - days * 24 * 60 * 60 * 1000); // 10 days ago
        
        // Query to check if there are any records for this customer in the last N days
        const query = `SELECT customer_id, updated_at  FROM ${self.customerUpdatesTable} WHERE customer_id = ? LIMIT 1`;
        const queryParams = [String(customerId)];
        
        self.L.log('isUserActiveInLastDays::', `checking if user ${customerId} is active in the last ${days} days`);
        
        return new Promise((resolve, reject) => {
            self.notificationNewClusterClient.execute(query, queryParams, { prepare: true })
                .then(result => {
                    let isActive = false;
                    if(result.rows && result.rows.length > 0) {
                        const updatedAt = result.rows[0].updated_at;
                        isActive = updatedAt >= cutoffTime;
                    }
                    self.L.log('isUserActiveInLastDays::', `user ${customerId} is ${isActive ? 'active' : 'inactive'} in the last ${days} days`);
                    utility._sendMetricsToDD(1, [
                        `REQUEST_TYPE:CHECK_USER_ACTIVE`,
                        `STATUS:${isActive ? 'ACTIVE' : 'INACTIVE'}`,
                        `DAYS:${days}`
                    ]);
                    return resolve(isActive);
                })
                .catch(error => {
                    self.L.error('isUserActiveInLastDays::', `error while checking if user ${customerId} is active: ${error}`);
                    utility._sendMetricsToDD(1, [
                        `REQUEST_TYPE:CHECK_USER_ACTIVE`,
                        `STATUS:ERROR`,
                        `DAYS:${days}`
                    ]);
                    // In case of error, we'll assume the user is inactive
                    return resolve(false);
                });
        });
    }

    /**
     * Insert customer update record into Cassandra
     * @param {Array} params - Array containing [customer_id, updated_at, file_name, date_folder, processed_at, created_at]
     * @returns {Promise} - Promise resolved when insert is complete
     */
    async insertCustomerUpdate(params, days = 10) {
        let self = this;
        
        return new Promise((resolve, reject) => {
            if (!params || !params.length || params.length < 2) {
                return reject(new Error('Invalid parameters for insertCustomerUpdate'));
            }
            
            // Set TTL for 10 days (10 * 24 * 60 * 60 = 864000 seconds)
            const TTL_SECONDS = 86400 * days;
            
            const query = `INSERT INTO ${self.customerUpdatesTable} 
                (customer_id, updated_at) 
                VALUES (?, ?) 
                USING TTL ${TTL_SECONDS}`;
                
            self.notificationNewClusterClient.execute(query, params, { prepare: true }, (err, result) => {
                if (err) {
                    self.L.error('cassandraBills::insertCustomerUpdate', 'Error inserting customer update:', err);
                    return reject(err);
                }
                
                self.L.info('cassandraBills::insertCustomerUpdate', 'Customer update inserted successfully for customer_id:', params[0]);
                resolve(result);
            });
        });
    }
    
    /**
     * Get the most recent update for a customer
     * @param {string} customer_id - Customer ID to search for
     * @returns {Promise} - Promise resolving to array of customer update records
     */
    async getCustomerUpdates(customer_id, days = 10) {
        let self = this;
        
        return new Promise((resolve, reject) => {
            if (!customer_id) {
                return reject(new Error('Customer ID is required'));
            }            
            // Query to get the latest update for a customer, sorted by updated_at in descending order
            const query = `SELECT * FROM ${self.customerUpdatesTable} 
                WHERE customer_id = ?
                LIMIT 1`;
                
            const params = [customer_id];
            
            self.notificationNewClusterClient.execute(query, params, { prepare: true }, (err, result) => {
                if (err) {
                    self.L.error('cassandraBills::getCustomerLastUpdate', 'Error getting customer update:', err);
                    return reject(err);
                }
                
                resolve(result.rows || []);
            });
        });
    }
    
    /**
     * Update an existing customer update record in Cassandra
     * @param {Array} params - Array containing [updated_at, customer_id] - updated_at is the new value, customer_id is for WHERE clause
     * @returns {Promise} - Promise resolved when update is complete
     */
    async updateCustomerUpdate(params, days =10) {
        let self = this;
        
        return new Promise((resolve, reject) => {
            if (!params || !params.length || params.length < 2) {
                return reject(new Error('Invalid parameters for updateCustomerUpdate'));
            }
            
            // Set TTL for 10 days (10 * 24 * 60 * 60 = 864000 seconds)
            const TTL_SECONDS = 86400 * days;
            
            const query = `UPDATE ${self.customerUpdatesTable} 
                USING TTL ${TTL_SECONDS}
                SET updated_at = ?
                WHERE customer_id = ?`;
                
            self.notificationNewClusterClient.execute(query, params, { prepare: true }, (err, result) => {
                if (err) {
                    self.L.error('cassandraBills::updateCustomerUpdate', 'Error updating customer update:', err);
                    return reject(err);
                }
                
                self.L.info('cassandraBills::updateCustomerUpdate', 'Customer update updated successfully for customer_id:', params[1]);
                resolve(result);
            });
        });
    }
}

export default cassandraBills
