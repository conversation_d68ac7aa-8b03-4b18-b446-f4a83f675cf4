import _ from 'lodash'
let L = null;
import utility from '../lib'

class NotificationService {
    /**
     * Here we will make a local copy of db instance and logger
     * @param {*} options 
     */
    constructor(options, redis) {
        this.L = options.L;
        this.config = options.config;
        this.redis = redis;
        this.dbInstance = options.dbInstance;
        this.esInstance = options.esInstance;
    }

    /**
     * Method will load product_map data, we will use it to prepare notification request
     * @param {*} cb | callback function
     */
    createOperatorProductMap(cb) {
        let self = this,
            query = 'select * from catalog_vertical_recharge',
            productMap = {};
        var latencyStart = new Date().getTime();
        self.dbInstance.exec((err, data) => {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'createOperatorProductMap'});
            if (!err || _.isArray(data)) {
                self.L.log("NotificationService model: createOperatorProductMap(): No of CVR product data loaded :", data.length);

                data.forEach((row) => {
                    if (row.product_id && row.vertical_id && row.merchant_id &&
                        row.operator && row.service && row.category_id && row.operator_label) {
                        try{
                            if(row.attributes){
                                let attributes = JSON.parse(row.attributes);
                                row.attributes = attributes;
                            }
                        }catch(ex){
                            self.L.critical("Notification service model: createOperatorProductMap:",ex);
                        }
                        productMap[row.product_id] = row;
                    }
                });
            } else {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:createOperatorProductMap`]);
                self.L.error('NotificationService model:: createOperatorProductMap', 'Error while creating CVR map', err);
            }
            cb(err, productMap);
        }, 'FS_RECHARGE_SLAVE*', query, []);
    }

    /**
     * Method will load merchant_configuration table data
     * @param {*} cb |callback
     */
    loadMerchantConfiguration(cb) {
        let self = this,
            query = 'SELECT merchant_id, email_id, mobile_no, template_id from merchant_notification_configuration WHERE status=?';
        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'loadMerchantConfiguration'});
            if (err) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:loadMerchantConfiguration`]);
                self.L.error('NotificationService: loadMerchantConfiguration(): ', 'error occurred while loading Merchant Configuration data from DB: ', query, err);
            } 
            cb(err, data);
        }, 'DIGITAL_REMINDER_SLAVE', query, [1]);
    }

    /**
     * Method will load notification_configuration table data
     * @param {*} cb |callback
     */
    loadNotificationConfiguration(cb) {
        let self = this,
            query = 'SELECT * from notification_configuration WHERE status in (?,?)';
        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'loadNotificationConfiguration'});
            if (err) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:loadNotificationConfiguration`]);
                self.L.error('NotificationService: loadNotificationConfiguration(): ', 'error occurred while loading Notification Configuration data from DB: ', query, err);
            }
            cb(err, data);
        }, 'DIGITAL_REMINDER_SLAVE', query, [0, 1]);
    }

    /**
     * Method will load template table data
     * @param {*} cb |callback
     */
    loadNotificationTemplateConfiguration(cb) {
        let self = this,
            query = 'SELECT * from notification_template';
        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'loadNotificationTemplateConfiguration'});
            if (err) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:loadNotificationTemplateConfiguration`]);
                self.L.error('NotificationService: loadNotificationTemplateConfiguration(): ', 'error occurred while loading Notification template Configuration data from DB: ', query, err);
            }
            cb(err, data);
        }, 'DIGITAL_REMINDER_SLAVE', query, []);
    }

    /**
     * Method will fetch redis items for notification
     * @param {*} cb | call back function
     * @param {*} rechargeData | contains recharges data
     */
    getRedisData(cb, rechargeData, NotificationPrefix) {
        let self = this,
            rechargeKey = NotificationPrefix + ":" + _.get(rechargeData, 'orderInfo_order_id'),
            redisRecords = [];
        self.L.log('Notification Service Model: getRedisData(): Fetching records for rechargeKey', rechargeKey);
        
        self.redis.getData((err, redisData) => {
            if (!err && redisData) {
                redisRecords = redisData;
                self.L.log("Notification Service Model: getRedisData(): Redis records fetched successfully rechargeKey:", rechargeKey);
            }
            cb(err, redisRecords);
        }, {
            key: rechargeKey
        });
    }

    /**
     * Method will set data in REDIS 
     * @param {*} cb | callback
     * @param {*} rechargeData | contains item data for notification
     * @param {*} order_items | contains exiting items in order 
     * @param {*} NotificationPrefix | prefix for redis data
     * @param {*} notificationDataRedisTTL | TTL for redis data
     */
    setRedisData(cb, rechargeData, order_items, NotificationPrefix, notificationDataRedisTTL, RedisItemKeys, createKey) {
        let self = this,
            rechargeKey = NotificationPrefix + ":" + _.get(rechargeData, 'orderInfo_order_id'),
            redisItem = {};

        self.L.log("Notification Service model: setRedisData() : rechargeKey, createKey=>", rechargeKey, createKey);

        RedisItemKeys.forEach(key => {
            _.set(redisItem, key, _.get(rechargeData, key, null));
        });
        order_items.push(redisItem);

        if (createKey) {

            self.redis.setData((err, data) => {
                err ? cb(err) : cb();
            }, {
                key: rechargeKey,
                value: order_items,
                ttl: notificationDataRedisTTL
            });
        } else {
            self.redis.updateData((err, data) => {
                err ? cb(err) : cb();
            }, {
                key: rechargeKey,
                value: order_items
            });
        }
    }

    /**
     * Method will remove notified records from REDIS
     * @param {*} cb callback function
     * @param {*} order_id items has to be remove
     */
    deleteRedisData(cb, rechargeData, NotificationPrefix) {
        let self = this,
            rechargeKey = NotificationPrefix + ":" + _.get(rechargeData, 'orderInfo_order_id');

        self.redis.deleteData((err) => {
            cb(err);
        }, {
            key: rechargeKey
        });
    }

    /**
     * Method is used to fetch data from ES respective to order_id
     * @param {*} cb | callback function
     * @param {*} rechargeData | recharge data contains order_id
     * @param {*} ES_INDEX | Elastic Search Index for notification service data
     * @param {*} esQuery | elastic search query to fetch data
     */
    getEsData(cb, ES_INDEX, esQuery) {
        let self = this,
            records = [];

        self.L.log("NotificationService Model: getEsData : start fetching records from ES index =>", ES_INDEX);

        self.esInstance.search({
                index: ES_INDEX,
                type: 'product',
                body: esQuery
            },

            (error, response) => {
                if (error) {
                    cb(error);
                } else {
                    // collect the doc from each response
                    response.hits.hits.forEach(hit => {
                        records.push(hit._source);
                    });
                    cb(null, records);
                }
            });
    }
}

export default NotificationService