let L = null;
import _ from 'lodash';
import utility from '../lib'

class DroppedTransactions {

    constructor(options) {
        this.L = options.L;
        this.dbInstance = options.dbInstance;
    }

    getDroppedTransaction(done, operatorName, gatewayName, offset, batchSize) {
        let self = this,
            query  = `select * from dropped_transactions where operator = ? and gateway = ? and notification_status = 0 limit ?, ?`,
            queryParams = [
                operatorName, gatewayName, offset, batchSize
            ];
        self.L.log(`Fetching txns from database dropped_transactions:`,  self.dbInstance.format(query, queryParams));
        var latencyStart = new Date().getTime();
   		self.dbInstance.exec(function (err, data){
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'getDroppedTransaction'});
            if(err || !data) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getDroppedTransaction`]);
                let errMsg = `dropped_transactions txns not found: ${err}`;
                self.L.critical(errMsg);
                return done(new Error(errMsg));
            }
            return done(null, data);
        }, 'DIGITAL_REMINDER_SLAVE', query, queryParams);
    }

    getDroppedTransactionByCustAndRecharge(done, customerId, rechargeNo) {
        let self = this,
            query  = `select * from dropped_transactions where customer_id = ? and recharge_number = ?`,
            queryParams = [
                customerId, rechargeNo
            ];
        self.L.log(`Fetching txns from database dropped_transactions:`,  self.dbInstance.format(query, queryParams));
        var latencyStart = new Date().getTime();
   		self.dbInstance.exec(function (err, data){
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'getDroppedTransactionByCustAndRecharge'});
            if(err || !data) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getDroppedTransactionByCustAndRecharge`]);
                let errMsg = `dropped_transactions txns not found: ${err}`;
                self.L.critical(errMsg);
                return done(new Error(errMsg));
            }
            return done(null, data);
        }, 'DIGITAL_REMINDER_SLAVE', query, queryParams); // reading from master, if data does not exist only then insert will apply
    }

    updatedDroppedTransaction(done, record) {
        let self = this,
            query = `update dropped_transactions set notification_status = 1 where customer_id = ? and recharge_number = ? and operator = ? and gateway = ?`,
            queryParams = [
                record.customer_id, record.recharge_number, record.operator, record.gateway,
            ];
        self.L.log(`Fetching txns from database dropped_transactions:`, self.dbInstance.format(query, queryParams));
        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'updatedDroppedTransaction'});
            if (err) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:updatedDroppedTransaction`]);
                let errMsg = `dropped_transactions is not updated: ${err}`;
                self.L.critical(errMsg);
                return done(new Error(errMsg));
            }
            return done(null, data);
        }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
    }
    
    storeDroppedTransaction(cb, params) {
        let
            self = this

        if (!params.service) params.service = _.get(self.config, ['CVR_DATA', params.productId, 'service'], '');

        let query = `INSERT INTO dropped_transactions (customer_id,recharge_number,product_id,category_id,operator,gateway,service,paytype,circle,customer_mobile,customer_email,validation_success,user_data,validation_channel)                
                VALUES ?`,
            queryParams = [[[
                params.customerId,
                params.rechargeNumber,
                params.productId,
                params.categoryId,
                params.operator,
                params.gateway,
                _.toLower(params.service),
                params.paytype,
                params.circle,
                params.customerMobile,
                params.customerEmail,
                params.validationSuccess,
                _.get(params,'user_data',{}),
                params.validationChannel
            ]]];
        
        self.L.log('storeDroppedTransaction::',self.dbInstance.format(query,queryParams));
        var latencyStart = new Date().getTime();
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'storeDroppedTransaction'});
            if (err) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:storeDroppedTransaction`]);
                self.L.critical('storeDroppedTransaction::', 'error occurred while inserting data in DB: ', err);
            }
            cb(err, data);
        }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
    }
}

export default DroppedTransactions;
