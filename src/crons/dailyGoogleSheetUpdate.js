const cron = require('node-cron');
const googleSheets = require('../services/googleSheets');
const logger = require('../lib/logger');

// Run at 12:01 AM every day (after alerts are collected)
cron.schedule('1 2 * * *', async () => {
    try {
        logger.info('Starting daily Google Sheets update');
        
        // Get yesterday's date (since we're running at midnight)
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        
        await googleSheets.updateDailySheet(yesterday);
        
        logger.info('Daily Google Sheets update completed');
    } catch (error) {
        logger.error('Error in daily Google Sheets update:', error);
    }
});