import startup from '../lib/startup';
import MOMENT from 'moment';
import _ from "lodash";
import L from 'lgr';
import fs from 'fs';
import path from 'path';
import { createReadStream, createWriteStream } from 'fs';
import { pipeline } from 'stream/promises';
import { createGunzip } from 'zlib';
import zstd from 'node-zstandard';
import os from 'os';
import readline from 'readline';
import { each, eachLimit, eachSeries, parallel } from 'async';
import AWSCsvIngester from '../lib/awscsvingester';
import utility from '../lib';
import ASYNC from 'async';
import AWS from 'aws-sdk';
import { v4 as uuidv4 } from 'uuid';
import AWSS3StreamUtils from '../lib/awsS3StreamUtils';
const csv = require('csv-parser');

let serviceName = "S3_CUSTOMER_UPDATES_PROCESSOR";
let progressFilePath = `/var/log/digital-notification/s3_customer_updates_processor-${MOMENT().format('YYYY-MM-DD')}.json`;
let progressTracker = {};

const { spawn } = require('child_process');
/**
 * Class to process customer update files from S3 and publish to Kafka
 * Handles:
 * - Reading .zst files from S3 bucket
 * - Processing each file once
 * - Extracting customer_id and updated_at from each file
 * - Publishing to Kafka
 * - Tracking processed files to avoid reprocessing
 */
class S3CustomerUpdatesProcessor {
    constructor(options) {
        this.L = options.L;
        this.dbInstance = options.dbInstance;
        this.config = options.config;
        this.infraUtils = options.INFRAUTILS;
        
        // Initialize progress tracker from local file
        progressTracker = this.getProgressObject();
        
        // Configuration for the cron
        this.pollingInterval = _.get(options.config, ['DYNAMIC_CONFIG', serviceName, 'polling_interval_seconds', 'value'], 5) * 1000;
        this.batchSize = _.get(options.config, ['DYNAMIC_CONFIG', serviceName, 'batch_size', 'value'], 100);
        this.maxRetriesPerFile = _.get(options.config, ['DYNAMIC_CONFIG', serviceName, 'max_retries', 'value'], 3);
        this.maxQueueSize = _.get(options.config, ['DYNAMIC_CONFIG', serviceName, 'max_queue_size', 'value'], 30);
        this.daysPriorToProcess = _.get(options.config, ['DYNAMIC_CONFIG', serviceName, 'days_prior_to_process', 'value'], 7);
        this.prioritizeOlderFiles = _.get(options.config, ['DYNAMIC_CONFIG', serviceName, 'prioritize_older_files', 'value'], true);
        
        // S3 configuration
        this.bucketName = _.get(options.config, ['DYNAMIC_CONFIG', serviceName, 'bucket_name', 'value'], "digital-reminder");
        this.baseFolderPath = _.get(options.config, ['DYNAMIC_CONFIG', serviceName, 'path', 'value'], "recharges-bff-reco-request-logs/");
        
        // Initialize S3 utilities
        this.s3Utils = new AWSS3StreamUtils(options);
        this.s3Utils.configure(this.bucketName, serviceName, this.batchSize);
        
        // Initialize AWS S3 for operations not covered by S3Utils
        this.s3 = new AWS.S3(options.config.AWS.S3);
        
        // Tracking variables
        this.logPrefix = serviceName;
        this.processingQueue = [];
        this.isProcessing = false;
        this.processingErrors = {};
        this.customerKafkaPublisher = null;
        this.lastDateFolderProcessed = null;
        this.emptyFolders = {}; // Track folders that are empty to avoid redundant checks
        this.tempDir = path.join(os.tmpdir(), 's3-processor-temp');
        
        // Format expected in the file
        this.requiredFields = ['customer_id', 'updated_at'];
        
        // Create temp directory if it doesn't exist
        if (!fs.existsSync(this.tempDir)) {
            fs.mkdirSync(this.tempDir, { recursive: true });
        }
    }

    /**
     * Load progress object from file system
     */
    getProgressObject() {
        let progress = {};
        this.L.info("Loading progress object from", progressFilePath);
        
        try {
            if (fs.existsSync(progressFilePath)) {
                const progressData = fs.readFileSync(progressFilePath, 'utf-8');
                progress = JSON.parse(progressData);
            }
        } catch (error) {
            this.L.error("Error loading progress file:", error);
            // Create a new progress file if it doesn't exist or is corrupted
            fs.writeFileSync(progressFilePath, JSON.stringify({}, null, 2));
        }
        
        this.L.info("Loaded progress tracker", progress);
        return progress;
    }

    /**
     * Update progress for a file
     * @param {string} filename - Name of the file
     * @param {number} count - Number of records processed or -1 if complete
     */
    updateProgress(filename, count) {
        if (_.get(progressTracker, [filename], 0) === -1) return;
        
        _.set(progressTracker, [filename], count);
        this.L.info("Updated progress object for file", filename, "count:", count);
        
        try {
            fs.writeFileSync(progressFilePath, JSON.stringify(progressTracker, null, 2));
        } catch (error) {
            this.L.error("Error writing progress file:", error);
        }
    }

    /**
     * Configure Kafka producer
     */
    configureKafka(done) {
        let self = this;
        self.L.info("configure kafka S3 Customer Updates Processor");
        try {
            self.customerKafkaPublisher = new self.infraUtils.kafka.producer({
                kafkaHost: this.config.KAFKA.TOPICS.REMINDER_KAFKA_CLUSTER.HOSTS
            });
            self.L.info("kafkaHost", this.config.KAFKA.TOPICS.REMINDER_KAFKA_CLUSTER.HOSTS);
        } catch (error) {
            self.L.error("Failed to initialize Kafka producer", error);
            throw error; // Re-throw the error after logging it
        }
        this.customerKafkaPublisher.initProducer('high', function (error) {
            if (error) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:S3_CUSTOMER_UPDATES_PROCESSOR", 'STATUS:ERROR', 'TYPE:KAFKA_PRODUCER_INIT']);
                self.L.critical('Could not initialize Kafka', error);
            }
            return done(error);
        });
    }

    /**
     * Start the cron service
     */
    start(callback) {
        let self = this;
        self.L.info("Starting S3 Customer Updates Processor");
        utility._sendMetricsToDD(1, ["REQUEST_TYPE:S3_CUSTOMER_UPDATES_PROCESSOR", 'STATUS:STARTING', 'TYPE:CRON_TRIGGERED']);
        
        // Configure Kafka
        self.configureKafka(function (error) {
            if (error) {
                self.L.critical('start', 'Unable to configure Kafka', error);
                return callback ? callback(error) : null;
            }
            
            self.L.log('start', 'Kafka configured successfully!');
            
            // Start polling for new files
            self.startPolling();
            
            if (callback) callback(null);
        });
    }

    /**
     * Start polling S3 for new files
     */
    startPolling() {
        let self = this;
        
        const pollForFiles = () => {
            // First check if queue is not already at capacity
            
            self.checkForDateFolders()
                .then(async () => {
                    // Process the next file if not already processing
                    if (!self.isProcessing && self.processingQueue.length > 0) {
                        await self.processNextFile();
                    } else if (self.processingQueue.length === 0) {
                            // If the queue is empty, reset the emptyFolders tracking
                            // This way we'll check all folders again after some time
                            self.L.info("Processing queue is empty, will check all date folders in the next cycle");
                            self.emptyFolders = {};
                    }
                })
                .catch(err => {
                    self.L.error("Error in polling cycle:", err);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:S3_CUSTOMER_UPDATES_PROCESSOR", 'STATUS:ERROR', 'TYPE:POLLING_CYCLE']);
                })
                .finally(() => {
                    // Continue polling
                    setTimeout(pollForFiles, self.pollingInterval);
                });
        };
        
        // Start the polling cycle
        pollForFiles();
        self.L.info(`Polling started with interval of ${self.pollingInterval}ms, max queue size: ${self.maxQueueSize}`);
    }

    /**
     * Get date folders from S3
     */
    async checkForDateFolders() {
        let self = this;
        
        return new Promise((resolve, reject) => {
            if (self.processingQueue.length >= self.maxQueueSize) {
                self.L.verbose("Queue at capacity, not checking for more folders");
                return resolve();
            }
            
            // Generate date folders for the configured number of days
            const dateFolders = [];
            const today = MOMENT();
            
            for (let i = 0; i < self.daysPriorToProcess; i++) {
                const folderDate = today.clone().subtract(i, 'days');
                dateFolders.push(folderDate.format('YYYY-MM-DD'));
            }
            
            // Sort folders by date
            dateFolders.sort((a, b) => {
                // If prioritizing older files, sort ascending
                if (self.prioritizeOlderFiles) {
                    return MOMENT(a).diff(MOMENT(b));
                }
                // Otherwise, sort descending
                return MOMENT(b).diff(MOMENT(a));
            });
            
            self.L.info(`Generated ${dateFolders.length} date folders to check within ${self.daysPriorToProcess} days range`);
            
            // If we have space in the queue, check for files in date folders
            if (dateFolders.length > 0 && self.processingQueue.length < self.maxQueueSize) {
                // To avoid repeatedly processing the same folders in every poll cycle,
                // we'll use a round-robin approach across the date range
                
                // If we've already processed a folder, move to the next one
                let nextIndex = 0;
                if (self.lastDateFolderProcessed) {
                    const lastIndex = dateFolders.indexOf(self.lastDateFolderProcessed);
                    if (lastIndex !== -1) {
                        // Move to the next folder, or wrap around to the beginning
                        nextIndex = (lastIndex + 1) % dateFolders.length;
                    }
                }
                
                const nextDateFolder = dateFolders[nextIndex];
                self.lastDateFolderProcessed = nextDateFolder;
                
                // Skip folders that were recently checked and found empty
                if (self.emptyFolders[nextDateFolder] && 
                    (Date.now() - self.emptyFolders[nextDateFolder]) < (self.pollingInterval * 5)) {
                    self.L.verbose(`Skipping recently checked empty folder: ${nextDateFolder}`);
                    return resolve();
                }
                
                // Get files for this date folder
                const folderPath = `${self.baseFolderPath}${nextDateFolder}`;
                
                // Check folder for files using S3Utils
                self.getFilesFromFolder(folderPath)
                    .then(() => resolve())
                    .catch(error => reject(error));
            } else {
                resolve();
            }
        });
    }
    
    /**
     * Get files from a specific S3 folder
     * @param {string} folderPath - Path to the folder in S3
     */
    async getFilesFromFolder(folderPath) {
        let self = this;
        self.L.log("Get files from folder ");
        
        return new Promise((resolve, reject) => {
            self.L.info(`Checking for files in folder: ${folderPath}`);
            
            // Use the S3Utils to get file names
            self.s3Utils.getFileNamesWithBucket(folderPath,self.bucketName, (err, fileNames) => {
                if (err) {
                    self.L.error(`Error while getting files from S3 folder ${folderPath}:`, err);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:S3_CUSTOMER_UPDATES_PROCESSOR", 'STATUS:ERROR', 'TYPE:S3_LIST_FILES']);
                    return reject(err);
                }
                
                // Filter and add new files to the queue
                const newFiles = (fileNames || [])
                    .filter(fileName => {
                        // Only process .zst files
                        if (!fileName.endsWith('.zst')) {
                            return false;
                        }
                        
                        // Skip files that are fully processed (marked as -1)
                        if (_.get(progressTracker, [fileName], null) === -1) {
                            return false;
                        }
                        
                        // Skip files already in the queue
                        if (self.processingQueue.includes(fileName)) {
                            return false;
                        }
                        
                        return true;
                    });
                
                if (newFiles.length > 0) {
                    self.L.info(`Found ${newFiles.length} new .zst files to process in ${folderPath}`);
                    self.processingQueue.push(...newFiles);
                    utility._sendMetricsToDD(newFiles.length, ["REQUEST_TYPE:S3_CUSTOMER_UPDATES_PROCESSOR", 'STATUS:PROCESSING', 'TYPE:NEW_FILES_FOUND']);
                } else {
                    // Mark folder as empty with timestamp
                    const dateFolder = folderPath.split('/').filter(p => p)[1]; // Extract date part from path
                    if (dateFolder) {
                        self.emptyFolders[dateFolder] = Date.now();
                        self.L.verbose(`Marked folder ${dateFolder} as empty at ${new Date().toISOString()}`);
                    }
                }
                
                resolve();
            });
        });
    }

    /**
     * Process the next file in queue
     */
    processNextFile() {
        let self = this;
        
        if (self.processingQueue.length === 0 || self.isProcessing) {
            return;
        }
        
        self.isProcessing = true;
        const s3Key = self.processingQueue.shift();
        
        self.L.info(`Processing file: ${s3Key}`);
        utility._sendMetricsToDD(1, ["REQUEST_TYPE:S3_CUSTOMER_UPDATES_PROCESSOR", 'STATUS:PROCESSING', 'TYPE:FILE_PROCESSING_STARTED']);
        
        // Check if we've reached max retries for this file
        const retryCount = _.get(self.processingErrors, [s3Key], 0);
        if (retryCount >= self.maxRetriesPerFile) {
            self.L.error(`Max retries reached for file ${s3Key}, skipping`);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:S3_CUSTOMER_UPDATES_PROCESSOR", 'STATUS:ERROR', 'TYPE:MAX_RETRIES_EXCEEDED']);
            
            // Mark as processed to avoid further attempts
            self.updateProgress(s3Key, -1);
            self.isProcessing = false;
            
            // Process next file
            self.processNextFile();
            return;
        }
        
        // Download, decompress, and process the .zst file
        self.processZstFile(s3Key)
            .then(() => {
                self.L.info(`Successfully processed file ${s3Key}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:S3_CUSTOMER_UPDATES_PROCESSOR", 'STATUS:SUCCESS', 'TYPE:FILE_PROCESSING_COMPLETE']);
                
                // Mark file as completely processed
                self.updateProgress(s3Key, -1);
                
                // Reset error count
                delete self.processingErrors[s3Key];
                
                self.isProcessing = false;
                
                // Process next file if any
                if (self.processingQueue.length > 0) {
                    self.processNextFile();
                }
            })
            .catch(error => {
                self.L.error(`Error processing file ${s3Key}:`, error);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:S3_CUSTOMER_UPDATES_PROCESSOR", 'STATUS:ERROR', 'TYPE:FILE_PROCESSING_ERROR']);
                
                // Increment retry count
                self.processingErrors[s3Key] = retryCount + 1;
                
                // Add back to queue for retry
                self.processingQueue.push(s3Key);
                
                self.isProcessing = false;
                
                // Process next file if any
                if (self.processingQueue.length > 0) {
                    self.processNextFile();
                }
            });
    }

    /**
     * Process a .zst file from S3
     * @param {string} s3Key - S3 key of the .zst file
     */
    async processZstFile(s3Key) {
        let self = this;
        const tempFilePath = path.join(self.tempDir, `${uuidv4()}.zst`);
        const decompressedPath = path.join(self.tempDir, `${uuidv4()}.csv`);
        
        try {
            // Download file from S3 using the AWSS3StreamUtils
            await self.downloadFileWithS3Utils(s3Key, tempFilePath);
            
            // Decompress file
            await self.decompressZstFile(tempFilePath, decompressedPath);
            
            // Process the decompressed file
            await self.processCSV(decompressedPath, s3Key);
            
            // Cleanup temporary files
            await self.cleanupTempFiles([tempFilePath, decompressedPath]);
            
            return true;
        } catch (error) {
            // Cleanup temp files in case of error
            await self.cleanupTempFiles([tempFilePath, decompressedPath]);
            throw error;
        }
    }

    /**
     * Download file from S3 using the AWSS3StreamUtils
     * @param {string} s3Key - S3 key of the file
     * @param {string} localPath - Local path to save the file
     */
    async downloadFileWithS3Utils(s3Key, localPath) {
        const self = this;
        const logger = self.L;
        logger.info({ s3Key, localPath }, 'Downloading file from S3 with S3Utils');
        
        // Create a write stream to the local file
        const fileWriteStream = fs.createWriteStream(localPath);
        
        try {
            // Use the s3Utils to get a read stream
            const params = {
                Bucket: self.bucketName,
                Key: s3Key
            };
            
            // Get a readable stream from S3 via the configured S3 client in s3Utils
            const s3ReadStream = self.s3Utils.s3.getObject(params).createReadStream();
            
            // Return a promise that resolves when the file is completely written
            return new Promise((resolve, reject) => {
                // Handle errors on the write stream
                fileWriteStream.on('error', (err) => {
                    logger.error({ err, s3Key, localPath }, 'Error writing S3 file to disk');
                    reject(err);
                });
                
                // Handle errors on the read stream
                s3ReadStream.on('error', (err) => {
                    logger.error({ err, s3Key }, 'Error reading file from S3');
                    fileWriteStream.end();
                    reject(err);
                });
                
                // When the write is complete, resolve the promise
                fileWriteStream.on('finish', () => {
                    logger.info({ s3Key, localPath }, 'Successfully downloaded file from S3');
                    resolve();
                });
                
                // Pipe the S3 read stream to the file write stream
                s3ReadStream.pipe(fileWriteStream);
            });
        } catch (err) {
            logger.error({ err, s3Key, localPath }, 'Error downloading file from S3');
            fileWriteStream.end();
            throw err;
        }
    }

    /**
     * Decompress a .zst file
     * @param {string} zstFilePath - Path to .zst file
     * @param {string} outputPath - Path for decompressed output
     */
    async decompressZstFile(zstFilePath, outputPath) {
        let self = this;

        return new Promise((resolve, reject) => {
            self.L.info(`Decompressing ${zstFilePath} to ${outputPath}`);
    
            if (!fs.existsSync(zstFilePath)) {
                const errMsg = `Input file does not exist: ${zstFilePath}`;
                self.L.error(errMsg);
                return reject(new Error(errMsg));
            }
    
            const zstd = spawn('zstd', ['-f', '-d', zstFilePath, '-o', outputPath]);
    
            zstd.stderr.on('data', (data) => {
                self.L.error(`[zstd stderr] ${data.toString()}`);
            });
    
            zstd.on('error', (err) => {
                self.L.error(`Failed to start zstd:`, err);
                reject(err);
            });
    
            zstd.on('close', (code) => {
                if (code === 0) {
                    self.L.info(`Successfully decompressed ${zstFilePath}`);
                    resolve();
                } else {
                    const errMsg = `zstd exited with code ${code}`;
                    self.L.error(errMsg);
                    reject(new Error(errMsg));
                }
            });
        });
    }

    /**
     * Process JSON lines from decompressed file
     * @param {string} filePath - Path to decompressed file
     * @param {string} originalS3Key - Original S3 key (for reference)
     */
    
    async processCSV(filePath, originalS3Key) {
        let self = this;
    
        return new Promise((resolve, reject) => {
            self.L.info(`Processing decompressed file ${filePath}`);
    
            const fileStream = fs.createReadStream(filePath);
            const records = [];
            let lineCount = 0;
    
            fileStream.pipe(csv())
                .on('data', (row) => {
                    lineCount++;
    
                    try {
                        // Extract customer_id and updated_at
                        self.L.log("row", row);
                        if (row.customerid && row.updatedAt) {
                            records.push({
                                customer_id: row.customerid,
                                updated_at: row.updatedAt,
                                file_name: path.basename(originalS3Key),
                                date_folder: originalS3Key.split('/')[1], // Extract date folder
                                processed_at: MOMENT().format()
                            });
                        }
    
                        // Process in batches to avoid memory issues
                        if (records.length >= self.batchSize) {
                            const batch = [...records]; // Clone current batch
                            records.length = 0; // Reset records array
    
                            // Publish batch to Kafka
                            self.publishToKafka(batch)
                                .catch(err => {
                                    self.L.error(`Error publishing batch to Kafka:`, err);
                                    // Continue processing even if we have Kafka errors
                                });
                        }
                    } catch (error) {
                        self.L.error(`Error processing row ${lineCount} in ${filePath}:`, error);
                        // Continue processing other rows
                    }
                })
                .on('end', () => {
                    // Process any remaining records
                    if (records.length > 0) {
                        self.publishToKafka(records)
                            .catch(err => {
                                self.L.error(`Error publishing final batch to Kafka:`, err);
                            });
                    }
                    self.L.info(`Finished processing ${lineCount} lines from ${filePath}`);
                    resolve();
                })
                .on('error', (error) => {
                    self.L.error(`Error reading file ${filePath}:`, error);
                    reject(error);
                });
        });
    }

    /**
     * Cleanup temporary files
     * @param {Array<string>} filePaths - Paths to files to delete
     */
    async cleanupTempFiles(filePaths) {
        let self = this;
        
        return Promise.all(filePaths.map(filePath => {
            return new Promise((resolve) => {
                if (fs.existsSync(filePath)) {
                    fs.unlink(filePath, (err) => {
                        if (err) {
                            self.L.error(`Error deleting temporary file ${filePath}:`, err);
                        } else {
                            self.L.verbose(`Deleted temporary file ${filePath}`);
                        }
                        resolve();
                    });
                } else {
                    resolve();
                }
            });
        }));
    }

    /**
     * Publish records to Kafka
     * @param {Array} records - Records to publish
     */
    publishToKafka(records) {
        let self = this;
        
        return new Promise((resolve, reject) => {
            if (!records || records.length === 0) {
                return resolve();
            }
            
            const kafkaTopic = _.get(self.config, ['KAFKA', 'TOPICS', 'CUSTOMER_UPDATES', 'TOPIC'], 'customer-updates');
            
            self.L.info(`Publishing ${records.length} records to Kafka topic ${kafkaTopic}`);
            
            // Create messages in batches to avoid memory issues
            const batchSize = 1000;
            const batches = [];
            
            for (let i = 0; i < records.length; i += batchSize) {
                const batch = records.slice(i, i + batchSize).map(record => {
                    return {
                        topic: kafkaTopic,
                        messages: JSON.stringify(record)
                    };
                });
                batches.push(batch);
            }
            self.L.log("batches", batches);
            
            // Process batches sequentially
            const processBatch = (index) => {
                if (index >= batches.length) {
                    return resolve();
                }
                self.customerKafkaPublisher.publishData(batches[index], function(error, data) {
                    if (error) {
                        self.L.error(`Error publishing batch ${index + 1}/${batches.length} to Kafka: ${JSON.stringify(error)}`);
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:S3_CUSTOMER_UPDATES_PROCESSOR", 'STATUS:ERROR', 'TYPE:KAFKA_PUBLISH']);
                        return reject(error);
                    }
                    
                    self.L.info(`Successfully published batch ${index + 1}/${batches.length} to Kafka`);
                    
                    // Clear memory references for the batch we just sent
                    batches[index] = null;
                    
                    // Process next batch
                    processBatch(index + 1);
                });
            };
            // Start processing batches
            processBatch(0);
        });
    }

    /**
     * Stop the cron service
     */
    suspendOperations() {
        this.L.info("Suspending operations for S3 Customer Updates Processor");
        
        // Close Kafka connection
        if (this.customerKafkaPublisher) {
            this.customerKafkaPublisher.close();
        }
        
        // Cleanup temp directory
        try {
            if (fs.existsSync(this.tempDir)) {
                fs.readdirSync(this.tempDir).forEach(file => {
                    const filePath = path.join(this.tempDir, file);
                    fs.unlinkSync(filePath);
                });
            }
        } catch (error) {
            this.L.error("Error cleaning up temp directory:", error);
        }
    }
}

// Export the cron service
module.exports = S3CustomerUpdatesProcessor; 