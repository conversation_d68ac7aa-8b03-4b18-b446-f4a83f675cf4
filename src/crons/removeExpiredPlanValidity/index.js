
"use strict";

import MOMENT from 'moment'
import { cloneDeep } from 'lodash'

import operator<PERSON>eep<PERSON>ata from './operatorKeepDataConfig'
import Helper from './helper'
import Util from '../../lib/requestWrapper/util';

/*
    sort keep_data config on basis of no of days to keep in revrese order, 
      we first remove rows of those operator whose data will stay most time in our db
*/

operatorKeepData.sort((operator1, operator2) => operator2.keepDataUpToDays - operator1.keepDataUpToDays);

/*
    Flow

    - Remove all data from plan_validity where validity_expiry_date <= now - 3days except VIL, 
       for VIL we remove 10 days old data from their expiry. 
*/

class RemoveExpiredPlanValidity extends Helper {
    constructor(opts) {
        super(opts);

        this.dbInstance = opts.dbInstance;

        this.dbFetchRecordsLimit = 1000;
        this.dbDeleteRecordsLimit = 100;

        this.planValidityTables = ["plan_validity", "plan_validity_dth", "plan_validity_fastag", "plan_validity_jio", "plan_validity_vil", "plan_validity_airtel", "plan_validity_mobile"];

        this.L = opts.L;
    }

    async start() {
        try {

            for (let table of this.planValidityTables) {
                this.table = table;
                
                let excludedOperators = [],
                    time;
                for (let record of operatorKeepData) {
                    this.id = 0;
                    
                    time = MOMENT().subtract(record.keepDataUpToDays, 'days').endOf('day').format('YYYY-MM-DD HH:mm:ss');

                    this.L.log('start', `Removing enteries from table:${this.table} with validity_expiry_date < ${time} and operator excluded: ${JSON.stringify(excludedOperators)}`);

                    await this.fetchRecords(time, excludedOperators);

                    this.L.log('done', `Entries removed from table:${this.table} with validity_expiry_date < ${time} and operator excluded: ${JSON.stringify(excludedOperators)}`);

                    if (Array.isArray(record.operators)) {
                        excludedOperators = excludedOperators.concat(record.operators);
                    }
                }
            }

            this.dbInstance.close((err) => {
                if (err) {
                    this.L.log('Error while closing db connection');
                }
                this.L.log('Terminating cron')
                process.exit(0)
            });


        } catch (error) {
            this.L.critical("remove_expired_plan_validity :: error", error);
        }
    }

    async fetchRecords(time, excludedOperators) {
        try {
            let records = [];

            do {
                let { query, params } = this.evaluateFetchRecordQuery(time, excludedOperators, this);
                records = await this.getRecords(query, params);

                if (records.length) {
                    this.id = records[records.length - 1].id;
                    await this.processRecords(records);
                }

            } while (records.length == this.dbFetchRecordsLimit);
        } catch (error) {
            this.L.error(`remove_expired_plan_validity:: error in fetchRecords from table: ${this.table}`, error);
        }
    }

    async processRecords(records) {
        let recordsTobeDeleted = cloneDeep(records);

        while (recordsTobeDeleted.length) {
            let ids = recordsTobeDeleted.splice(0, this.dbDeleteRecordsLimit);
            let deleteQuery = this.getDeleteQuery(ids, this.table);
            await Util.sleep(200);
            await this.deleteRecords(deleteQuery);
        }
    }

    async getRecords(query, params) {
        try {
            return await this.runQuery('RECHARGE_ANALYTICS_SLAVE', query, params);
        } catch (error) {
            this.L.error(`remove_expired_plan_validity:: error in getRecords: ${this.table}`, error);
            return await this.getRecords(query, params);
        }
    }

    async deleteRecords(query) {
        try {
            return await this.runQuery('RECHARGE_ANALYTICS', query, {});
        } catch (error) {
            this.L.error(`remove_expired_plan_validity:: error in deleteRecord on table: ${this.table}`, error);
        }
    }
}

module.exports = RemoveExpiredPlanValidity;
