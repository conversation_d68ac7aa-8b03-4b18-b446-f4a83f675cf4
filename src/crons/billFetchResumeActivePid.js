import L from 'lgr'
import _ from 'lodash'
import async from 'async'
import MOMENT from 'moment'
import utility from '../lib';
import BILLS from '../models/bills'
import startup from '../lib/startup'
import Helper from '../lib/helper'

let
    billsModel = null, CONFIG = null,    
    BATCH_SIZE = 100,
    chunkSize = 10,
    cvrData = {},    
    nextBillFetchDate = MOMENT().add(1,'days').format('YYYY-MM-DD'),
    maxId = 0;

function runForProductId(product_id, done) {
    
    let operator = _.get(cvrData,[product_id,'operator'],null)?  _.get(cvrData,[product_id,'operator'],null).toLowerCase() : null,
        productStatus = _.get(cvrData,[product_id,'status'],null),
        table = _.get(CONFIG, ['OPERATOR_TABLE_REGISTRY', operator], null);

    L.log('runForProductId', 'Processing for product_id : ', product_id);    

    if (!table || !productStatus) {
        L.error('runForProductId :: table not found or productStatus is inactive in CVR',`table_${table}_productStatus_${productStatus}`);        
        return done();
    }

    resetFromIdOffset();
    
    fetchAndUpdate(table,product_id,function () {                
        return done();
    });
}

function fetchAndUpdate(tableName,productId,done) {

    L.log("fetchAndUpdate", `Running for table_${tableName}_productId_${productId} and id > ${getFromIdOffset()}`);

    async.waterfall([
        next => {
            return billsModel.fetchDisabledRecords(next, tableName,productId,BATCH_SIZE, getFromIdOffset());
        },
        (records, next) => {
            L.log("fetchAndUpdate", `No. of Fetched records for ${tableName}`, _.get(records, 'length', 0));
            if (_.get(records, 'length', 0) >= BATCH_SIZE ) { //If batch size is equal to records fetched that means there can be more records.
                return updateDisableRecords( function(){
                    fetchAndUpdate(tableName,productId,done);
                }, records, tableName, productId);   
            }
            else if(_.get(records, 'length', 0)){ 
                return updateDisableRecords(next, records, tableName, productId);  
            }            
            else {                
                resetFromIdOffset();
                return next(null);
            }
        }
    ], function (err) {
        if (err)
            L.error('fetchAndResume', 'Error - ', err);                    
        L.log("fetchDisabledRecords", `completed for productId : ${productId}`);
        done();
    });
}

function updateDisableRecords(done, records, tableName, productId) {    
    let currentPointer = 0,update_ids;

    async.whilst(
        () => {
            return currentPointer < records.length;
        },
        (callback) => {
            let nextChunk = records.slice(currentPointer, (currentPointer+chunkSize));
            currentPointer += chunkSize;
            update_ids = [... nextChunk.map((record)=>{return record.id;})];
            updateFromIdOffset(update_ids[update_ids.length -1]);                
            billsModel.updateDisableRecord((err,data) => {
                if (err) {
                    callback(err)
                }else{
                    utility._sendMetricsToDD(1, ['REQUEST_TYPE:BILL_FETCH_RESUME','STATUS:UPDATE_SUCCESSFULLY','PRODUCT:' + productId]);
                    callback();
                }
            }, tableName, nextBillFetchDate, update_ids);
        },
        (err) => {
            if (err) {
                L.error("error in updateDisableRecords : ", err);
            }
            done();
        }
    );
}

function getFromIdOffset(){
    return maxId;
}

function resetFromIdOffset(){
    maxId = 0;
}

function updateFromIdOffset(newId){
    maxId = Math.max(maxId,newId)
}


function start(done, options) {
    
    if (!options.product_id || !options.product_id.length) {
        return done(new Error('please provide product_id'));
    }
    
    cvrData = _.get(CONFIG ,'CVR_DATA', {});
    if (_.isEmpty(cvrData)) {
        L.critical('BillFetchReume cron: CVR data is empty');
        return done();
    }
    
    async.eachLimit(options.product_id, 1, runForProductId, function (error) {
        L.log("execute", "finished for all productId !!");
        return done(error);
    });    
}

(function () {
    if (require.main === module) {
        var commander = require('commander');
        commander
            .version('0.0.1')
            .option('-v, --verbose', 'Run in verbose mode')
            .option('-p, --product_id <value>', 'product_id', Helper.list)
            .parse(process.argv);

        console.time('Execution Time');        
        startup.init({
            billFetchResumeActivePidCron : true
        }, function (err, options) {
            try {
                if (err) {
                    L.critical("Service has crashed!! Please check on priority", err)
                    process.exit(1);
                }
                CONFIG = options.config;
                billsModel = new BILLS({
                    dbInstance: options.dbInstance,
                    config: CONFIG,
                    L: L
                });
                start(function (err) {
                    setTimeout(function () {
                        if (err) {
                            console.log(err);
                            console.log('FAILURE');
                            console.timeEnd('Execution Time');
                            process.exit(1);
                        }
                        console.log('SUCCESS');
                        console.timeEnd('Execution Time');
                        process.exit(0);
                    }, 1000);
                }, commander);
            } catch (error) {
                L.critical("Service has crashed!! Please check on priority", error)
                process.exit(1);
            }
        });
    }
})();

// NODE_ENV=production node dist/crons/billFetchResumeActivePid.js --productid "194,195,201"
