import startup from "../lib/startup";
import MOMENT from "moment";
import _, { reject } from "lodash";
import L from "lgr";
import fs from "fs";
import { parseStream } from "fast-csv";
import { each, eachLimit, eachSeries, parallel } from "async";
import AWSCsvIngester from "../lib/awscsvingester";
import utility from "../lib";
import ASYNC from 'async'
import BILLS from '../models/bills';
import { resolve } from "path";



let serviceName = "CC_INGESTION_KAFKA";
let progressFilePath = `/var/log/digital-notification/progress-CC-Kafka-${MOMENT().subtract(1, "day").format("MMYYYY")}.json`;
let progressTracker = {};
/** Maintain offset and processed files in a path */
class DeleteNonRuScript {
    constructor(options) {
        this.L = options.L;
        this.dbInstance = options.dbInstance;
        this.activePidLib = options.activePidLib;
        this.greyScaleEnv = _.get(options, 'greyScaleEnv', false);
        this.rechargeConfig = options.rechargeConfig;
        this.infraUtils = _.get(options, 'INFRAUTILS', null);
        this.config = options.config;
        this.bucketName = "digital-reminder";
        progressTracker = this.getProgressObject();
        this.csvIngester = new AWSCsvIngester(options, this.updateProgress);
        this.logPrefix = serviceName;
        this.readBatchSize = 100;
        this.parallelWrites = 5;
        this.serviceMissing = 0;
        this.operatorMissing = 0;
        this.productidMissing = 0;
        this.rechargeNumberMissing = 0;
        this.invalidRechargeNumber = 0;
        this.invalidCustomerId = 0;
        this.customerIdMissing = 0;
        this.inserted_row_count = 0;
        this.operatorMisMatch = 0;
        this.recordExists = 0;
        this.operatorMapping = {};
        this.currentFile = "";
        this.bills = new BILLS(options);
        this.folderPath = `${_.get(options.config, ["DYNAMIC_CONFIG", "CC_INGESTION", "path", "value"], "digital-reminder/upms_correction")}`;

        this.files = [];
    }

    getProgressObject() {
        let progress = {};
        progressFilePath = `/var/log/digital-notification/progress-CC-Kafka-${MOMENT().subtract(1, "day").format("MMYYYY")}.json`
        this.L.info("Loading progress object from", progressFilePath);
        if (fs.existsSync(progressFilePath)) {
            const progressData = fs.readFileSync(progressFilePath, "utf-8");
            progress = JSON.parse(progressData);
        }
        this.L.info("Loaded", progress);
        return progress;
    }

    updateProgress(filename, count) {
        let self = this;
        if (_.get(progressTracker, [filename], 0) == -1) return;
        if (self.greyScaleEnv) {
            self.L.info("updateProgress", "Kafka publishing is disabled in greyScaleEnv");
            return;
        }
        _.set(progressTracker, [filename], count);
        this.L.info("Updated progess Object", JSON.stringify(progressTracker), count);
        fs.writeFileSync(progressFilePath, JSON.stringify(progressTracker, null, 2));
    }

    filterFileByMonth(filename) {
        try {
            let date = filename.split('$')[1].split('.')[0].slice(0, 7)
            if (date == MOMENT().subtract(1, 'day').format('YYYY-MM')) return true

        } catch (err) {
            return false
        }
        return false
    }

    start() {
        let self = this;
        self._start(function (err) {
            setTimeout(function () {
                if (err) {
                    console.log(err);
                    console.log('FAILURE');
                    console.timeEnd('Execution Time');
                    process.exit(1);
                }
                console.log('SUCCESS');
                console.timeEnd('Execution Time');
                process.exit(0);
            }, 1000);
        });
    }

    _start(callback) {
        let self = this;
        let data = {};
        progressTracker = this.getProgressObject();
        ASYNC.waterfall([
            next => {
                self.L.log('_start', 'Going to initialize Kakfa Publisher');
                return self.configureKafkaPublisher(next);
            },
            next => {
                self.L.log('_start', 'Going to cofigure csv ingestor');
                self.csvIngester.configure(this.bucketName, this.logPrefix, this.batchSize);
                next();
            },
            next => {
                self.L.info("Getting Files in the folder");
                this.csvIngester.getFileNames(this.folderPath, function (err, data) {
                    console.log("🚀 ~ DeleteNonRuScript ~ data:", data)
                    if (err) {
                        self.L.error("Error while getting files");
                        return callback(err);
                    } else {
                        data = _.filter(data, self.filterFileByMonth);
                        return eachSeries(data, self.processEachFile.bind(self), callback);
                    }
                });
            }], function (error) {
                if (error) {
                    self.L.error('_start', 'Error', error);
                }
                return callback(error);
            });
    }

    configureKafkaPublisher(done) {
        let self = this;

        self.kafkaPublisher = new self.infraUtils.kafka.producer({
            "kafkaHost": self.config.KAFKA.TOPICS.NON_PAYTM_RECORDS.HOSTS
        });
        self.kafkaPublisher.initProducer('high', function (error) {
            if (!error) {
                self.L.log("notify :: configureKafkaPublisher", "publisher Configured");
                return done();
            }
            return done(error);
        });
    }

    processEachFile(filename, callback) {
        if (_.get(progressTracker, filename, null) == -1) {
            /** File has already been processed we can skip*/
            this.L.info("Skipping file ", filename, "as it has been already processed");
            return callback();
        }
        this.L.info("Processing file :- ", filename);
        this.currentFile = filename;
        let skipRows = _.get(progressTracker, [filename], 0);
        this.csvIngester.start(
            this.processRecordinBatch.bind(this),
            filename,
            function (error, data) {
                return callback();
            },
            skipRows
        );
    }

    async processRecordinBatch(data) {
        let self = this;
        let records = [];
        /** Distribute a  batch of 1000 records into further smaller batches */
        for (let i = 0; i < data.length; i = i + self.readBatchSize) {
            records.push(data.slice(i, Math.min(i + self.readBatchSize, data.length)));
        }
        await new Promise((resolve, reject) => {
            eachSeries(records, self.processOneBatch.bind(self), function (error) {
                if (error) self.L.error(self.logPrefix, "Error while processing batch", error);
                return resolve();
            });
        });
    }

    processOneBatch(records, callback) {
        let self = this;
        /** Start processing Batch */
        eachLimit(
            records,
            self.parallelWrites,
            function (record, cb) {
                self.processRecord(
                    function () {
                        cb();
                    },
                    record
                );
            },
            function (error) {
                /** Release memory of records variable */
                (records = null);
                if (error) {
                    self.L.error(self.logPrefix, "Error while processing batch", error);
                    return callback(error);
                }
                return callback();
            });
    }

    validateRecord(data) {
        let self = this;

        let productId = _.get(data, "product_id", null), service, operator, paytype;
        try {
            service = _.toLower(_.get(self.config, ["CVR_DATA", productId, "service"], "{}"));
            operator = _.toLower(_.get(self.config, ["CVR_DATA", productId, "operator"], "{}"));
            paytype = _.toLower(_.get(self.config, ["CVR_DATA", productId, "paytype"], "{}"));
        }
        catch (error) {
            this.L.error('validationRecor', `Error occured while parsing CVR_DATA:${data}`, error)
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION_KAFKA", 'STATUS:ERROR', 'TYPE:PID_INVALID_PARSE', `PRODUCT_ID : ${productId}`]);
            return false;
        }

        let params = {
            service: service,
            operator: operator,
            product_id: productId,
            recharge_number: _.get(data, "recharge_number", null),
            customer_id: _.get(data, "customer_id", null),
            paytype: paytype
        };

        if (!params.customer_id) {
            self.L.error(self.currentFile, "Customer ID missing", params);
            self.customerIdMissing++;
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION_KAFKA", "STATUS:PARAMS_MISSING", `SERVICE:${params.service}`, `OPERATOR:${params.operator}`, "TYPE:CUSTOMER_ID_NOT_FOUND_FROM_CSV"]);
            return false;
        }
        if (!params.recharge_number) {
            self.L.error(self.currentFile, "Recharge number missing", params);
            self.rechargeNumberMissing++;
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION_KAFKA", "STATUS:PARAMS_MISSING", `SERVICE:${params.service}`, `OPERATOR:${params.operator}`, "TYPE:RECHARGE_NUMBER_NOT_FOUND_FROM_CSV"]);
            return false;
        }
        if (!params.operator) {
            self.L.error(self.currentFile, "Operator missing", params, _.get(data, "operator", null));
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION_KAFKA", "STATUS:PARAMS_MISSING", `SERVICE:${params.service}`, `OPERATOR:${params.operator}`, "TYPE:OPERATOR_NOT_FOUND_FROM_CSV"]);
            return false;
        }
        if (!params.service) {
            self.L.error(self.currentFile, "Service name missing", params);
            self.serviceMissing++;
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION_KAFKA", "STATUS:PARAMS_MISSING", `SERVICE:${params.service}`, `OPERATOR:${params.operator}`, "TYPE:SERVICE_NOT_FOUND_FROM_CSV"]);
            return false;
        }
        if (!params.product_id) {
            self.L.error(self.currentFile, "Product Id missing", params);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION_KAFKA", "STATUS:PARAMS_MISSING", `SERVICE:${params.service}`, `OPERATOR:${params.operator}`, "TYPE:PAYTYPE_NOT_FOUND_FROM_CSV"]);
            return false;
        }

        return params;
    }

    async getRecordsFromDbByCustId(record) {
        return new Promise((resolve, reject) => {
            let self = this;
            self.bills.getBillByCustomer((err, data) => {
                if (err) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION_KAFKA", "STATUS:ERROR", "TYPE:DB_ERROR"]);
                    return reject(err);
                }
                if (!data || !_.isArray(data) || data.length < 1) return resolve(false);

                let dbRecord = false;
                for (let row of data) {
                    const last4_MCN = row.recharge_number.substr(-4);
                    if (last4_MCN == record.recharge_number.replace(/\s+/g, '').substr(-4)) {
                        let attributes = JSON.parse(_.get(self.config, ['CVR_DATA', row.product_id, 'attributes'], '{}'))
                        let bankName = _.toLower(_.get(attributes, ['bank_code'], ''));
                        if (record.bank_name == bankName) {
                            dbRecord = true;
                        }
                    }
                }
                return resolve(dbRecord);
            }, "bills_creditcard", record.customer_id);
        });
    }
    

    validateCustomerID(customer_id){
        let self =this;
        // Regular expression to match only numeric characters
        let numericRegex =/^[0-9]+$/;

        // Check if the trimmed input string matches the numeric regex
        return numericRegex.test(customer_id);
    }

    validateRechargeNumber(rechargeNumber){
        let self=this;
        let trimmedString = rechargeNumber.replace(/\s+/g, '');

        // Check if last 4 characters are numbers and all other characters are 'X'
        const lastFourDigits = trimmedString.slice(-4);
        const allXExceptLastFour = trimmedString.slice(0, -4).replace(/X/g, '');

        return lastFourDigits.match(/^\d{4}$/) && allXExceptLastFour.length === 0;
    }

    async processRecord(cb, data) {
        _.set(data, "service", "financial services");
        _.set(data, "operator", "sbi");
        _.set(data, "product_id", "**********");
        _.set(data, "recharge_number", "XXXX XXXX XXXX 6492");
        _.set(data, "customer_id", "**********");
        _.set(data, "paytype", "credit card");
        _.set(data, "source", "pg");
        _.set(data, "bank_code", "sbi");
        _.set(data, "alternate_number_1", "12");
        _.set(data, "alternate_number_2", "123");
        _.set(data, "alternate_number_3", "1234");
        _.set(data, "alternate_number_4", "12345");
        _.set(data, "alternate_number_5", "123456");
        let self = this;
        try {
            console.log("Entering processRecord function");
            let params = await self.validateRecord(data);
            console.log("Validation successful, proceeding to publishRecord");
            if (params === false) {
                console.log("Validation failed, skipping record");
                return cb();
            }
            self.publishRecord(params, (error) => {
                if (error) {
                    console.log("Error occurred while publishing record:", error);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION_KAFKA", "STATUS:RECORDS_NOT_PUBLISHED", `SERVICE:${params.service}`, `OPERATOR:${params.operator}`, "TYPE:RECORDS_NOT_PUBLISHED"]);
                    L.error("error", error);
                } else {
                    console.log("Record published successfully");
                    self.L.info(self.currentFile, "Record published");
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION_KAFKA", "STATUS:RECORDS_PUBLISHED", `SERVICE:${params.service}`, `OPERATOR:${params.operator}`, "TYPE:RECORDS_PUBLISHED"]);
                    self.L.log(`publishRecords :: Records Published  for customerId:${params.customer_id},Processed records so far:${++self.inserted_row_count}`);
                }
                return cb(error);
            });
        } catch (err) {
            console.log("Error occurred during processing record:", err);
            return cb();
        }
    }

    publishRecord(record, done) {
        let
            self = this,
            payload = self.prepareDataToPublish(record);
        if (self.greyScaleEnv) {
            self.L.info("publishRecord", "Kafka publishing is disabled in greyScaleEnv");
            console.log("🚀 ~ DeleteNonRuScript ~ publishRecord ~ payload:", payload)
            return done();
        }
        self.kafkaPublisher.publishData([{
            topic: _.get(self.config.KAFKA, 'SERVICES.NON_PAYTM_RECORDS_DWH.TOPIC', ''),
            messages: JSON.stringify(payload)
        }], function (error) {
            if (error) {
                self.L.critical('publishData', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(payload), error);
                utility._sendMetricsToDD(1, [
                    'STATUS:ERROR',
                    'TOPIC:' + _.get(self.config.KAFKA, 'SERVICES.NON_PAYTM_RECORDS_DWH.TOPIC', null),
                    'REQUEST_TYPE:CC_INGESTION_KAFKA',
                    'OPERATOR:' + _.get(payload, 'operator')
                ])
            } else {
                self.L.log('publishData', 'Message published successfully in Kafka', ' on topic PUBLISHER_BILL_FETCH', JSON.stringify(payload));
                utility._sendMetricsToDD(1, [
                    'STATUS:PUBLISHED',
                    'TOPIC:' + _.get(self.config.KAFKA, 'SERVICES.NON_PAYTM_RECORDS_DWH.TOPIC', null),
                    'REQUEST_TYPE:CC_INGESTION_KAFKA',
                    'OPERATOR:' + _.get(payload, 'operator')
                ])
            }
            return done();
        }, [200, 800]);
    }

    prepareDataToPublish(record) {
        let extra = {};
        extra.updated_data_source = 'UPMS_CORRECTION_FILES_CUST_ID0';
        let nonPaytmKafkaPayload = {
            customerId: record.customer_id,
            service: record.service,
            paytype: record.paytype,
            productId: record.product_id,
            operator: record.operator,
            rechargeNumber: record.recharge_number,
            dbEvent: 'delete',
            extra: JSON.stringify(extra)
        }
        return nonPaytmKafkaPayload;
    }
}

(function main() {
    if (require.main === module) {
        startup.init({}, function (err, options) {
            let script;
            try {
                script = new CCIngestionKafka(options);
                script._start(function (err) {
                    setTimeout(function () {
                        if (err) {
                            console.log("main::Error" + err);
                            process.exit(1);
                        } else {
                            console.log("main::completed");
                            process.exit(0);
                        }
                    }, 1000);
                });
            } catch (err) {
                options.L.error(err);
                process.exit(1);
            }
        });
    }
})();

export default DeleteNonRuScript;
