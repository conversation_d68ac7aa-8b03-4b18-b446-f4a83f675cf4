import L from 'lgr'
import _ from 'lodash'
import ASYNC from 'async'
import BILLS from '../models/bills'
import startup from '../lib/startup'
import utility from '../lib'
import service from 'recharge-config/service'
import { product } from 'digital-in-util'
import EncryptionDecryptioinHelper from '../lib/EncryptionDecryptioinHelper'
import CassandraBills from '../models/cassandraBills';


let
    billsModel = null, CONFIG = null, infraUtils = null, automaticKafkaProducer = null,
    destTable = 'bills_archive',
    dumpTable = 'cust_id_rn_mapping',
    BATCH_SIZE = 100,
    ANALYTICS = {},
    maxId = 0, DELETE_STATUS = "DELETED";

class ArchivalRecords {
    constructor(options) {
        this.config = options.config;
        this.L = options.L;
        this.infraUtils = _.get(options, 'INFRAUTILS', null);
        this.dbInstance = options.dbInstance;
        this.billsModel = new BILLS(options);
        this.encryptionDecryptionHelper = new EncryptionDecryptioinHelper(options);
        this.operatorTableMap = _.get(this.config, 'OPERATOR_TABLE_REGISTRY', {});
        this.allowedOperatorsForSmartFetch = _.get(this.config, ['DYNAMIC_CONFIG', 'NON_PAYTM_CONFIG', 'SMART_FETCH_CONFIG', 'OPERATORS'], null);
        this.allowedServicesForSmartFetch = _.get(this.config, ['DYNAMIC_CONFIG', 'NON_PAYTM_CONFIG', 'SMART_FETCH_CONFIG', 'SERVICES'], null);
        this.allowedServicessForUserInitiatedDelete = _.get(this.config, ['DYNAMIC_CONFIG', 'USER_INITIATED_DELETE_CONFIG', 'ALLOWED_SERVICES', 'SERVICES'], ['electricity']);
        this.cassandraBills = new CassandraBills(options);
        setInterval(() => {
            this.initializeVariable();
        }, _.get(this.config, 'COMMON.INTERVAL_FOR_REINITIALIZE', 15) * 60 * 1000);
    }

    initializeVariable() {
        let self = this;
        self.allowedOperatorsForSmartFetch = _.get(self.config, ['DYNAMIC_CONFIG', 'NON_PAYTM_CONFIG', 'SMART_FETCH_CONFIG', 'OPERATORS'], null)
        self.allowedServicesForSmartFetch = _.get(self.config, ['DYNAMIC_CONFIG', 'NON_PAYTM_CONFIG', 'SMART_FETCH_CONFIG', 'SERVICES'], null)
        self.L.log('initializeVariable', 'Reinitializing the variables');
    }

    start() {
        let self = this;
        self.configureKafka(function (error) {
            if (error) {
                self.L.critical('SMS_PARSING_ELECTRICITY_BILL_PAYMENT :: start', 'unable to configure kafka', error);
                // process.exit(0);
            }
            else {
                self.L.log('SMS_PARSING_ELECTRICITY_BILL_PAYMENT :: start', 'Kafka Configured successfully !!');
                self._start(function (err) {
                    setTimeout(function () {
                        if (err) {
                            console.log(err);
                            console.log('FAILURE');
                            console.timeEnd('Execution Time');
                            // process.exit(1);
                        }
                        console.log('SUCCESS');
                        console.timeEnd('Execution Time');
                        // process.exit(0);
                    }, 1000);
                });
            }
        });
    }

    getFromIdOffset() {
        return maxId;
    }

    updateFromIdOffset(newId) {
        maxId = Math.max(maxId, newId)
    }

    updateAnalytics(tableName, recordsCount) {
        let recordProcessed = _.get(ANALYTICS, [tableName], 0);
        _.set(ANALYTICS, [tableName], recordProcessed + recordsCount);
    }

    resetFromIdOffset() {
        maxId = 0;
    }

    getArchivedRecordCount(tableName) {
        return _.get(ANALYTICS, [tableName], 0);
    }

    _start(done, options) {
        let self = this;
        let tableList;

        if (options && options.table) {
            tableList = [options.table];
        } else {
            tableList = _.uniq(_.values(_.get(self.config, 'OPERATOR_TABLE_REGISTRY', {})));
        }
        tableList.push("bills_airtelprepaid");
        for (let i = 0; i <= 9; i++) {
            tableList.push("bills_airtelprepaid" + i)
        }
        self.resetFromIdOffset();
        ASYNC.eachLimit(tableList, 1, self.fetchAndArchive.bind(self), function () {
            self.showAnalytics();
            done();
        });
    }

    configureKafka(done) {
        let self = this;
        ASYNC.waterfall([
            next => {
                /**
                * Kafka publisher to update events to non paytm bills pipeline 
                */
                self.nonPaytmKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.NON_PAYTM_RECORDS.HOSTS
                });
                this.nonPaytmKafkaPublisher.initProducer('high', function (error) {
                    if (error) {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:ERROR', 'TYPE:NON_PAYTM_RECORDS_PUBLISHER', 'SOURCE:MAIN_FLOW']);
                    }
                    return next(error)
                });
            },
            next => {
                /**
                 * Kafka publisher to update events to non paytm bills pipeline 
                 */
                self.nonPaytmKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.NON_PAYTM_RECORDS.HOSTS
                });
                this.nonPaytmKafkaPublisher.initProducer('high', function (error) {
                    if (error) {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:ERROR', 'TYPE:NON_PAYTM_RECORDS_PUBLISHER', 'SOURCE:MAIN_FLOW']);
                    }
                    return next(error);
                });
            },
            next => {
                /**
                 * Kafka publisher to check active user and then publish in non paytm bills pipeline 
                 */
                self.checkActiveUserKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.CHECK_ACTIVE_USERS.HOSTS
                });
                this.checkActiveUserKafkaPublisher.initProducer('high', function (error) {
                    if (error) {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:ARCHIVAL_CRON", 'STATUS:ERROR', 'TYPE:CHECK_ACTIVE_USERS_PUBLISHER', 'SOURCE:INITIALIZE_PUBLISHER']);
                    }
                    return next(error);
                });
            }
        ], function (error) {
            if (error) {
                self.L.critical('configureKafka', 'Could not initialize Kafka', error);
                return done(error)
            }
            return done(null);
        });
    }

 fetchAndArchive(tableName, done) {
    let self = this;

        ASYNC.waterfall([
            next => {
                return self.billsModel.fetchNotInUseRecords(next, tableName, BATCH_SIZE, self.getFromIdOffset());
            },
            (records, lengthOfFetchRecords, lastRecordId, next) => {
                L.log("fetchAndArchive", `No. of Fetched records for ${tableName}`, _.get(records, 'length', 0));
                if (lengthOfFetchRecords > 0) {
                    self.updateAnalytics(tableName, _.get(records, 'length', 0));
                    return self.archiveRecords(function () {
                        self.fetchAndArchive(tableName, done);
                    }, records, tableName, lastRecordId);
                }
                else {
                    self.resetFromIdOffset();
                    return next(null);
                }
            }
        ], function (err) {
            if (err)
                L.error('fetchAndArchive', 'Error - ', err);

            L.log("fetchAndArchive", `Archival completed for table : ${tableName}, archived ${self.getArchivedRecordCount(tableName)} records`);
            done();
        });
    }

    archiveRecords(done, records, tableName, lastRecordId) {
        let self = this;
        self.L.log("archiveRecords", `Current from id offset is ${self.getFromIdOffset()} for table ${tableName}`);
        self.updateFromIdOffset(lastRecordId);
        self.L.log("archiveRecords", `Updating from id offset to ${lastRecordId} for table ${tableName}`);
        ASYNC.eachLimit(records, 1, ASYNC.ensureAsync(self.archiveRecord.bind(self, tableName)), function () {
            return done();
        });
    }

    pushRecordToNONPAYTM(done, payload) {
        let self = this;
        self.nonPaytmKafkaPublisher.publishData([{
            topic: _.get(self.config.KAFKA, 'SERVICES.NON_PAYTM_RECORDS_DWH.TOPIC', ''),
            messages: JSON.stringify(payload)
        }], (error) => {
            if (error) {
                utility._sendMetricsToDD(1, [
                    "REQUEST_TYPE:ARCHIVAL_CRONS",
                    `SERVICE:${_.get(payload, 'service', null)}`,
                    'STATUS:ERROR',
                    `OPERATOR:${_.get(payload, 'operator', 'NO_OPERATOR')}`,
                    "TYPE:NON_PAYTM_EVENTS",
                    "TOPIC:NON_PAYTM_RECORDS_DWH"
                ]);
                self.L.critical('ARCHIVAL_RECORDS :: nonPaytmKafkaPublisher', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(payload), error);
                return done(null);
            } else {
                utility._sendMetricsToDD(1, [
                    "REQUEST_TYPE:ARCHIVAL_CRONS",
                    `SERVICE:${_.get(payload, 'service', null)}`,
                    'STATUS:SUCCESS',
                    `OPERATOR:${_.get(payload, 'operator', 'NO_OPERATOR')}`,
                    "TYPE:NON_PAYTM_EVENTS",
                    "TOPIC:NON_PAYTM_RECORDS_DWH"
                ]);
                self.L.log('ARCHIVAL_RECORDS :: nonPaytmKafkaPublisher', 'Message published successfully in Kafka', ' on topic NON_PAYTM_RECORDS', JSON.stringify(payload));
                return done(null);
            }
        })
    }

    preparePayloadForCustIdRn(record) {

        let reason = _.get(record, 'reason', null), status = DELETE_STATUS, self = this;
        let is_encrypted = _.get(record, 'is_encrypted', false);
        let meta = {
            user_type: "RU",
            deleted_bills_status: _.get(record, 'status', null),
            deleted_bills_notification_status: _.get(record, 'notification_status', null)
        }

        let payload = {
            customer_id: _.get(record, 'customer_id', null),
            service: _.get(record, 'service', null),
            recharge_number: _.get(record, 'recharge_number', null),
            operator: _.get(record, 'operator', null),
            paytype: _.get(record, 'paytype', null),
            product_id: _.get(record, 'product_id', null),
            status: status,
            meta: JSON.stringify(meta),
            is_encrypted: is_encrypted
        }
        return payload;
    }

    insertingIntoCustIdRnMappingTable(done, record) {
        let self = this;
        let allowForInsertIntoCustIdRn = _.get(self.config, ['DYNAMIC_CONFIG', 'ARCHIVAL_CRONS', 'SMART_FETCH_CONFIG', 'ALLOW_FOR_INSERT_INTO_CUST_ID_RN_MAPPING'], true);
        if (allowForInsertIntoCustIdRn) {
            let payloadForCustIdRn = self.preparePayloadForCustIdRn(record);
            self.cassandraBills.dumpIntoCustIdRnMappingTable((err) => {
                if (err) {
                    utility._sendMetricsToDD(1, [
                        `REQUEST_TYPE:ARCHIVAL_CRON`,
                        `STATUS:DB_ERROR`,
                        `TYPE:DB_INSERTION_CUST_ID_RN_MAPPING`,
                        `OPERATOR:${_.get(record, 'operator', 'NO_OPERATOR')}`,
                        `SERVICE:${_.get(record, 'service', 'NO_SERVICE')}`
                    ]);
                    self.L.error('ArchivalCrons::archiveRecord::', `Error while dumping data into cust_id_rn_mapping table for record customer_id::${record.customer_id} recharge_number::${record.recharge_number}`, err);
                    return done();
                } else {
                    utility._sendMetricsToDD(1, [
                        `REQUEST_TYPE:ARCHIVAL_CRON`,
                        `STATUS:SUCCESS`,
                        `TYPE:DB_INSERTION_CUST_ID_RN_MAPPING`,
                        `OPERATOR:${_.get(record, 'operator', 'NO_OPERATOR')}`,
                        `SERVICE:${_.get(record, 'service', 'NO_SERVICE')}`
                    ]);
                    self.L.log('ArchivalCrons::archiveRecord::', `succesfully created entry in cust_id_rn_mapping table for record customer_id::${record.customer_id} recharge_number::${record.recharge_number}`);
                    return done();
                }
            }, payloadForCustIdRn);
        } else {
            return done(null);
        }
    }

    archiveRecord(sourceTable, record, cb) {
        let self = this;


        ASYNC.waterfall([
            next => {
                if (!sourceTable || !destTable || !dumpTable) {
                    utility._sendLatencyToDD(1, ['REQUEST_TYPE: ARCHIVAL_CRONS', `STATUS: ERROR`, 'TYPE: INVALID_TABLE_NAME']);
                    return next(`Invalid archive request for operator ${record.operator} and recharge_number ${record.recharge_number} and catalogProductID ${record.product_id}`);
                }
                else if (_.get(record, 'is_automatic', 0) == 1 || _.get(record, 'is_automatic', 0) == 3) {
                    utility._sendLatencyToDD(1, ['REQUEST_TYPE: ARCHIVAL_CRONS', `STATUS: ERROR`, 'TYPE: AUTOMATIC_BILL']);
                    return next(`Subscription exists for ${record.operator} and recharge_number ${record.recharge_number} and catalogProductID ${record.product_id}`);
                }
                utility._sendLatencyToDD(1, ['REQUEST_TYPE: ARCHIVAL_CRONS', `STATUS: SUCCESS`, 'TYPE: STARTING_ARCHIVAL']);
                return next();
            },
            next => {
                let payload = self.preparePayload(record);
    
                // Parallel execution of Kafka publishing and DB insertion
                ASYNC.parallel([
                    // Dump into the cust_id_rn_mapping table
                    (done) => {
                        self.insertingIntoCustIdRnMappingTable((err) => {
                            if (err) {
                                L.error('archiveRecord', `Error during DB operation for ${sourceTable} -`, err);
                            }
                            done(null);
                        }, record);
                    },
                    // Publish the payload to Kafka for checking if user is active or not
                    (done) => {
                        if (_.get(record, 'reason', null) == "EXPIRED_USER" && ((self.allowedServicesForSmartFetch && self.allowedServicesForSmartFetch.includes(record.service)) || (self.allowedOperatorsForSmartFetch && self.allowedOperatorsForSmartFetch.includes(record.operator)))) {
                            self.checkActiveUserKafkaPublisher.publishData([{
                                topic: _.get(self.config.KAFKA, 'SERVICES.CHECK_ACTIVE_USERS.TOPIC', ''),
                                messages: JSON.stringify(payload)
                            }], (error) => {
                                if (error) {
                                    utility._sendMetricsToDD(1, [
                                        "REQUEST_TYPE:ARCHIVAL_CRON",
                                        "STATUS:ERROR",
                                        "TYPE:CHECK_ACTIVE_USERS_PUBLISHER",
                                        "SOURCE:PUBLISH_FAILURE",
                                        `OPERATOR:${_.get(record, 'operator', 'NO_OPERATOR')}`,
                                        `SERVICE:${_.get(record, 'service', 'NO_SERVICE')}`
                                    ]);
                                    self.L.critical('archiveRecord :: checkActiveUserKafkaPublisher', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(payload), error);
                                    return done(null);
                                } else {
                                    utility._sendMetricsToDD(1, [
                                        "REQUEST_TYPE:ARCHIVAL_CRON",
                                        "STATUS:SUCCESS",
                                        "TYPE:CHECK_ACTIVE_USERS_PUBLISHER",
                                        "SOURCE:PUBLISH_SUCCESS",
                                        `OPERATOR:${_.get(record, 'operator', 'NO_OPERATOR')}`,
                                        `SERVICE:${_.get(record, 'service', 'NO_SERVICE')}`
                                    ]);
                                    self.L.log('archiveRecord :: checkActiveUserKafkaPublisher', 'Message published successfully in Kafka', ' on topic CHECK_ACTIVE_USERS ', JSON.stringify(payload));
                                    return done(null);
                                }
                            });
                        } else {
                            L.log(`archiveRecord`, `Service ${record.service} and Operator ${record.operator} is not allowed for smart fetch`);
                            return done(null);
                        }
                    }
                ], (err) => {
                    if (err) {
                        L.error('archiveRecord', `Error during DB or Kafka operation for ${sourceTable} -`, err);
                    }
                    next();
                });
            },
            /*next => {
                let payload = self.preparePayload(record);
    
                // Parallel execution of Kafka publishing and DB insertion
                ASYNC.parallel([
                    // Dump into the cust_id_rn_mapping table
                    // (done) => {
                    //     self.billsModel.dumpIntoCustIdRnMapping((err) => {
                    //         if (err) {
                    //             utility._sendMetricsToDD(1, [
                    //                 "REQUEST_TYPE:ARCHIVAL_CRON",
                    //                 "STATUS:ERROR",
                    //                 "TYPE:DB_INSERTION",
                    //                 "TABLE:CUST_ID_RN_MAPPING"
                    //             ]);
                    //             return done(null);
                    //         } else {
                    //             utility._sendMetricsToDD(1, [
                    //                 "REQUEST_TYPE:ARCHIVAL_CRON",
                    //                 "STATUS:SUCCESS",
                    //                 "TYPE:DB_INSERTION",
                    //                 "TABLE:CUST_ID_RN_MAPPING"
                    //             ]);
                    //             return done(null);
                    //         }
                    //     },dumpTable,record);
                    // },
                    // Publish the payload to Kafka for checking if user is active or not
                    (done) => {
                        if (_.get(record, 'reason', null) == "EXPIRED_USER" && self.allowedOperatorsForSmartFetch && self.allowedOperatorsForSmartFetch.includes(record.operator)) {
                            self.checkActiveUserKafkaPublisher.publishData([{
                                topic: _.get(self.config.KAFKA, 'SERVICES.CHECK_ACTIVE_USERS.TOPIC', ''),
                                messages: JSON.stringify(payload)
                            }], (error) => {
                                if (error) {
                                    utility._sendMetricsToDD(1, [
                                        "REQUEST_TYPE:ARCHIVAL_CRON",
                                        "STATUS:ERROR",
                                        "TYPE:CHECK_ACTIVE_USERS_PUBLISHER",
                                        "SOURCE:PUBLISH_FAILURE"
                                    ]);
                                    self.L.critical('archiveRecord :: checkActiveUserKafkaPublisher', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(payload), error);
                                    return done(null);
                                } else {
                                    utility._sendMetricsToDD(1, [
                                        "REQUEST_TYPE:ARCHIVAL_CRON",
                                        "STATUS:SUCCESS",
                                        "TYPE:CHECK_ACTIVE_USERS_PUBLISHER",
                                        "SOURCE:PUBLISH_SUCCESS"
                                    ]);
                                    self.L.log('archiveRecord :: checkActiveUserKafkaPublisher', 'Message published successfully in Kafka', ' on topic CHECK_ACTIVE_USERS ', JSON.stringify(payload));
                                    return done(null);
                                }
                            });
                        } else {
                            L.log(`archiveRecord`, `Operator ${record.operator} is not allowed for smart fetch`);
                            return done(null);
                        }
                    }
                ], (err) => {
                    if (err) {
                        L.error('archiveRecord', `Error during DB or Kafka operation for ${sourceTable} -`, err);
                    }
                    next();
                });
            },*/
            async next => {
                if (self.allowedServicessForUserInitiatedDelete.includes(record.service) && _.get(record, 'reason', '') === "userInitDeletion") {
                    let nonPaytmKafkapayload = self.preparePayloadForUserInitDeletion(record);
                    L.log(`archiveRecord`, `Going to push the record for userInitDeletion to NON_PAYTM_RECORDS_DWH for operator ${record.operator} and recharge_number ${record.recharge_number} and catalogProductID ${record.product_id}, \n\n nonPaytmKafkapayload - ${JSON.stringify(nonPaytmKafkapayload)}`);
                    return self.pushRecordToNONPAYTM(next, nonPaytmKafkapayload);
                } else {
                    L.log(`archiveRecord`, `userInitDeletion, skipping record push to NON_PAYTM_RECORDS_DWH for operator ${record.operator} and recharge_number ${_.get(record, 'service') === 'financial services' ? this.encryptionDecryptionHelper.encryptData(record.recharge_number) : record.recharge_number} and catalogProductID ${record.product_id}, allowedServicessForUserInitiatedDelete - ${JSON.stringify(self.allowedServicessForUserInitiatedDelete)}`);
                    return next();
                }
            },
            next => {
                L.log(`archiveRecord`, `Going to insert for operator ${record.operator} and recharge_number ${_.get(record, 'service') === 'financial services' ? this.encryptionDecryptionHelper.encryptData(record.recharge_number) : record.recharge_number} and catalogProductID ${record.product_id}`);
                /**
                 * Modify data before inserting into bills_archive
                 */

                try {
                    record.extra = JSON.parse(record.extra);
                    _.set(record, ['extra', 'referenceId'], _.get(record, 'reference_id', null));
                    record.extra = JSON.stringify(record.extra);
                } catch (error) {
                    L.error('archiveRecord', `Error for operator ${record.operator} and recharge_number ${_.get(record, 'service') === 'financial services' ? this.encryptionDecryptionHelper.encryptData(record.recharge_number) : record.recharge_number}`, error);
                }

                if (sourceTable == 'bills_airtelprepaid') {
                    return self.billsModel.insertRecordInArchiveAirtelPrepaid(next, destTable, record);
                } else {
                    return self.billsModel.insertRecordInArchive(next, destTable, record);
                }
            },
            next => {
                L.log(`archiveRecord`, `Going to Remove Entry from ${sourceTable} for operator ${record.operator} and recharge_number ${_.get(record, 'service') === 'financial services' ? this.encryptionDecryptionHelper.encryptData(record.recharge_number) : record.recharge_number} and catalogProductID ${record.product_id}`);
                return self.billsModel.removeRecord(next, sourceTable, record);
            }
        ],
            (err) => {
                if (err) {
                    L.error(`archiveRecord`, `Error for ${sourceTable} -`, err);
                }
                cb(null);
            }
        );
    }

    preparePayload(record) {
        let self = this, extraInfo = {};
        L.verbose(`preparePayload`, `Preparing payload for operator ${record.operator} and recharge_number ${record.recharge_number} and catalogProductID ${record.product_id}`);
        extraInfo.eventState = "bill_gen";
        extraInfo.billSource = "archivalCronsExpiredUser";
        extraInfo.updated_data_source = "archivalCronsExpiredUser";
        extraInfo.created_source = "archivalCronsExpiredUser";
        return {
            customerId: record.customer_id,
            rechargeNumber: record.recharge_number,
            operator: record.operator,
            paytype: record.paytype,
            service: record.service,
            productId: record.product_id,
            status: _.get(self.config, 'COMMON.bills_status.PENDING', 1),
            customerMobile: record.customer_mobile,
            customerEmail: record.customer_email,
            categoryId: _.get(self.config, ['CVR_DATA', record.product_id, 'category_id'], null),
            notificationStatus: _.get(self.config, ['COMMON', 'notification_status', 'ENABLED'], 1),
            customerOtherInfo: record.customer_other_info,
            source: "archivalCronsExpiredUser",
            extra: JSON.stringify(extraInfo),
            dbEvent: "upsert",
            partialSmsFound: true
        }
    }

    preparePayloadForUserInitDeletion(record) {
        let self = this, extraInfo = {};
        L.log(`preparePayload`, `Preparing payload for operator ${record.operator} and recharge_number ${record.recharge_number} and catalogProductID ${record.product_id}`);
        extraInfo.eventState = "bill_gen";
        extraInfo.billSource = "archivalCronsUserInitDeletion";
        extraInfo.updated_data_source = "archivalCronsUserInitDeletion";
        extraInfo.created_source = "archivalCronsUserInitDeletion";
        return {
            customerId: record.customer_id,
            rechargeNumber: record.recharge_number,
            operator: record.operator,
            paytype: record.paytype,
            service: record.service,
            productId: record.product_id,
            status: record.status,
            customerMobile: record.customer_mobile,
            customerEmail: record.customer_email,
            categoryId: _.get(self.config, ['CVR_DATA', record.product_id, 'category_id'], null),
            notificationStatus: record.notification_status,
            customerOtherInfo: record.customer_other_info,
            source: "archivalCronsUserInitDeletion",
            extra: JSON.stringify(extraInfo),
            dbEvent: "upsert",
            amount:record.amount,
            dueDate:record.due_date
        }
    }

    showAnalytics() {
        L.log('showAnalytics', `Final Archival Report`, ANALYTICS);
    }
}
export default ArchivalRecords;

/** 
 * 1. find table list
 * 2. query on table to get status = 13 records
 * 3. move it to archive
*/

// function start(done, options) {
//     let tableList;

//     if (options && options.table) {
//         tableList = [options.table];
//     } else {
//         tableList = _.uniq(_.values(_.get(CONFIG, 'OPERATOR_TABLE_REGISTRY', {})));
//     }
//     tableList.push("bills_airtelprepaid");
//     for(let i=0;i<=9;i++){
//         tableList.push("bills_airtelprepaid"+i)
//     }
//     resetFromIdOffset();
//     async.eachLimit(tableList, 1, fetchAndArchive, function () {
//         showAnalytics();
//         done();
//     });

// }

// function fetchAndArchive(tableName, done) {

//     L.log("fetchAndArchive", `Running archival for table : ${tableName} and id > ${getFromIdOffset()}`);

//     async.waterfall([
//         next => {
//             return billsModel.fetchNotInUseRecords(next, tableName, BATCH_SIZE, getFromIdOffset());
//         },
//         (records, next) => {
//             L.log("fetchAndArchive", `No. of Fetched records for ${tableName}`, _.get(records, 'length', 0));
//             if (_.get(records, 'length', 0) > 0) {
//                 updateAnalytics(tableName,_.get(records, 'length', 0));
//                 return archiveRecords( function(){
//                     fetchAndArchive(tableName,done);
//                 }, records, tableName);
//             }
//             else {
//                 resetFromIdOffset();
//                 return next(null);
//             }
//         }
//     ], function (err) {
//         if (err)
//             L.error('fetchAndArchive', 'Error - ', err);

//         L.log("fetchAndArchive", `Archival completed for table : ${tableName}, archived ${getArchivedRecordCount(tableName)} records`);
//         done();
//     });
// }

// function archiveRecords(done, records, tableName) {

//     async.eachLimit(records, 1, async.ensureAsync(archiveRecord.bind(null, tableName)), function () {
//         return done();
//     });
// }

// function archiveRecord(sourceTable, record, cb) {
//     let self = this;

//     updateFromIdOffset(record.id);

//     async.waterfall([
//         next => {
//             if (!sourceTable || !destTable) {
//                 utility._sendLatencyToDD(1, ['REQUEST_TYPE: ARCHIVAL_CRONS', `STATUS: ERROR`, 'TYPE: INVALID_TABLE_NAME']);
//                 return next(`Invalid archive request for operator ${record.operator} and recharge_number ${record.recharge_number} and catalogProductID ${record.product_id}`);
//             }
//             else if (_.get(record, 'is_automatic', 0) == 1 || _.get(record, 'is_automatic', 0) == 3) {
//                 utility._sendLatencyToDD(1, ['REQUEST_TYPE: ARCHIVAL_CRONS', `STATUS: ERROR`, 'TYPE: AUTOMATIC_BILL']);
//                 return next(`Subscription exists for ${record.operator} and recharge_number ${record.recharge_number} and catalogProductID ${record.product_id}`);
//             }
//             utility._sendLatencyToDD(1, ['REQUEST_TYPE: ARCHIVAL_CRONS', `STATUS: SUCCESS`, 'TYPE: STARTING_ARCHIVAL']);
//             return next();
//         },
//         next => {
//             let payload = preparePayload(record);
//             L.log(`archiveRecord`, `Going to push the record to NON_PAYTM_RECORDS_DWH for operator ${record.operator} and recharge_number ${record.recharge_number} and catalogProductID ${record.product_id}`);
//             return billsModel.pushRecordToDWH(next, payload);
//         },
//         next => {
//             L.log(`archiveRecord`, `Going to insert for operator ${record.operator} and recharge_number ${record.recharge_number} and catalogProductID ${record.product_id}`);
//             /**
//              * Modify data before inserting into bills_archive
//              */

//             try {
//                 record.extra = JSON.parse(record.extra);
//                 _.set(record, ['extra', 'referenceId'], _.get(record, 'reference_id', null));
//                 record.extra = JSON.stringify(record.extra);
//             } catch (error) {
//                 L.error('archiveRecord', `Error for ${record.operator} and recharge_number ${record.recharge_number}`, error);
//             }

//             if (sourceTable == 'bills_airtelprepaid') {
//                 return billsModel.insertRecordInArchiveAirtelPrepaid(next, destTable, record);
//             } else {
//                 return billsModel.insertRecordInArchive(next, destTable, record);
//             }
//         },
//         next => {
//             L.log(`archiveRecord`, `Going to Remove Entry from ${sourceTable} for operator ${record.operator} and recharge_number ${record.recharge_number} and catalogProductID ${record.product_id}`);
//             return billsModel.removeRecord(next, sourceTable, record);
//         }
//     ],
//         (err) => {
//             if (err) {
//                 L.error(`archiveRecord`, `Error for ${sourceTable} -`, err);
//             }
//             cb(null);
//         }
//     );
// }

// function preparePayload(record) {
//     let self = this, extraInfo = {};
//     console.log("🚀 ~ preparePayload ~ record:", record)
//     L.verbose(`preparePayload`, `Preparing payload for operator ${record.operator} and recharge_number ${record.recharge_number} and catalogProductID ${record.product_id}`);
//     extraInfo.eventState = "bill_gen";
//     extraInfo.billSource = "archivalCrons";
//     extraInfo.updated_data_source = "archivalCrons";
//     extraInfo.created_source = "archivalCrons";
//     return {
//         customerId: record.customer_id,
//         rechargeNumber: record.recharge_number,
//         operator: record.operator,
//         paytype: record.paytype,
//         service: record.service,
//         productId: record.product_id,
//         status: _.get(self.config, 'COMMON.bills_status.PENDING', 1),
//         customerMobile: record.customer_mobile,
//         customerEmail: record.customer_email,
//         categoryId: _.get(CONFIG, ['CVR_DATA', processedRecord.productId, 'category_id'], null),
//         notificationStatus: _.get(CONFIG, ['COMMON', 'notification_status', 'ENABLED'], 1),
//         customerOtherInfo: record.customer_other_info,
//         source: "archivalCrons",
//         extra: JSON.stringify(extraInfo),
//         dbEvent: "upsert",
//         partialSmsFound: true
//     }
// }

// function updateFromIdOffset(newId){
//     maxId = Math.max(maxId,newId)
// }

// function getArchivedRecordCount(tableName){
//     return _.get(ANALYTICS,[tableName],0);
// }

// function updateAnalytics(tableName, recordsCount){
//     let recordProcessed = _.get(ANALYTICS,[tableName],0);
//     _.set(ANALYTICS,[tableName], recordProcessed + recordsCount);
// }

// function showAnalytics(){
//     L.log('showAnalytics',`Final Archival Report`, ANALYTICS);
// }

// (function () {
//     if (require.main === module) {
//         var commander = require('commander');
//         commander
//             .version('0.0.1')
//             .option('-t, --table <value>', 'product_id', String)
//             .parse(process.argv);

//         console.time('Execution Time');

//         startup.init({
//             archiveRecordsCron: true
//         }, function (err, options) {
//             try {
//                 if (err) {
//                     L.critical("Service has crashed!! Please check on priority", err)
//                     process.exit(1);
//                 }
//                 CONFIG = options.config;
//                 billsModel = new BILLS({
//                     dbInstance: options.dbInstance,
//                     config: CONFIG,
//                     L: L
//                 });
//                 start(function (err) {
//                     setTimeout(function () {
//                         if (err) {
//                             console.log(err);
//                             console.log('FAILURE');
//                             console.timeEnd('Execution Time');
//                             process.exit(1);
//                         }
//                         console.log('SUCCESS');
//                         console.timeEnd('Execution Time');
//                         process.exit(0);
//                     }, 1000);
//                 }, commander);
//             } catch (error) {
//                 L.critical("Service has crashed!! Please check on priority", error)
//                 process.exit(1);
//             }
//         });
//     }
// })();

// NODE_ENV=production node dist/crons/archive_records.js --table "bills_airtel"