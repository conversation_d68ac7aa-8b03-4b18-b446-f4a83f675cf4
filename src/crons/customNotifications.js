import startup from '../lib/startup';
import MOMENT from 'moment';
import _ from "lodash";
import L from 'lgr';
import fs from 'fs';
import { parseStream } from 'fast-csv';
import { eachLimit } from 'async';
import ASYNC from 'async'
import AWSS3StreamUtils from '../lib/awsS3StreamUtils';
import REQUEST from 'request'
import VA<PERSON><PERSON><PERSON><PERSON> from 'validator'
import utility from '../lib';
import NOTIFIER from '../services/notify'
import billsLib from '../lib/bills'
import CUSTOMNOTIFIER from '../services/customNotificationCreate'
import NotificationLibrary from '../lib/notification'

let serviceName = "CustomNotifications";
let progressFilePath = `/var/log/digital-reminder/progress-customNotifications-${MOMENT().format('YYYY-MM')}.json`;
let progressTracker = {};
var env = (process.env.NODE_ENV || 'development').toLowerCase();

class CustomNotifications {
    constructor(options) {
        this.L = options.L;
        this.dbInstance = options.dbInstance;
        this.activePidLib = options.activePidLib;
        this.rechargeConfig = options.rechargeConfig;
        this.config = options.config;
        this.bucketName = "digital-reminder";
        this.mode = options.mode;
        this.billsLib = new billsLib(options);
        this.cvrData = {};
        this.infraUtils = _.get(options, 'INFRAUTILS', null);
        progressTracker = this.getProgressObject();
        this.notificationLibrary = new NotificationLibrary(options);
        this.csvIngester = new AWSS3StreamUtils(options, this.updateProgress, this);
        this.logPrefix = serviceName;
        this.batchSize = 10;
        this.chunkSizeToPublishKafka = 200;
        this.customnotify = new CUSTOMNOTIFIER(options);
        this.notify = new NOTIFIER(options);
        this.operatorMapping = {};
        this.isInitialized = false;
        this.isRunning = false;
        this.fileName = "";


    }

    getProgressObject() {
        let progress = {};
        progressFilePath = `/var/log/digital-reminder/progress-customNotifications-${MOMENT().format('YYYY-MM')}.json`;
        this.L.info("Loading progress object from", progressFilePath);
        if (fs.existsSync(progressFilePath)) {
            const progressData = fs.readFileSync(progressFilePath, 'utf-8');
            if (progressData.trim() !== '') { // Check if data is not empty
                try {
                    progress = JSON.parse(progressData);
                    this.L.info("Loaded", progress);
                } catch (error) {
                    this.L.error("Error parsing progress data:", error);
                }
            } else {
                this.L.warn("Progress data is empty");
            }
        } else {
            this.L.warn("Progress file does not exist");
            try {
                fs.writeFileSync(progressFilePath, JSON.stringify(progress, null, 2), 'utf-8');
                this.L.info("Created new progress file:", progressFilePath);
            } catch (error) {
                this.L.error("Error creating progress file:", error);
            }
    
        }
        return progress;
    }


    async updateProgress(filename, count, customNotification,totalRecords) {
        let self = this;
        if (_.get(progressTracker, [filename], 0) == -1) return;
        _.set(progressTracker, [filename], count);
        if(count == -1){
            await customNotification.updateTotalRecords(filename,totalRecords);
        }
        
        this.L.info("Updated progress Object", JSON.stringify(progressTracker), count);
        try {
            if (customNotification)
                await customNotification.updateFileStatus(filename, 'done');

        }
        catch (error) {
            L.log('updateProgress', 'error in updating file status ', error);


        }

        if (fs.existsSync(progressFilePath)) {
            fs.writeFileSync(progressFilePath, JSON.stringify(progressTracker, null, 2)); //why 2
        }


    }

    async getFileStatus(file_name) {
        return new Promise((resolve, reject) => {
            let query = `SELECT file_status FROM custom_notifications WHERE file_name=?`;
            let queryParams = [file_name];
            let self = this;
            var latencyStart = new Date().getTime();
            self.dbInstance.exec(function (error, data) {
                utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'getFileStatus' });
                if (error || !(data) || !Array.isArray(data)) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getFileStatus`]);
                    L.critical('getFileStatus', 'error in fetching data from the db:  ', error);
                    reject(error);
                } else {
                    if (data.length > 1) {
                        L.critical('getFileStatus', 'more than one record fetched from the db: ', data.length);
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:MORE_THAN_ONE_RECORD', `TYPE:getFileStatus`]);
                    }
                    resolve(data[0]);
                }
            }, 'DIGITAL_REMINDER_SLAVE', query, queryParams);
        });
    }



    async getReadyToProcessRecords() {

        return new Promise((resolve, reject) => {
            let query = `SELECT * FROM custom_notifications
            WHERE (file_status = 'uploaded' AND is_locked_for_test_record = 0)
               OR (Date(start_date) <= CURDATE() AND file_status IN ('ready_to_process', 'processing') AND is_locked = 0)
               OR  (file_status IN ('processing'))
               ORDER BY updated_at`;
            let queryParams = [];
            let self = this;
            var latencyStart = new Date().getTime();
            self.dbInstance.exec(function (error, data) {
                utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'getReadyToProcessRecords' });
                if (error || !(data) || !Array.isArray(data)) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getReadyToProcessRecords`]);
                    L.critical('getReadyToProcessRecords', 'error in fetching data from the db: ', error);
                    reject(error);
                } else {
                    if (data.length > 1) {
                        L.critical('getReadyToProcessRecords', 'more than one record fetched from the db: ', data.length);
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:MORE_THAN_ONE_RECORD', `TYPE:getReadyToProcessRecords`]);
                    }
                    resolve(data);
                }
            }, 'DIGITAL_REMINDER_SLAVE', query, queryParams);
        });
    }

    async getInvalidRecords() {


        return new Promise((resolve, reject) => {
            console.log("file name is ---", this.fileName);
            if (!this.fileName || this.fileName == "") {
                console.log("resolving this");
                return resolve();
            }
            let query = `SELECT * FROM custom_notifications where file_status='invalid' and file_name=?`;
            let queryParams = [this.fileName];
            let self = this;
            var latencyStart = new Date().getTime();
            console.log(self.dbInstance.format(query, queryParams));
            self.dbInstance.exec(function (error, data) {
                utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'getInvalidRecords' });
                if (error || !(data) || !Array.isArray(data)) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:getInvalidRecords`]);
                    L.critical('getInvalidRecords', 'error in fetching data from the db: ', error);
                    reject(error);
                } else {
                    if (data.length > 1) {
                        L.critical('getInvalidRecords', 'more than one record fetched from the db: ', data.length);
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:MORE_THAN_ONE_RECORD', `TYPE:getInvalidRecords`]);
                    }
                    resolve(data);
                }
            }, 'DIGITAL_REMINDER_SLAVE', query, queryParams);
        });
    }

    /* start(done) {
         let self = this;
         progressTracker;
         self.cvrData = _.get(self.config, 'CVR_DATA', {});
         ASYNC.waterfall([
             next => {
                 self.L.log('_start', 'Going to initialize Kakfa Publisher');
                 return self.configureKafkaPublisher(next);
             },
             next => {
                 self.L.log('_start', 'Going to get Progress Object');
                 progressTracker = self.getProgressObject();
                 next();
             },
 
             next => {
                 try {
                     this.csvIngester.configure(this.bucketName, this.logPrefix, this.batchSize);
                     return next();
                 } catch (error) {
                     self.L.critical(this.logPrefix, "Cannot initialize AWS");
                     return next(error);
                 }
 
             },
             async next => {
                 self.L.log('_start', 'Going to start processing of Notifications');
                 return self.processNotifications(next);
             }
         ], function (error) {
             if (error) {
                 self.L.error('_start', 'Error', error);
             }
             return done(error);
         });
     }*/


    initialize(done) {
        let self = this;
        ASYNC.waterfall([
            next => {
                self.L.log('initialize', 'Going to initialize Kafka Publisher');
                return self.configureKafkaPublisher(next);
            },
            next => {
                self.L.log('initialize', 'Going to get Progress Object');
                progressTracker = self.getProgressObject();
                next();
            },
            next => {
                try {
                    this.csvIngester.configure(this.bucketName, this.logPrefix, this.batchSize);
                    return next();
                } catch (error) {
                    self.L.critical(this.logPrefix, "Cannot initialize AWS");
                    return next(error);
                }
            }
        ], function (error) {
            if (error) {
                self.L.error('initialize', 'Error', error);
                return done(error);
            }
            self.isInitialized = true;
            return done();
        });
    }
    startProcessing(done) {
        let self = this;
        self.cvrData = _.get(self.config, 'CVR_DATA', {});

        self.L.log('startProcessing', 'Going to start processing of Notifications');
        self.processNotifications(done);
    }

    isCheckInvalid(callback) {
        let self = this;
        self.getInvalidRecords()
            .then((records) => {
                if (records && records.length > 0) {
                    let record = records[0];
                    //self.L.info("Eligible record from DB is", record);
                    if (record.file_status === 'invalid') {
                        return callback(true);
                    } else {
                        return callback(false);
                    }
                } else {
                    return callback(false);
                }
            }).catch(error => {
                self.L.error("Error fetching records:", error);
                callback(false);
            })

    }





    processNotifications(done) {
        let self = this;
        self.getReadyToProcessRecords()
            .then((records) => {
                if (records && records.length > 0) {
                    let record = records[0];
                    //self.L.info("eligible record from db  is ", record);
                    if (!record.is_approved && record.file_status === 'uploaded' && record.is_locked_for_test_record == 0) {
                        self.updateLockingFlagForTestRecord(record.file_name)
                            .catch(async (error) => {
                                L.critical("File canot be locked hence not processsing it", error);
                                try {
                                    await self.updateFileStatus(record.file_name, 'invalid', error);

                                }
                                catch (error) {
                                    await self.updateFileStatus(record.file_name, 'invalid', 'error in updating file status');
                                    L.critical("startProcessFirstRecord:: error in updating file");

                                }
                                //mark file invalid here
                                done(error);
                            }).then(() => {
                                this.fileName = record.file_name;
                                return self.processFirstRecord(record)

                            }).catch(async (error) => {
                                self.L.info('Error processing first record:', error);
                                try {
                                    await self.updateFileStatus(record.file_name, 'invalid', error);

                                }
                                catch (error) {
                                    await self.updateFileStatus(record.file_name, 'invalid', error);
                                    L.critical("startProcessFirstRecord:: error in updating file");

                                }
                                done(error);
                            }).then(() => {
                                self.L.info('First record processed successfully');
                                done();
                            })




                    } else if (record.is_approved && record.file_status === 'processing') {
                        self.updateLockingFlag(record.file_name).catch(async (error) => {
                            L.critical("File canot be locked hence not processsing it", error);

                            try {
                                await self.updateFileStatus(record.file_name, 'invalid', error);

                            }
                            catch (error) {
                                await self.updateFileStatus(record.file_name, 'invalid', error);
                                L.critical("startProcessFirstRecord:: error in updating file");

                            }
                            done(error);

                        }).then(() => {
                            return self.processFile(record.file_name, record);

                        }).catch(async (error) => {
                            self.L.error('Error processing file:', error);

                            try {
                                await self.updateFileStatus(record.file_name, 'invalid', error);

                            }
                            catch (error) {
                                await self.updateFileStatus(record.file_name, 'invalid', error);
                                L.critical("startProcessFirstRecord:: error in updating file");

                            }
                            done(error);

                        }).then(() => {
                            self.L.info('File processed successfully');
                            done();
                        })



                    } else {
                        done();
                    }
                } else {
                    done();
                }
            })
            .catch((error) => {
                self.L.error('Error in processing:', error);
                done(error);
            });
    }



    //give option for status of test record
    //give option of file status
    //on action drop throw error


    async startProcessFirstRecord(record, fileRecord) {
        let self = this;
        self.L.info("first record to be processed ", record);

        return new Promise(async (resolve, reject) => {

            if (record.is_record_for_test && record.is_record_for_test.toLowerCase() == 'true') {


                self.sendNotification(record, fileRecord.file_name, async function (error) {
                    if (error) {
                        self.L.error('Notification failed:', error);
                        try {
                            await self.updateFileStatus(fileRecord.file_name, 'invalid', error);
                            reject(error);
                        }
                        catch (error1) {
                            L.critical("startProcessFirstRecord:: error in updating file");
                            reject(error);
                        }

                    } else {
                        self.L.info('Notification sent successfully');
                        resolve();
                    }
                });



            } else {
                try {
                    await self.updateFileStatus(fileRecord.file_name, 'invalid', "is record for test variable not present");
                    reject("is record for test variable not present");


                }
                catch (error) {
                    L.critical("startProcessFirstRecord:: error in updating file");
                    reject(error);


                }

            }


        });
    }

    async processFirstRecord(record) {

        return new Promise((resolve, reject) => {
            let self = this;
            self.csvIngester.readFirstRecord(record, this.startProcessFirstRecord.bind(this), (error, data) => {
                if (error) {
                    console.log("error is in processFirstRecord ", error);
                    reject(error);
                } else {
                    resolve();
                }
            });
        });
    }

    //End of CSV file
    //First record processed successfully

    processFile(filename, record) {
        return new Promise((resolve, reject) => {
            if (_.get(progressTracker, filename, null) == -1) {
                this.L.info("Skipping file ", filename, "as it has been already processed");
                resolve();
            } else {
                this.L.info("Processing file :- ", filename);
                this.currentFile = filename;
                let skipRows = _.get(progressTracker, [filename], 0);
                this.csvIngester.start(this.processRecordinBatch.bind(this), filename, record, (error, data) => {
                    if (error) {
                        reject(error);
                    } else {
                        resolve();
                    }
                }, skipRows);
            }
        });
    }


    chunkArray(array, chunkSize) {
        let chunks = [];
        for (let i = 0; i < array.length; i += chunkSize) {
            chunks.push(array.slice(i, i + chunkSize));
        }
        return chunks;
    }

    validateAndPrepareData(record, fileRecord) {
        let self=this;
        try{
            let [validationFailed, error_msg] = self.validateRecord(record);
            if (validationFailed) {
                return self.notify.insertRejectedNotifications(done, error_msg, { ...record, dataFrom: 'customNotificationsCron' });
            }
            let
                payload = self.prepareDataToPublish(record, fileRecord);
                return JSON.stringify(payload);
        }catch(e){
            L.error("Error in validateAndPrepareData",e);
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:CUSTOM_NOTIFICATIONS_VALIDATE_PREPARE_DATA', 'STATUS:ERROR']);
            return null;
        }
    }

    async processRecordinBatch(data, fileRecord) {
        let self = this;
        return new Promise(async (resolve, reject) => {

            try {
                const fileStatus = await self.getFileStatus(fileRecord.file_name);
                if (fileStatus.file_status == 'invalid') {
                    self.L.error(self.logPrefix, `File ${fileRecord.file_name} is not ready to process, current status: ${fileStatus}`);
                    return reject(new Error(`File ${fileRecord.file_name} is not ready to process, current status: ${fileStatus}`));
                }

                let formattedData = data.map((record) => {
                    return self.validateAndPrepareData(record, fileRecord);
                })

                let chunks = self.chunkArray(formattedData, self.chunkSizeToPublishKafka);

                eachLimit(chunks, self.batchSize, function (chunk, cb) {
                    self.publishData(chunk, function () {
                        cb();
                    });
                }, function (error) {
                    if (error) {
                        self.L.error(self.logPrefix, "Error while processing batch", error);
                        return reject(error);
                    }
                    return resolve();
                });
            }
            catch (error) {
                self.L.error(self.logPrefix, "Error while checking file status", error);
                return reject(error);
            }
        });
    }

    updateFileStatus(fileName, status, errorMsg = null) {
        return new Promise((resolve, reject) => {

            console.log("error in query is ", errorMsg);

            console.log("fileName in query is ", fileName);



            let self = this;
            let error = null;
            if (errorMsg !== null) {
                error = self.billsLib.createErrorMessage(errorMsg).replace(/'/g, "\\'");
                if (error.length > 255) {
                    error = error.substring(0, 255);
                }

            }
            const query = `UPDATE custom_notifications SET file_status = ?,error='${error}' WHERE file_name = ?`;
            const params = [status, fileName];
            var latencyStart = new Date().getTime();
            console.log(self.dbInstance.format(query, params));
            self.dbInstance.exec((error, res) => {
                if (error) {
                    console.log("error in db query", error);
                    self.L.critical('updateRecordStatus::', query, error);
                    return reject(error);
                }
                else {
                    console.log("sucessfully updated query");
                    return resolve(res);
                }

            }, 'DIGITAL_REMINDER_MASTER', query, params);
        });

    }

    updateTotalRecords(fileName, count) {
        return new Promise((resolve, reject) => {
            console.log("fileName in query is ", fileName);
            let self = this;
            
            const query = `UPDATE custom_notifications SET total_no_of_records=total_no_of_records+? WHERE file_name = ? `;
            const params = [count,fileName];
            console.log(self.dbInstance.format(query, params));
            self.dbInstance.exec((error, res) => {
                if (error) {
                    console.log("error in db query", error);
                    self.L.critical('updateTotalRecords::', query, error);
                    return resolve();
                }
                else {
                    console.log("sucessfully updated total records");
                    return resolve(res);
                }

            }, 'DIGITAL_REMINDER_MASTER', query, params);
        });

    }

    updateLockingFlag(fileName) {
        return new Promise((resolve, reject) => {

            let self = this;
            const query = `UPDATE custom_notifications SET is_locked = ? WHERE file_name = ?`;
            const params = [1, fileName];
            var latencyStart = new Date().getTime();
            self.dbInstance.exec((error, res) => {
                if (error) {
                    self.L.critical('updateRecordStatus::', query, error);
                    return reject(error);
                }
                else {
                    return resolve(res);
                }

            }, 'DIGITAL_REMINDER_MASTER', query, params);
        });

    }

    updateLockingFlagForTestRecord(fileName) {
        return new Promise((resolve, reject) => {

            let self = this;
            const query = `UPDATE custom_notifications SET is_locked_for_test_record = ? WHERE file_name = ?`;
            const params = [1, fileName];
            var latencyStart = new Date().getTime();
            self.dbInstance.exec((error, res) => {
                if (error) {
                    self.L.critical('updateRecordStatus::', query, error);
                    return reject(error);
                }
                else {
                    return resolve(res);
                }
                return resolve(res);
            }, 'DIGITAL_REMINDER_MASTER', query, params);
        });

    }




    //check date
    //update error in table
    //insertrejection wherever need



    configureKafkaPublisher(done) {
        ASYNC.parallel([
            callback => {
                this.kafkaCustomNotificationPublisher = new this.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.CUSTOM_NOTIFICATIONS_PIPELINE.HOSTS
                });
                this.kafkaCustomNotificationPublisher.initProducer('high', function (error) {
                    if (!error)
                        L.log("notify :: configureKafkaPublisher", "publisher Configured");
                    return callback(error);
                });
            },

            callback => {
                this.notify.configureKafkaPublisher((err) => {
                    return callback(err);
                })
            }

        ], (error, result) => {
            return done(error);
        })
    }




    prepareDataToPublish(row, fileRecord) {
        let self = this;
        let payload = {};


        try {
            let productInfo = self.cvrData[_.get(row, 'product_id', null)];
            let attributes = JSON.parse(_.get(productInfo, 'attributes'));
            let cardNetwork = _.get(attributes, ['card_network'], '');
            let bankName = _.get(attributes, ['operator_label'], '');
            payload = {
                recharge_number: _.get(row, 'recharge_number'),
                product_id: _.get(row, 'product_id'),
                category_id: _.get(row, 'category_id'),
                template_id: _.get(row, 'template_id'),
                customer_id: _.get(row, 'customer_id'),
                paytype: _.get(row, 'paytype'),
                template_name: _.get(row, 'template_name'),
                icon_image: _.get(row, 'icon_image'),
                expiry: _.get(row, 'expiry'),
                notif_type: _.get(row, 'notif_type'),
                promocode: _.get(row, 'promocode'),
                operator: _.get(row, 'operator'),
                service: _.get(row, 'service'),
                due_amount: _.get(row, 'due_amount'),
                due_date: _.get(row, 'due_date'),
                bill_date: _.get(row, 'bill_date'),
                bill_fetch_date: _.get(row, 'bill_fetch_date'),
                next_bill_fetch_date: _.get(row, 'next_bill_fetch_date'),
                gateway: _.get(row, 'gateway'),
                circle: _.get(row, 'circle'),
                customer_mobile: _.get(row, 'customer_mobile'),
                customer_email: _.get(row, 'customer_email'),
                payment_date: _.get(row, 'payment_date'),
                deviceType: _.get(row, 'device_type'),
                data_consumed: _.get(row, 'data_consumed'),
                source: 'customNotifications',
                is_record_for_test: _.get(row, 'is_record_for_test'),
                campaign_id: _.get(row, 'campaign_id'),
                bank_name: bankName,
                card_network: cardNetwork,
                data_source: _.get(row, 'data_source'),
                is_automatic: _.get(row, 'is_automatic'),
                time_intervals: _.get(row, 'time_intervals', 24),
                customNotifications_onBoardTime: MOMENT().format("YYYY-MM-DD HH:mm:ss"),
                start_date: fileRecord.start_date,
                end_date: fileRecord.end_date,
                campaign_name: _.get(fileRecord, 'campaign_name', null)

            };
            for (let key in row) {
                if (row.hasOwnProperty(key) && key.startsWith('param-')) {
                    payload[key] = row[key];
                }
            }
        }
        catch (error) {
            L.log("prepareDataToPublish::Error in creating kafka payload")
        }


        return payload;


    }
    validateRecord(record) {
        let self = this;
        //self.L.info("validating record {}", record);
        if (!record.recharge_number || !record.product_id || !record.template_id || !record.customer_id || !record.template_name || !record.notif_type) {
            return [true, "Missing required fields"];
        }
        if (record.notif_type == 'BILLDUE' || record.notif_type == 'DUEDATE') {
            if (!record.due_date) {
                return [true, "Missing Due Date"];

            }

        }
        if (record.notif_type == 'BILLGEN') {
            if (!record.bill_date) {
                return [true, "Missing Bill Date"];

            }

        }

        return [false, null];
    }

    handleErrorForChunk(chunk, errorMsg, done) {
        let self = this;
        eachLimit(chunk, self.batchSize, function (record, cb) {
            self.notify.insertRejectedNotifications(cb, errorMsg, { ...record, dataFrom: 'customNotificationsCron' });
        }, function (error) {
            if (error) {
                self.L.error('Error while inserting rejected notifications', error);
                return done(error);
            }
            return done();
        });
    }


    publishData(chunk, done) {

        let
            self = this;
        //self.L.info("prepareDataToPublish payload is {}", payload);
        
        self.kafkaCustomNotificationPublisher.publishData([{
            topic: _.get(self.config.KAFKA, 'SERVICES.CUSTOM_NOTIFICATIONS_PIPELINE.CUSTOM_NOTIFICATIONS', null),
            messages: chunk
        }], function (error) {
            if (error) {
                self.L.critical('publishData', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(chunk), error);
                let errorMsg = self.billsLib.createErrorMessage(error).replace(/'/g, "\\'");
                return self.handleErrorForChunk(chunk, errorMsg, done);
                // return self.notify.insertRejectedNotifications(done, errorMsg, { ...record, dataFrom: 'customNotificationsCron' });
            } else {
                self.L.log('publishData', 'Message published successfully in Kafka', ' on topic CUSTOM_NOTIFICATIONS', JSON.stringify(chunk));
                utility._sendMetricsToDD(1, [
                    'STATUS:PUBLISHED',
                    'TOPIC:' + _.get(self.config.KAFKA, 'SERVICES.CUSTOM_NOTIFICATIONS_PIPELINE.CUSTOM_NOTIFICATIONS', null)
                ])
                return done();
            }
        }, [200, 800]);
    }

    async sendNotification(record, fileRecord, callback) {
        try {
            let self = this,
               productIdForTesting= self.activePidLib.getActivePID(_.get(record, 'product_id', null)),
               productInfo = self.cvrData[productIdForTesting];

                

            if (!productInfo || (productInfo && !_.get(productInfo, 'status', null))) {
                self.L.log('validateDataToProcessForNotification :: PID is marked inactive for : ', `${_.get(record, 'product_id')} ProductStatus:${_.get(productInfo, 'status', null)}`);
                return callback('PID Is marked inactive', record);
            }

            if (!record.recharge_number || !record.product_id || !record.template_id || !record.customer_id || !record.template_name || !record.notif_type) {
                return callback('Missing fields', record);
            }
            if (record.notif_type == 'BILLDUE' || record.notif_type == 'DUEDATE') {
                if (!record.due_date) {
                    return callback('Missing due date', record);

                }

            }
            if (record.notif_type == 'BILLGEN') {
                if (!record.bill_date) {
                    return callback('Missing bill date', record);

                }

            }



            let dueDate = MOMENT(_.get(record, 'due_date', null), 'YYYY-MM-DD').isValid() ? MOMENT(_.get(record, 'due_date', null), 'YYYY-MM-DD').format('DD MMM YYYY') : null;
            let billDate = MOMENT(_.get(record, 'bill_date', null), 'YYYY-MM-DD').isValid() ? MOMENT(_.get(record, 'bill_date', null), 'YYYY-MM-DD').format('DD MMM YYYY') : null;
            let billFetchDate = MOMENT(_.get(record, 'bill_fetch_date', null), 'YYYY-MM-DD').isValid() ? MOMENT(_.get(record, 'bill_fetch_date', null), 'YYYY-MM-DD').format('DD MMM YYYY') : null;
            let nextBillFetchDate = MOMENT(_.get(record, 'next_bill_fetch_date', null), 'YYYY-MM-DD').isValid() ? MOMENT(_.get(record, 'next_bill_fetch_date', null), 'YYYY-MM-DD').format('DD MMM YYYY') : null;
            let attributes = JSON.parse(_.get(productInfo, 'attributes'));
            let cardNetwork = _.get(attributes, ['card_network'], '');
            let bankName = _.get(attributes, ['operator_label'], '');

            console.log("cardNetwork is  ", cardNetwork);
            console.log("bankName is  ", bankName);
            console.log("attributes is  ", attributes);

            let payLoad = {
                amount: _.get(record, 'due_amount', null),
                dataConsumed: _.get(record, 'data_consumed', null),
                recharge_number: _.get(record, 'recharge_number', null),
                operator: _.get(productInfo, 'operator'),
                operator_label: _.get(productInfo, 'operator_label'),
                brand: _.get(productInfo, 'brand'),
                thumbnail: _.get(productInfo, 'thumbnail'),
                product_id: _.get(record, 'product_id', null),
                category_id: _.get(productInfo, 'category_id'),
                service: _.get(productInfo, 'service'),
                customer_id: _.get(record, 'customer_id', null),
                notificationType: _.get(record, 'notif_type', null),
                bank_name: bankName,
                card_network: cardNetwork,
                paytype: _.get(productInfo, 'paytype'),
                time_interval: _.get(record, 'time_intervals', null),//what time_interval does
                start_date: _.get(record, 'start_date', null),
                end_date: _.get(record, 'end_date', null),
                due_date: dueDate,
                template_name: _.get(record, 'template_name', null),
                promocode: _.get(record, 'promocode'),
                gateway: _.get(productInfo, 'gateway'),
                circle: _.get(productInfo, 'circle'),
                customer_mobile: _.get(record, 'customer_mobile'),
                customer_email: _.get(record, 'customer_email'),
                payment_date: _.get(record, 'payment_date'),
                campaign_id: _.get(record, 'campaign_id'),
                is_automatic: _.get(record, 'is_automatic'),
                bill_date: billDate,
                bill_fetch_date: billFetchDate,
                next_bill_fetch_date: nextBillFetchDate


            }

            for (let key in record) {
                if (record.hasOwnProperty(key) && key.startsWith('param-')) {
                    payLoad[key] = record[key];
                }
            }

            let emojiData = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'EMOJI_JAVA_ESCAPE_CODES'], null);//should i put custom notification
            _.extend(payLoad, emojiData);
            let short_operator_name = null;


            if (_.get(payLoad, 'service').toLowerCase() == 'financial services') {
                _.set(payLoad, 'total_due', _.get(record, 'amount', null));
                _.set(payLoad, 'last_four_digits', self.getLast4digitsOfCC(_.get(record, 'recharge_number', null)));
                _.set(payLoad, 'minimum_due', _.get(record, 'amount', null));
            } else if (_.get(this.config, ['COMMON', 'EMI_DUE_CONSUMER_CONFIG', env, _.get(record, 'operator', null), 'notificationCreateSendMinDueAmount'], null)) {
                _.set(payLoad, 'total_due', _.get(record, 'amount', null));
                _.set(payLoad, 'minimum_due', _.get(record, 'amount', null));
            }
            try {
                short_operator_name = _.get(JSON.parse(productInfo.attributes), 'short_operator_name', null);
            }
            catch (error) {
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:CUSTOM_NOTIFICATIONS_PID_PARSE', 'STATUS:ERROR']);
                self.L.error('Error while parsing attributes for PID', _.get(record, 'product_id', null), error);
            }

            short_operator_name = short_operator_name || _.get(productInfo, 'brand');
            _.set(payLoad, 'short_operator_name', short_operator_name);

            let notificationRecord = {
                type: 'PUSH',
                recipients: _.get(record, 'customer_id', null),
                template_id: _.get(record, 'template_id', null),


            };

            if (!_.get(notificationRecord, 'template_id', null)) {
                return callback('Template_id is null');
            }

            else if (_.get(notificationRecord, 'recipients', null) != null) {
                /*let
                    notificationData = {
                        "template_type": _.get(notificationRecord, 'type', 'PUSH').toLowerCase(),
                        "template_id": _.get(notificationRecord, 'template_id', null),
                        "options": {
                            "notificationOpts": {
                                "recipients": VALIDATOR.toString(_.get(notificationRecord, 'recipients', null))
                            },
                            "type": "async",
                            "data": payLoad
                        }
                    };
                let url_type = "external";
                let landing_path = _.get(this.config, ['NOTIFICATION', 'category_url_type_map', _.get(payLoad, 'category_id', null)]);
                let deeplink_url = _.get(this.config, ['NOTIFICATION', 'notificationapi', 'DIGITAL_DEEPLINK_URL'], null);
                let deeplink_api = _.get(this.config, ['NOTIFICATION', 'notificationapi', 'DEEPLINK_API'], null) + "/" + _.get(payLoad, 'category_id', null);
    
                let url = `paytmmp://${landing_path}?url=${deeplink_url}${deeplink_api}`;
    
                let deepLinkData = {
                    "channel_id": "both",
                    "deepLinkObj": {
                        "extra": {
                            "url": url + "?product_id=" + _.get(record, 'product_id', null) + "$recharge_number=" + _.get(record, 'recharge_number', null),
                            "url_type": url_type
                        }
                    },
                    "noRich": false
                };*/

                console.log("payload is ",payLoad);

                let notificationData = self.getApiBody(notificationRecord, payLoad);

                //_.extend(notificationData.options.notificationOpts, deepLinkData);

                // We are waiting till morning for notification adding minimum hours to current time
                let
                    sendAt = MOMENT().format("YYYY-MM-DD HH:mm:ss");

                let
                    apiOpts = {
                        "uri": _.get(this.config, 'NOTIFICATION.notificationapi.DIGITALNOTIFICATIONAPI', null),
                        "method": "POST",
                        "timeout": 1000,
                        "json": {
                            "source_id": 14,
                            "category_id": 14,
                            "recharge_number": _.get(record, 'recharge_number', null),
                            "product_id": _.get(record, 'product_id', null),
                            "max_retry_count": 2,
                            "retry_interval": 30,
                            "type": _.get(notificationRecord, 'type', null),
                            "template_id": _.get(notificationRecord, 'template_id', null),
                            "recipient": _.get(notificationRecord, 'recipients', null),
                            "send_at": sendAt,
                            "data": notificationData,
                            "time_interval": _.get(record, 'time_intervals', 1),
                            "rules": {
                                "condition": `category_id=14 and source_id=14 and recharge_number='${_.get(record, 'recharge_number', null)}' and product_id=${_.get(record, 'product_id', null)} 
                                   and type='${_.get(notificationRecord, 'type', null)}' and template_id=${_.get(notificationRecord, 'template_id', null)} and recipient='${_.get(record, 'customer_id', null)}'`,
                                "actions": [
                                    {
                                        "status": "pending",
                                        "action": "drop"
                                    },
                                    {
                                        "status": "sent",
                                        "action": "drop"
                                    },
                                    {
                                        "status": "error",
                                        "action": "drop"
                                    }
                                ]
                            }
                        }
                    };

                var latencyStart = new Date().getTime();
                // on drop mark invalid

                REQUEST(apiOpts, async (error, response, body) => {
                    self.L.info("apiOpts are ", JSON.stringify(apiOpts));

                    utility._sendLatencyToDD(latencyStart, {
                        'REQUEST_TYPE': 'NOTIFY_API',
                        'URL': _.get(this.config, 'NOTIFICATION.notificationapi.DIGITALNOTIFICATIONAPI', null)
                    });

                    if (body && typeof body === 'string') {
                        try {
                            body = JSON.parse(body);
                        } catch (e) {
                            this.L.error("rechargeNudgeValidationConsumer :: sendNotification", "Error parsing data received", e);
                        }
                    }
                    if (error || (body && body.status && body.status != 200)) {
                        let errorMsg = (error) ? error : ((body.error) ? body.error : "body status: " + body.status);
                        this.L.error("CustomNotifications :: sendNotification", 'Error in sending ' + _.get(notificationRecord, 'type', null) + ' notification to the recipient: ' + _.get(notificationRecord, 'recipients', null) + ' ' + errorMsg);
                        callback(errorMsg);
                    } else {
                        console.log("body is ", body);
                        let action = body.data.split(': ')[1];
                        console.log("action is ", action);
                        console.log("action is ", action, "type is ", typeof action);
                        console.log("action length: ", action.length); // To check for trailing spaces or unexpected characters
                        console.log("Comparison result: ", action === 'drop');

                        if (action !== 'create') {
                            console.log("Notification is goin to drop");
                            await self.updateFileStatus(fileRecord, 'invalid', 'Notification is going to drop !!!');
                            callback("Notification dropped");
                        }

                        try {
                            await self.updateFileStatus(fileRecord, 'ready_to_process');

                        }
                        catch (error) {
                            await self.updateFileStatus(fileRecord, 'invalid', 'error in updating file status');
                            L.critical("startProcessFirstRecord:: error in updating file");

                        }
                        this.L.log("CustomNotifications :: sendNotification", _.get(notificationRecord, 'type', null) + ' notification sent to the recipient: ' + _.get(notificationRecord, 'recipients', null) + ' ' + body.data);
                        callback();
                    }
                });
            }
            else {
                // recipients not found, sending error
                callback("Recipients not found while sending notification");
            }
        }
        catch (errorInBlock) {
            this.L.error("CustomNotifications :: sendNotification", errorInBlock);

            callback("errorInBlock");
        }
    }

    getApiBody(notificationRecord, data) {

        
        
        if (_.get(notificationRecord, 'recipients', null) && _.get(notificationRecord, 'template_id', null)) {
            let self = this,
                paramsForUrl,
                payLoad = _.clone(data);
                self.L.info("data are ", JSON.stringify(data));

            if (_.get(notificationRecord, 'type', null) == 'PUSH') {
                let deepLinkData = {};
                let url_type = "external";
                let landing_path = _.get(self.config, ['NOTIFICATION', 'category_url_type_map', _.get(payLoad, 'category_id', null)], null);
                let deeplink_url = _.get(self.config, ['NOTIFICATION', 'notificationapi', 'DIGITAL_DEEPLINK_URL'], null);
                let deeplink_api = _.get(self.config, ['NOTIFICATION', 'notificationapi', 'DEEPLINK_API'], null) + "/" + _.get(payLoad, 'category_id', null);
                let product_service = _.get(payLoad, 'service', null) != null ? _.get(payLoad, 'service', null).toLowerCase() : null;
                let paytype = _.get(payLoad, 'paytype', null) != null ? _.get(payLoad, 'paytype', null).toLowerCase() : null;



                if (!landing_path) {
                    if (product_service === 'mobile') {
                        landing_path = "mobile_postpaid";
                    } else if (product_service === 'datacard') {
                        landing_path = "datacard_postpaid";
                    } else if (product_service === 'dth') {
                        landing_path = "dth";
                    } else {
                        landing_path = 'utility';
                    }
                }

                let url = `paytmmp://${landing_path}?url=${deeplink_url}${deeplink_api}`;
                if (_.toLower(product_service) == 'paytm postpaid') {
                    let deeplinkForPaytmPostpaid = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'SMART_URL_BY_SERVICE_PAYTYPE', `${product_service}::${paytype}`], []);
                    if (deeplinkForPaytmPostpaid && deeplinkForPaytmPostpaid.length) {
                        url = deeplinkForPaytmPostpaid[0];
                    }
                }
                paramsForUrl = self.customnotify.getParamsForChatAndPush(payLoad, payLoad);
                let completeUrl = null;
                if (product_service == 'mobile') {
                    completeUrl = url + "?$" + self.customnotify.getQueryParams(paramsForUrl, '$')

                } else {
                    completeUrl = url + "?" + self.customnotify.getQueryParams(paramsForUrl, '$')

                }

                deepLinkData = {
                    "payLoad": payLoad,
                    "extra": {
                        "url": completeUrl,
                        "url_type": url_type
                    }
                }

                self.L.info("payLoad are ", JSON.stringify(payLoad));

                if(_.get(payLoad, 'param-deeplink', null)){
                    deepLinkData.extra.url=_.get(payLoad, 'param-deeplink', null);
                }

                self.L.info("deepLinkData are ", JSON.stringify(deepLinkData));


                let pushNotificationData = self.notificationLibrary.getPushNotiData(deepLinkData, notificationRecord, 14);

                return pushNotificationData;
            }
        }
    }
    _utmParams(mode, delimiter = '&') {
        let utm = _.get(this.config, 'NOTIFICATION.RECHARGE_NUDGE_UTM.' + mode, null);
        if (!utm) {
            this.L.error(`UTM config not found for template: ${mode}`);
            utm = _.get(this.config, ['NOTIFICATION', 'RECHARGE_NUDGE_UTM', 'notfound'], '');
        }
        return utm.replace(/&/g, delimiter);
    }

    getLast4digitsOfCC(rechargeNumber = "") {
        let self = this;
        try {
            let last4Digits = rechargeNumber.replace(/ /g, "");
            last4Digits = last4Digits.substring(last4Digits.length - 4);
            return last4Digits;
        }
        catch (err) {
            self.L.error('billReminder::getLast4digitsOfCC', 'Error while finding last 4 digits of CC', err);
            return null;
        }
    }

    getMinDueAmount(record) {
        let self = this;
        try {
            let customerOtherInfo = JSON.parse(_.get(record, 'customerOtherInfo', null));
            let currentMinBillAmount = _.get(customerOtherInfo, 'currentMinBillAmount', null)
            return currentMinBillAmount;
        }
        catch (err) {
            self.L.error('billReminder::getMinDueAmount', 'Error while parsing customerOtherInfo', err);
            return null;
        }
    }


}
(function main() {
    if (require.main === module) {
        startup.init({

        }, function (err, options) {
            let script;
            try {
                script = new CustomNotifications(options);
                script.start(
                    function (err) {
                        setTimeout(function () {
                            if (err) {
                                console.log("main::Error" + err);
                                process.exit(1);
                            } else {
                                console.log("main::completed");
                                process.exit(0);
                            }
                        }, 1000);
                    })
            } catch (err) {
                options.L.error(err)
                process.exit(1)
            }

        });
    }
})();
export default CustomNotifications
