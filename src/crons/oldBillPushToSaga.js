'use strict';
var
    L = require('lgr'),
    Q = require('q'),
    _ = require('lodash'),
    MOMENT = require('moment');


class oldBillPushToSaga {
    constructor(options) {
        this.batchSize = 200;
        this.startId = 0;
        this.parallelCount = 100; // we can update 100 rows at once by using single query as per DBA.
        this.waitTime = 100;
        this.dbInstance = options.dbInstance;
        this.L = options.L;
    }
}    

oldBillPushToSaga.prototype.start = function () {
    var
        self = this;

    Q(undefined)
        .then(function () {
            L.log('oldBillPushToSaga :: START','Calling performOperation');
            return self.performOperation();
        })
        .then(function () {
            L.log('oldBillPushToSaga :: START', 'EXECUTATION COMPLETED.');
        })
        .catch(function (err) {
            if (err) {
                L.error('oldBillPushToSaga :: START', 'ERROR ENCOUNTERED ', err);
            }
        });
};

oldBillPushToSaga.prototype.performOperation = function () {
    var self = this, deferred = Q.defer();
    Q(undefined)
        .then(function () {
            let defer = Q.defer();
            self.performDumyUpdationForOldData(function (err) {
                if (err) {
                    defer.resolve(err);
                } else {
                    defer.resolve();
                }
            });
            return defer.promise;
        })
        .catch(function (err) {
            if (err) {
                L.error('oldBillPushToSaga :: START', 'ERROR WHILE PUSHING DATA FOR OLD PENDING DUES', err);
            }
            deferred.reject(err);
   
        })    
        .then(function () {
            L.log('oldBillPushToSaga :: START', 'PUSHED COMPLETED.');
            deferred.resolve();
        });
    return deferred.promise;    
};

oldBillPushToSaga.prototype.performDumyUpdationForOldData = function(cb){    
    var self = this, subscribersIds = [];
    Q(undefined)
        .then(function () {
            L.log('oldBillPushToSaga::performDumyUpdationForOldData', 'Getting subscriber ids from table starting from id :', self.startId);
            return self.getBatchData(); 
        })
        .then(function (batchData) {
            L.log('oldBillPushToSaga::performDumyUpdationForOldData', 'Number of data :', _.size(batchData));
            if (_.size(batchData) == 0) {
                return Q.reject();
            }
            return self.processRecordsInbatches(batchData,0); 
        })
        .then(function () {
            L.log('oldBillPushToSaga::performDumyUpdationForOldData', 'Getting subscriber after id', self.startId, "CALLING performDumyUpdationForOldData again");
            self.performDumyUpdationForOldData(cb);
        }).catch(function (err) {
            if (err) {
                L.error('oldBillPushToSaga::performDumyUpdationForOldData', "ERROR :: ", err)
                cb(err);
            } else {
                L.log('oldBillPushToSaga::performDumyUpdationForOldData', 'Completed Syncing data table')
                cb(null);
            }
        })
};
oldBillPushToSaga.prototype.processRecordsInbatches = function(batchData, index){    
    var deferred = Q.defer(), self = this;
    L.log("oldBillPushToSaga :: processRecordsInbatches", "req recieved for", batchData.length, " With index ", index);
    Q(undefined)
        .then(function () {
            var deferred2 = Q.defer();
            var arrAllTxn = [];
            var count = 1;
            var minId = -1;
            var maxId = -1
            for (var i = index; i < batchData.length; i++) {
                count++;
                if(minId== -1 || minId > batchData[i].id){
                    minId = batchData[i].id;
                }
                if(minId== -1 ||maxId < batchData[i].id){
                    maxId = batchData[i].id;
                }
                self.startId = _.get(batchData[i],'id',0);
                if (count > parseInt(self.parallelCount)) {
                    break;
                }
            }
            arrAllTxn.push(self.processRecord(minId,maxId));
            index = index + parseInt(self.parallelCount);
            Q.all(arrAllTxn)
                .then(function () {
                    deferred2.resolve();
                })
                .catch(function (error) {
                    L.error("oldBillPushToSaga :: processRecordsInbatches", "ERROR IN PROCESS BATCH", error);
                    deferred2.resolve();
                });
            return deferred2.promise;
        }).then(function () {
            var newDeferred = Q.defer();
            L.log("oldBillPushToSaga :: processRecordsInbatches waiting for ", self.waitTime, "MS before processing next subscription");
            setTimeout(function () {
                L.log("oldBillPushToSaga :: processRecordsInbatches ", self.waitTime, "MS wait completed");
                return newDeferred.resolve();
            }, self.waitTime);
            return newDeferred.promise;
        }).then(function () {
            if (index > batchData.length) {
                return deferred.resolve();
            }
            return self.processRecordsInbatches(batchData, index)
        }).then(function () {
            return deferred.resolve();
        })
    return deferred.promise;
};

oldBillPushToSaga.prototype.processRecord = function(minId, maxId){        
    var self = this;
    var deferred = Q.defer();
    var query,queryParams;
    Q(undefined)
        .then(() => {
            query = 'UPDATE bills_paytmpostpaid set updated_at = ? where id  between ? and ?',
            queryParams = [
                MOMENT().format('YYYY-MM-DD HH:mm:ss'),
                minId,
                maxId
        ];
            return new Promise((resolve, reject) => {
                self.dbInstance.exec(function (err, data){
                    if(err || !data) {
                        let errMsg = `bills_paytmpostpaid txns not found: ${err}`;
                        self.L.critical(errMsg);
                        reject(errMsg);
                    }
                    resolve(data);
                }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
            }) 
        })
        .catch(function (err) {
            L.error("oldBillPushToSaga :: processRecord", "Error occured for id in updating data", queryParams, err);
            return Q.resolve();
        })
        .then(function () {
            L.log("oldBillPushToSaga :: processRecord" , "process completed for id: ", queryParams);
            deferred.resolve();
        });
    return deferred.promise;
};

oldBillPushToSaga.prototype.getBatchData = function(){    
    var deferred = Q.defer(), self = this, query, queryParams;  
    L.log("oldBillPushToSaga :: getBatchData: SELECT QUERY");
    query ='select * from bills_paytmpostpaid where updated_at < ? AND id > ? order by id limit ?;',
    queryParams = [
        MOMENT('2024-03-15','YYYY-MM-DD').format('YYYY-MM-DD 00:00:00'),
        self.startId,
        self.batchSize];;
            
    L.log("oldBillPushToSaga :: getBatchData :: Query ::", query + " Query params ::", queryParams)
    return new Promise((resolve, reject) => {
        self.dbInstance.exec(function (err, data){
            if(err || !data) {
                let errMsg = `dropped_transactions txns not found: ${err}`;
                self.L.critical(errMsg);
                reject(errMsg);
            }
            resolve(data)
        }, 'DIGITAL_REMINDER_SLAVE', query, queryParams);
    })
};
module.exports = oldBillPushToSaga;    
