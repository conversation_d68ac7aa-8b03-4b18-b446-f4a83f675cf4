import _, { result } from 'lodash'
import BIL<PERSON>UBSCRIBER from '../services/billSubscriber'
import PUBLISHER from '../services/publisher'
import PLAN_VALIDITY_MODEL from '../models/planValidity'
import BILLS from '../models/bills'
import NonPaytmBills from '../models/nonPaytmBills'
import cassandraBills from '../models/cassandraBills'
import USERS from '../models/users'
import NOTIFIER from '../services/notify'
import NOTIFICATION from '../models/notification'
import transactionCounterModel from '../models/allTransactionsCounter'
import ASYNC from 'async'
import Q from 'q'
import utility from '../lib'
import remindableUser from '../lib/remindableUser'
import orderSummary from '../lib/orderSummary'
import reminderFlowManager from '../lib/reminderFlowManager'
import digitalUtility from 'digital-in-util'
import CATALOGVERTICALRECHARGE from '../models/catalogVerticalRecharge';
import UpdateUserConsentLibrary from '../lib/updateUserConsent'
import EncryptorDecryptor from 'encrypt_decrypt';
import RecentsLayerLib from '../lib/recentsLayer'
import DROPPED_TRANSACTIONS from '../models/droppedTransactions';
import moment from 'moment';
import VALIDATOR from 'validator';
import DISHTV_D2HPLANVALIDITY from './dishtv-d2hPlanValidity';
import CustomError from '../lib/customError';
import BillFetchAnalytics from '../lib/billFetchAnalytics.js'
import MOMENT from 'moment';
import PaymentRemindLaterEvents from '../models/paymentRemindLaterEvents';
import AES256Encryption from '../utils/aes256encryption.js';
import HeuristicCustomNotifications from '../crons/heuristicCustomNotifications';
import EncryptionDecryptioinHelper from '../lib/EncryptionDecryptioinHelper'
import Logger from '../lib/logger';
import billsLib from '../lib/bills'
import LoanUtil from '../utils/loanUtil';

let L = null;

/*
   Controller for distrbuting the request to the service files.
 */

class Controller {
    constructor(options) {
            L = options.L;
        this.options = options;
        this.L = options.L;
        this.config = options.config;
        this.infraUtils = options.INFRAUTILS;
        this.dbInstance = options.dbInstance;
        this.activePidLib = options.activePidLib;
        this.options.users = this.users =  new USERS(options);
        this.billSubscriber = new BILLSUBSCRIBER(options);
        this.publisher = new PUBLISHER(options);
        this.notify = new NOTIFIER(options);
        this.planValidityModel = new PLAN_VALIDITY_MODEL(options);
        this.cassandraBills = new cassandraBills(options)
        this.options.billsModel = this.bills = new BILLS(options);
        this.commonLib = new utility.commonLib(options);
        this.object = new utility.TinyUrl();
        this.remindableUser = new remindableUser(options);
        this.orderSummaryLib = new orderSummary(options);
        this.options.recentsLayer = this.recentsLayer = new utility.RecentsLayer(options);
        this.reminderFlowManager = new reminderFlowManager(this.options);
        this.notification = new NOTIFICATION(this.options);
        this.reminderUtils = new digitalUtility.ReminderUtils();
        this.catalogVerticalRecharge = new CATALOGVERTICALRECHARGE(options);
        this.activePidLib = options.activePidLib;
        this.updateUserConsentLibrary = new UpdateUserConsentLibrary(this.options);
        this.transactionCounterModel = new transactionCounterModel(this.options);
        this.cryptr = new EncryptorDecryptor();
        this.nonPaytmBillsModel = new NonPaytmBills(this.options)
        this.recentsLayerLib = new RecentsLayerLib(options);
        this.billFetchAnalytics = new BillFetchAnalytics(options)
        this.paymentRemindLaterEvents = new PaymentRemindLaterEvents(this.options);
        this.encryptionDecryptioinHelper = new EncryptionDecryptioinHelper(options);
        this.logger = new Logger(options);
        this.billsLib = new billsLib(options);
        this.loanUtil = new LoanUtil(options);

        this.includedOperatorList = _.get(this.config, ['DYNAMIC_CONFIG', 'OPERATOR_MONTHLY_PAYMENT', 'COMMON', 'INCLUDE_OPERATOR'], 'rent payment');
        this.includedOperator = this.includedOperatorList.split(',').map((e) => e.trim());
        this.droppedTransactions = new DROPPED_TRANSACTIONS(options);
        this.aes256Encryption = new AES256Encryption(options);

        this.planValidityPublisher = new this.infraUtils.kafka.producer({
            "kafkaHost": this.config.KAFKA.TOPICS.RECHARGE.HOSTS
        });
    
        this.planValidityPublisher.initProducer('high', function (err) {
    
            if (err) {
                L.error('controller', 'index', 'Getting error in planValidityPublisher publisher configuration: ', err);
            } else {
                L.log('controller', 'index', 'planValidityPublisher kafka publisher configured');
            }
        });
        this.dishtv_d2hPlanValidity = new DISHTV_D2HPLANVALIDITY(options, this);
        this.heuristicCustomNotifications=new HeuristicCustomNotifications(options);
        this.allowedServiceToSaveOldDueDate = _.get(this.config, ['DYNAMIC_CONFIG', 'OLD_BILL_FETCH_CONFIG', 'COMMON', 'ALLOWED_SERVICES'], ['electricity']);
        this.setVarFromDynamicConfig();
    }

    setVarFromDynamicConfig() {
        let self = this;
        this.allowedServiceToSaveOldDueDate = _.get(this.config, ['DYNAMIC_CONFIG', 'OLD_BILL_FETCH_CONFIG', 'COMMON', 'ALLOWED_SERVICES'], ['electricity']);

        setTimeout(() => {
            self.L.log(`setVarFromDynamicConfig`, `Updating service params from dynamic config..`)
            self.setVarFromDynamicConfig()
        }, 15 * 60 * 1000);
    }

    initializeServer(done) {
        let self = this;
        let sourceObj = {
            'SOURCE' : 'BILL_SUBSCRIBER'
        };
        ASYNC.waterfall([
            (next)=> {
                self.billSubscriber._configureKafkaPublisher(function(err) {
                    if(err){
                        self.L.error('initializeServer', 'Error while configuring kafka publisher', err);
                    }
                    next(err);
                }, sourceObj);
            },
            (next)=> {
                self.notify.configureKafkaPublisher(function(err) {
                    if(err){
                        self.L.error('initializeServer', 'Error while configuring kafka publisher', err);
                    }
                    next(err);
                });
            }
        ],function(err){
            if(err){
                self.L.error('initializeServer', 'Error while initializing server', err);
            }
            done(err);
        }) 
    }

    createUrl(req, res) {
        let self = this,
            qParams = req.query,
            headers = req.headers;

        if (_.get(req, 'headers.p-category-key', null) == null || _.get(req, 'headers.p-product-key', null) == null || _.get(req, 'body.PayForID', null) == null || _.get(req, 'body.Amount', null) == null) {
            res.json({ "message": "Invalid Params", "code": "101", "status": "ERROR" });
        } else {
            try {
                let unsubscribe_url = "paytmmp://utility?url=https://catalog.paytm.com/v2/mobile/getproductlist/" + _.get(req, 'headers.p-category-key', null) + "?product_id=" + _.get(req, 'headers.p-product-key', null) + "&recharge_number=" + _.get(req, 'body.PayForID', null) + "&price=" + _.get(req, 'body.Amount', 0);
                self.object.createShortUrl(function (x, y) { console.log(x, y); res.json({ "message": y, "code": "999", "status": "OK" }); }, _.get(self.config, ['TINYURL_CONFIG'], null), unsubscribe_url);
            } catch (ex) {
                res.json({ "message": "generic error", "code": "101", "status": "ERROR" });
            }
        }
    }

    d2hPlanValidity(req, res) {
        let self = this;
        return self.dishtv_d2hPlanValidity.transaction(req, res);
    }

    /*
    * Function will get data on the basis of customerId,
    * productId and rechargeNumber
    */
    getBill(req, res) {
        let self = this,
            qParams = req.query;
        let service = _.toLower(_.get(this.config,['CVR_DATA',qParams.productId,'service']));
        //test this
        qParams.isCreditCardOperator = self.commonLib.isCreditCardOperator(service);
        L.verbose('getBill', 'Parameters', qParams.operator, qParams.customerId, qParams.productId, qParams.isCreditCardOperator ? self.encryptionDecryptioinHelper.encryptData(qParams.rechargeNumber) : qParams.rechargeNumber)
        if (_.isEmpty(qParams.operator) || _.isEmpty(qParams.customerId) || _.isEmpty(qParams.productId) || _.isEmpty(qParams.rechargeNumber)) {
            L.verbose('getBill', 'Invalid Parameters', qParams.operator, qParams.customerId, qParams.productId, qParams.isCreditCardOperator ? self.encryptionDecryptioinHelper.encryptData(qParams.rechargeNumber) : qParams.rechargeNumber)
            return res.status(422).json({ error_message: 'Operator, customerId,productId and rechargeNumber is mandatory field' });
        } else {
            qParams.operator = _.toLower(qParams.operator);
            if(qParams.parId && qParams.parId!= '' && qParams.referenceId == null)
                qParams.referenceId = qParams.parId;
            if(qParams.referenceId != null && qParams.isCreditCardOperator){
                self.billSubscriber.getCCBill(function (error, data) {
                    if (error) {
                        L.verbose('getBill', 'Error response ', qParams.operator, qParams.customerId, qParams.productId, qParams.isCreditCardOperator ? self.encryptionDecryptioinHelper.encryptData(qParams.rechargeNumber) : qParams.rechargeNumber)
                        return res.status(200).json({ "status": 204, "error": error });
                    } else {
                        L.verbose('getBill', 'Data Sent ', qParams.operator, qParams.customerId, qParams.productId, qParams.isCreditCardOperator ? self.encryptionDecryptioinHelper.encryptData(qParams.rechargeNumber) : qParams.rechargeNumber)
                        return res.status(200).json({ "status": 200, "data": data });
                    }
                }, qParams.operator, qParams.customerId, qParams.productId, qParams.referenceId)
            } else {
                self.billSubscriber.getBill(function (error, data) {
                    if (error) {
                        L.verbose('getBill', 'Error response ', qParams.operator, qParams.customerId, qParams.productId, qParams.rechargeNumber)
                        return res.status(200).json({ "status": 204, "error": error });
                    } else {
                        L.verbose('getBill', 'Data Sended ', qParams.operator, qParams.customerId, qParams.productId, qParams.rechargeNumber)
                        return res.status(200).json({ "status": 200, "data": data });
                    }
                }, qParams.operator, qParams.customerId, qParams.productId, qParams.rechargeNumber)
            }
        }
    }

    
    getRealTimeBill(req, res) {                    
        let self = this,
            qParams = req.query;
        if (_.isEmpty(qParams.operator) || _.isEmpty(qParams.productId) || _.isEmpty(qParams.rechargeNumber)) {
            L.verbose('getRealTimeBill ', 'Invalid Parameters', qParams.operator , qParams.productId, qParams.rechargeNumber)
            return res.status(422).json({ error_message: 'Operator, productId and rechargeNumber is mandatory field' });
        // } else if (_.get(self.config, ['DYNAMIC_CONFIG', 'OPERATOR_CONFIG', qParams.operator,'DISABLED_PERMANENT'], false)){
        //     this.L.log('getRealTimeBill ', 'Operator is disabled or is moved to UPMS', qParams.operator)
        //     return res.status(200).json({ "status": 404, "error": 'OPERATOR_DISABLED' });
        } else {            
            let paytype = _.get(qParams,'paytype',_.get(this.config,['CVR_DATA',qParams.productId,'paytype']),null),
                service = _.get(qParams,'service',_.get(this.config,['CVR_DATA',qParams.productId,'service']),null);
            qParams.paytype = _.toLower(paytype);
            qParams.service = _.toLower(service);
            qParams.operator = _.toLower(qParams.operator);
            qParams.productId = self.activePidLib.getActivePID(qParams.productId);

            this.logger.log('getRealTimeBill :: incoming request', qParams, service);
            /*
            if(qParams.paytype == 'prepaid' || qParams.paytype == 'credit card'|| qParams.operator == 'rent payment' ){
                let errors = `getRealTimeBill not supported for OP:${qParams.operator}_paytype:${qParams.paytype}`;
                return res.status(422).json({ error_message: errors });
            }
            */
            if(qParams.paytype == 'prepaid' ||self.includedOperator.includes(qParams.operator)){
                let errors = `getRealTimeBill not supported for OP:${qParams.operator}_paytype:${qParams.paytype}`;
                return res.status(422).json({ error_message: errors });
            }
            let userData = _.extend({"recharge_number": _.get(qParams, 'rechargeNumber', null)}, _.pick(qParams, [
                'recharge_number_2',
                'recharge_number_3',
                'recharge_number_4',
                'recharge_number_5',
                'recharge_number_6',
                'recharge_number_7',
                'recharge_number_8',
            ]));
            let message = { "userData": userData,
                            "catalogProductID": _.get(qParams, 'productId', null),
                            "productInfo": {"operator":_.get(qParams, 'operator', null),"service":_.get(qParams, 'service', '')},
                            "customerInfo": {"customer_id": _.get(qParams, 'customerId', null)},
                "channel_id": _.get(qParams, 'channelId', null) ? "digital-reminder-realtime-" + _.toLower(_.get(qParams, 'channelId', null)) : "digital-reminder-realtime",
                "source": "getRealTimeBill"
                          },
                currentRecord = {"operator": qParams.operator},
                source = "getRealTimeBill";
            
            self.publisher.hitFfrValidationApi(function (error, data) {
                if (error) {
                    L.verbose('getRealTimeBill ', 'Error response ', qParams.operator, qParams.productId, qParams.rechargeNumber)
                    return res.status(200).json({ "status": 204, "error": error });
                } else {
                    let isConnectionError = _.get(data, 'validationGwResponse.connectionError', false),
                        deducedStatus = _.get(data, 'validationGwResponse.deducedStatus', true),
                        noBill = _.get(data, 'validationGwResponse.noBill', false),
                        custInfoValues = _.get(data, 'customerDataResponse', null),
                        errorMessageCode = _.get(data, 'validationGwResponse.errorMessageCode', null) ?Number(_.get(data,'validationGwResponse.errorMessageCode', null)) : NaN ;
                    if (isConnectionError) {
                        L.error('getRealTimeBill', `Error response isConnectionError :${isConnectionError} `, qParams.operator, qParams.productId, service === 'financial services' ? self.encryptionDecryptioinHelper.encryptData(qParams.rechargeNumber) : qParams.rechargeNumber);
                        return res.status(200).json({ "status": 204, "error": 'CONNECTION_ERROR' });                        
                    }else if (noBill || (!_.isNaN(errorMessageCode) && _.get(self.config, ['SUBSCRIBER_CONFIG', 'NO_BILL_ERROR_MESSAGE_CODES'], []).indexOf(errorMessageCode) >= 0) ){
                        L.error('getRealTimeBill', `Error response noBill :${noBill} or errorMessageCode : ${errorMessageCode}`, qParams.operator, qParams.productId, service === 'financial services' ? self.encryptionDecryptioinHelper.encryptData(qParams.rechargeNumber) : qParams.rechargeNumber);
                        return res.status(200).json({ "status": 204, "error": 'BILL_NOT_FOUND' });                        
                    }else if (_.get(custInfoValues, 'invalid', 0) == 1) {
                        L.error('getRealTimeBill', `Error response invalid bill :${_.get(custInfoValues, 'invalid', 0)} `, qParams.operator, qParams.productId, service === 'financial services' ? self.encryptionDecryptioinHelper.encryptData(qParams.rechargeNumber) : qParams.rechargeNumber);
                        return res.status(200).json({ "status": 204, "error": 'NOT_IN_USE' });
                    }else if (!deducedStatus) { 
                        L.error('getRealTimeBill', `Error response deducedStatus :${deducedStatus} `, qParams.operator, qParams.productId, service === 'financial services' ? self.encryptionDecryptioinHelper.encryptData(qParams.rechargeNumber) : qParams.rechargeNumber);
                        return res.status(200).json({ "status": 204, "error": 'VALIDATION_FAILED' });                        
                    }else {
                        L.verbose('getRealTimeBill', 'Data Sended ', qParams.operator, qParams.productId, service === 'financial services' ? self.encryptionDecryptioinHelper.encryptData(qParams.rechargeNumber) : qParams.rechargeNumber);
                        if( (_.get(qParams, 'channelId', null) === "SUBS 1") && (_.get(qParams, 'customerId', null)) ){
                            let
                                operator = _.get(message, 'productInfo.operator', null),
                                rechargeNumber = _.get(message, 'userData.recharge_number', null),
                                service = _.get(message, 'productInfo.service', ''),
                                customerId = _.get(message, 'customerInfo.customer_id', null),
                                referenceId = qParams.referenceId,
                                tableName = self.billSubscriber.getTableName(_.get(message, 'catalogProductID', null), operator);

                                self.bills.getBillsRecord((error, dbData) => {
                                    if(error || (_.get(dbData, 'length', 0) == 0)) {
                                        _.set(data, "isRecordPresentInReminderDB", false);
                                        L.error("getRealTimeBill", `Bill not found in reminderDB for custId: ${customerId} amd rechargeNumber: ${service === 'financial services' ? self.encryptionDecryptioinHelper.encryptData(qParams.rechargeNumber) : qParams.rechargeNumber}`);
                                    } else {
                                        _.set(data, "isRecordPresentInReminderDB", true);
                                        
                                        if(_.get(dbData, '0.is_encrypted', 0) == 0 && self.encryptionDecryptioinHelper.isWhitelistedForCC(service, paytype, customerId)) {
                                            if(dbData[0].due_date && dbData[0].due_date != '' && dbData[0].due_date != null) {
                                                if(MOMENT(dbData[0].due_date, MOMENT.ISO_8601).isValid()) {
                                                    dbData[0].due_date = MOMENT(dbData[0].due_date, MOMENT.ISO_8601).utc().format('YYYY-MM-DD HH:mm:ss');
                                                } else if(typeof dbData[0].due_date == "object") {
                                                    dbData[0].due_date = self.encryptionDecryptioinHelper.parseToString(dbData[0].due_date);
                                                }
                                            }
                                            self.bills.createAndDeleteCCBill((e, dbResp) => {
                                                if(e) {
                                                    L.error('getRealTimeBill', ` Error in createAndDeleteCCBill : ${e} `, qParams.operator, qParams.productId, self.encryptionDecryptioinHelper.encryptData(qParams.rechargeNumber));
                                                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:GET_REAL_TIME_BILL", 'STATUS:RECORDS_UNPROCESSED', 'ERROR:CC_BILL_CREATION_AND_DELETION_FAILED']);
                                                } else {
                                                    _.set(dbData[0], 'is_encrypted', 1);
                                                    let insertId = _.get(dbResp, 'insertId', null);
                                                    if(insertId == null) {
                                                        L.error('getRealTimeBill', ` Error while createAndDeleteCCBill execution. Null insert ID. `, qParams.operator, qParams.productId, self.encryptionDecryptioinHelper.encryptData(qParams.rechargeNumber));
                                                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:GET_REAL_TIME_BILL", 'STATUS:RECORDS_UNPROCESSED', 'ERROR:NULL_INSERT_ID_CC_BILL_CREATION_AND_DELETION_FAILED']);
                                                    } else {
                                                        _.set(dbData[0], 'id', insertId);
                                                        self.billSubscriber.dumpInSQL(message, (err) => {
                                                            if (err) {
                                                                utility._sendMetricsToDD(1, ["REQUEST_TYPE:GET_REAL_TIME_BILL", 'STATUS:RECORDS_UNPROCESSED']);                                    
                                                            }
                                                            else {
                                                                utility._sendMetricsToDD(1, ['REQUEST_TYPE:GET_REAL_TIME_BILL', 'STATUS:RECORDS_PROCESSED']);
                                                            }
                                                        },dbData);
                                                    }
                                                }
                                            }, tableName, dbData[0], true);
                                        }
                                        else {   
                                            self.billSubscriber.dumpInSQL(message, (err) => {
                                                if (err) {
                                                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:GET_REAL_TIME_BILL", 'STATUS:RECORDS_UNPROCESSED']);                                    
                                                }
                                                else {
                                                    utility._sendMetricsToDD(1, ['REQUEST_TYPE:GET_REAL_TIME_BILL', 'STATUS:RECORDS_PROCESSED']);
                                                }
                                            },dbData);
                                        }
                                    }
                                    return res.status(200).json({ "status": 200, "data": data });
                                }, tableName, operator, service, rechargeNumber, customerId, referenceId, paytype);
                        }
                        else{
                            return res.status(200).json({ "status": 200, "data": data });
                        }  
                    }
                }
            }, currentRecord, message,source);
        }
    }


    /**
    * Function will get data on the basis of customerId, productId , rechargeNumber 
    * reference id in case of credit card operator
    * for postpaid , prepaid , rentpayment , credit card , Airtel prepaid 
     * @param {*} req 
     * @param {*} res 
     */
     getBillV2(req, res) {                    
        let self = this,
            qParams = req.query;
        L.verbose('getBillV2', 'Parameters', qParams.operator, qParams.customerId, qParams.rechargeNumber);
        if (_.isEmpty(qParams.operator) || _.isEmpty(qParams.customerId) || _.isEmpty(qParams.productId) || _.isEmpty(qParams.rechargeNumber)) {
            L.verbose('getBillV2', 'Invalid Parameters', qParams.operator, qParams.customerId, qParams.productId, qParams.rechargeNumber)
            return res.status(422).json({ error_message: 'Operator, customerId,productId and rechargeNumber is mandatory field' });
        } else {
            let service = _.get(qParams,'service',_.get(this.config,['CVR_DATA',qParams.productId,'service']),null),
                paytype = _.get(qParams,'paytype',_.get(this.config,['CVR_DATA',qParams.productId,'paytype']),null);
            qParams.service = _.toLower(service);
            qParams.paytype = _.toLower(paytype);                            

            this.logger.log('getBillV2 :: incoming request for ', qParams, service);
            qParams.traceKey = `RN:${qParams.service === 'financial services' ? self.encryptionDecryptioinHelper.encryptData(qParams.rechargeNumber) : qParams.rechargeNumber}_CID:${qParams.customerId}_OP:${qParams.operator}_paytype:${qParams.paytype}`;
            this.L.log('getBillV2',`serving request for ${qParams.traceKey}`)
            let reminderFlowInstance = this.reminderFlowManager.getFlowInstance(qParams);
            if(!reminderFlowInstance) {
                this.L.critical('getBillV2',`could not identify flow for qParams:${JSON.stringify(qParams)}`);                
                return res.status(500).json({ error_message: 'Unable to identify internal flow' });                
            }
            let validationResponse = reminderFlowInstance.notificationManager_validateReq(qParams);
            if(!validationResponse.status) {
                let errors = validationResponse.errors;
                this.L.error('getBillV2',`Invalid request for ${qParams.traceKey} : ${JSON.stringify(errors)}`)                                
                return res.status(422).json({ error_message: 'CIN is not present for CC operator' });
            }
            let tableName = reminderFlowInstance.getTableName(qParams.operator,qParams.paytype);
            if(!tableName) {
                this.L.error('getBillV2',`table not found for:${JSON.stringify(qParams)}`);                
                return res.status(200).json({ "status": 204, "error": "Operator not migrated" });                                            
            }
            reminderFlowInstance.notificationManager_getBill(function (error, data) {
                if (error) {
                    L.error('getBillV2', 'Error response ', qParams.operator, qParams.customerId, qParams.productId, qParams.service === 'financial services' ? self.encryptionDecryptioinHelper.encryptData(qParams.rechargeNumber) : qParams.rechargeNumber);
                    return res.status(200).json({ "status": 204, "error": error });
                } else {     
                    if (data.length === 0) {
                        L.error('getBillV2', 'Error response ', qParams.operator, qParams.customerId, qParams.productId, qParams.service === 'financial services' ? self.encryptionDecryptioinHelper.encryptData(qParams.rechargeNumber) : qParams.rechargeNumber);
                        return res.status(200).json({ "status": 204, "error": 'No Data found!!!' });                        
                    } else {          
                        L.verbose('getBillV2', 'Data Sended ', qParams.operator, qParams.customerId, qParams.productId, qParams.service === 'financial services' ? self.encryptionDecryptioinHelper.encryptData(qParams.rechargeNumber) : qParams.rechargeNumber)
                        return res.status(200).json({ "status": 200, "data": self.commonLib.mapBillsTableColumns(data) });
                    }                                                       
                }
            }, qParams)
        }
    }


    /**
     * 
     * @param {*} req 
     * @param {*} res 
     * @returns 
     */
    getUserConsentStatus(req, res) {
        let self = this,
            qParams = _.get(req, 'query', {}),
            result = {
                response: null,
                statusInfo : {
                    status : null,
                    statusMessage : null
                }
            };
        try{
            let params = {
                customer_id: +req.header('X-USER-ID'), 
                deviceId: req.header('deviceId'),                  // optional
                appVersion: req.header('appVersion'),              // optional
                clientId: req.header('clientId'),                  // optional
                preferenceKey: _.get(qParams, 'preferenceKey', null)
            };
            L.log('getUserConsentStatus', `customer_id:${params.customer_id}_preferenceKey:${params.preferenceKey}_deviceId:${params.deviceId}_clientId:${params.clientId}_appVersion:${params.appVersion}`);
            if (isNaN(params.customer_id) || !_.isNumber(params.customer_id) || _.isEmpty(params.preferenceKey)) {
                L.log('getUserConsentStatus', `Invalid Parameters::customer_id:${params.customer_id}_preferenceKey:${params.preferenceKey}_params:${JSON.stringify(params)}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:GET_USER_CONSENT_STATUS_V1", 'STATUS:ERROR','TYPE:INVALID_PARAMS']);

                result.statusInfo.status = "FAILURE";
                result.statusInfo.statusMessage = "CustomerId and preferenceKey values are Invalid/missing";    
                return res.status(422).json({ "status": 422, "data": result });
            }
            /**Validation of values & type */
            let supportedPreferenceKeys = ['ocl.user.consent.ru_whatsapp_reminders']
            if (supportedPreferenceKeys.indexOf(params.preferenceKey) == -1) {
                L.log('getUserConsentStatus', `Invalid preferenceKey'${params.preferenceKey}_params:${JSON.stringify(params)}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:GET_USER_CONSENT_STATUS_V1", 'STATUS:ERROR','TYPE:INVALID_PREFERENCE_KEY']);

                result.statusInfo.status = "FAILURE";
                result.statusInfo.statusMessage = "Invalid preferenceKey";    
                return res.status(422).json({ "status": 422, "data": result });
            }
            //JSON.parse({}) error case

            self.transactionCounterModel.fetchCustomerWhatsAppNotificationStatus(function (error, whatsapp_notification_status) {
                if (error) {    // mysql error, invalid customer_id
                    self.L.critical('getUserConsentStatus',`Error occured for params:${JSON.stringify(params)}`,error)
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:GET_USER_CONSENT_STATUS_V1", 'STATUS:ERROR','TYPE:MYSQL_READ_ERROR']);

                    result.statusInfo.status = "FAILURE";
                    result.statusInfo.statusMessage = "Something went wrong";    
                    return res.status(500).json({ "status": 500, "data": result });
                } else { // whatsapp_notification_status => success
                    self.L.log('getUserConsentStatus',`params:${JSON.stringify(params)}_whatsapp_notification_status:${whatsapp_notification_status}`);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:GET_USER_CONSENT_STATUS_V1", 'STATUS:SUCCESS']);

                    result.response = {
                        preferences : [{
                            "key": params.preferenceKey,
                            "value": whatsapp_notification_status
                        }]
                    };
                    result.statusInfo.status = "SUCCESS";
                    result.statusInfo.statusMessage = "Request sucessfully served";
                    return res.status(200).json({ "status": 200, "data": result });
                }
                // params.customer_id => didn't exist in table => case did not exists
                // whatsapp_notification_status ==> customer_id exists but whatsapp status not available => return -1 (pick from config.)
                // whatsapp_notification_status => success

            }, params.customer_id);

            /**
            let requestData = {
                "customer_id" : params.customer_id,
                "preferenceKey" : params.preferenceKey
            };
            self.updateUserConsentLibrary.getUserPreferences(function (error, userPreferenceResponse) {
                if (error) {
                    result.statusInfo.status = "FAILURE";
                    result.statusInfo.statusMessage = "Something went wrong";    
                    return res.status(500).json({ "status": 500, "data": result });
                } else if (!userPreferenceResponse.response || _.get(userPreferenceResponse, ['statusInfo' , 'status'] ) === "FAILURE" || _.get(userPreferenceResponse, ['statusInfo' , 'status'] ) != "SUCCESS") {
                    result.statusInfo.status = "FAILURE";
                    result.statusInfo.statusMessage = `Something went wrong at UPS with Error Msg: ${_.get(userPreferenceResponse, [ 'statusInfo' , 'statusMessage'] , '')} `;    
                    return res.status(500).json({ "status": 500, "data": result });
                } else {
                    result.response = userPreferenceResponse.response;
                    result.statusInfo.status = "SUCCESS";
                    result.statusInfo.statusMessage = "Request sucessfully served";
                    return res.status(200).json({ "status": 200, "data": result });
                }
            }, requestData, null);
             */

        } catch (error) {
            this.L.critical('getUserConsentStatus',`Error occured for qParams:${JSON.stringify(qParams)}`,error)
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:GET_USER_CONSENT_STATUS_V1", 'STATUS:ERROR','TYPE:MAIN_FLOW_EXECUTION']);
            result.statusInfo.status = "FAILURE";
            result.statusInfo.statusMessage = "Error while serving request,Please try again in some time";    
            return res.status(500).json({ "status": 500, "data": result });
        }

    }

    /**
     * 
     * @param {*} req 
     * @param {*} res 
     * @returns 
     */
    updateUserConsentStatus(req, res) {
        let self = this,
            body = _.get(req, 'body', {}),
            result = {
                response: null,
                statusInfo : {
                    status : null,
                    statusMessage : null
                }
            };
        try{
            let params = {
                customer_id: +req.header('X-USER-ID'), 
                deviceId: req.header('deviceId'),                  // optional
                appVersion: req.header('appVersion'),              // optional
                clientId: req.header('clientId'),                  // optional
                preferenceKey: _.get(body, 'preferenceKey', null),
                preferenceValue:  _.get(body, 'preferenceValue', null) //[0,1]
            };

            L.log('updateUserConsentStatus', `customer_id:${params.customer_id}_preferenceKey:${params.preferenceKey}_preferenceValue:${params.preferenceValue}_deviceId:${params.deviceId}_clientId:${params.clientId}_appVersion:${params.appVersion}`);

            if (isNaN(params.customer_id) || !_.isNumber(params.customer_id) || _.isEmpty(params.preferenceKey)) {
                L.log('updateUserConsentStatus', `Invalid Parameters::customer_id:${params.customer_id}_preferenceKey:${params.preferenceKey}_preferenceValue:${params.preferenceValue}_params:${JSON.stringify(params)}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:UPDATE_USER_CONSENT_STATUS_V1", 'STATUS:ERROR','TYPE:INVALID_PARAMS']);

                result.statusInfo.status = "FAILURE";
                result.statusInfo.statusMessage = "customer_id and preferenceKey values are Invalid/missing";    
                return res.status(422).json({ "status": 422, "data": result });
            }
            /**Validation of values & type */
            let supportedStatus = [1, 0];
            if (typeof params.preferenceValue != 'number' || supportedStatus.indexOf(params.preferenceValue) == -1) {
                L.log('updateUserConsentStatus', `Invalid preferenceValue:${params.preferenceValue}_params:${JSON.stringify(params)}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:UPDATE_USER_CONSENT_STATUS_V1", 'STATUS:ERROR','TYPE:INVALID_PREFERENCE_VALUE']);

                result.statusInfo.status = "FAILURE";
                result.statusInfo.statusMessage = "Invalid preferenceValue";    
                return res.status(422).json({ "status": 422, "data": result });
            }

            let supportedPreferenceKeys = ['ocl.user.consent.ru_whatsapp_reminders']
            if (supportedPreferenceKeys.indexOf(params.preferenceKey) == -1) {
                L.log('updateUserConsentStatus', `Invalid preferenceKey'${params.preferenceKey}_params:${JSON.stringify(params)}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:UPDATE_USER_CONSENT_STATUS_V1", 'STATUS:ERROR','TYPE:INVALID_PREFERENCE_KEY']);

                result.statusInfo.status = "FAILURE";
                result.statusInfo.statusMessage = "Invalid preferenceKey";    
                return res.status(422).json({ "status": 422, "data": result });
            }

            ASYNC.waterfall([
                next => {
                    let dbFormatData = self.updateUserConsentLibrary.getDbRecordToUpdate(params);
                    self.transactionCounterModel.writeCustomerDetailsForUpdateUserConsent(function (error, dbResponse) {
                        if (error) {
                            self.L.critical('updateUserConsentStatus',`Error occured for params:${JSON.stringify(params)}`,error);
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:UPDATE_USER_CONSENT_STATUS_V1", 'STATUS:ERROR','TYPE:MYSQL_WRITE_ERROR']);
                            result.statusInfo.status = "FAILURE";
                            result.statusInfo.statusMessage = `Something went wrong at Reminders system with Error Msg: ${error}`;    
                            return next(error);
                        }
                        return next();
                    }, dbFormatData);
                },
                next => {
                    let requestData = self.updateUserConsentLibrary.getRequestDataToUpdate(params);
                    self.updateUserConsentLibrary.updateUserPreferences(function (error, userPreferenceResponse) {
                        if (error) {
                            self.L.error('updateUserConsentStatus',`Error occured for params:${JSON.stringify(params)}`,error);
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:UPDATE_USER_CONSENT_STATUS_V1", 'STATUS:ERROR','TYPE:UPDATE_UPS_API_ERROR']);

                            result.statusInfo.status = "FAILURE";
                            result.statusInfo.statusMessage = "Something went wrong"; 
                            return next(error);
                        } else if (!userPreferenceResponse.response || _.get(userPreferenceResponse, ['statusInfo' , 'status'] ) === "FAILURE" || _.get(userPreferenceResponse, ['statusInfo' , 'status'] ) != "SUCCESS") {
                            self.L.error('updateUserConsentStatus',`Error in API response for params:${JSON.stringify(params)}`,JSON.stringify(userPreferenceResponse));
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:UPDATE_USER_CONSENT_STATUS_V1", 'STATUS:ERROR','TYPE:UPDATE_UPS_API_FAILURE']);

                            result.statusInfo.status = "FAILURE";
                            result.statusInfo.statusMessage = `Something went wrong at UPS with Error Msg: ${_.get(userPreferenceResponse, ['statusInfo' ,'statusMessage'] , '')} `;    
                            return next("Something went wrong at UPS");
                        } else {
                            self.L.log('updateUserConsentStatus',`params:${JSON.stringify(params)}_userPreferenceResponse:${JSON.stringify(userPreferenceResponse)}`);
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:UPDATE_USER_CONSENT_STATUS_V1", 'STATUS:SUCCESS']);
                            result.response = {
                                preference : {
                                    "key": params.preferenceKey,
                                    "value": params.preferenceValue
                                }
                            };
                            result.statusInfo.status = "SUCCESS";
                            result.statusInfo.statusMessage = "Request sucessfully served";
                            return next();
                        }
                    }, requestData, null, 'digitalReminderAppServer');
                    /**
                     * To do
                     * after update in UPS
                     * retry in case of UPS update API failure
                     * return proper error cases that API returns
                     */
                }
            ], function (error) {
                if (error) {
                    self.L.error('updateUserConsentStatus',`Error in response for params:${JSON.stringify(params)}`,error);
                    return res.status(500).json({ "status": 500, "data": result });
                } else {
                    self.L.log('updateUserConsentStatus',`params:${JSON.stringify(params)}_result:${JSON.stringify(result)}`);
                    return res.status(200).json({ "status": 200, "data": result });
                }
                
            });            
        } catch (error) {
            this.L.error('updateUserConsentStatus',`Error occured for data:${JSON.stringify(body)}`,error)
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:UPDATE_USER_CONSENT_STATUS_V1", 'STATUS:ERROR','TYPE:MAIN_FLOW_EXECUTION']);
            result.statusInfo.status = "FAILURE";
            result.statusInfo.statusMessage = "Error while serving request,Please try again in some time";    
            return res.status(500).json({ "status": 500, "data": result });
        }

    }   

    /*
    * Function will get all operator's list
    */
    getOperatorList(req, res) {
        let self = this,
            operatorTableMapping = _.get(this.config, 'OPERATOR_TABLE_REGISTRY', '');
        let result = _.keys(operatorTableMapping);
        return res.status(200).json({ "status": 200, "data": result });
    }

    /* Function to fetch all notifications against cust_id or rech_num
        * Scan all tables
    */
    getNotificationRecords(req, res) {
        let self = this;
        let qParams = req.query;
        if (_.isEmpty(qParams.key) || _.isEmpty(qParams.value) || (qParams.key != 'customer_id' && qParams.key != 'recharge_number')) {
            L.verbose('getNotificationRecords', 'Invalid Parameters', qParams.key, qParams.value);
            return res.status(422).json({ error_message: 'key and value are mandatory field' });
        } else {
            let key = qParams.key;
            let value = qParams.value;
            let operatorTableMapping = _.get(this.config, 'OPERATOR_TABLE_REGISTRY', {});
            let tablesToScan = _.uniq(_.values(operatorTableMapping));
            self.billSubscriber.getAllNotificationRecords(function (error, data) {
                if (error) {
                    L.verbose('getAllNotificationRecords', 'Error response ', qParams.key, qParams.value);
                    L.error('getAllNotificationRecords', 'Error response ', error)
                    return res.status(200).json({ "status": 204, "error_message": "Something went wrong!" });
                } else {
                    L.verbose('getAllNotificationRecords', 'Data Sended ', qParams.key, qParams.value);
                    return res.status(200).json({ "status": 200, "data": data });
                }
            }, key, value, tablesToScan);
        }
    }

    /*
    * Function will get data on the basis of customerId,
    * productId and recharge number objects 
    * maximum of 500 objects can be called using post action
    */
    getMultipleBill(req, res) {
        let self = this,
            limit = _.get(self.config, ['COMMON', 'GET_MULTIPLE_BILL_LIMIT'], 500),
            qParams = req.body;

        L.verbose('getMultipleBill', 'Parameters', qParams.bills);
        if (_.isEmpty(qParams.bills) || !_.isArray(qParams.bills)) {
            L.verbose('getMultipleBill', 'Invalid Parameters', qParams.bills);
            return res.status(422).json({ error_message: 'Operator, customerId,productId and rechargeNumber is mandatory field' });
        } else if (qParams.bills.length > limit) {
            return res.status(422).json({ error_message: 'Query Limit Exceeded, Only 500 records can be fetched at a time' });
        } else {

            // seperating elements for multiple calls
            let
                customerIds = [],
                productIds = [],
                rechargeNumbers = [],
                operators = [];

            qParams.bills.forEach(function (bill) {
                customerIds.push(bill.customerId);
                productIds.push(bill.productId);
                rechargeNumbers.push(String(bill.rechargeNumber)); // for query optimisation : recharge_number field in DB is 'string'
                operators.push(bill.operator.toLowerCase());
                if (_.get(self.config, ['OPERATOR_TABLE_REGISTRY', bill.productId], null)) operators.push(bill.productId.toString());
            });

            customerIds = _.uniq(customerIds);
            productIds = _.uniq(productIds);
            rechargeNumbers = _.uniq(rechargeNumbers);
            operators = _.uniq(operators);
            let
                promiseChain = [];

            operators.forEach(function (operator) {
                promiseChain.push(new Promise(function (resolve, reject) {
                    self.billSubscriber.getMultipleBill(function (error, data) {
                        if (error) {
                            L.verbose('getMultipleBill', 'Error response ', error)
                            return resolve({ "status": 204, "error": error });
                        } else {
                            L.verbose('geMultipletBill', 'Data Sended. Error -', error)
                            return resolve({ "status": 200, "data": data });
                        }
                    }, operator, customerIds, productIds, rechargeNumbers);
                }));
            });

            Promise.all(promiseChain).then(function (data) {
                let bills = {};

                data.forEach(function (response) {
                    if (response.status == 200 && _.isArray(response.data)) {
                        response.data.forEach(function (bill) {
                            let key = [bill.customerId, bill.productId, bill.rechargeNumber].join('~');
                            _.set(bills, key, bill);
                        })
                    }
                })

                return res.status(200).json({ 'data': _.values(bills) });
            });

        }
    }

    /*
     *  Function will create data in Bills Tables on the basis of customerId,
     *  productId and rechargeNumber as Key
    */
    createBill(req, res) {
        let self = this,
            params = req.body;

        L.verbose('createBill', 'Parameters', params.operator, params.customerId, params.productId, params.rechargeNumber)

        if (_.isEmpty(params.operator) || !_.get(params, 'customerId', null) || !_.get(params, 'productId', null) || _.isEmpty(params.rechargeNumber)) {
            L.verbose('createBill', 'Invalid Parameters', params.operator, params.customerId, params.productId, params.rechargeNumber)
            return res.status(422).json({ error_message: 'Operator, customerId,productId and rechargeNumber is mandatory field' });
        } else {
            params.operator = _.toLower(params.operator);
            params.source = "api";
            self.billSubscriber.createBill(function (error, data) {
                if (error) {
                    L.verbose('createBill', 'Error in response', params.operator, params.customerId, params.productId, params.rechargeNumber)
                    return res.status(200).json({ "status": 204, "error": error });
                } else {
                    L.verbose('createBill', 'Succesfully saved', params.operator, params.customerId, params.productId, params.rechargeNumber)
                    return res.status(200).json({ "status": 200, "data": "OK" });
                }
            }, params)
        }
    }

    /*
     *  Function will create data in Bills Tables on the basis of customerId,
     *  productId and rechargeNumber as Key
    */
    createMultipleBill(req, res) {
        let self = this,
            params = req.body;
        L.verbose('createMultipleBill', 'Parameters')
        let service = self.billSubscriber;

        if (!params) {
            L.verbose('createMultipleBill', 'Invalid Parameters', params.operator, params.customerId, params.productId, params.rechargeNumber)
            return res.status(422).json({ error: 'Provide array of object' });
        } else {
            res.status(200).json({ "status": 200, "data": "OK" });
            params.forEach(function (param) {
                param.operator = _.toLower(param.operator);
                if (self.billSubscriber.isCreateBillParamsValid(param)) {
                    service.createBill(function (error, data) {
                        if (error) {
                            L.critical('createMultipleBill', 'Error in response', JSON.stringify(param));
                        }
                    }, param)
                } else {
                    L.error('createMultipleBill', 'Mandatory Params missing.', param.operator, param.customerId, param.productId, param.rechargeNumber);
                }

            });
        }
    }

    /*
    *  Function will create data in Bills Tables on the basis of customerId,
    *  productId and rechargeNumber as Key
    * 
    *  Request: 
    * 
    *  {"data":[<"bills data array">], "callback": "<callback-url>"}
   */
    createMultipleBillv2(req, res) {
        let self = this,
            params = req.body,
            callbackUrl = params.callback;

        if (!params || !params.data) {
            L.verbose('createMultipleBillv2', 'Invalid Parameters');
            res.status(422).json({ error: 'Provide array of object' });
        } else {
            // res.status(200).json({ "status": 200, "data": "OK" });
            var allBills = [];
            let service = self.billSubscriber;

            Q(undefined).then(function () {
                var deferred = Q.defer();
                ASYNC.each(_.get(params, 'data', []),
                    function (param, cb) {
                        var billObj = {};
                        billObj.recharge_number = _.get(param, 'rechargeNumber', '');
                        billObj.operator = _.get(param, 'operator', '');
                        billObj.operator = billObj.operator.toLowerCase();
                        billObj.productId = _.get(param, 'productId', '');
                        param.operator = param.operator.toLowerCase();
                        if (!self.billSubscriber.isCreateBillParamsValid(param)) {

                            billObj.status = _.get(self.config, 'COMMON.CALLBACK_STATUS_CODES.FAILURE', 1);
                            billObj.error_message = 'Mandatory params missing';
                            allBills.push(billObj);
                            cb();

                        } else {
                            service.createBill(function (error, data) {
                                if (error) {
                                    L.error('createMultipleBillv2', 'Error in response', error, JSON.stringify(param));

                                    if (typeof error == 'string') {
                                        billObj.status = _.get(self.config, 'COMMON.CALLBACK_STATUS_CODES.FAILURE', 1);
                                        billObj.error_message = error;
                                    } else {
                                        billObj.status = _.get(self.config, 'COMMON.CALLBACK_STATUS_CODES.UNKNOWN_ERROR', 1);
                                        // billObj.error_message = error;
                                    }
                                } else {
                                    billObj.status = _.get(self.config, 'COMMON.CALLBACK_STATUS_CODES.SUCCESS', 0);;
                                }
                                allBills.push(billObj);
                                cb();
                            }, param);
                        }
                    }, function (err) {
                        deferred.resolve();
                    }
                );

                return deferred.promise;
            }).then(function () {

                if (callbackUrl) {
                    L.log('createMultipleBillv2, Callback URL::::', callbackUrl);

                    service.sendDataToCallBack(allBills, callbackUrl, function () {
                        L.log('Data sent');
                    })
                } else {
                    return res.status(200).json({ "status": 200, "data": allBills });
                }
            });

        }
    }

    /*
    * Function will update data in Bills Tables on the basis of customerId,
    * productId and rechargeNumber as Key
      */
    updateBill(req, res) {

        let self = this,
            params = req.body;

        L.verbose('updateBill', 'Parameters', params.operator, params.customerId, params.productId, params.rechargeNumber)

        if (_.isEmpty(params.operator) || !_.get(params, 'customerId', null) || !_.get(params, 'productId', null) || _.isEmpty(params.rechargeNumber)) {
            L.verbose('updateBill', 'Invalid Parameters', params.operator, params.customerId, params.productId, params.rechargeNumber)

            return res.status(422).json({ error_message: 'Operator, customerId,productId and rechargeNumber is mandatory field' });
        } else {
            params.operator = _.toLower(params.operator);
            self.billSubscriber.updateBill((error, data) => {
                if (error) {
                    L.verbose('updateBill', 'Error in request processing', params.operator, params.customerId, params.productId, params.rechargeNumber)
                    return res.status(200).json({ "status": 204, "error": error });
                } else {
                    L.verbose('updateBill', 'Succesfully updated', params.operator, params.customerId, params.productId, params.rechargeNumber)
                    return res.status(200).json({ "status": 200, "data": "OK" });
                }
            }, params)
        }

    }

    async updateBillV2(req, res) {
        let self = this,
            latencyStart = new Date().getTime(),
            params = req.body;
        L.log(`UpdateBillV2::Parameter::cust:${params.customerId}:rech:${params.service == 'financial services' ? self.encryptionDecryptioinHelper.encryptData(params.rechargeNumber) : params.rechargeNumber}:ser:${params.service}:op:${params.operator}`)
        if (_.isEmpty(_.get(params, 'operator', null)) || !_.get(params, 'customerId', null) || _.isEmpty(params.rechargeNumber) || _.isEmpty(params.service) || _.isEmpty(params.paytype)) {
            let latency = new Date().getTime() - latencyStart;
            L.error('updateBillV2', `Invalid Parameters - Latency:${latency}ms`, params.operator, params.customerId, params.productId, params.rechargeNumber)
            await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(params, null), 'mandatory params missing');
            utility._sendLatencyToDD(latencyStart, {
                'REQUEST_TYPE': 'UPDATE_BILL_V2_API',
                'URL': "updateBillV2",
                'STATUS': 'ERROR',
                'OPERATOR': params.operator,
                'TYPE': 'MANDATORY_PARAMS_MISSING'
            });
            return res.status(422).json({
                status: _.get(self.config, 'COMMON.BILL_PUSH_ERROR_CODE.MANDATORY_PARAM_MISSING.status', '01'),
                message: _.get(self.config, 'COMMON.BILL_PUSH_ERROR_CODE.MANDATORY_PARAM_MISSING.message', 'mandatory params missing')
            });
        }
        params.debugKey = `rech:${params.service == 'financial services' ? self.encryptionDecryptioinHelper.encryptData(params.rechargeNumber) : params.rechargeNumber}::cust:${params.customerId}::op:${params.operator}::ser:${params.service}`;
        // Check for invalid billInfo
        let parsedDueDateObj = utility.getFilteredDate(_.get(params, 'billDueDate', null)),
            isBillDueDateFmtValid = parsedDueDateObj.isDateFmtValid,
            parsedBillDateObj = utility.getFilteredDate(_.get(params, 'billDate', null)),
            billDateFmtValid = parsedBillDateObj.isDateFmtValid,
            amount = utility.getFilteredAmount(_.get(params, 'currentBillAmount', null));
        if (_.isNaN(params.currentBillAmount) || _.isEmpty(params.billDate) || _.isEmpty(params.billDueDate) || !isBillDueDateFmtValid || !billDateFmtValid) {
            let latency = new Date().getTime() - latencyStart;
            L.error('updateBillV2', `Invalid billInfo - Latency:${latency}ms`, params.debugKey)
            utility._sendLatencyToDD(latencyStart, {
                'REQUEST_TYPE': 'UPDATE_BILL_V2_API',
                'URL': "updateBillV2",
                'STATUS': 'ERROR',
                'OPERATOR': params.operator,
                'SERVICE': params.service,
                'TYPE': 'INVALID_BILL_INFO'
            });
            await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(params, null), 'Invalid billInfo');
            return res.status(200).json({
                status: _.get(self.config, 'COMMON.BILL_PUSH_ERROR_CODE.INVALID_BILL_INFO.status', '01'),
                message: _.get(self.config, 'COMMON.BILL_PUSH_ERROR_CODE.INVALID_BILL_INFO.message', 'Invalid billInfo')
            });
        }
        // Check if both billDate and billDueDate are in the past
        let currentDate = MOMENT().startOf('day');
        let billDueDate = parsedDueDateObj.value;

        if (billDueDate.isBefore(currentDate)) {
            let latency = new Date().getTime() - latencyStart;
            if (self.allowedServiceToSaveOldDueDate.includes(_.toLower(params.service))) {
                _.set(params, 'status', _.get(this.config, 'COMMON.bills_status.OLD_BILL_FOUND', 5))
                L.log('updateBillV2', `Old bill found (allowed) - Latency:${latency}ms`, params.debugKey);
                utility._sendLatencyToDD(latencyStart, {
                    'REQUEST_TYPE': 'UPDATE_BILL_V2_API',
                    'URL': "updateBillV2",
                    'STATUS': 'TRAFFIC',
                    'OPERATOR': params.operator,
                    'SERVICE': params.service,
                    'TYPE': 'OLD_BILL_FOUND'
                });
            } else {
                L.error('updateBillV2', `billDate or billDueDate are in the past - Latency:${latency}ms`, params.debugKey);
                utility._sendLatencyToDD(latencyStart, {
                    'REQUEST_TYPE': 'UPDATE_BILL_V2_API',
                    'URL': "updateBillV2",
                    'STATUS': 'ERROR',
                    'OPERATOR': params.operator,
                    'SERVICE': params.service,
                    'TYPE': 'INVALID_BILL_INFO'
                });
                await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(params, null), 'billDate or billDueDate are in the past');
                return res.status(200).json({
                    status: _.get(self.config, 'COMMON.BILL_PUSH_ERROR_CODE.INVALID_BILL_INFO.status', '01'),
                    message: _.get(self.config, 'COMMON.BILL_PUSH_ERROR_CODE.INVALID_BILL_INFO.message', 'Invalid billInfo')
                });
            }
        }
        params.operator = _.toLower(params.operator);
        params.service = _.toLower(params.service);
        self.billSubscriber.updateBillV2((error, data) => {
            let latency = new Date().getTime() - latencyStart;
            if (error) {
                utility._sendLatencyToDD(latencyStart, {
                    'REQUEST_TYPE': 'UPDATE_BILL_V2_API',
                    'URL': "updateBillV2",
                    'STATUS': 'ERROR',
                    'OPERATOR': params.operator,
                    'SERVICE': params.service,
                    'TYPE': 'INTERNAL_ERROR'
                });
                L.error('updateBillV2', `Error in request processing - Latency:${latency}ms`, params.debugKey);
                if (_.get(error, 'errorCode', null) == '5XX') {
                    return res.status(500).json({
                        status: _.get(self.config, 'COMMON.BILL_PUSH_ERROR_CODE.INTERNAL_SYSTEM_ERROR.status', '03'),
                        message: _.get(self.config, 'COMMON.BILL_PUSH_ERROR_CODE.INTERNAL_SYSTEM_ERROR.message', 'Internal system error')
                    });
                } else {
                    return res.status(200).json({ status: '02', message: _.get(error, 'message', error) });
                }
            }
            else {
                utility._sendLatencyToDD(latencyStart, {
                    'REQUEST_TYPE': 'UPDATE_BILL_V2_API',
                    'URL': "updateBillV2",
                    'STATUS': 'SUCCESS',
                    'OPERATOR': params.operator,
                    'SERVICE': params.service,
                });
                L.log('updateBillV2', `Successfully updated - Latency:${latency}ms`, params.debugKey);
                return res.status(200).json({ status: '00', message: 'success' });
            }
        }, params);
    }

    /*
      Function for scheduling notifications

    */

    createNotification(req, res) {
        let self = this,
            body = req.body;

        self.notify.validate((error, data) => {
            if (error) {
                return res.status(200).json({ "status": 204, "error": error });
            } else {
                self.notify.createNotification(function (error, data) {
                    if (error) {
                        L.critical('Controller', 'createNotification', 'Error :', error);
                        return res.status(200).json({ "status": 204, "error": error.message });
                    } else {
                        return res.status(200).json({ "status": 200, "data": data });
                    }
                }, body);
            }
        }, body);
    }


    /*
        Function to update notification status of bills by default it will disable notification
    */
    updateNotificationStatus(req, res) {
        let self = this,
            notificationStatusMap = _.get(this.config, 'COMMON.NOTIFICATION_STATUS_MAP', {}),
            allowedNotificationStatuses = _.invert(_.get(this.config, 'COMMON.notification_status', {})),
            params = req.body;
        _.set(params, 'X-USER-ID', req.header('X-USER-ID'));
        L.verbose('updateNotificationStatus', 'Parameters', params.operator, params.customerId, params.notificationStatus, params.rechargeNumber, params.stopBillFetch);
        if (!_.isEmpty(params.operator) && (!_.isEmpty(params.rechargeNumber) || _.get(params, 'customerId', null)) && _.has(params, 'notificationStatus')) {
            params.operator = _.toLower(params.operator);
            let tableName = _.get(this.config, ['OPERATOR_TABLE_REGISTRY', params.operator], null),
                service = self.billSubscriber;
            _.set(params, 'stopBillFetch', _.get(params, 'stopBillFetch', false));

            if (!tableName) {
                L.error("updateNotificationStatus", "Operator not migrated:: ", params.operator);
                return res.status(200).json({ "status": 204, "message": 'Operator not migrated' });
            }
            if (!_.get(allowedNotificationStatuses, params.notificationStatus, null)) {
                L.error("updateNotificationStatus", "NotificationStatus is not valid:: ", params.notificationStatus);
                return res.status(200).json({ "status": 204, "message": 'NotificationStatus is not valid' });
            }
            service.updateNotificationStatus((error, data) => {
                if (error) {
                    L.error('updateNotificationStatus', 'Error in request processing', params.operator, params.customerId, params.rechargeNumber)
                    return res.status(200).json({ "status": 204, "message": _.get(error, 'message', error) });
                } else {
                    var totalRecords = _.get(data, 'affectedRows', 0),
                        message,
                        changedRecords = _.get(data, 'changedRows', 0);
                    if (totalRecords <= 0) {
                        message = "No record found";
                    } else if (changedRecords == 0) {
                        message = `Bills are already ${_.get(notificationStatusMap, _.get(params, 'notificationStatus', 0))}`;
                    } else if (totalRecords == changedRecords) {
                        message = ` ${totalRecords} bills are ${_.get(notificationStatusMap, _.get(params, 'notificationStatus', 0))}`;
                    } else {
                        message = `Out of ${totalRecords} records, ${changedRecords} are ${_.get(notificationStatusMap, _.get(params, 'notificationStatus', 0))} as rest were already updated`;
                    }
                    let eventName = _.get(self.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENTS', 'NOTIFICATION_STATUS_UPDATE'], 'notificationStatusUpdate')
                    if(changedRecords > 0){
                        const debugKey = `rech:${params.rechargeNumber}::cust:${params.customerId}::op:${params.operator}`
                        const mappedData = self.reminderUtils.createCTPipelinePayload(params, eventName, debugKey);
                        if(self.commonLib.isCTEventBlocked(eventName)){
                            self.L.info(`Blocking CT event ${eventName}`)
                            L.log('updateNotificationStatus', message, 'for data', params.operator, params.customerId, params.rechargeNumber)
                            return res.status(200).json({ "status": 200, "message": message });
                        }
                        self.notify.ctKafkaPublisher.publishData([{
                            topic: _.get(self.config.KAFKA, 'SERVICES.CT_EVENTS_PUBLISHER.TOPIC', ''),
                            messages: JSON.stringify(mappedData)
                        }], error => {
                            if(error){
                                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DISABLE_NOTIFICATION_V1", 'STATUS:ERROR', "TYPE:CT_EVENTS", "OPERATOR:" + params.operator,`EVENT_NAME:${eventName}`]);
                                self.L.error("Controller::updateNotificationStatusV1 error while publishing CT events", error, JSON.stringify(mappedData))
                            } else { 
                                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DISABLE_NOTIFICATION_V1", 'STATUS:PUBLISHED', "TYPE:CT_EVENTS", "OPERATOR:" + params.operator,`EVENT_NAME:${eventName}`]);
                                self.L.log("Controller::updateNotificationStatusV1 published CT events", JSON.stringify(mappedData))
                            }
                        }, [200, 800])
                    }
                    L.log('updateNotificationStatus', message, 'for data', params.operator, params.customerId, params.rechargeNumber)
                    return res.status(200).json({ "status": 200, "message": message });
                }
            }, params, tableName)            
        } else {
            L.log('updateNotificationStatus', 'Invalid Parameters', params.operator, params.customerId, params.rechargeNumber, params.notificationStatus)
            return res.status(422).json({ message: 'customerId or rechargeNumber along with operator and notificationStatus are mandatory fields' });
        }
    }

    /**
     * v3 version for enable or disable notification
     * @param {*} req 
     * @param {*} res 
     */
    async updateNotificationStatusV3(req, res) {
        let body = _.get(req, 'body', {});
        try{
            let self = this,                        
            params = req.body;            
            _.set(params, 'stopBillFetch', _.get(params, 'stopBillFetch', (params.notificationStatus==0? true:false )));
            _.set(params, 'v3UpdateNotificationStatus',true);
            if (req.header('X-USER-ID')) params.customerId = req.header('X-USER-ID') ;
            L.verbose('updateNotificationStatusV3', 'Parameters', params.operator, params.customerId,params.productId, params.notificationStatus, params.rechargeNumber, params.stopBillFetch);            

            if(params && params.productId) {
                let service = _.get(params,'service',_.get(this.config,['CVR_DATA',params.productId,'service']),null);
                let paytype = _.get(params,'paytype',_.get(this.config,['CVR_DATA',params.productId,'paytype']),null);
                params.service = _.toLower(service);
                params.paytype = _.toLower(paytype);
            }

            params.isCreditCardOperator = self.commonLib.isCreditCardOperator(params.service);

            this.L.log('updateNotificationStatusV3',`Incoming request for ${JSON.stringify(params)}`);
            params.traceKey = `RN:${params.rechargeNumber}_CID:${params.customerId}_OP:${params.operator}_paytype:${params.paytype}`;
            this.L.log('updateNotificationStatusV3',`serving request for ${params.traceKey}`);
            let reminderFlowInstance = this.reminderFlowManager.getFlowInstance(params);
            if(!reminderFlowInstance) {
                this.L.critical('updateNotificationStatusV3',`could not identify flow for params:${JSON.stringify(params)}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:UPDATE_NOTI_STATUS_V3", 'STATUS:ERROR','TYPE:OBJ_INSTANCE_NOT_FOUND']);
                return this.respondWithMessage(res,500,"Unable to identify internal flow");
            }
            let validationResponse = reminderFlowInstance.notificationManager_validateReq(params);
            if(!validationResponse.status) {
                let errors = validationResponse.errors;
                this.L.error('updateNotificationStatusV3',`Invalid request for ${params.traceKey} : ${JSON.stringify(errors)}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:UPDATE_NOTI_STATUS_V3", 'STATUS:ERROR','TYPE:INVALID_REQ']);
                return this.respondWithMessage(res,422,errors);
            }
            let tableName = reminderFlowInstance.getTableName(params.operator,params.paytype);

            if(!tableName) {
                this.L.error('updateNotificationStatusV3',`table not found for:${JSON.stringify(params)}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:UPDATE_NOTI_STATUS_V3", 'STATUS:ERROR','TYPE:TABLE_NOT_FOUND']);
                return res.status(200).json({ "status": 204, "message": 'Operator not migrated' });                  
            }
            await reminderFlowInstance.notificationManager_updateReminderDB(params);
            this.L.log('updateNotificationStatusV3',`Db update success for ${params.traceKey}`);
            
            // await reminderFlowInstance.notificationManager_updateBillReminderFlag(params);                
            // this.L.log('updateNotificationStatusV3',`request success for ${params.traceKey}`)

            let eventName = _.get(self.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENTS', 'NOTIFICATION_STATUS_UPDATE'], 'notificationStatusUpdate')
            const debugKey = `rech:${params.rechargeNumber}::cust:${params.customerId}::op:${params.operator}`
            const mappedData = self.reminderUtils.createCTPipelinePayload(params, eventName, debugKey);

           
            if(self.commonLib.isCTEventBlocked(eventName) == false){
                self.notify.ctKafkaPublisher.publishData([{
                    topic: _.get(self.config.KAFKA, 'SERVICES.CT_EVENTS_PUBLISHER.TOPIC', ''),
                    messages: JSON.stringify(mappedData)
                }], error => {
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:DISABLE_NOTIFICATION_V3", 'STATUS:ERROR', "TYPE:CT_EVENTS", "OPERATOR:" + params.operator]);
                        self.L.error("Controller::updateNotificationStatusV3 error while publishing CT events", error, JSON.stringify(mappedData))
                    } else { 
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:DISABLE_NOTIFICATION_V3", 'STATUS:PUBLISHED', "TYPE:CT_EVENTS", "OPERATOR:" + params.operator,`EVENT_NAME:${eventName}`]);
                        self.L.log("Controller::updateNotificationStatusV3 published CT events", JSON.stringify(mappedData))
                    }
                }, [200, 800])
            }else{
                self.L.info(`Blocking CT event ${eventName}`)
            }
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:UPDATE_NOTI_STATUS_V3", 'STATUS:SUCCESS','TYPE:MAIN_FLOW_EXECUTION']);
            return this.respondWithMessage(res,200,"Notification status updates successfully");          
        } catch (error) {
            this.L.error('updateNotificationStatusV3',`Error occured for data:${body}`,error);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:UPDATE_NOTI_STATUS_V3", 'STATUS:ERROR','TYPE:MAIN_FLOW_EXECUTION']);
            return this.respondWithMessage(res,500,"Error while serving request,Please try again in some time");
        }
    }
    /**
     * v2 version for disable notification
     * @param {*} req 
     * @param {*} res 
     */
    async updateNotificationStatusV2(req, res) {
        let body = _.get(req, 'body', {});
        let self = this;
        try{
            let params = {
                operator: _.get(body, 'operator', null),
                customerId: +req.header('X-USER-ID'),
                rechargeNumber: _.get(body, 'rechargeNumber', null),
                amount: _.get(body, 'amount', null),
                notificationStatus: _.get(body, 'notificationStatus', null),
                referenceId: _.get(body, 'referenceId', null),
                productId: _.get(body, 'productId', null),
                triggerChannel: _.get(body, 'triggerChannel', null)
            }
            if(params && params.productId) {
                let service = _.get(params,'service',_.get(this.config,['CVR_DATA',params.productId,'service']),null);
                let paytype = _.get(params,'paytype',_.get(this.config,['CVR_DATA',params.productId,'paytype']),null);
                params.service = _.toLower(service);
                params.paytype = _.toLower(paytype);
            }

            params.isCreditCardOperator = self.commonLib.isCreditCardOperator(params.service);

            this.L.log('updateNotificationStatusV2',`Incoming request for ${JSON.stringify(params)}`)
            params.traceKey = `RN:${params.rechargeNumber}_CID:${params.customerId}_OP:${params.operator}_paytype:${params.paytype}`;
            this.L.log('updateNotificationStatusV2',`serving request for ${params.traceKey}`)
            let reminderFlowInstance = this.reminderFlowManager.getFlowInstance(params);
            if(!reminderFlowInstance) {
                this.L.critical('updateNotificationStatusV2',`could not identify flow for params:${JSON.stringify(params)}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:UPDATE_NOTI_STATUS_V2", 'STATUS:ERROR','TYPE:OBJ_INSTANCE_NOT_FOUND']);
                return this.respondWithMessage(res,500,"Unable to identify internal flow");
            }
            let validationResponse = reminderFlowInstance.notificationManager_validateReq(params);
            if(!validationResponse.status) {
                let errors = validationResponse.errors;
                this.L.error('updateNotificationStatusV2',`Invalid request for ${params.traceKey} : ${JSON.stringify(errors)}`)
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:UPDATE_NOTI_STATUS_V2", 'STATUS:ERROR','TYPE:INVALID_REQ']);
                return this.respondWithMessage(res,422,errors);
            }

            // await reminderFlowInstance.notificationManager_updateRecents(params); 
            // this.L.log('updateNotificationStatusV2',`request processed for ${params.traceKey}`)

            let tableName = reminderFlowInstance.getTableName(params.operator,params.paytype);
            this.L.log('updateNotificationStatusV2',`printing reminder flow instance and config: ${JSON.stringify(this.config.OPERATOR_TABLE_REGISTRY)} -- ${this.reminderFlowInstance}`);

            if(!tableName) {
                this.L.error('updateNotificationStatusV2',`table not found for:${JSON.stringify(params)}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:UPDATE_NOTI_STATUS_V2", 'STATUS:ERROR','TYPE:TABLE_NOT_FOUND']);
                return this.respondWithMessage(res,204,"Operator not migrated");
            }
            await reminderFlowInstance.notificationManager_updateReminderDB(params);
            this.L.log('updateNotificationStatusV2',`Db update success for ${params.traceKey}`)
            
            let eventName = _.get(self.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENTS', 'NOTIFICATION_STATUS_UPDATE'], 'notificationStatusUpdate')
            const debugKey = `rech:${params.rechargeNumber}::cust:${params.customerId}::op:${params.operator}`
            
            const mappedData = self.reminderUtils.createCTPipelinePayload(params, eventName, debugKey);
            if(self.commonLib.isCTEventBlocked(eventName) == false){
                self.notify.ctKafkaPublisher.publishData([{
                    topic: _.get(self.config.KAFKA, 'SERVICES.CT_EVENTS_PUBLISHER.TOPIC', ''),
                    messages: JSON.stringify(mappedData)
                }], error => {
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:DISABLE_NOTIFICATION_V2", 'STATUS:ERROR', "TYPE:CT_EVENTS", "OPERATOR:" + params.operator]);
                        self.L.error("Controller::disableNotificationV2 error while publishing CT events", error, JSON.stringify(mappedData))
                    } else { 
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:DISABLE_NOTIFICATION_V2", 'STATUS:PUBLISHED', "TYPE:CT_EVENTS", "OPERATOR:" + params.operator,`EVENT_NAME:${eventName}`]);
                        self.L.log("Controller::disableNotificationV2 published CT events", JSON.stringify(mappedData))
                    }
                }, [200, 800])
            }else{
                self.L.info(`Blocking CT event ${eventName}`)
            }
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:UPDATE_NOTI_STATUS_V2", 'STATUS:SUCCESS','TYPE:MAIN_FLOW_EXECUTION']);
            return this.respondWithMessage(res,200,"Notification status updates successfully");
        } catch (error) {
            this.L.error('updateNotificationStatusV2',`Error occured for data:${body}`,error)
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:UPDATE_NOTI_STATUS_V2", 'STATUS:ERROR','TYPE:MAIN_FLOW_EXECUTION']);
            return this.respondWithMessage(res,500,"Error while serving request,Please try again in some time");
        }
    }

    /**
     * v1 version for disable non paytm records notification
     * @param {*} req 
     * @param {*} res 
     */
    updateNonPaytmNotificationStatusV1(req, res) {

        let body = _.get(req, 'body', {});
        const self = this;
        try{ 
            let params = {
                operator: _.get(body, 'operator', null),
                customerId: +req.header('X-USER-ID'),
                rechargeNumber: _.get(body, 'rechargeNumber', null),
                notificationStatus: _.get(body, 'notificationStatus', null),
                productId: _.get(body, 'productId', null),
            }

            if(params && params.productId) {
                let service = _.get(params,'service',_.get(this.config,['CVR_DATA',params.productId,'service']),null);
                params.service = _.toLower(service);
            }

            let bankAttributes,bankName;
            bankAttributes = JSON.parse(_.get(self.config, ['CVR_DATA', params.productId, 'attributes'] , '{}'))
            bankName = _.toLower(_.get(bankAttributes, ['bank_code'] , ''));

            if(params.service == "financial services"){
                params.operator = bankName;
            }

            if(!params.operator){
                 return this.respondWithMessage(res, 422, "Operator missing");
            }
            if(_.get(params, 'service', '').toLowerCase() === 'loan') {
                self.loanUtil.updateRNtoDummy(params);
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:UPDATE_NP_NOTI_STATUS_V1','STATUS:SUCCESS','TYPE:LOAN_RN_UPDATE', `OPERATOR:${params.operator}`]);
            }

            if(params.service == "mobile"){
                let encryptedMobileNumber = this.cryptr.encrypt(params.rechargeNumber);
                params.rechargeNumber = encryptedMobileNumber;
            }
            let customerId = [(params.customerId).toString()];
            let paramsObj = {"customerIds" : customerId};
            self.recentsLayerLib.cacheCleanByCustId(function (error, data) {
                if (error) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE: CLEAR_CACHE_RECENT_API", 'STATUS:ERROR', "SOURCE:UPDATE_NONPAYTM_NOTIFICATION_STATUS"]);
                    self.L.error(' _processBillsData', `Error in clean Cache Api call :`,error);
                }else{
                    self.L.log('updateNonPaytmNotificationStatusV1 :: cacheCleanByCustId: successfully cleaned cache');
                }
            },paramsObj , "updateNonPaytmNotificationStatus");

            if(_.get(this.config, ['DYNAMIC_CONFIG','NON_RU_CONFIG','NON_RU_SERVICES','ONBOARDED_SERVICES'],[]).includes(params.service)){
                self.cassandraBills.updateNotificationStatusForNonRU(params,(error,data)=>{
                    if(error){
                        self.L.error('updateNonPaytmNotificationStatusV1',`Error occured for data:${JSON.stringify(body)} `,error)
                        return this.respondWithMessage(res,500,"Error while serving request,Please try again in some time");
                    }
                    const updatedRecord = _.get(data.rows[0], '[applied]', false);
                    if(updatedRecord == false){
                        return this.respondWithMessage(res,202,"Non RU record does not exist");
                    }
                    return this.respondWithMessage(res,200,"Notification status updated successfully");
                })
            }
            else{
                let listOfParams = [params];
                let [isOperatorPrefixEnabled, alternateRechargeNumber] = self.commonLib.getAlternateRechargeNumber(params.rechargeNumber, params.operator);
                if(isOperatorPrefixEnabled){
                    let alernativeParams = _.cloneDeep(params);
                    alernativeParams.rechargeNumber = alternateRechargeNumber;
                    listOfParams.push(alernativeParams);
                }
                if (_.get(self.config, ['DYNAMIC_CONFIG', 'OPERATOR_CONFIG', params.operator, 'DEMERGER_MAPPED_OPERATOR'], null)) {
                    let clonedData = _.cloneDeep(params);
                    clonedData.operator = _.get(self.config, ['DYNAMIC_CONFIG', 'OPERATOR_CONFIG', params.operator, 'DEMERGER_MAPPED_OPERATOR'], null);
                    listOfParams.push(clonedData);
                }
                ASYNC.eachLimit(listOfParams, 10, (param,callback)=>{
                    self.nonPaytmBillsModel.updateNonPaytmRecords(param, self.billSubscriber.cdcRecoveryPublisher, (error, data) => {
                        if(error){
                            self.L.error('updateNonPaytmNotificationStatusV1',`Error occured for data:${body} `,error)
                            if(error == "Record not found in DB"){
                                return callback();
                            }
                            else{
                                return callback(error);
                            }   
                        }

                    let eventName = _.get(self.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENTS', 'NOTIFICATION_STATUS_UPDATE'], 'notificationStatusUpdate')
                    const debugKey = `rech:${param.rechargeNumber}::cust:${param.customerId}::op:${param.operator}`



                    const mappedData = self.reminderUtils.createCTPipelinePayload(param, eventName, debugKey);
                    if(self.commonLib.isCTEventBlocked(eventName) == false){
                        self.notify.ctKafkaPublisher.publishData([{
                            topic: _.get(self.config.KAFKA, 'SERVICES.CT_EVENTS_PUBLISHER.TOPIC', ''),
                            messages: JSON.stringify(mappedData)
                        }], error => {
                            if(error){
                                utility._sendMetricsToDD(1, ["REQUEST_TYPE:updateNonPaytmNotificationStatusV1", 'STATUS:ERROR', "TYPE:CT_EVENTS", "OPERATOR:" + param.operator]);
                                self.L.error("Controller::updateNonPaytmNotificationStatusV1 error while publishing CT events", error, JSON.stringify(mappedData))
                            } else { 
                                utility._sendMetricsToDD(1, ["REQUEST_TYPE:updateNonPaytmNotificationStatusV1", 'STATUS:PUBLISHED', "TYPE:CT_EVENTS", "OPERATOR:" + param.operator,`EVENT_NAME:${eventName}`]);
                                self.L.log("Controller::updateNonPaytmNotificationStatusV1 published CT events", JSON.stringify(mappedData))
                            }
                            callback();
                        }, [200, 800])
                    }else{
                        self.L.info(`Blocking CT event ${eventName}`)
                        callback();
                    }
                        // return this.respondWithMessage(res,200,"Notification status updated successfully");
                    });
                }, function(error){
                    if(error){
                        self.L.error('updateNonPaytmNotificationStatusV1',`Error occured for data:${body} `,error)
                        return self.respondWithMessage(res,500,"Error while serving request,Please try again in some time");
                    }
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:UPDATE_NP_NOTI_STATUS_V1", 'STATUS:SUCCESS','TYPE:MAIN_FLOW_EXECUTION']);
                    return self.respondWithMessage(res,200,"Notification status updated successfully");
                });
            }
        } catch (error) {
            this.L.error('updateNonPaytmNotificationStatusV1',`Error occured for data:${body}`,error)
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:UPDATE_NP_NOTI_STATUS_V1", 'STATUS:ERROR','TYPE:MAIN_FLOW_EXECUTION']);
            return this.respondWithMessage(res,500,"Error while serving request,Please try again in some time");
        }
    }

    async getNonPaytmBills(req, res){
        const self = this;
        let qParams = req.query;

        let params = {
            customerId: +req.header('X-USER-ID'),
            service : _.get(qParams, 'service', null)
        }

        self.nonPaytmBillsModel.readRecentRecords(params, (error, data) => {
            if(error){
                self.L.error('getNonPaytmBill', 'Error response ', error)
                return res.status(200).json({ "status": 204, "error": error });
            }
            if(data.length == 0){
                self.L.error('getNonPaytmBill', 'Error response ', error)
                return res.status(200).json({ "status": 204, "error":  "No Data found!!!" });
            }
            let nData = self.filter_old_opertors_data(data);
            
            if(nData.length == 0){
                self.L.error('getNonPaytmBill', 'Error response ', error)
                return res.status(200).json({ "status": 204, "error":  "No Data found!!!" });
            }

            nData =self.getPreciseAmount(nData);
            return res.status(200).json({ "status": 200, "data": nData });
        });
    }

    filter_old_opertors_data(data){
        const self = this;
        let nData = [],
            cleanCacheForcIds = [];
        for(let i=0;i<data.length;i++){
            let service = _.get(data[i], 'service', null)
            if(service != "financial services"){
                nData.push(data[i]);
            }
            else{
                let operator = _.get(data[i], 'operator', null);
                let recharge_number = _.get(data[i],'recharge_number','')
                let temp = operator.split("_");
                if(recharge_number.includes('null')){
                    self.L.log("filter_old_opertors_data:: Not adding this recharge number in response", JSON.stringify(recharge_number));
                    continue;
                }
                else if (temp.length == 1 ) {
                    nData.push(data[i]);
                } else {
                    let params = {
                        rechargeNumber: data[i].recharge_number,
                        customerId: data[i].customer_id,
                        service: data[i].service,
                        operator: data[i].operator,
                        paytype: data[i].paytype
                    }
                    this.nonPaytmBillsModel.deleteUsingRecentRecords(params, self.billSubscriber.cdcRecoveryPublisher ,function(){});
                }
            }
        }

        let paramsObj = {"customerIds" : cleanCacheForcIds};
        self.recentsLayerLib.cacheCleanByCustId(function (error, data) {
            if (error) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE: CLEAR_CACHE_RECENT_API", 'STATUS:ERROR', "SOURCE:GET_NON_PAYTM_BILLS_API"]);
                self.L.error(' _processBillsData', `Error in clean Cache Api call :`,error);
            }else{
                self.L.log('getNonPaytmBills :: filter_old_opertors_data', ' cacheCleanByCustId: successfully cleaned cache');
            }

        },paramsObj , "getNonPaytmBillsAPI");

        return nData;

    }

    filter_notification_disabled_data(data){
        const self = this;
        let nData = [];
        for(let i=0;i<data.length;i++){
            let notificationStatus = _.get(data[i], 'notification_status', null);
            if(notificationStatus == 1)
                nData.push(data[i]);
        }
        return nData;
    }

    getPreciseAmount(data){
        const self = this;
        if(!data || !_.isArray(data)) return data;

        for(let i=0;i<data.length;i++){
            let originalAmount;
            try{
                let customer_other_info = JSON.parse(_.get(data[i], 'customer_other_info', null))
                originalAmount = _.get(customer_other_info, 'currentBillAmount', null)
            }
            catch(err){
                self.L.error('getPreciseAmount', 'Could not parse amount from json object with error', err);
                continue;
            }
            let amount = _.get(data[i], 'amount', null);
            if(amount!=null && originalAmount!=null && Math.abs(amount-originalAmount)<1){
                _.set(data[i],'amount',originalAmount);
            }  
        }
        return data;
    }

    async getNonPaytmBillsMultipleServices(req, res){
            const self = this;
            let services = req.query.service.split(',');
            let response={};
            L.log('getServices', 'Services :: ', services)
            if (_.isEmpty(services)) {
                L.verbose('getServices', 'Invalid Parameters', services)
                return res.status(422).json({ error_message: 'services is mandatory field' });
            }
            else{
                ASYNC.each(services, (service,callback) => { 
                    service = service.trim().toLowerCase();
                    var params = {
                        customerId: +req.header('X-USER-ID'),
                        service : service
                    }
                    self.nonPaytmBillsModel.readRecentRecords(params, (err, dataToProcess)=>{
                        if(err){
                            self.L.error('getNonPaytmBill', 'Error response ', err)
                            return callback(err);
                        }
                        if(dataToProcess.length == 0){
                            self.L.error('getNonPaytmBill', 'Error response No Data found!!!', err)
                        }

                        let data=[];
                        if(dataToProcess.length > 0)
                            data = self.filter_old_opertors_data(dataToProcess);
                        if(dataToProcess.length > 0 && data.length > 0)
                            data = self.filter_notification_disabled_data(data);
                        if(dataToProcess.length > 0 && data.length > 0)
                            data =self.getPreciseAmount(data);
                        if(service=="mobile"){
                            data.forEach(row => {
                                let decryptedMobileNumber = this.cryptr.decrypt(row["recharge_number"]);
                                _.set(row,["recharge_number"],decryptedMobileNumber);
                            });
                        }
                        _.set(response,[service],data);
                        callback();
                    });
                },(err)=>{
                    if(err){
                        return res.status(200).json({ "status": 204, "error": err});
                    }
                    return res.status(200).json({ "status": 200, "data": response});
                });
            }
    }

    /**
     * Return response based on params
     * @param {*} res 
     * @param {*} status 
     * @param {*} message 
     * @param {*} errors 
     */
    respondWithMessage(res,status,message=null,errors=[]){
        let resonseBody= {
            status : status,
            message: message,
            errors : errors
        }
        return res.status(status).json(resonseBody)
    }

    updateBillStatus(req, res) {
        let self = this,
            params = req.body,
            qParams;

        L.log('updateBillStatus', 'Parameters', params.bills);
        if (params && params.bills) {
            qParams = params.bills;

            res.status(200).json({ "status": 200, "data": "OK" });
            qParams.forEach(function (param) {
                if (!param.productId || !param.rechargeNumber || !param.customerId || !param.operator) {
                    L.error("Digital-reminder:", "updateBillStatus - Please enter mandatory fields productId, rechargeNumber, customerId and operator in record", qParams);
                }
                else {
                    param.operator = _.toLower(param.operator);
                    self.billSubscriber.updateBillStatus((err, data) => {
                        if (err) {
                            L.error("Digital-reminder: ", "updateBillStatus - Error Caught while disabling status of bill for customer_id:", param.customerId, " productId:", param.productId, " rechargeNumber:", param.rechargeNumber, "operator:", params.operator);

                        }
                        else {
                            L.log("Digital-reminder: ", "updateBillStatus - Record disabled sucessfully for customer_id:", param.customerId, " productId:", param.productId, " rechargeNumber:", param.rechargeNumber, "operator:", params.operator);

                        }
                    }, param);

                }
            });
        }
        else {
            L.log("Digital-reminder: ", "updateBillStatus - Invalid query parameters");
            return res.status(422).json({ status_code: "01", message: "Please enter valid details" });
        }
    }
    /*
        Function to update amount in bills
    */
    updateAmount(req, res) {
        let self = this,
            params = req.body;
        L.verbose('updateAmount', 'Parameters', params.operator, params.product_id, params.recharge_number);
        if (!_.isEmpty(params.operator) && !_.isEmpty(params.recharge_number) && _.get(params, 'product_id', null)) {
            params.operator = _.toLower(params.operator);
            let tableName = _.get(this.config, ['OPERATOR_TABLE_REGISTRY', params.product_id], null) || _.get(this.config, ['OPERATOR_TABLE_REGISTRY', params.operator], null),
                service = self.billSubscriber;
            if (!tableName) {
                L.error("updateAmount", "Operator not migrated:: ", params.operator);
                return res.status(200).json({ "status": 422, "message": 'Validation failed' });
            }
            service.updateAmount((error, data) => {
                if (error) {
                    L.error('updateAmount', 'Error in request processing', params.operator, params.recharge_number, params.product_id, "with error ", error)
                    return res.status(200).json({ "status": 500, "message": "Something went wrong" });
                } else {
                    return res.status(200).json({ "status": 200, "message": "OK" });
                }
            }, tableName, params)
        } else {
            L.log('updateAmount', 'Invalid Parameters', params.operator, params.customer_id, params.recharge_number)
            return res.status(200).json({ "status": 422, "message": 'Validation failed' });
        }
    }

    deletePlan(req, res) {
        let self = this,
            params = req.body;
        if (!_.isEmpty(params.recharge_number) && _.get(params, 'amount', null)) {
            self.planValidityModel.deletePlan(params, function (err) {
                if (err) {
                    L.error("deletePlan", "error occurred while deleting plan", err);
                    return res.send(500);
                } else {
                    return res.send(200);
                }
            });
        } else {
            L.error("deletePlan", "Validation failed", err);
            return res.send(500);
        }
    }

    /*
    * Function will get customer_data on the basis of customerId
    */
    getCustomerData(req, res) {
        let
            self = this,
            bodyParams = _.get(req, ['body'], {}),
            customerIds = _.get(bodyParams, ['customerIds'], []),
            response = {};
        L.log('getCustomerData', 'CustomerIds :: ', customerIds)
        if (_.isEmpty(customerIds)) {
            L.verbose('getCustomerData', 'Invalid Parameters', customerIds)
            return res.status(422).json({ error_message: 'customerIds is mandatory field' });
        } else {
            ASYNC.each(customerIds, (customerId, callback) => {

                var data = { customer_id: customerId };
                self.remindableUser._getUserDetails((err, res) => {
                    if (err) {
                        L.error('getCustomerData', 'Unable to fetch user details for customerId :: ', customerId);
                    }
                    _.set(response, [customerId], _.get(data, ['customer_name'], ''));
                    callback();
                }, data);

            }, (err) => {
                return res.status(200).json(response);
            });
        }
    }

    updateExpiry(req,res)
    {
        let body = _.get(req, 'body', {});
        const self = this;
        const paramList = [
            'service',
            'circle',
            'productId'
            ];
        let RN = _.get(body, 'rechargeNumber', null);
        let rech_num = (typeof RN === 'number') ? RN : (typeof RN === 'string' && VALIDATOR.isNumeric(RN)) ? VALIDATOR.toInt(RN) : null;
        if (!rech_num) return res.send(500);

        rech_num = rech_num.toString();
        if (rech_num.length < 10) return res.send(500);
        rech_num = rech_num.slice(-10);
        

        let operator = _.get(body, 'operator', null);
        operator = operator.toLowerCase();
        
        if (this.config.COMMON.VI_GROUP.includes(operator)) {
            operator = 'vodafone idea';
        }
        let validityExpiryDate = _.get(body, 'validityExpiryDate', null);
        validityExpiryDate = moment(validityExpiryDate);
       
        if(!validityExpiryDate || validityExpiryDate.isBefore()) return res.send(500);
        validityExpiryDate = validityExpiryDate.format("YYYY-MM-DD");


        let params = {};
        params.customerId = +req.header('X-USER-ID');
        params.planBucket = 'Special Recharge';
        params.latestRechargeDate = moment().format("YYYY-MM-DD HH:mm:ss");
        params.orderDate = moment().format("YYYY-MM-DD HH:mm:ss");
        params.extra = JSON.stringify({user_set_manual_expiry: true});
        params.status = 14;
        params.notificationStatus = 1;
        params.categoryName = 'Recharge Plan';
        params.rechargeNumber = rech_num;
        params.operator = operator;
        params.validityExpiryDate = validityExpiryDate;
        params.amount =0;

        for (const param of paramList) {
            params[param] = _.get(body, param, null);
        }
        self.planValidityModel.updateExpiry(params, function (error)
        {
                if (error) {
                    L.error("updateExpiry", "error occurred while updating expiry", error);
                    return res.send(500);
                } else {
                    return res.send(200);
                }
        });
    }

    encryptRecord(req, res) {
        let self = this;
        let body = req.body;
        try {
            let data = body.record;
            if (!data) {
                return res.status(400).json({ error: 'Input is required' });
            }
            let encryptedData = self.aes256Encryption.encrypt(data);
            return res.status(200).json({ encryptedData: encryptedData });
        } catch (error) {
            console.error('Error encrypting data:', error);
            return res.status(500).json({ error: 'Internal Server Error' });
        }
    }
    decryptRecord(req, res) {
        let self = this;
        let body = req.body;
        try {
            let data = body.record;
            if (!data) {
                return res.status(400).json({ error: 'Input is required' });
            }
            let decryptedData = self.aes256Encryption.decrypt(data);
            return res.status(200).json({ decryptedData: decryptedData });
        } catch (error) {
            console.error('Error decrypting data:', error);
            return res.status(500).json({ error: 'Internal Server Error' });
        }
    }

    deleteRecord(req, res) {
        let self = this;
        let body = req.body;
        let tableName;

        try{
            body.operator = _.toLower(body.operator);
            body.service = _.toLower(body.service);
            body.paytype = _.toLower(body.paytype);
            let includedOperator = _.get(this.config, ['DYNAMIC_CONFIG', 'OPERATOR_POSTPAIDFLOW', 'COMMON', 'INCLUDE_OPERATOR'],[]);           
            
            const ALLOWED_DTH_OPERATOR = _.get(this.config, ['DYNAMIC_CONFIG', 'VALIDATION_SYNC', 'COMMON', 'ALLOWED_DTH_OPERATOR'], []);
            includedOperator.push(...ALLOWED_DTH_OPERATOR);

            if(body.paytype == "prepaid" && body.operator == "airtel"){
                tableName = "bills_airtelprepaid"
            }  
            else if(body.paytype=='prepaid' && includedOperator.indexOf(body.operator) < 0){
                tableName = "plan_validity"
            } 
            else{
                tableName = _.get(this.config, ['OPERATOR_TABLE_REGISTRY', body.operator], null);
            }
    
            self.logger.log(`deleteRecord:: operator table name ${tableName}`, body, _.get(body, 'service', null));

            let productId = _.get(body, ['product_id'] , '');
            let bankAttributes,bankName, cardNetwork;
            bankAttributes = JSON.parse(_.get(self.config, ['CVR_DATA', productId, 'attributes'] , '{}'))
            bankName = _.toLower(_.get(bankAttributes, ['bank_code'] , ''));
            cardNetwork = _.toLower(_.get(bankAttributes, ['card_network'] , ''));

        let isCreditCardOperator = self.commonLib.isCreditCardOperator(body.service);

        let error_message = self.validateDeleteRecord(body, isCreditCardOperator);
        let debugKey = `rech_num:${body.service == 'financial services' ? self.encryptionDecryptioinHelper.encryptData(body.recharge_number) : body.recharge_number} cust_id:${body.customer_id} operator:${body.operator} service:${body.service} pId:${body.product_id}`;
        if (error_message) {
            self.L.error(`deleteRecord:: `, debugKey, error_message);
            return res.status(422).json({ code: 422, message: error_message });
        }

        if (!tableName) {
            error_message = `Operator ${body.operator} is not migrated! Table Not found.`;
            return res.status(200).json({ code: 204, message: error_message });
        }

        let params = { destTable: 'bills_archive',paytype: body.paytype, service: body.service, product_id: body.product_id, operator: body.operator, recharge_number: body.recharge_number, customer_id: body.customer_id, reference_id: body.reference_id, tableName, isCreditCardOperator, bankName, cardNetwork};

            let eventName = _.get(self.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENTS', 'RECORD_DELETED'], 'recordDeleted')
            let CTDebugKey = `rech:${body.recharge_number}::cust:${body.customer_id}::op:${body.operator}`
        let mappedData = self.reminderUtils.createCTPipelinePayload(body, eventName, CTDebugKey);

        let userInitiatedServices = _.get(this.config, ['DYNAMIC_CONFIG', 'USER_INITIATED_DELETE_CONFIG', 'ALLOWED_SERVICES', 'SERVICES'], ['electricity']);
        let exludedTables = _.get(this.config, ['DYNAMIC_CONFIG', 'USER_INITIATED_DELETE_CONFIG', 'EXCLUDED_TABLES', 'TABLE_NAME'], ['plan_validity']);

        if (userInitiatedServices.includes(body.service) && (!exludedTables.includes(tableName))){
            self.L.log(`deleteRecord:: handleUserInitDeletion flow initiated`, debugKey, tableName);
            self.handleUserInitDeletion(params, body, mappedData, eventName, CTDebugKey, res);
        } else {
            self.L.log(`deleteRecord:: deleteRecord flow initiated`, debugKey, tableName);
            self.billSubscriber.deleteRecord(params, (err, data) => {
                if (err) {
                    self.L.error(`deleteRecord:: Error occurred, debugKey: ${debugKey}, ${err}`);
                    return res.status(500).json({ code: 500, message: 'Something went wrong!' });
                }

                if (data && data.length === 0) {
                    self.L.error(`deleteRecord:: No record found for debugKey: ${debugKey}`);
                    return res.status(200).json({ code: 200, message: 'No Record found!' });
                }

                self.L.log(`deleteRecord:: Record successfully deleted for debugKey: ${debugKey}`);
                res.status(200).json({ code: 200, message: 'Successfully deleted!' });

                self.publishCTEvent(mappedData, eventName, body.operator, CTDebugKey);

                self.insertRecordIntoArchive(params, data, debugKey);
            });
        }
        }
        catch(error){
            self.L.error("Error in parsing attriute of CVR_DATA", error);
            res.status(500).json({ code: 500, message: 'Error in parsing attriute of CVR_DATA' });
        }
    }

    handleUserInitDeletion(params, body, mappedData, eventName, CTDebugKey, res) {
        let self = this;
        self.billSubscriber.updateReasonAsUserInitDeletion(params, (err, data) => {
            if (err) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_INITIATED_DELETION", 'STATUS:ERROR', `SERVICE:${params.service}`, `OPERATOR:${body.operator}`]);
                self.L.error(`handleUserInitDeletion:: Error occurred, debugKey: ${CTDebugKey}, ${err}`);
                return res.status(500).json({ code: 500, message: 'Something went wrong!' });
            }
            if (data && data.length === 0) {
                self.L.error(`handleUserInitDeletion:: No record found for debugKey: ${CTDebugKey}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_INITIATED_DELETION", 'STATUS:RECORD_NOT_FOUND', `SERVICE:${params.service}`, `OPERATOR:${body.operator}`]);
                return res.status(200).json({ code: 200, message: 'No Record found!' });
            }
            self.L.log(`handleUserInitDeletion:: Record successfully updated for debugKey: ${CTDebugKey}`);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_INITIATED_DELETION", 'STATUS:SUCCESS', `SERVICE:${params.service}`, `OPERATOR:${body.operator}`]);
            res.status(200).json({ code: 200, message: 'Successfully updated!' });
            self.publishCTEvent(mappedData, eventName, body.operator, CTDebugKey);
        });
    }

    publishCTEvent(mappedData, eventName, operator) {
        let self = this;
        if (self.commonLib.isCTEventBlocked(eventName) === false) {
            self.notify.ctKafkaPublisher.publishData(
                [{
                    topic: _.get(self.config.KAFKA, 'SERVICES.CT_EVENTS_PUBLISHER.TOPIC', ''),
                    messages: JSON.stringify(mappedData)
                }],
                error => {
                    if (error) {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:DELETE_RECORD", 'STATUS:ERROR', "TYPE:CT_EVENTS", `OPERATOR:${operator}`]);
                        self.L.error(`publishCTEvent:: Error while publishing CT events`, error, JSON.stringify(mappedData));
                    } else {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:DELETE_RECORD", 'STATUS:PUBLISHED', "TYPE:CT_EVENTS", `OPERATOR:${operator}`, `EVENT_NAME:${eventName}`]);
                        self.L.log(`publishCTEvent:: Successfully published CT event`, JSON.stringify(mappedData));
                    }
                },
                [200, 800] // Retry options
            );
        } else {
            self.L.info(`publishCTEvent:: Blocking CT event ${eventName}`);
        }
    }

    insertRecordIntoArchive(params, data, debugKey) {
        let self = this;
        self.bills.insertRecordInArchive((error, result) => {
            if (error) {
                self.L.critical(`insertRecordIntoArchive:: Error while inserting into archive table for debugKey: ${debugKey}, record: ${JSON.stringify(data[0])}`);
            } else {
                self.L.log(`insertRecordIntoArchive:: Record successfully inserted into archive for debugKey: ${debugKey}`);
            }
        }, params.destTable, data[0]);
    }


    validateDeleteRecord(body, isCreditCardOperator) {
        let self = this;
        let error_message = '';
        let productId = _.get(body, ['product_id'] , '');
        let bankAttributes,bankName, cardNetwork;
        try{
            bankAttributes = JSON.parse(_.get(self.config, ['CVR_DATA', productId, 'attributes'] , '{}'))
            bankName = _.toLower(_.get(bankAttributes, ['bank_code'] , ''));
            cardNetwork = _.toLower(_.get(bankAttributes, ['card_network'] , ''));
        }
        catch(error){
            self.L.error("Error in parsing attriute of CVR_DATA", error);
            res.status(500).json({ code: 500, message: 'Error in parsing attriute of CVR_DATA' });
        }

        if(isCreditCardOperator){
            if(!bankName || !cardNetwork || !body.customer_id || !body.recharge_number || !body.service || isNaN(body.customer_id) || !body.paytype){
                error_message = 'In case of creditCard Either of mandatory fields bank_name, card_network, customer_id, recharge_number,service, paytype not present or in correct format! '
                return error_message;
            }
        }

        if (!body.operator || !body.customer_id || !body.recharge_number || !body.service || isNaN(body.customer_id) || !body.paytype) {
            error_message = 'Either of mandatory fields operator, customer_id, recharge_number,service, paytype not present or in correct format!';
            return error_message;
        }
        
        return error_message;
    }

    getOrderSummarydata(req, res) {
        let self = this,
            reqBody = _.get(req, 'body', {});

        if (_.get(reqBody, "ipRoleId", null)) {
            self.orderSummaryLib.getPGDonationData((error, response) => {
                if (error) {
                    L.error("getOrderSummarydata: failed to get data from PG  ", reqBody);
                    return res.status(500).json({
                        code: 500,
                        message: "Internal server error"
                    });
                } else {
                    L.log("getOrderSummarydata: response sent successfully ", reqBody);
                    return res.status(200).json(response);
                }
            }, reqBody);
        } else {
            L.error("getOrderSummarydata: Invalid request params ", reqBody);
            return res.status(423).json({
                code: 423,
                message: "ipRoleId is missing in request"
            });
        }
    }

    deleteFromPlanValidityAndNonPaytm(params){
        return new Promise((resolve, reject) => {
            var self = this;
            let promise1 = self.planValidityModel.deletePlanByBucket(params);
            let promise2 = self.cassandraBills.markAsPaidFromNonPaytm(params, self.billSubscriber.cdcRecoveryPublisher);
            Promise.all([promise1, promise2])
                .then(results => {
                    return resolve(results);
                })
                .catch(error => {
                    return reject(error)
                })
        });
    }

    /**
     * 
     * @param {*} data conatins the request body with the validated result 
     * @param {*} res Response that needs to be used to return result
     */
    async markAsPaid(data, res) {
        try {
            const self = this,
                operator = data.operator.toLowerCase(),
                paytype = data.paytype.toLowerCase();

            data.productID = self.activePidLib.getActivePID(data.productID);
            const productId = data.productID;

            L.log('Controller :: markAsPaid','Parameters',  JSON.stringify(data));
            let tableName = _.get(this.config, ['OPERATOR_TABLE_REGISTRY', operator], null),
                billsFlow = false,
                changedRows = 0,
                deletedFromNonPaytmTable = false,
                deletedFromNonRUTable = false,
                NonPaytmRecord= false,
                NonRURecord=false,
                result;
            if (this.config.COMMON.VI_GROUP.includes(operator)) {
                tableName = 'bills_vodafone';
            }

            let attributes = JSON.parse(_.get(self.config, ['CVR_DATA', productId, 'attributes'] , '{}'))
            let card_network = _.get(attributes, ['card_network'], '');
            let service = _.get(this.config,['CVR_DATA',productId,'service'],'').toLowerCase(); 
            self.logger.log('Controller :: markAsPaid :: Parameters', data, _.get(data, 'service', service));

            let bankName = _.toLower(_.get(attributes, ['bank_code'] , ''));  
            if(_.get(this.config, ['DYNAMIC_CONFIG','NON_RU_CONFIG','NON_RU_SERVICES','ONBOARDED_SERVICES'],[]).includes(service)){
                NonRURecord=true;
            }
            if(card_network=='dummyNetwork'){
                NonPaytmRecord=true;
                if(service == 'financial services'){
                    if(!bankName || bankName == ''){
                        self.logger.error(`processRecord:: bank code unavailable`, data, service);
                        return res.status(400).json(
                            {
                                statusCode: 'ERR01',
                                data: {
                                    rows_updated: 0,
                                    message: "Bank code unavailable for pid"
                                }
                            }
                        );
                    }else{
                        _.set(data, 'operatorForSmsCC', bankName);
                    }
                }
            }
            if(!tableName && paytype !== 'prepaid' && !NonRURecord && !NonPaytmRecord) {
                L.error("markAsPaid", "Operator not present::", JSON.stringify(data));
                return res.status(200).json(
                    {
                        statusCode: 422,
                        data: { 
                            errors: [
                                {
                                    "value": data.operator,
                                    "msg": "Operator doesn't exist",
                                    "param": "operator"
                                }
                            ]
                        }
                    }
                );
            }
            if(self.includedOperator.includes(operator)){ //rentpayment
                let reminderFlowInstance = this.reminderFlowManager.getFlowInstance({operator : operator, paytype : paytype});
                let result = await reminderFlowInstance.markAsPaid(data).catch(function(error){
                    return res.status(400).json(
                        {
                            statusCode: 'ERR01',
                            data: {
                                rows_updated: 0,
                                message: "Error encountered while performig mark as paid operation"
                            }
                        }
                    );
                });
                
                return res.status(202).json({
                        statusCode: 202,
                        data: {
                            rows_updated: result
                        }
                    }
                );           
            }
            if(NonRURecord || NonPaytmRecord){ //UPI P2P
                let result = await self.cassandraBills.markAsPaid(data,NonRURecord,NonPaytmRecord, self.billSubscriber.cdcRecoveryPublisher)
                if(NonPaytmRecord) self.publishToCT(data);//markaspaid event blocked
                    return res.status(202).json({
                        statusCode: 202,
                        data: {
                            result: result
                        }
                    }
                );           
            }
            if(paytype=='credit card' && (_.get(data,'referenceID',null)==null || _.get(data,'referenceID',null)=='')){
                return this.respondWithMessage(res,422,null,[{
                    value: "",
                    msg: "Invalid referenceID",
                    param: "referenceID",
                    location: "body"
                }])
            }
            switch (paytype) {
                case 'prepaid':
                    if (operator === 'airtel') {
                        L.log('Controller :: markAsPaid','Deleting airtel prepaid from plan_validity', JSON.stringify(data));
                        tableName = 'bills_airtelprepaid';
                        billsFlow = true;
                        result = await self.deleteFromPlanValidityAndNonPaytm(data);
                        changedRows += result[0].affectedRows;
                        deletedFromNonPaytmTable=true;
                    }
                    break;
                default:
                    billsFlow = true;
                    break;
            }
            if (billsFlow) {
                //console.log("printing the data :: " +data + " service:: " + service);
                self.logger.log('Controller :: markAsPaid :: Updating bills', data, _.get(data, 'service', service));
                let updatedRows = 0;
                if(service=='financial services'){
                    result = await self.billSubscriber.markAsPaidForCC(tableName, data);
                }else{
                    result = await self.billSubscriber.markAsPaid(tableName, data);
                }
                changedRows += result.changedRows;
                updatedRows += result.changedRows;
                if(updatedRows == 0 && service!='financial services'){
                    L.log('Controller :: markAsPaid','Updating cassandra', JSON.stringify(data));
                    self.cassandraBills.markAsPaid(data,false,true, self.billSubscriber.cdcRecoveryPublisher);
                    changedRows += 1;
                }
            } else {
                // Calling plan_validity for hard delete based on conditions
                L.log('Controller :: markAsPaid','Deleting plan_validity', JSON.stringify(data));

                // result = await self.planValidityModel.deletePlanByBucket(data);
                result = await self.deleteFromPlanValidityAndNonPaytm(data);
                deletedFromNonPaytmTable=true;
                changedRows += result[0].affectedRows;
            }
            if(changedRows > 0 || deletedFromNonPaytmTable){
                if(_.get(data, 'service', '').toLowerCase() === 'loan') {
                    self.loanUtil.updateRNtoDummy(data);
                }
                self.publishToCT(data)
            }
            return res.status(202).json(
                {
                    statusCode: 202,
                    data: {
                        rows_updated: result
                    }
                }
            );
        } catch (error) {
            return res.status(400).json(
                {
                    statusCode: 'ERR01',
                    data: {
                        rows_updated: 0,
                        message: error.message
                    }
                }
            );
        }
    }

    publishToCT(data){
        var self=this;
        const eventName = _.get(self.config, ['DYNAMIC_CONFIG', 'CT_CONFIG','CT_EVENTS', 'MARK_AS_PAID'], 'billMarkAsPaid')
                const debugKey = `rech:${data.rechargeNumber}::cust:${data.customerID}::op:${data.operator}`
                const mappedData = self.reminderUtils.createCTPipelinePayload(data, eventName, debugKey);
                if(self.commonLib.isCTEventBlocked(eventName) == false){
                    self.notify.ctKafkaPublisher.publishData([{
                        topic: _.get(self.config.KAFKA, 'SERVICES.CT_EVENTS_PUBLISHER.TOPIC', ''),
                        messages: JSON.stringify(mappedData)
                    }], error => {
                        if(error){
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:MARK_AS_PAID", 'STATUS:PUBLISHED', "TYPE:CT_EVENTS", "OPERATOR:" + data.operator]);
                            self.L.error("Controller::markAsPaid error while publishing CT events", error, JSON.stringify(mappedData))
                        } else {
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:MARK_AS_PAID", 'STATUS:PUBLISHED', "TYPE:CT_EVENTS", "OPERATOR:" + data.operator,`EVENT_NAME:${eventName}`]);
                            self.L.log("Controller::markAsPaid published CT events", JSON.stringify(mappedData))
                        } 
                    }, [200, 800])
                }else{
                    self.L.info(`Blocking CT event ${eventName}`)
                }
    }

    createRecordForAnalytics(record) {
        let recordForAnalytics = {};
        recordForAnalytics.source = "UPMS";
        recordForAnalytics.source_subtype_2 = "FULL_BILL";
        recordForAnalytics.user_type = null;
        recordForAnalytics.customer_id = _.get(record, 'customerId', null);
        recordForAnalytics.service = _.get(record, 'service', null);
        recordForAnalytics.recharge_number = _.get(record, 'rechargeNumber', null);
        recordForAnalytics.operator = _.get(record, 'operator', null);
        recordForAnalytics.due_amount = _.get(record, 'currentBillAmount', null);
        recordForAnalytics.paytype = _.get(record, 'paytype', null);
        recordForAnalytics.updated_at = _.get(record, 'updatedAt', null);
        return recordForAnalytics;
    }

    /**
     * callback for notifications
     */
    async notificationCallback(req, res) {
        const body = _.get(req, 'body', {});
        const failStatus = _.get(this.config, ['NOTIFICATION', 'status', 'FAILED'], null);
        

        try{
            let params = {
                job_id: _.get(body, 'job_id', null),
                status: _.get(body, 'status', null),
            }
            if(params && params.job_id && (params.status === 'FAIL')) {
                this.L.log(`notificationCallback:: notification failed for job_id: ${params.job_id} `);
                
                const whereCondition = 'job_id = ?'
                const fields = ['status'];
                const value = [failStatus, params.job_id];
                this.notification.updateNotification((error, data) => {
                    if (!error) {
                        this.L.log('notificationCallback:: notification entry is updated in the database for job_id: ', params.job_id);
                    }
                }, fields, whereCondition, value);
            } else {
                this.L.error(`notificationCallback:: error while handling notification callback req: ${JSON.stringify(req.body)}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_CALLBACK", 'STATUS:ERROR','TYPE:NOTIFICATION_FAILURE_CALLBACK']);
                return this.respondWithMessage(res,500,"Error while handling notification callback");
            }
            
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_CALLBACK", 'STATUS:SUCCESS','TYPE:NOTIFICATION_FAILURE_CALLBACK']);
            return this.respondWithMessage(res,200,"Notification callback handled successfully");
        } catch (error) {
            this.L.error(`notificationcallback::Error for Notification callback :${body} \n Error: ${error}`)
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_CALLBACK", 'STATUS:ERROR','TYPE:NOTIFICATION_FAILURE_CALLBACK']);
            return this.respondWithMessage(res,500,"Error while handling notification callback");
        }
    }

    async setRemindLater(reqData, res) {
        var self=this;
        try {
            
            self.L.info('setRemindLater starts {}', JSON.stringify(reqData));
            const customerId = reqData.customer_id;
            const operator = reqData.operator.toLowerCase();
            const rechargeNumber = reqData.recharge_number;
            const service = reqData.service.toLowerCase();
            const source = reqData.userSource;
            reqData.customerId = customerId;
            reqData.rechargeNumber = rechargeNumber;
            const productId = self.activePidLib.getActivePID(reqData.productId);
            const paytype = _.get(self.config, ['CVR_DATA', productId, 'paytype'], '').toLowerCase();
           
            reqData.paytype = paytype;
            const isMobilePostpaid = service === 'mobile' && paytype === 'postpaid';


            if (service !== 'electricity' && !isMobilePostpaid) {
                self.L.error('setRemindLater',`RemindLater is supported only for electricity. reqData:${JSON.stringify(reqData)}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:REMIND_LATER", 'STATUS:ERROR', 'TYPE:INVALID_SERVICE']);              
                return res.status(400).json({ "status": "failure", "message": 'RemindLater is supported only for electricity.', "type" : "error" });         
            }

           
            
           // this.L.info("all CVR Data is " , _.get(this.config, ['CVR_DATA']));
      
          
            reqData.operator = operator;
            reqData.service = reqData.service.toLowerCase();
            
          
            //this.L.info("reminderFlowInstance is {}", reminderFlowInstance);


            let remindLaterDate;
            let dueDate;
            let amount;
            let isDataFromNonPaytm = false;
            let cassandraRowData ;
            let reminderFlowInstance = null;
            let cTData ;
            let extra, updatedExtra;

           

            reminderFlowInstance = self.reminderFlowManager.getFlowInstance(reqData);
    
            let tableName = null, billsData = null;
            if (reminderFlowInstance)
                tableName = reminderFlowInstance.getTableName(operator, paytype);
    
            self.L.info("tableName is {}", tableName);
        
          

            reqData.traceKey = `RN:${reqData.rechargeNumber}_CID:${reqData.customerId}_OP:${reqData.operator}_paytype:${reqData.paytype}`;
           

            if (reminderFlowInstance && tableName)
                billsData = await reminderFlowInstance.getBillsData(reminderFlowInstance, reqData);


           
            if (billsData == null || billsData.length === 0) {
                L.info('setReminderLater', 'Data not found in mysql', operator, customerId, productId, rechargeNumber);
                if(service == "mobile"){
                    let encryptedMobileNumber = this.cryptr.encrypt(reqData.rechargeNumber);
                    reqData.rechargeNumber = encryptedMobileNumber;
                }
    
                let result = await self.cassandraBills.getAsyncDataFromNonPaytm(reqData);
                if(result.length == 0){
                    
                    if(!reminderFlowInstance) {
                        self.L.error('setRemindLater',`could not identify flow for reqData:${JSON.stringify(reqData)}`);  
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:REMIND_LATER", 'STATUS:ERROR', 'TYPE:UNABLE_TO_IDENTIFY_FLOW']);              
                        return res.status(500).json({ "status": "failure", "message": 'Unable to identify internal flow', "type" :"error" });         
                    }

                    if (!tableName) {
                        return res.status(207).json(self.handleNoTableName(reqData));
                    }        

                    L.info('setReminderLater', 'Data not found in cassandra', operator, customerId, productId, rechargeNumber);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:REMIND_LATER", 'STATUS:ERROR', 'TYPE:NO_DATA_FOUND']);
                    return res.status(207).json(self.handleNoBillsData(operator, customerId, productId, rechargeNumber));
                }

                remindLaterDate =  _.get(result[0],'remind_later_date',null);
                dueDate =  _.get(result[0],'due_date',null);
                amount = _.get(result[0],'amount',null);
                cassandraRowData = result[0];
                isDataFromNonPaytm = true;
                cTData = result[0];
                extra = _.get(result[0],'extra',null);
                updatedExtra = extra;
            }
            else {
                remindLaterDate = _.get(billsData[0],'remind_later_date',null);
                dueDate = _.get(billsData[0],'due_date',null);
                amount = _.get(billsData[0],'amount',null);
                cTData = billsData[0];
                extra = _.get(billsData[0],'extra',null);
                updatedExtra = extra;
               
            }


            const dueDateMoment = MOMENT(dueDate);

            if (remindLaterDate && MOMENT(remindLaterDate).endOf('day').isAfter(MOMENT())) {
                L.info('setReminderLater', 'Remind Later Date is already set and is a future date ', remindLaterDate);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:REMIND_LATER", 'STATUS:ERROR', 'TYPE:DATE_ALREADY_SET']);
                const remindLater = MOMENT(remindLaterDate);
                const dueDateOffset = dueDateMoment.diff(remindLater, 'days');
                return res.status(207).json({ "status": "failure", "message": 'Remind Later Date is already set and is a future date', "type":"alreadySet" , "dueDateOffset" : dueDateOffset , "remindLaterDate": remindLaterDate.getTime()});
            }

            try {
                remindLaterDate = self.calculateRemindLaterDate(dueDate, amount, reqData.remindLaterOffset);
            } catch (error) {
                L.info('setReminderLater', 'unable to set date', error);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:REMIND_LATER", 'STATUS:ERROR', 'TYPE:' + error.message.toUpperCase()]);
                return res.status(207).json({ "status": "failure", "message": error.message , "type" : error.type});
            }

            L.info("final remindLaterDate is {}", remindLaterDate);
            reqData.remindLaterDate = remindLaterDate;
           

            if (source) {
                updatedExtra = self.setSourceInExtra(updatedExtra, source);
                L.info("After updating source extra is ", updatedExtra);
            }

            
            reqData.extra = extra;
            reqData.updatedExtra = updatedExtra;

            if(!isDataFromNonPaytm){
                _.set(reqData, "type", "RU");
                await reminderFlowInstance.updateRemindLaterDate(reqData);
            }
            else {
                _.set(reqData, "type", "NON_RU");
                await self.nonPaytmBillsModel.updateRemindLaterDate(reqData, cassandraRowData,  self.notify.cassandraCdcPublisher);
            }
            
            const eventName = _.get(self.config, ['DYNAMIC_CONFIG', 'CT_CONFIG','CT_EVENTS', 'WHATSAPP_REMIND_LATER'], 'whatsappReminderLater')
       
            try{
                L.info("Adding data to remind later date table with tye reqData.type {}",reqData.type);
                await self.nonPaytmBillsModel.insertIntoRemindLaterTable(reqData);
            }catch(error){
                L.info('setRemindLater', 'Error in inserting into remind_later_event table', error);
            }
            
            const remindLater = MOMENT(remindLaterDate);
            let dueDateOffset;
            if (dueDateMoment.isAfter(remindLater)) {
                dueDateOffset = -dueDateMoment.diff(remindLater, 'days');
            } else {
                dueDateOffset = remindLater.diff(dueDateMoment, 'days');
            }
            
            //cTData.debugKey = reqData.traceKey;
            cTData.remindLaterDate = remindLaterDate;
            cTData.rechargeNumber  = rechargeNumber;
            cTData.userSource = source;
            await self.paymentRemindLaterEvents.insertPaymentRemindLaterEvent(reqData);
            await self.publishRemindLaterToCT(cTData, eventName);
           
            return res.status(200).json({ "status": "success", "message": "Reminder Setup Successfully Completed.","dueDateOffset" : dueDateOffset, "remindLaterDate": remindLaterDate.getTime()});


        } catch (error) {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:REMIND_LATER", 'STATUS:ERROR', 'TYPE:ERROR']);              
            L.error('ReminderController :: setRemindLater', 'Error', error);
            return res.status(500).json({ "status": "failure", "message": "Internal server error." , "type" : "error"});
        }
    }

    calculateRemindLaterDate(dueDate, amount, remindLaterOffset) {
        
        let currentDate = MOMENT().endOf('day');

        if(dueDate === null || dueDate === undefined) {
            throw new CustomError('Due date is null or undefined', '');
        }
        dueDate = MOMENT(dueDate);
        
        let futureDate = MOMENT().add(32, 'days');
        
        L.info('calculateRemindLaterDate amount {} , dueDate {}, currentDate{}, futureDate{}, remindLaterOffset{}', amount, dueDate, currentDate, futureDate, remindLaterOffset);
        
        if ((amount === null || amount > 0) && dueDate.isAfter(currentDate) && dueDate.isSameOrBefore(futureDate)) {
            let remindLaterDate = MOMENT(dueDate).add(remindLaterOffset, 'days');
 
            L.info("calculateRemindLaterDate remindLaterDate {}", remindLaterDate);
            dueDate.endOf('day');
            
            if(remindLaterDate.isAfter(dueDate)) {
                throw new CustomError('Remind Later Date should be smaller than dueDate', 'error');
            }
            if (remindLaterDate.isSameOrBefore(currentDate, 'day')) {
                remindLaterDate = dueDate;
            }
            return remindLaterDate.toDate();
        } else if (amount <=0) {
            throw new CustomError('Bill is Already Paid', 'error');
        } else if (dueDate.isSame(currentDate, 'day')) {
            throw new CustomError('Due date is today', 'dueToday');
        } else if (dueDate.isBefore(currentDate, 'day')) {
            throw new CustomError('Due date is already past', 'overDue');
        }else if (dueDate.isAfter(futureDate)) {
            throw new CustomError('Future Due Date', 'error');
        }
    
       return null;
    }

     async publishRemindLaterToCT (data, eventName){
        const debugKey = `rech:${data.recharge_number}::cust:${data.customer_id}::op:${data.operator}`;
        const mappedData = this.reminderUtils.createCTPipelinePayload(data, eventName, debugKey);
        if(this.commonLib.isCTEventBlocked(eventName) === false){
            try {
                await new Promise((resolve, reject) => {
                    this.notify.ctKafkaPublisher.publishData([{
                        topic: _.get(this.config.KAFKA, 'SERVICES.CT_EVENTS_PUBLISHER.TOPIC', ''),
                        messages: JSON.stringify(mappedData)
                    }], error => {
                        if(error){
                            reject(error);
                        } else {
                            resolve();
                        } 
                    }, [200, 800])
                });
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:REMIND_LATER", 'STATUS:PUBLISHED', "TYPE:CT_EVENTS", "OPERATOR:" + data.operator,`EVENT_NAME:${eventName}`]);
                this.L.log("Controller::remindLater published CT events", JSON.stringify(mappedData));
            } catch (error) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:REMIND_LATER", 'STATUS:ERROR', "TYPE:CT_EVENTS", "OPERATOR:" + data.operator]);
                this.L.error("Controller::remindLater error while publishing CT events", error, JSON.stringify(mappedData));
                throw error; // re-throw the error
            }
        } else {
            this.L.info(`Controller::remindLater blocking CT event ${eventName}`);
        }
    }

     setSourceInExtra (extra , remindLaterSource){
        /** If extra is null or string */
        if(_.isEmpty(extra)){
            return JSON.stringify({
                remindLaterSource 
            })
        }
        try{
            let tempExtra
            if(typeof extra != "object")
                tempExtra = JSON.parse(extra)
            else tempExtra = extra
            if(tempExtra == null){
                return JSON.stringify({
                    remindLaterSource
                    
                })
            }else{
                
                if(_.isString(tempExtra)){
                    /** This should not be required since we have aleady parsed the string
                     * But in some cases the string was stringified twice , therefore will need to be parsed twice as well. 
                     */
                    tempExtra = JSON.parse(tempExtra)
                }
                _.set(tempExtra , 'remindLaterSource',remindLaterSource)
                return JSON.stringify(tempExtra)
            }
        }catch(err){
            return extra;
        }
    }

    handleNoTableName(reqData) {
        L.error('setReminderLater', `table not found for:${JSON.stringify(reqData)}`);
        utility._sendMetricsToDD(1, ["REQUEST_TYPE:REMIND_LATER", 'STATUS:ERROR', 'TYPE:TABLE_NOT_FOUND']);
        return { "status": "failure", "message": 'Table not found' , "type":"error"};
    }
    
     handleNoBillsData(operator, customerId, productId, rechargeNumber) {
        L.info('setReminderLater', 'Data not found in any table', operator, customerId, productId, rechargeNumber);
        utility._sendMetricsToDD(1, ["REQUEST_TYPE:REMIND_LATER", 'STATUS:ERROR', 'TYPE:DATA_NOT_FOUND']);
        return { "status": "not found", "message": 'No Data found!!!' , "type":"error"};
    }


    

    async testCustomNotification(reqData, res) {
        let self = this;
        let service = _.get(reqData, 'service', 'Default');
        let dummyPid = _.get(
            self.config, ['DYNAMIC_CONFIG', 'HEURISTIC_CUSTOM_NOTIFICATION_DUMMY_CONFIG', 'PRODUCT_ID', service],
            _.get(self.config, ['DYNAMIC_CONFIG', 'HEURISTIC_CUSTOM_NOTIFICATION_DUMMY_CONFIG', 'PRODUCT_ID', 'Default']));
        let dummyRechargeNumber = _.get(
            self.config, ['DYNAMIC_CONFIG', 'HEURISTIC_CUSTOM_NOTIFICATION_DUMMY_CONFIG', 'RECHARGE_NUMBER', service],
            _.get(self.config, ['DYNAMIC_CONFIG', 'HEURISTIC_CUSTOM_NOTIFICATION_DUMMY_CONFIG', 'RECHARGE_NUMBER', 'Default']));

        const meta_data = reqData.meta_data;
        let payLoad = {
            campaign_name: reqData.campaign_name,
            customer_id: reqData.test_customer_id,
            recharge_number: reqData.recharge_number || dummyRechargeNumber,
            service: reqData.service,
            template_id: reqData.template_id,
            template_name: reqData.template_name,
            product_id: reqData.product_id || dummyPid

        };

        for (let key in meta_data) {
            if (meta_data.hasOwnProperty(key) && key.startsWith('param-')) {
                payLoad[key] = meta_data[key];
            }
            let payloadKey = key.replace('param-', '');
            if (payLoad.hasOwnProperty(payloadKey)) {
                payLoad[payloadKey] = meta_data[key];
            }

        }

        try {
            await this.heuristicCustomNotifications.processNotification(payLoad, res);
            return res.status(200).json({ "status": "success", "message": "Tested Successfully" });

        }
        catch (error) {
            L.error('CustomNotification :: testCustomNotification', 'Error', error);
            return res.status(500).json({ "status": "failure", "message": "Internal server error.", "type": "error", "error": error });
        }

    }

    isCreditCardOperator(paytype, service) {
        return (paytype === 'credit card' && service === 'financial services');
    }


    /*
    * Function will fetch UPMS data from RU/NonRu DB based on below params
    * recharge_number, cust_id, operator, service
    */
    fetchUPMStoken(req, res) {
        let self = this,
            params = req.body,
            mandatoryParams = ['operator', 'cust_id', 'recharge_number', 'service'],
            absentParams = [];

        if(!_.isEmpty(params.service) && params.service.toLowerCase() === 'financial services') {
            mandatoryParams.push('referenceId');
        }

        for(let index in mandatoryParams) {
            let param = mandatoryParams[index];
            if (!_.get(params, param, null)) {
                absentParams.push(param);
            }
        }

        if (absentParams.length > 0) {
            self.L.error('fetchUPMStoken', 'Manadatory Params are missing : ' + absentParams);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:FETCH_UPMS_TOKEN", 'STATUS:ERROR','TYPE:MANDATORY_PARAMS_MISSING']);
            return res.status(422).json({ "status": 422, "data": null, "errorStatus": "MANDATORY_FIELD_MISSING", "message": 'Manadatory Params are missing : ' + absentParams });
        } else {
            let 
                operator = _.toLower(params.operator),
                customerId = params.cust_id,
                rechargeNumber = params.recharge_number,
                referenceId = _.get(params,'referenceId', null),
                service = _.toLower(params.service),
                paytype = params.paytype,
                isCreditCardOperator = self.isCreditCardOperator(paytype, service);

            self.L.log('fetchUPMStoken', 'Parameters', operator, customerId, service == 'financial services' ? self.encryptionDecryptioinHelper.encryptData(rechargeNumber) : rechargeNumber, service);

            ASYNC.waterfall([
                next => {
                    let tableName = _.get(self.config, ['OPERATOR_TABLE_REGISTRY', operator], null);
                    self.bills.getBillsRecord(function (error, data) {
                        if (error) {
                            self.L.error("Data not found in RU DB due to error", error);   
                            return next(null, "DB_ERROR");
                        } else if (!data || (_.isArray(data) && _.isEmpty(data))) {
                            self.L.log("Data not found in RU DB");   
                            return next(null, null);
                        } else {
                            self.L.log("Data found in RU DB");   
                            return next("FOUND_IN_RU_DB", data);
                        }
                    }, tableName, operator, service, rechargeNumber, customerId, referenceId);
                },
                (errorMessage, next) => {

                    if(isCreditCardOperator) {
                        if (errorMessage === "DB_ERROR") {
                            return next("DB_ERROR");
                        } else {
                            return next("NOT_FOUND");
                        }
                    }
                    self.nonPaytmBillsModel.getBillsRecord(function (error, data) {
                        if (error) {
                            self.L.error("Data not found in NonRU DB due to error", error);   
                            return next("DB_ERROR");
                        } else if (!data || (_.isArray(data) && _.isEmpty(data))) {
                            if (errorMessage == "DB_ERROR") {
                                return next("DB_ERROR");
                            } else {
                                self.L.log("Data not found in NonRU DB");   
                                return next("NOT_FOUND");
                            }
                        } else {
                            self.L.log("Data found in NonRU DB");  
                            return next("FOUND_IN_NonRU_DB", data); 
                        }
                    }, {operator, customerId, rechargeNumber, service});
                }
            ], function (error, data) {
                if (error == "DB_ERROR") {
                    self.L.error('fetchUPMStoken', 'DB error occurred');
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:FETCH_UPMS_TOKEN", 'STATUS:ERROR','TYPE:DB_ERROR']);
                    return res.status(500).json({ "status": 500, "data": null, "errorStatus": "FAILURE", "message": 'DB error occurred' });
                } else if (error == "NOT_FOUND") {
                    self.L.log('fetchUPMStoken',`Data not found for params:${JSON.stringify(params)}`,error);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:FETCH_UPMS_TOKEN", 'STATUS:ERROR','TYPE:DATA_NOT_FOUND']);
                    return res.status(200).json({ "status": 200, "data": null, "errorStatus": "NOT_FOUND", "message" : "Data not found in both RU and NonRU"});
                } else {
                    let record = data[0],
                        extra = null;

                    try {
                        extra = JSON.parse(_.get(record, 'extra', null));
                    } catch (err) {
                        self.L.error('fetchUPMStoken', 'Error occurred during parsing of extra fetched from DB', err);
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:FETCH_UPMS_TOKEN", 'STATUS:ERROR','TYPE:PARSING_EXTRA']);
                        return res.status(500).json({ "status": 500, "data": null, "errorStatus": "FAILURE", "message": 'DB error occurred' });
                    }
                    
                    if(extra && _.get(extra, 'billSource', null) == "UPMS") {
                        let result = {
                            upmsBillPaymentToken : _.get(extra, 'upmsBillPaymentToken', null),
                            upmsRegistrationNumber : _.get(extra, 'upmsRegistrationNumber', null),
                            reminderAmount : _.get(record, 'due_amount', _.get(record, 'amount', null)),
                            reminderDueDate : _.get(record, 'due_date', null),
                            reminderBillDate : _.get(record, 'bill_date', null)
                        };
                        let loggingObject = {
                            result : result,
                            params : params
                        }
                        self.logger.log(`fetchUPMStoken params: and result ::`, loggingObject, service);
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:FETCH_UPMS_TOKEN", 'STATUS:SUCCESS','TYPE:UPMS_DATA_FOUND']);
                        return res.status(200).json({ "status": 200, "data": result, "errorStatus": "SUCCESS", "message" : "success"});
                    } else {
                        self.logger.log("fetchUPMStoken Data found but not of UPMS for params:", params, service);
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:FETCH_UPMS_TOKEN", 'STATUS:ERROR','TYPE:UPMS_DATA_NOT_FOUND']);
                        return res.status(200).json({ "status": 200, "data": null, "errorStatus": "NON_UPMS", "message" : "Data found but not of UPMS"});
                    }
                }
            });
        }
    }

    /**
     * Handles the request to get all customers.
     * 
     * @param {Object} req - The request object.
     * @param {Object} res - The response object.
     * @returns {Object} The response object with httpstatus and customer data.
     */
    async getAllCustomers(req, res) {
        let self = this,
            latencyStart = new Date().getTime();
    
        try {     
            let customerDetails = _.get(req.body, 'customerDetails', null);
    
            if (customerDetails === null || !_.isArray(customerDetails) || _.isEmpty(customerDetails)) {
                self.L.error('getAllCustomers', 'Invalid customer details');
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:GET_ALL_CUSTOMERS", 'STATUS:ERROR','TYPE:INVALID_CUSTOMER_DETAILS']);
                utility._sendLatencyToDD(latencyStart, ["REQUEST_TYPE:GET_ALL_CUSTOMERS_LATENCY", 'TYPE:INVALID_CUSTOMER_DETAILS']);
                return res.status(422).json({ "status": 422, "errorStatus": "FAILURE", "customerData" : null, "message": "INVALID_CUSTOMER_DETAILS" });
            }

            if (customerDetails.length > 20) {
                self.L.log('getAllCustomers', 'Exceeded limit of 20 customer details');
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:GET_ALL_CUSTOMERS", 'TYPE:EXCEEDED_LIMIT', `VALUE:${customerDetails.length}`]);
            }

            self.L.log("getAllCustomers", "Request received", JSON.stringify(customerDetails));
            utility._sendMetricsToDD(customerDetails.length, ["REQUEST_TYPE:GET_ALL_CUSTOMERS", 'TYPE:TRAFFIC']);

            let queries = [
                self.bills.getCustomerIdsByRNServiceOperator(customerDetails)
                    .then(result => ({ status: 'fulfilled', value: result }))
                    .catch(error => ({ status: 'rejected', reason: error })), 
                self.bills.getCustomerIdsByRNServiceOperator(customerDetails, true)
                    .then(result => ({ status: 'fulfilled', value: result }))
                    .catch(error => ({ status: 'rejected', reason: error })), 
                self.nonPaytmBillsModel.getCustomerIdsByRNServiceOperator(customerDetails)
                    .then(result => ({ status: 'fulfilled', value: result }))
                    .catch(error => ({ status: 'rejected', reason: error }))
            ];
    
            let [ruResult, ruPrepaidResult, nonRuResult] = await Promise.all(queries);
            let dataFromRu = ruResult.status === 'fulfilled' ? ruResult.value : {};
            let prepaidDataFromResult = ruPrepaidResult.status === 'fulfilled' ? ruPrepaidResult.value : {};
            let dataFromNonRu = nonRuResult.status === 'fulfilled' ? nonRuResult.value : {};

            let data = _.mergeWith(dataFromRu, prepaidDataFromResult, dataFromNonRu, (objValue, srcValue) => {
                if (_.isArray(objValue)) {
                    return _.union(objValue, srcValue);
                }
            });
    
            if (Object.keys(data).length === 0) {
                self.L.error('getAllCustomers', 'No data found');
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:GET_ALL_CUSTOMERS", 'STATUS:SUCCESS', 'TYPE:DATA_NOT_FOUND']);
                utility._sendLatencyToDD(latencyStart, ["REQUEST_TYPE:GET_ALL_CUSTOMERS_LATENCY", 'TYPE:DATA_NOT_FOUND']);
                return res.status(200).json({ "status": 200, "customerData": {}, "errorStatus": "SUCCESS", "message": "DATA_NOT_FOUND" });
            } else {
                self.L.log('getAllCustomers', 'Data found successfully', JSON.stringify(data));
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:GET_ALL_CUSTOMERS", 'STATUS:SUCCESS', 'TYPE:DATA_FOUND']);
                utility._sendLatencyToDD(latencyStart, ["REQUEST_TYPE:GET_ALL_CUSTOMERS_LATENCY", 'TYPE:DATA_FOUND']);
                return res.status(200).json({ "status": 200, "customerData": data, "errorStatus": "SUCCESS", "message": "SUCCESS" });
            }
        } catch (error) {
            self.L.error('getAllCustomers', 'Error occurred', error);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:GET_ALL_CUSTOMERS", 'STATUS:ERROR', 'TYPE:PROCESSING_ERROR']);
            utility._sendLatencyToDD(latencyStart, ["REQUEST_TYPE:GET_ALL_CUSTOMERS_LATENCY", 'TYPE:PROCESSING_ERROR']);
            return res.status(500).json({ "status": 500, "customerData": null, "errorStatus": "FAILURE", "message": "PROCESSING_ERROR" });
        }
    }

}
export default Controller
