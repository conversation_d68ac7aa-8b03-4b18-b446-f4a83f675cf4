import startup from '../lib/startup';
import MOMENT from 'moment';
import _ from "lodash";
import <PERSON>Y<PERSON> from 'async';
import fs from 'fs'
import { eachLimit, eachSeries } from 'async';
import AWSCsvIngester from '../lib/awscsvingester';
import utility from '../lib'
import cassandraBills from '../models/cassandraBills';

let serviceName = "ACTIVE_PAYTM_USER_INGEST"
let progressFilePath = ''
let progressTracker = {}

/** Maintain offset and processed files in a path */
class ActivePaytmUserIngester {

    constructor(options) {
        let self = this;
        self.L = options.L;
        self.config = options.config;
        progressTracker = self.getProgressObject();
        self.csvIngester = new AWSCsvIngester(options, self.updateProgress);
        self.cassandraBills = new cassandraBills(options);

        self.logPrefix = serviceName;
        self.currentFile = "";
        progressFilePath = `/var/log/digital-reminder/progress-activePaytm-${MOMENT().format('YYYY-MM')}.json`;

        self.batchSize = _.get(options.config, ['DYNAMIC_CONFIG', serviceName, 'batch_size', 'value'], 1);
        self.bucketName = `${_.get(options.config, ['DYNAMIC_CONFIG', serviceName, 'bucket', 'value'], 'digital-reminder')}`;
        self.folderPath = `${_.get(options.config, ['DYNAMIC_CONFIG', serviceName, 'path', 'value'], 'digital-reminder/ACTIVE_PAYTM_USER_INGEST_TEST')}`;
        self.allowedNumberOfPaymentDates = _.get(options.config, ['DYNAMIC_CONFIG', 'SMART_FETCH_CONFIG', 'ACTIVE_PAYTM_USER', 'PAYMENT_DATE_LIST_SIZE'], 10);
        self.paymentDateFormat = _.get(options.config, ['DYNAMIC_CONFIG', serviceName, 'PAYMENT_DATE_FORMAT', 'value'], 'YYYY-MM-DD HH:mm:ss');

    }

    getProgressObject() {
        let self = this,
            progress = {};
        
        self.L.info(self.logPrefix, "::getProgressObject:", "Loading progress object from", progressFilePath);

        if (fs.existsSync(progressFilePath)) {
            const progressData = fs.readFileSync(progressFilePath, 'utf-8');
            progress = JSON.parse(progressData);
        }

        self.L.info(self.logPrefix, "::getProgressObject:", "Loaded", progress);
        return progress;
    }

    updateProgress(filename, count) {
        let self = this;

        if (_.get(progressTracker, [filename], 0) !== -1) {
            _.set(progressTracker, [filename], count);
            self.L.info(self.logPrefix, "::updateProgress:", "Updated progess Object", JSON.stringify(progressTracker), count);
            fs.writeFileSync(progressFilePath, JSON.stringify(progressTracker, null, 2));
        }
    }
        

    filterFileByMonth(filename) {
        try {
            let date = filename.split('$')[1].split('.')[0].slice(0, 7);
            if (date == MOMENT().format('YYYY-MM')) {
                return true;
            } else {
                return false;
            }
        } catch (err) {
            return false;
        }
    }

    start(callback) {
        let self = this;

        progressTracker = self.getProgressObject();

        try {
            self.csvIngester.configure(self.bucketName, self.logPrefix, self.batchSize);
        } catch (error) {
            self.L.critical(self.logPrefix, "::start:", "Cannot initialize AWS");
            return callback(error);
        }

        self.L.info(self.logPrefix, "::start:", "Getting Files in the folder");
        self.csvIngester.pushTestDataFileToS3();
        self.csvIngester.getFileNames(self.folderPath, function (err, data) {
            if (err) {
                self.L.error(self.logPrefix, "::start:", "Error while getting files");
                return callback(err)
            } else {
                data = _.filter(data, self.filterFileByMonth);
                return eachSeries(data, self.processEachFile.bind(self), callback);
            }
        })
    }

    processEachFile(filename, callback) {
        let self = this;

        if (_.get(progressTracker, filename, null) == -1) {
            self.L.info(self.logPrefix, "::processEachFile:", "Skipping file ", filename, " as it has been already processed");
            return callback();
        }

        self.L.info(self.logPrefix, "::processEachFile:", "Processing file :- ", filename);
        self.currentFile = filename;

        let skipRows = _.get(progressTracker, [filename], 0);
        self.L.info(self.logPrefix, "::processEachFile:", "Progress file loaded,", skipRows, " rows will be skipped.");

        self.csvIngester.start(self.processRecordinBatch.bind(this), filename, function (error, data) {
            return callback();
        }, skipRows);
    }

    async processRecordinBatch(data) {
        let self = this;

        return new Promise((resolve, reject) => {
            eachLimit(data, self.batchSize, function (record, cb) {
                self.processRecord(function () {
                    cb();
                }, record);
            }, function (error) {
                setTimeout(() => self.L.log(self.logPrefix, "::processRecordinBatch:", "Wating for 500ms before starting next batch"), 500);
                if (error) {
                    self.L.error(self.logPrefix, "::processRecordinBatch:", "Error while processing batch", error);
                    return reject(error);
                } else {
                    return resolve();
                }
            })
        })
    }

    processRecord(cb, data) {
        let self = this;

        let record = {
            customer_id: _.get(data, 'customer_id', null),
            service: _.get(data, 'service', null),
            payment_date: _.get(data, 'payment_date', null),
            source: 'productCSV'
        };

        if (!record.customer_id) {
            self.L.error(self.logPrefix, "::processRecordinBatch:", self.currentFile, 'Customer ID missing', record);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:ACTIVE_PAYTM_USER_INGEST", 'STATUS:CUST_ID_MISSING','TYPE:DB_INSERTION_CSV']);
            return cb();
        }

        if (typeof record.customer_id === 'string') {
            record.customer_id = parseInt(record.customer_id, 10);
            if (isNaN(record.customer_id)) {
                self.L.error(self.logPrefix, "::processRecordinBatch:", self.currentFile, 'Invalid Customer ID', record);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:ACTIVE_PAYTM_USER_INGEST", 'STATUS:INVALID_CUST_ID','TYPE:DB_INSERTION_CSV']);
                return cb();
            }
        }

        if (!record.service || _.isEmpty(record.service)) {
            self.L.error(self.logPrefix, "::processRecordinBatch:", self.currentFile, 'Service missing', record);
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:ACTIVE_PAYTM_USER_INGEST', 'STATUS:SERVICE_MISSING','TYPE:DB_INSERTION_CSV']);
            return cb();
        }

        if (!record.payment_date || _.isEmpty(record.payment_date)) {
            self.L.error(self.logPrefix, "::processRecordinBatch:", self.currentFile, 'Payment date missing', record);
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:ACTIVE_PAYTM_USER_INGEST', 'STATUS:PAYMENT_DATE_MISSING','TYPE:DB_INSERTION_CSV']);
            return cb();
        }
        
        try {
            record.payment_date = MOMENT(record.payment_date, self.paymentDateFormat).format('YYYY-MM-DD HH:mm:ss');
        } catch (error) {
            self.L.error(self.logPrefix, "::processRecordinBatch:", self.currentFile, 'Invalid payment date', record);
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:ACTIVE_PAYTM_USER_INGEST', 'STATUS:INVALID_PAYMENT_DATE','TYPE:DB_INSERTION_CSV']);
            return cb();
        }

        self.writeInTable(() => {
            return cb();
        }, record);
    }

    writeInTable(cb, record) {
        let self = this;

        ASYNC.waterfall([
            next => {
                self.cassandraBills.readActivePaytmUsersNewByCId(record.customer_id)
                .then((data) => {
                    self.L.info(self.logPrefix, "::writeInTable:", 'Data fetched from DB of size ', data.length, 'for ', record.customer_id);
                    return next(null, data);
                })
                .catch((error) => {
                    self.L.error(self.logPrefix, "::writeInTable:", 'select DB exception! for ' + JSON.stringify(record) + error);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:ACTIVE_PAYTM_USER_INGEST",'STATUS:ERROR','TYPE:DB_INSERTION_IN_TABLE','OPERATION:SELECT', 'SERVICE:' + record.service]);
                    return next(self.logPrefix, "::writeInTable:", 'select DB exception! for ' + JSON.stringify(record) + error);
                });
            },
            (data, next) => {
                let latest_payment_date = record.payment_date;
                let filteredData = [];
                if (!_.isEmpty(data)) {
                    data.forEach((row) => {
                        if (row.service === record.service) {
                            filteredData.push(row);
                        }
                        let payment_date_list = row.payment_date_list;
                        if (payment_date_list && _.isArray(payment_date_list)) {
                            let maxPaymentDate = Math.max(...payment_date_list.map(date => MOMENT(date).valueOf()));
                            if (MOMENT(maxPaymentDate).isAfter(MOMENT(latest_payment_date))) {
                                latest_payment_date = maxPaymentDate;
                            }
                        }
                    });
                    self.L.log(self.logPrefix, "::writeInTable:", 'Latest payment date for ', record.customer_id, ' is ', latest_payment_date);
                    return next(null, data, latest_payment_date, filteredData);
                } else {
                    self.L.log(self.logPrefix, "::writeInTable:", 'Latest payment date for ', record.customer_id, ' is ', latest_payment_date);
                    return next(null, data, latest_payment_date, filteredData);
                }
            },
            (data, latest_payment_date, filteredData, next) => {
                if (!_.isEmpty(filteredData)) {
                    let row = filteredData[0];
                    let payment_date_list = row.payment_date_list;
                    let created_at = row.created_at;
                    let created_source = row.created_source;

                    if (payment_date_list && _.isArray(payment_date_list)) {
                        payment_date_list.push(record.payment_date);
                        payment_date_list.sort((a, b) => MOMENT(a).valueOf() - MOMENT(b).valueOf());
                        if (payment_date_list.length > self.allowedNumberOfPaymentDates) {
                            payment_date_list = payment_date_list.slice(-self.allowedNumberOfPaymentDates);
                            self.L.log(self.logPrefix, "::writeInTable:", 'payment_date_list for ', record.customer_id, ' is ', payment_date_list);
                            return next(null, data, latest_payment_date, payment_date_list, created_source, created_at);
                        } else {
                            self.L.log(self.logPrefix, "::writeInTable:", 'payment_date_list for ', record.customer_id, ' is ', payment_date_list);
                            return next(null, data, latest_payment_date, payment_date_list, created_source, created_at);
                        }
                    } else {
                        payment_date_list = [record.payment_date];
                        self.L.log(self.logPrefix, "::writeInTable:", 'payment_date_list was not an array so initialized with ', payment_date_list);
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:ACTIVE_PAYTM_USER_INGEST",'STATUS:ERROR','TYPE:PAYMENT_DATE_COLUMN_IS_NOT_LIST', 'SERVICE:' + record.service]);
                        return next(null, data, latest_payment_date, payment_date_list, created_source, created_at);
                    }
                } else {
                    self.L.log(self.logPrefix, "::writeInTable:", 'No data found for ', record.customer_id, ' so initializing payment_date_list with ', [record.payment_date]);
                    return next(null, data, latest_payment_date, [record.payment_date], record.source, MOMENT().format('YYYY-MM-DD HH:mm:ss'));
                }
            },
            (data, latest_payment_date, payment_date_list, created_source, created_at, next) => {
                let promises = data.map((row) => {
                    if (row.service !== record.service) {
                        let params = [row.customer_id, row.service, latest_payment_date, row.payment_date_list, row.created_source, record.source, row.created_at, MOMENT().format('YYYY-MM-DD HH:mm:ss')];
                        return self.cassandraBills.writeInActivePaytmUsersNew(params)
                        .catch((error) => {
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:ACTIVE_PAYTM_USER_INGEST",'STATUS:ERROR','TYPE:DB_INSERTION_IN_TABLE','OPERATION:WRITE', 'SERVICE:' + row.service]);
                            throw new Error('activePaytmUsersIngester::writeInTable::insert DB exception! for other servcies of ' + JSON.stringify(record) + error);
                        });
                    } else {
                        return Promise.resolve();
                    }
                });

                // write out of loop for handling the case when writing first time for incoming service
                let params = [record.customer_id, record.service, latest_payment_date, payment_date_list, created_source, record.source, created_at, MOMENT().format('YYYY-MM-DD HH:mm:ss')];
                promises.push(
                    self.cassandraBills.writeInActivePaytmUsersNew(params)
                    .catch((error) => {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:ACTIVE_PAYTM_USER_INGEST",'STATUS:ERROR','TYPE:DB_INSERTION_IN_TABLE','OPERATION:WRITE', 'SERVICE:' + record.service]);
                        throw new Error('activePaytmUsersIngester::writeInTable::insert DB exception! for' + JSON.stringify(record) + error);
                    })
                );

                Promise.all(promises)
                .then(() => {
                    return next();
                })
                .catch((error) => {
                    return next(error);
                });
            }
        ], function (error) {
            if (error) {
                self.L.error(self.logPrefix, "::writeInTable:", 'Error inserting data in table for ', JSON.stringify(record),' error: ', error);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:ACTIVE_PAYTM_USER_INGEST", 'STATUS:ERROR','TYPE:DB_INSERTION_IN_TABLE', 'SERVICE:' + record.service]);
                return cb(error);
            } else {
                self.L.info(self.logPrefix, "::writeInTable:", 'Data inserted in table for ', record.customer_id, record.service);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:ACTIVE_PAYTM_USER_INGEST", 'STATUS:SUCCESS','TYPE:DB_INSERTION_IN_TABLE', 'SERVICE:' + record.service]);
                return cb();
            }
        });
    }
}

(function main() {
    if (require.main === module) {
        startup.init({

        }, function (err, options) {
            let script;
            try {
                script = new ActivePaytmUserIngester(options);
                script.start(
                    function (err) {
                        setTimeout(function () {
                            if (err) {
                                console.log("main::Error" + err);
                                process.exit(1);
                            } else {
                                console.log("main::completed");
                                process.exit(0);
                            }
                        }, 1000);
                    })
            } catch (err) {
                options.L.error(err)
                process.exit(1)
            }
        });
    }
})();

export default ActivePaytmUserIngester
