import L from 'lgr'
import _ from 'lodash'
import MOMENT from 'moment';

import ASYNC from 'async'
import startup from '../lib/startup'
import utility from '../lib'
import BILLS from '../models/bills'

class cleanup_billsCreditCard {
    constructor(options) {
        this.L = L;
        this.config = options.config;
        this.greyScaleEnv = _.get(options, 'greyScaleEnv', false);
        this.dbBatchSize = _.get(options, 'greyScaleEnv', false) == false ? 1000 : 5
        this.dbUpdateBatchSize = _.get(options, 'greyScaleEnv', false) == false ? 100 : 2
        this.id = _.get(options, 'startingID', 111111111)//ask for starting id
        this.dbInstance = options.dbInstance
        this.bills = new BILLS(options);
    }

    start(cb, opts) {
        let self = this;

        if (opts.verbose) {
            self.L.setLevel('verbose');
        }

        self.fetchRecords(function _doUntilNoMoreRecords(error, data) {
            if (!error && data && data.length > 0) {
                self._processRecordsInBatch(() => {
                    self.id = _.last(data)['id']

                    if (self.greyScaleEnv) {
                    }

                    self.L.log('fetchRecords', `processing next batch , Starting id  ${self.id}`);
                    setTimeout(() => {
                        self.fetchRecords(_doUntilNoMoreRecords);
                    }, 100);
                }, data, 0);
            } else {
                self.L.log('fetchRecords', `No record found for Starting id  ${self.id}`);
                cb();
            }
        });

    }


    fetchRecords(done) {
        let self = this;
        const query = `SELECT * from bills_creditcard where id > ${self.id} and par_id is not null order by id asc limit ${self.dbBatchSize} `
        console.log("fetchRecords", self.id);
        self.dbInstance.exec((error, res) => {
            if (error) {
                self.L.critical('writeCustomerDetails::', query, error);
            }
            return done(error, res);
        }, 'DIGITAL_REMINDER_SLAVE', query, [self.dbBatchSize, self.id]);
    }


    _processRecordsInBatch(done, records) {
        let
            self = this;

        ASYNC.eachLimit(records, 10, function (dataRow, cb) {
            self.bills.getBillByCustomer(function (error, record) {
                if (error) {
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:cleanup_billsCreditCard',
                        'STATUS: ERROR',
                        'TYPE:ERROR_GETTING_RECORD_FROM_DB',
                    ]);
                    self.L.critical(`getBillbyCustomer`, `failed with error:${error}`);
                    return done(error);
                } else if (record && _.isArray(record) && record.length > 0) {
                    for (let index = 0; index < record.length; index++) {
                        console.log("entered for loop");
                        if (dataRow.par_id && record[index].reference_id != dataRow.reference_id) {// do not compare same record
                            console.log("entered reference id check");
                            let dbRechargeNumber = record[index].recharge_number.replace(/\s+/g, '');
                            let incomingRechargeNumber = dataRow.recharge_number.replace(/\s+/g, '');
                            if (dbRechargeNumber && incomingRechargeNumber && dbRechargeNumber.substr(dbRechargeNumber.length - 4) == incomingRechargeNumber.substr(incomingRechargeNumber.length - 4) && _.toLower(record[index].bank_name) == _.toLower(dataRow.bank_name)) {
                                console.log("here", MOMENT(dataRow.due_date).format('YYYY-MM-DD HH:mm:ss') > MOMENT(record[index].due_date).format('YYYY-MM-DD HH:mm:ss'))
                                console.log("here2", MOMENT(dataRow.due_date) > MOMENT());//check if lastpaymentdate of record[index] is less than datarow.billfetchdate
                                if ((!record[index].payment_date || (record[index].payment_date && MOMENT(record[index].payment_date) < MOMENT(dataRow.bill_fetch_date))) &&
                                    (MOMENT(dataRow.due_date).format('YYYY-MM-DD HH:mm:ss') > MOMENT(record[index].due_date).format('YYYY-MM-DD HH:mm:ss') && MOMENT(dataRow.due_date) > MOMENT())) {

                                    let dbExtra = JSON.parse(_.get(record[index], 'extra', null));
                                    dbExtra.updated_source = "sms";
                                    let dataRowExtra = JSON.parse(_.get(dataRow, 'extra', null));
                                    let dbCustomerOtherInfo = JSON.parse(_.get(dataRow, 'customerOtherInfo', null));
                                    dbExtra.recon_id = dataRowExtra.recon_id;
                                    console.log("entered due date check");
                                    let params = {
                                        id: record[index].id,
                                        due_date: dataRow.due_date,
                                        amount: dataRow.amount,
                                        bill_date: dataRow.bill_date,
                                        bill_fetch_date: dataRow.bill_fetch_date,
                                        next_bill_fetch_date: dataRow.next_bill_fetch_date,
                                        status: dataRow.status,
                                        extra: JSON.stringify(dbExtra),
                                        customerOtherInfo: JSON.stringify(dbCustomerOtherInfo)
                                    };
                                    self.updateDb(function (error) {
                                        console.log("entered update db");
                                        if (error) {
                                            self.L.critical(`updateDb`, `Error updating table :: ${error}`);
                                        }
                                        utility._sendMetricsToDD(1, [
                                            'REQUEST_TYPE:cleanup_billsCreditCard',
                                            'STATUS: UPDATE_CIN_RECORD',
                                            'TYPE:UPDATED_RECORD',
                                        ]);
                                        console.log("Iteration complete for updateDb");
                                        //cb();
                                    }, params);
                                } else{
                                    self.updateCircle(function (error) {
                                        if (error) {
                                            self.L.critical(`updateCircle`, `Error updating table :: ${error}`);
                                        }
                                        utility._sendMetricsToDD(1, [
                                            'REQUEST_TYPE:cleanup_billsCreditCard',
                                            'STATUS: UPDATE_CIRCLE_RECORD',
                                            'TYPE:UPDATED_RECORD',
                                        ]);
                                        console.log("Iteration complete for updateCircle");
                                        //cb();    
                                    }, record[index].id);
                                }
                                self.deleteRecord(function (error) {
                                    if (error) {
                                        self.L.critical(`deleteRecord`, `Error deleting data :: ${error}`);
                                    }
                                    utility._sendMetricsToDD(1, [
                                        'REQUEST_TYPE:cleanup_billsCreditCard',
                                        'STATUS: RECORDS_DELETED',
                                        'TYPE:DELETED_RECORD',
                                    ]);
                                    console.log("Iteration complete");
                                    cb();
                                }, dataRow.id);
                            }
                        }
                    }
                    return done(null, "successfully deleted record");
                } else {
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:cleanup_billsCreditCard',
                        'STATUS:NO_TXN_FOUND'
                    ]);
                    return done(null);
                }

            }, "bills_creditcard", _.get(dataRow, 'customer_id', ''));
        }, function (err) {
            if (err) console.log("final err");
            done(err);
        });
    }


    updateDb(cb, params) {
        let self = this;

        let query = `UPDATE bills_creditcard SET amount = ?,due_date = ?,next_bill_fetch_date = ?,status = ?, bill_date = ?, bill_fetch_date = ?, extra = ?, circle = ?, customerOtherInfo = ? WHERE id = ?`,
            queryParams = [
                params.amount,
                params.due_date,
                params.next_bill_fetch_date,
                params.status,
                params.bill_date,
                params.bill_fetch_date,
                params.extra,
                'bug_fix',
                params.customerOtherInfo,
                params.id
            ];
        self.L.log("updateDb", 'query', self.dbInstance.format(query, queryParams));

        self.dbInstance.exec(function (err) {
            if (err) {
                self.L.log("self.dbInstance.exec error ", err, queryParams);
            }
            return cb(err);
        }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
    }

    updateCircle(cb, id) {
        let self = this;
        let query = `UPDATE bills_creditcard SET circle = ? WHERE id = ?`,//decide circle
        queryParams = [
            'bug_fix',
            id
        ];
        self.L.log("updateDb", 'query', self.dbInstance.format(query, queryParams));
        self.dbInstance.exec(function (err) {
            if (err) {
                self.L.log("self.dbInstance.exec error ", err, queryParams);
            }
            return cb(err);
        }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
    }

    deleteRecord(cb, id) {
        let self = this;
        if (!id || typeof (id) !== 'number') {//check id is number
            return cb("id is not provided");
        }
        let query = `DELETE FROM bills_creditcard WHERE id = ?`,
            queryParams = [
                id
            ];
        self.L.log("deleteRecord", 'query', self.dbInstance.format(query, queryParams));

        self.dbInstance.exec(function (err) {
            if (err) {
                self.L.log("self.dbInstance.exec error ", err, queryParams);
            }
            return cb(err);
        }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
    }

}

(function () {
    if (require.main === module) {
        var commander = require('commander');
        console.time('Execution Time');
        commander
            .version('0.0.1')
            .option('-v, --verbose', 'Run in verbose mode')
            .option('-r, --path <value>', 'path', String)
            .option('-s, --startingID <value>', 'startingID', Number)
            .parse(process.argv);

        startup.init({
        }, function (err, options) {
            if (err) {
                L.critical(' failed to load', err);
                process.exit(1);
            }
            try {
                let script = new cleanup_billsCreditCard({ ...options, startingID: commander.startingID });
                script.start(function (err) {
                    setTimeout(function () {
                        if (err) {
                            console.log(err);
                            console.log('FAILURE');
                            console.timeEnd('Execution Time');
                            process.exit(1);
                        }
                        console.log('SUCCESS');
                        console.timeEnd('Execution Time');
                        process.exit(0);
                    }, 1000);
                }, commander);
            } catch (err) {
                console.log(err)
            }
        });
    }
})();

// NODE_ENV=production node dist/scripts/airtel_csv_loader.js --clean_up 1 --path '/path/to/file/airtel.csv' -v