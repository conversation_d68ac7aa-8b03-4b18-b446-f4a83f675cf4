import L from "lgr";
import _ from "lodash";
import MOMENT from 'moment'
import startup from "../lib/startup";
import csv<PERSON><PERSON><PERSON> from "csvtojson";
const path = require("path");

class ct_ingest {
    constructor(options) {
        this.L = L;
        this.config = options.config;
        this.infraUtils = options.INFRAUTILS;
        this.initKafkaProducer();
    }

    initKafkaProducer() {
        this.smsKafkaPublisher = new this.infraUtils.kafka.producer({
            "kafkaHost": this.config.KAFKA.TOPICS.SMS_PARSING_CC.HOSTS
        });
        
        this.smsKafkaPublisher.initProducer('high', function (error) {
            if (error){
                console.error('Error initializing smsKafkaPublisher Producer: ', error);
                throw new Error('Error initializing Kafka Producer');
            }
            console.log("smsKafkaPublisher KAFKA PRODUCER STARTED....");
        });
    }

    async runForRecords(record) {
        let self = this;
        return new Promise(async (resolve, reject) => {
            const payload = {
                data: [record],
                kafka_topic: ["dwh-ingest-SMS_PARSING_CC_BILLS"]
            };

            self.smsKafkaPublisher.publishData([{
                topic: "dwh-ingest-SMS_PARSING_CC_BILLS",
                messages: JSON.stringify(payload)
            }],
            (error) => {
                if (error) {
                    console.error('publishInKafka :: publishCtEvents', 'Error while publishing message topic SMS_PARSING_CC - MSG:- ' + JSON.stringify(payload), error);
                    return reject(error);
                } else {
                    console.log('prepareKafkaResponse :: publishSMSEvents', 'Message published successfully in Kafka', ' on topic SMS_PARSING_CC', JSON.stringify(payload));
                    return resolve();
                }
            });
        });
    }

    async execute() {
        L.setLevel('verbose');

        const filePath = path.join(__dirname, "ingest_sms.csv");
        const payloads = await this.convertCSVToJson(filePath);

        console.log("payload", payloads);

        let i = 0;
        return new Promise(async (resolve, reject) => {
            try {
                if (payloads.length == 0) return resolve();
                let funSync = async () => {
                    await this.runForRecords(payloads[i])
                    .then(()=>{
                        i++;
                        if (i == payloads.length){
                            resolve();
                        } 
                        else funSync();
                    })
                    .catch((error)=>{
                        reject(error);
                    });
                };
                funSync();
            } 
            catch (e) {
                reject(e);
            }
        });
    }

    async convertCSVToJson(filePath) {
        try {
            const jsonArray = await csvtojson().fromFile(filePath);
            console.log(`CSV file converted to JSON with ${jsonArray.length} records`);
            return jsonArray.map(this.transformRecord);
        } catch (error) {
            throw new Error(`Error converting CSV to JSON: ${error.message}`);
        }
    }

    transformRecord(record) {
        return {
            appVersion: record.appversion,
            netWorkType: record.networktype,
            latitude: record.latitude,
            deviceId: record.deviceId,
            deviceDateTime: record.deviceDateTime,
            collector_timestamp: record.collector_timestamp,
            wakeUpTimeInterval: record.wakeUpTimeInterval,
            osVersion: record.osVersion,
            osType: record.osType,
            model: record.model,
            msg_id: record.msg_id,
            brand: record.brand,
            user_agent: record.user_agent,
            cId: record.cId,
            longitude: record.longitude,
            timestamp: record.timestamp,
            appCount: record.appCount,
            uploadFrequency: record.uploadFrequency,
            clientId: record.clientId,
            preference: [
                {
                    prefCat: "permission",
                    prefKeys: "ocl.permission.universal.sms_read_consent",
                    prefSubCat: "sms consent"
                },
                {
                    prefCat: "permission",
                    prefKeys: "ocl.permission.universal.whatsapp_consent",
                    prefSubCat: "sms consent"
                }
            ],
            mId: record.mId,
            eventType: record.eventType,
            uploadTime: record.uploadTime,
            true_client_ip: record.true_client_ip,
            consent: record.consent,
            realTime: record.realTime,
            db_name: record.db_name,
            newUser: record.newUser,
            event_name: record.event_name,
            batteryPercentage: record.batteryPercentage,
            smsUUID: record.smsUUID,
            smsDateTime: record.smsDateTime,
            smsRecSubId: record.smsRecSubId,
            smsBody: record.payload_smsbody,
            smsSenderID: record.payload_smssenderid,
            smsReceiver: record.payload_smsreceiver,
            smsOperator: record.smsOperator,
            lastCC: record.lastCC,
            dueAmt: record.dueAmt,
            totalAmt: record.totalAmt,
            dueDate: MOMENT(record.dueDate, "MM-DD-YYYY").format("YYYY-MM-DD"),
            bankName: record.bankName,
            billDate: record.billDate,
            isReliable: record.isReliable,
            isBlocked: record.isBlocked,
            paymentUnblocks: record.paymentUnblocks,
            isOverdue: record.isOverdue,
            url: [record.url],
            predicted_category: record.predicted_category,
            producerTime: record.producerTime
        };
    }
}

(async function () {
    if (require.main === module) {
        console.time('Execution Time');

        startup.init({}, function (err, options) {
            if (err) {
                console.log("Couldn't load config ", err);
                process.exit(1);
            } else {
                let script = new ct_ingest(options);
                script.execute()
                .then(() => {
                    console.log('SUCCESS');
                    console.timeEnd('Execution Time');
                    process.exit(0);
                })
                .catch(error => {
                    console.log('Error: FAILURE', error);
                    console.timeEnd('Execution Time');
                    process.exit(1);
                });
            }
        });
    }
})();