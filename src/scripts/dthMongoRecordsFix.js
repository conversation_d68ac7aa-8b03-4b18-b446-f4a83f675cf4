const INFRAUTILS = require('infra-utils');
const _ = require("lodash");
const path = require("path");
const FS = require("fs");
const csv = require('fast-csv');


let records = [];
let oid = {}

let noRecords = 0;
let deletedRecords = 0

/**
// prod
let slave = {
    username      : '',
    password      : '',
    host          : '***********',
    replicaHostStr: '***********:27017',
    replicas      : ['***********'],
    options       : 'replicaSet=rs0&w=0&readPreference=nearest',
    db            : 'inUsers',
    poolSize      : 5,
    port          : 27017
}

// prod
let master = {
    username      : '',
    password      : '',
    host          : '**********',
    replicaHostStr: '***********:27017,***********:27017',
    replicas      : ['***********','***********'],
    options       : 'replicaSet=rs0&w=0&readPreference=nearest',
    db            : 'inUsers',
    poolSize      : 5,
    port          : 27017
}
*/


/**
//dev
let slave = {
    username      : '',
    password      : '',
    host          : 'localhost',
    replicaHost   : 'localhost',
    replicaHostStr: 'localhost:27017',
    replicas      : ['localhost'],
    options       : 'replicaSet=localReplica&w=0&readPreference=nearest',
    db            : 'inUsers',
    poolSize      : 5,
    port          : 27017
}

// development
let master = {
    username      : '',
    password      : '',
    host          : 'localhost',
    replicaHost   : 'localhost',
    replicaHostStr: 'localhost:27017',
    replicas      : ['localhost'],
    options       : 'replicaSet=localReplica&w=0&readPreference=nearest',
    db            : 'inUsers',
    poolSize      : 5,
    port          : 27017
}
*/

//staging
// let master = {
//     username      : '',
//     password      : '',
//     host          : '***********',
//     replicaHostStr: '**********:27017',
//     replicas      : ['**********'],
//     options       : 'replicaSet=rs_0&w=0&readPreference=nearest',
//     db            : 'inUsers',
//     poolSize      : 5,
//     port          : 27017
// }

//staging
// let slave = {
//     username      : '',
//     password      : '',
//     host          : '***********',
//     replicaHostStr: '**********:27017',
//     replicas      : ['**********'],
//     options       : 'replicaSet=rs_0&w=0&readPreference=nearest',
//     db            : 'inUsers',
//     poolSize      : 5,
//     port          : 27017
// }

const readCsv = () => {
    FS.createReadStream(path.resolve(__dirname, '', 'data.csv'))
    .pipe(csv.parse())
    .on('error', error => console.error(error))
    .on('data', row => {
        let oidValue = JSON.parse(Object.values(row));
        oid[oidValue.$oid] = {}
    })
    .on('end', async rowCount => {
        console.log(`Parsed ${rowCount} rows`)
        await readMongodata();
        await deleteMongoData();
    });
    
}

const readMongodata = async () => {
    console.log("coming in to read Mongo data")
    try {
        let mongoDbInstance = new INFRAUTILS.mongo(slave);
        let masterMongoInstance = new INFRAUTILS.mongo(master)

        console.log("-- making_connection---");

        await mongoDbInstance.connect(() => { });
        console.log("--slave connected---");

        await masterMongoInstance.connect(() => {});
        console.log("--master connected---");

        for(let id in oid) {
            let mongoId = await mongoDbInstance.getObjectIdByString(id);
            let readQuery = {
                query: {
                    _id : mongoId 
                }
            }
            let deleteQuery = {
                _id: mongoId
            }
            console.log("fetching records for Oid ", mongoId);
            let resp = await mongoDbInstance.fetchDataFromCollection(() => { }, 'users', readQuery);

            if(resp.length == 1 && _.get(resp, [0, 'service']) === 'dth') {
                await masterMongoInstance.deleteDataInCollection(()=>{}, 'users', deleteQuery)
                deletedRecords += 1
            } else {
                console.log("no record found ", id, " resp", resp);
                noRecords++;
            }
        }

        console.log(" No records counter", noRecords);
        console.log("deleted records counter", deletedRecords)
    } catch (error) {
        console.log("Error while reading mongo data", error);
    }
}


const start = async () => {
    await readCsv();
}

start();
