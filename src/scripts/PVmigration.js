const async = require('async');
import startup from '../lib/startup'
import _ from 'lodash'
import Helper from '../lib/helper'



let OPTIONS = null;
let id = 0;
let recordsAdded = 0;

function updatedNewTables(options, data, cb) {
    async.eachLimit(data, 50, function (row, localCb) {
        setImmediate(function () {
            const rowValues = _.values(row)
            const operator = row.operator;
            const tableName = _.get(OPTIONS.config, ['PREPAID_TABLE_REGISTRY', operator], null);
            console.log(tableName)
            if(!tableName){
                return localCb('updateNewTables:: missing table name /operator')
            }
            let sql = `INSERT INTO \
                        ${tableName} (recharge_number, customer_id,operator, service , circle , amount, extra, \
                        validity_expiry_date, category_name, order_date , latest_recharge_date , plan_bucket , order_ids,status, \
                        cust_mobile, cust_email) \
                        VALUES ? ON DUPLICATE KEY UPDATE \
                        customer_id=VALUES(customer_id),amount=VALUES(amount),\
                        service=VALUES(service),operator=VALUES(operator),circle=VALUES(circle), extra=VALUES(extra), \
                        validity_expiry_date=VALUES(validity_expiry_date),order_ids=VALUES(order_ids), \
                        category_name=VALUES(category_name),latest_recharge_date=VALUES(latest_recharge_date),status=VALUES(status), \
                        cust_mobile=VALUES(cust_mobile), cust_email=VALUES(cust_email);`;

            recordsAdded++;
            OPTIONS.dbInstance.exec(function (err) {
                if (err) {
                    return localCb(err);
                }
                
                return localCb(null);
            }, 'RECHARGE_ANALYTICS', sql, [[rowValues]]);
        });
    }, cb);
}

function readDataFromPV(options, cb) {
    let returnData = [];
    console.log("Now using id: ", id)
    let sql = `SELECT id,recharge_number, customer_id, operator, service, circle, amount, extra, validity_expiry_date, category_name, order_date, latest_recharge_date, plan_bucket, order_ids, status, cust_mobile, cust_email from recharge_analytics.${options.pvTableName} where operator in (?) and id > ${id} ORDER BY id limit ${options.size}`;
    console.log( OPTIONS.dbInstance.format(sql, [options.operator]));

    OPTIONS.dbInstance.exec(function (err, data) {
        //console.log("data in read Pv", JSON.stringify(data))
        if (err || data.length == 0) {
            console.log(err)
            console.log("total records ADDED from plan Validity table: ", recordsAdded);
            return cb(err);
        }

        data.forEach(element => {
            let row = {}
            if(!element.customer_id || !element.operator || !element.recharge_number) {
                return;
            }
            row['recharge_number'] = element.recharge_number
            row['customer_id'] = element.customer_id
            row['operator'] = element.operator
            row['service'] = element.service
            row['circle'] = element.circle
            row['amount'] = element.amount
            row['extra'] = element.extra
            row['validity_expiry_date'] = element.validity_expiry_date
            row['category_name'] = element.category_name
            row['order_date'] = element.order_date
            row['latest_recharge_date'] = element.latest_recharge_date
            row['plan_bucket'] = element.plan_bucket
            row['order_ids'] = element.order_ids
            row['status'] = element.status
            row['cust_mobile'] = element.cust_mobile
            row['cust_email'] = element.cust_email
            returnData.push(row)
        });
        
        id = data[data.length-1].id //max id
        return cb(null, returnData);
    }, 'RECHARGE_ANALYTICS_SLAVE', sql, [options.operator]);
}

function executeRecursive(options, cb){
    readDataFromPV(options, function(err, data) {
        if(err){
            return cb(err);
        }
        if(!data) {
            return cb("no more data in PV table")
        }
        updatedNewTables(options, data, function (err) {
            executeRecursive(options, cb)
        })
    })
}

function executeFlow(options, cb) {
    executeRecursive(options, function (err) {
        if (err) {
            return cb(err);
        }
    });
}

function main(opts, cb) {
    startup.init({
        exclude: {
            cvr: true,
            mongoDb: true,
            ruleEngine: true,
            activePidLib: true,
            dynamicConfig: false
        }
    }, function (err, options) {
        if (err) {
            return cb(err);
        }
        if (!opts.operator) {
            return cb(new Error('operator is mandatory'));
        }
        OPTIONS = options;
        executeFlow({
            pvTableName: 'plan_validity',
            operator : opts.operator,
            size: 1000
        }, function (err) {
            setTimeout(function () {
                if (err) {
                    console.log(err);
                    process.exit(1);
                }
                process.exit(0);
            }, 1000);
        })
    });
}

(function () {
    if (require.main === module) {
        var commander = require('commander');
        commander
            .version('0.0.1')
            .option('-t, --operator <value>', 'operator', Helper.list)
            .parse(process.argv);
        main(commander, function (err) {
            setTimeout(function () {
                if (err) {
                    console.log(err);
                    console.log('FAILURE');
                    console.timeEnd('Execution Time');
                    process.exit(1);
                }
                console.log('SUCCESS');
                console.timeEnd('Execution Time');
                process.exit(0);
            }, 1000);
        });
    }
})();
