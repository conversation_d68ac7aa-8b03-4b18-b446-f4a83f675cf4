import startup from '../lib/startup';
import ASYNC from 'async'
import _ from 'lodash';
import L from 'lgr';
import AWS from 'aws-sdk'
import csv from 'fast-csv'
import { parseStream } from 'fast-csv';
import fs from 'fs'
import utility from '../lib'
import VALIDATOR from 'validator'

let operatorMissing = 0,
    customerIdMissing = 0,
    serviceMissing = 0,
    invalidRow = 0,
    totalRow = 0,
    batch = 1000,
    id=0;
class cappingNotification{

    constructor(options) {
        this.L = options.L;
        this.rechargeConfig = options.rechargeConfig;
        this.dbInstance = options.dbInstance;
        this.activePidLib = options.activePidLib;
        this.config = options.config;
    }

    execPostpaidDBQuery(query, operationType , cb) {
        let self = this;
        self.L.log(`Executing: ${query}`);
        let MASER_SLAVE_TYPE = '';
        if(operationType === "READ") {
            MASER_SLAVE_TYPE = 'DIGITAL_REMINDER_SLAVE';
        } else if (operationType === "WRITE") {
            MASER_SLAVE_TYPE = 'DIGITAL_REMINDER_MASTER';
        } else {
            cb('ERROR :: DB operation not specified');
        }
    
        self.dbInstance.exec(function (err, data) {
            if (err) {
                return cb(err);
            }
            return cb(null, data);
        }, MASER_SLAVE_TYPE, query, []);
    }

    execPrepaidDBQuery(query, operationType , cb) {
        let self = this;
        self.L.log(`Executing: ${query}`);
        let MASER_SLAVE_TYPE = '';
        if(operationType === "READ") {
            MASER_SLAVE_TYPE = 'RECHARGE_ANALYTICS_SLAVE';
        } else if (operationType === "WRITE") {
            MASER_SLAVE_TYPE = 'RECHARGE_ANALYTICS';
        } else {
            cb('ERROR :: DB operation not specified');
        }
    
        self.dbInstance.exec(function (err, data) {
            if (err) {
                return cb(err);
            }
            return cb(null, data);
        }, MASER_SLAVE_TYPE, query, []);
    }


    delay(cb, timeout) {
        setTimeout(()=>{
            return cb()
        },timeout)        
    }

    readPostpaidTableAndUpdate(params, tableName, cb) {
       let self = this,
            recordIds = [],
            customerId = _.get(params, 'customerId', null),
            service    = _.get(params, 'service', null),
            operator   = _.get(params, 'operator', null);

        console.log("Now using id greater than: ", id);

        ASYNC.waterfall([
            (next)=> {
                let select_query = `SELECT id FROM ${tableName} WHERE customer_id = ${customerId} AND service = '${service}' AND operator = '${operator}' AND  id > ${id} ORDER BY id limit ${batch};`;
                self.L.log(`readPostpaidTableAndUpdate :: select_query:${select_query}`);
                return self.execPostpaidDBQuery(select_query, "READ", next);
            },
            (dbResponse, next)=> {
                recordIds =  dbResponse ? dbResponse : [];
                if (recordIds && recordIds.length) {
                    recordIds = recordIds.map((record)=>{return record.id});
                    let update_query = `UPDATE ${tableName} SET notification_status = 0, status = 7, extra = JSON_SET(COALESCE(extra,'{}' ) ,'$.blockedBy','cappingReminder') WHERE id  IN (${recordIds}) ;`;
                    self.L.log(`readPostpaidTableAndUpdate :: update_query:${update_query}`);  
                    return self.execPostpaidDBQuery(update_query, "WRITE", next); 
                } else {
                    self.L.log(`readPostpaidTableAndUpdate :: No matching Records found in table '${tableName}' for id > ${id}`);
                    next(null, []);
                }
            },
            (dbResponse, next)=>{
                let delayTimeinMilliseconds = 10;
                self.delay(next, delayTimeinMilliseconds);
            }
            ], 
            (err) => {
                if(err) {
                    let errMsg = 'Error in readPostpaidTableAndUpdate :: ' + err;
                    L.error(errMsg);
                    return cb(errMsg);
                } 
                if (recordIds && recordIds.length) {    
                    id = recordIds[recordIds.length-1];
                    self.readPostpaidTableAndUpdate(params, tableName, cb);
                } else {
                    id=0;
                    cb();
                }
        });
    }

    async readPrepaidTableAndUpdate(params, tableName, cb) {
        let self = this,
            recordIds = [],
            customerId = _.get(params, 'customerId', null),
            service    = _.get(params, 'service', null),
            operator   = _.get(params, 'operator', null);

        console.log("Now using id greater than: ", id);

        ASYNC.waterfall([
            (next)=> {
                let select_query = `SELECT id FROM ${tableName} WHERE customer_id = ${customerId} AND service = '${service}' AND operator = '${operator}' AND  id > ${id} ORDER BY id limit ${batch};`;
                self.L.log(`readPrepaidTableAndUpdate :: select_query:${select_query}`);
                return self.execPrepaidDBQuery(select_query, "READ", next);
            },
            (dbResponse, next)=> {
                recordIds =  dbResponse ? dbResponse : [];
                if (recordIds && recordIds.length) {
                    recordIds = recordIds.map((record)=>{return record.id});
                    let update_query = `UPDATE ${tableName} SET notification_status = 0, status = 7, extra = JSON_SET(COALESCE(extra,'{}' ) ,'$.blockedBy','cappingReminder') WHERE id  IN (${recordIds}) ;`;
                    self.L.log(`readPrepaidTableAndUpdate :: update_query:${update_query}`);  
                    return self.execPrepaidDBQuery(update_query, "WRITE", next); 
                } else {
                    self.L.log(`readPrepaidTableAndUpdate :: No matching Records found in table '${tableName}' for id > ${id}`);
                    next(null, []);
                }
            },
            (dbResponse, next)=>{
                let delayTimeinMilliseconds = 10;
                self.delay(next, delayTimeinMilliseconds);
            }
            ], 
            (err) => {
                if(err) {
                    let errMsg = 'Error in readPrepaidTableAndUpdate :: ' + err;
                    L.error(errMsg);
                    return cb(errMsg);
                } 
                if (recordIds && recordIds.length) {    
                    id = recordIds[recordIds.length-1];
                    self.readPrepaidTableAndUpdate(params, tableName, cb);
                } else {
                    id=0;
                    cb();
                }
        });
             
    }

    async  processRecord(cb, data){
        let self = this,
            prepaidServices = _.get(self.config, ['DYNAMIC_CONFIG', 'PREPAID_SERVICES_LIST', 'SERVICES', 'INCLUDE'], 'mobile'),
            excludedOperator = _.get(self.config, ['DYNAMIC_CONFIG', 'OPERATOR_POSTPAIDFLOW', 'COMMON', 'INCLUDE_OPERATOR'],[]),
            params = {
            'customerId': _.get(data, 'cust_id', null),
            'service': _.get(data, 'service', null).toLowerCase(),
            'operator': _.get(data, 'operator', null).toLowerCase()
            }

        if(!params.customerId){
            customerIdMissing++;
            return cb('Get customeId as null from CSV File');
        }
        if(!params.operator){
            operatorMissing++;
            return cb('Get operator as null from CSV File');
        }
        if(!params.service){
            serviceMissing++;
            return cb('Get service as null from CSV File');
        } 
        
        // parse customerId to integer if it is a number
        if (params.customerId) {
            params.customerId = typeof (params.customerId) === 'string' ? VALIDATOR.toInt(params.customerId) : params.customerId;   
        }

        let tableName = _.get(self.config, ['OPERATOR_TABLE_REGISTRY', params.operator], null);

        if(tableName){   
            ASYNC.series([
                function (callback) {
                    //for postpaid records
                    self.readPostpaidTableAndUpdate(params, tableName, (error)=>{
                        if (error) {
                            callback(error);
                        } else {
                            callback();
                        }
                    });
                },
                function (callback) {
                    if(prepaidServices.includes(params.service) && excludedOperator.indexOf(params.operator) < 0){
                        //for prepaid records
                        tableName = 'plan_validity';
                        self.L.log(`After postpaid check for prepaid, cust_Id: ${params.customerId}, service: '${params.service}'  , operator: '${params.operator}' ',' check update for prepaid table: '${tableName}'`);
                        self.readPrepaidTableAndUpdate(params, tableName, (error)=>{
                            if (error) {
                                callback(error);
                            } else {
                                callback();
                            }
                        });
                    }
                    else{
                    callback();
                    }
                }   
            ], function done(err) {
                return cb(err);
        });
        }
        else if(prepaidServices.includes(params.service)){
            //for prepaid records
            tableName = 'plan_validity';
            self.L.log(`Table not found for cust_Id: ${params.customerId}, service: '${params.service}'  , operator: '${params.operator}' ',' check update for prepaid table: '${tableName}'`);
            self.readPrepaidTableAndUpdate(params, tableName, (error)=>{
                return cb(error);
                });
        }else{
            invalidRow++;
            return cb('Invalid record');
        }
    }

    async getCSV_DataFrom_AWS_S3_Bucket(cb){
        let self = this;
        const path = 'digital-reminder/NotificationCapping**/'
        //let fileName = `Reminders_Notification_Capping_${(new Date().toJSON().slice(0,10))}.csv`,
        //Changing file name for automation purpose
        let fileName = `mapping2.csv`,
            fileKey = path + fileName ;
        const params = {
            Bucket:  "digital-reminder",
            Key: fileKey
        };
        var s3 = new AWS.S3();
        try {
        //   const s3Stream = fs.createReadStream('/Users/<USER>/Downloads/test.csv')
            const head =  await s3.headObject(params).promise();
            const s3Stream = s3.getObject(params).createReadStream();
            const csvStream = parseStream(s3Stream,{headers : true});
        csvStream
            .on('data', (data) => {
                    totalRow++;
                    self.L.log(`getCSV_DataFrom_AWS_S3_Bucket :: processing for cust_Id: ${data.cust_id}, service: '${data.service}'  , operator: '${data.operator}' ,Row no: ${totalRow}`);
                    csvStream.pause();
                    self.processRecord((err)=>{
                        if(err){   
                        self.L.error('getCSV_DataFrom_AWS_S3_Bucket :: Error in processing data', err, data);
                        }
                        utility._sendMetricsToDD(1, ['REQUEST_TYPE:CAPPING_NOTIFICATION', 'STATUS:READ_CSV_ROW']);
                        csvStream.resume();
                    }, data);
            })
            .on('end', rowCount => {
                setTimeout(()=>{
                    self.L.log(`getCSV_DataFrom_AWS_S3_Bucket :: ${rowCount} Data rows processed succesfully !!`);
                    return cb();
                },1000);
            })
            .on('error', error => {
                    return cb(error);
            });

        }catch(err)
        {
        return cb(err);
        }
    }
    
    async executeFlow(cb){
        let self = this;              
        await self.getCSV_DataFrom_AWS_S3_Bucket(function (err) {
            if (err) {
                self.L.error('executeFlow :: Error ',err);
            }else{
                self.L.log(`executeFlow::completed, totalRowCount : ${totalRow}, invalidRowCount : ${invalidRow}, customerIdMissingCount : ${customerIdMissing}, serviceMissingCount : ${serviceMissing}, operatorMissingCount : ${operatorMissing}`);
            }
            return cb(err);
        });
    }
 
}
    
function main() {
    startup.init({
        exclude: {
            cvr: true,
            mongoDb: true,
            ruleEngine: true,
            activePidLib: true,
            dynamicConfig: true
        }
    }, function (err, options) {
        if (err) {
            return cb(err);
        }
        let script = new cappingNotification(options);
        script.executeFlow(function (err) {
            setTimeout(function () {
            if (err) {
                console.log("main :: Error " ,err);
                process.exit(1);
                } else {
                console.log("main :: completed");
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:CAPPING_NOTIFICATION', 'STATUS:SUCCESS']);
                process.exit(0);
                }
            }, 1000); 
        })
    });
}
    
(function () {
    if (require.main === module) {
        main();
    }
})();