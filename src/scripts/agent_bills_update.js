import L        from 'lgr'
import startup from '../lib/startup'
import utility from '../lib'
import async from 'async'
import _ from 'lodash'
import cassandra from 'cassandra-driver'

import cassandraBills from '../models/cassandraBills';

class agent_bill_udate {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.cassandraBills = new cassandraBills(options);
        this.rechargeSagaCassandraDb = options.rechargeSagaCassandraDb;
        let cassConfig = _.get(this.config, 'CASSANDRA.RECHARGE_SAGA_CASSANDRA', {});
        let recentKeyspace = this.config.CASSANDRA.recent_keyspace;
        _.set(cassConfig, 'keyspace', recentKeyspace);
        this.rechargeSagaCassandraDbRecentKeySpace = new cassandra.Client(cassConfig);
    }
        
    processRecord(cb) {
        let self = this;
        self.L.log("processRecord:: Start");
        async.waterfall([ 
            next => {
                let query = `SELECT customerid FROM user_agent`;
                this.queryRecentCassDbOnRecentsTable(query, [])
                .then(records => {
                    next(null, records);
                })
                .catch(err => {
                    next(err);
                });
            },
            (records, next) => {
                async.eachSeries(records, (record, callback) => {
                    this.cassandraBills.deleteAgentrecentBill((err) => {
                        if(err) {
                            return callback(err);
                        }
                        return callback();
                    }, record);
                }, (err) => {
                    if(err) {
                        return next(err);
                    }
                    return next();
                });
            },
        ],  (err) => {
            if(err) {
                let errMsg = 'Error in processRecord :: ' + err;
                L.error(errMsg);
                return cb(errMsg);
            } 
            cb();
        });
    }

    async queryRecentCassDbOnRecentsTable(query, params, next) {
        this.L.log("queryRecentCassDbOnRecentsTable:: query: ", query, " and params: ", params);
        this.L.log("this.rechargeSagaCassandraDbRecentKeySpace: ", this.rechargeSagaCassandraDbRecentKeySpace);
        return new Promise((resolve, reject) => {
            let latencyStart = new Date();
            this.rechargeSagaCassandraDbRecentKeySpace.execute(query, params, { prepare: true})
            .then(result => {
            this.L.log("queryRecentCassDbOnRecentsTable:: result: ", result.rows);
                resolve(result.rows);
            })
            .catch(err => {
                reject(err);
            });
        });
    }
}

function execute(opts, cb) {
    L.setLevel('verbose');
    console.log('Execution Time');
    startup.init({}, function (err, options) {
        if (err) {
            console.log(' Error in startup.init :: ', err);
            return cb(err);
        }
        let batchSize = 1; // <null/1/500>
        console.log('tem Time');
        let agent_bill_update = new agent_bill_udate(options);
        console.log('Execution Time');
        agent_bill_update.processRecord(cb);
    });
}

(function () {
    if (require.main === module) {
        var commander = require('commander');
        console.time('Execution Time');
        commander
            .version('0.0.1')
            .parse(process.argv);

        execute(commander, function (err) {
            setTimeout(function () {
                if (err) {
                    console.log(err);
                    console.log('FAILURE');
                    console.timeEnd('Execution Time');
                    process.exit(1);
                }
                console.log('SUCCESS');
                console.timeEnd('Execution Time');
                process.exit(0);
            }, 1000);
        });
    }
})();